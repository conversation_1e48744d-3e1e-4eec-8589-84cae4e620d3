<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomeFurnitureGroup (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeFurnitureGroup (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":42,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeFurnitureGroup.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeFurnitureGroup.html" target="_top">Frames</a></li>
<li><a href="HomeFurnitureGroup.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class HomeFurnitureGroup" class="title">Class HomeFurnitureGroup</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomePieceOfFurniture</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomeFurnitureGroup</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeFurnitureGroup</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></pre>
<div class="block">A group of furniture of furniture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.HomeFurnitureGroup">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#EMPTY_PROPERTY_ARRAY">EMPTY_PROPERTY_ARRAY</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.util.List-float-boolean-java.lang.String-">HomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  float&nbsp;angle,
                  boolean&nbsp;modelMirrored,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-java.lang.String-">HomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;leadingPiece,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.util.List-java.lang.String-">HomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.lang.String-java.util.List-float-boolean-java.lang.String-">HomeFurnitureGroup</a></span>(java.lang.String&nbsp;id,
                  java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  float&nbsp;angle,
                  boolean&nbsp;modelMirrored,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this group with cloned furniture.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#containsPoint-float-float-float-">containsPoint</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if one of the pieces of this group contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#duplicate--">duplicate</a></span>()</code>
<div class="block">Returns a copy of this object and its children with new ids.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getAllFurniture--">getAllFurniture</a></span>()</code>
<div class="block">Returns the furniture of this group and of all its subgroups, including the possible child furniture groups.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getCatalogId--">getCatalogId</a></span>()</code>
<div class="block">Returns the catalog ID of this group.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getCreator--">getCreator</a></span>()</code>
<div class="block">Returns the creator set for this group.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getCurrency--">getCurrency</a></span>()</code>
<div class="block">Returns the currency of the furniture of this group
 or <code>null</code> if one piece has no currency
 or has a currency different from the other furniture.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getDepth--">getDepth</a></span>()</code>
<div class="block">Returns the depth of this group.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getDepthInPlan--">getDepthInPlan</a></span>()</code>
<div class="block">Returns the depth of this group.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getDropOnTopElevation--">getDropOnTopElevation</a></span>()</code>
<div class="block">Returns the elevation at which should be placed an object dropped on this group.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getFurniture--">getFurniture</a></span>()</code>
<div class="block">Returns an unmodifiable list of the furniture of this group.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height of this group.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getHeightInPlan--">getHeightInPlan</a></span>()</code>
<div class="block">Returns the height of this group.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getInformation--">getInformation</a></span>()</code>
<div class="block">Returns the information associated with this group.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getModelFlags--">getModelFlags</a></span>()</code>
<div class="block">Returns 0.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getModelMaterials--">getModelMaterials</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getModelRotation--">getModelRotation</a></span>()</code>
<div class="block">Returns an identity matrix.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getModelSize--">getModelSize</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getModelTransformations--">getModelTransformations</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns 0.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getPlanIcon--">getPlanIcon</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getPrice--">getPrice</a></span>()</code>
<div class="block">Returns the price of the furniture of this group with a price.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getPriceValueAddedTaxIncluded--">getPriceValueAddedTaxIncluded</a></span>()</code>
<div class="block">Returns the total price of the furniture of this group.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getRoll--">getRoll</a></span>()</code>
<div class="block">Returns 0.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getShininess--">getShininess</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getTexture--">getTexture</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getValueAddedTax--">getValueAddedTax</a></span>()</code>
<div class="block">Returns the VAT of the furniture of this group.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></span>()</code>
<div class="block">Returns the VAT percentage of the furniture of this group
 or <code>null</code> if one piece has no VAT percentage
 or has a VAT percentage different from the other furniture.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the width of this group.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getWidthInPlan--">getWidthInPlan</a></span>()</code>
<div class="block">Returns the width of this group.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></span>(float&nbsp;x0,
                   float&nbsp;y0,
                   float&nbsp;x1,
                   float&nbsp;y1)</code>
<div class="block">Returns <code>true</code> if one of the pieces of this group intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isBackFaceShown--">isBackFaceShown</a></span>()</code>
<div class="block">Returns <code>false</code>.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isDeformable--">isDeformable</a></span>()</code>
<div class="block">Returns <code>true</code> if all furniture of this group are deformable.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isDoorOrWindow--">isDoorOrWindow</a></span>()</code>
<div class="block">Returns <code>true</code> if all furniture of this group are doors or windows.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></span>()</code>
<div class="block">Returns <code>false</code>.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isHorizontallyRotated--">isHorizontallyRotated</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece or a child of this group is rotated around an horizontal axis.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isModelCenteredAtOrigin--">isModelCenteredAtOrigin</a></span>()</code>
<div class="block">Returns <code>true</code>.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isMovable--">isMovable</a></span>()</code>
<div class="block">Returns <code>true</code> if this group is movable.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isResizable--">isResizable</a></span>()</code>
<div class="block">Returns <code>true</code> if all furniture of this group are resizable.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#isTexturable--">isTexturable</a></span>()</code>
<div class="block">Returns <code>true</code> if all furniture of this group are texturable.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#scale-float-">scale</a></span>(float&nbsp;scale)</code>
<div class="block">Scales this group and its children with the given <code>ratio</code>.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setAngle-float-">setAngle</a></span>(float&nbsp;angle)</code>
<div class="block">Sets the <code>angle</code> of the furniture of this group.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setBackFaceShown-boolean-">setBackFaceShown</a></span>(boolean&nbsp;backFaceShown)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the <code>color</code> of the furniture of this group.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setDepth-float-">setDepth</a></span>(float&nbsp;depth)</code>
<div class="block">Sets the <code>depth</code> of this group, then moves and resizes its furniture accordingly.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setElevation-float-">setElevation</a></span>(float&nbsp;elevation)</code>
<div class="block">Sets the <code>elevation</code> of this group, then moves its furniture accordingly.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setHeight-float-">setHeight</a></span>(float&nbsp;height)</code>
<div class="block">Sets the <code>height</code> of this group, then moves and resizes its furniture accordingly.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setIcon-com.eteks.sweethome3d.model.Content-">setIcon</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon)</code>
<div class="block">Sets the icon of this piece of furniture.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Set the level of this group and the furniture it contains.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Sets the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setModelFlags-int-">setModelFlags</a></span>(int&nbsp;modelFlags)</code>
<div class="block">Sets the flags applied to the piece of furniture model.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">setModelMaterials</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;modelMaterials)</code>
<div class="block">Sets the materials of the furniture of this group.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setModelMirrored-boolean-">setModelMirrored</a></span>(boolean&nbsp;modelMirrored)</code>
<div class="block">Sets whether the furniture of this group should be mirrored or not.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setModelRotation-float:A:A-">setModelRotation</a></span>(float[][]&nbsp;modelRotation)</code>
<div class="block">Sets the rotation 3 by 3 matrix of this piece of furniture and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setModelSize-java.lang.Long-">setModelSize</a></span>(java.lang.Long&nbsp;modelSize)</code>
<div class="block">Sets the size of the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">setModelTransformations</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;modelTransformations)</code>
<div class="block">Sets the transformations of this group.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setMovable-boolean-">setMovable</a></span>(boolean&nbsp;movable)</code>
<div class="block">Sets whether this group is movable or not.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setPlanIcon-com.eteks.sweethome3d.model.Content-">setPlanIcon</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon)</code>
<div class="block">Sets the plan icon of this piece of furniture.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setPrice-java.math.BigDecimal-">setPrice</a></span>(java.math.BigDecimal&nbsp;price)</code>
<div class="block">Sets the price of this group.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setShininess-java.lang.Float-">setShininess</a></span>(java.lang.Float&nbsp;shininess)</code>
<div class="block">Sets the shininess of the furniture of this group.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setStaircaseCutOutShape-java.lang.String-">setStaircaseCutOutShape</a></span>(java.lang.String&nbsp;staircaseCutOutShape)</code>
<div class="block">Sets the shape used to cut out upper levels when they intersect with the piece
 like a staircase.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setTexture-com.eteks.sweethome3d.model.HomeTexture-">setTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Sets the <code>texture</code> of the furniture of this group.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setVisible-boolean-">setVisible</a></span>(boolean&nbsp;visible)</code>
<div class="block">Sets whether the furniture of this group should be visible or not.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setWidth-float-">setWidth</a></span>(float&nbsp;width)</code>
<div class="block">Sets the <code>width</code> of this group, then moves and resizes its furniture accordingly.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setX-float-">setX</a></span>(float&nbsp;x)</code>
<div class="block">Sets the <code>abscissa</code> of this group and moves its furniture accordingly.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setY-float-">setY</a></span>(float&nbsp;y)</code>
<div class="block">Sets the <code>ordinate</code> of this group and moves its furniture accordingly.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getAngle--">getAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getFurnitureComparator-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">getFurnitureComparator</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getGroundElevation--">getGroundElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getLevel--">getLevel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameAngle--">getNameAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameStyle--">getNameStyle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameXOffset--">getNameXOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameYOffset--">getNameYOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPoints--">getPoints</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getX--">getX</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getY--">getY</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBottomLeftPointAt-float-float-float-">isBottomLeftPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBottomRightPointAt-float-float-float-">isBottomRightPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isModelMirrored--">isModelMirrored</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isNameCenterPointAt-float-float-float-">isNameCenterPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isNameVisible--">isNameVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isParallelToWall-com.eteks.sweethome3d.model.Wall-">isParallelToWall</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isPointAt-float-float-float-">isPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTopLeftPointAt-float-float-float-">isTopLeftPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTopRightPointAt-float-float-float-">isTopRightPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isVisible--">isVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#move-float-float-">move</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCatalogId-java.lang.String-">setCatalogId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCreator-java.lang.String-">setCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCurrency-java.lang.String-">setCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDepthInPlan-float-">setDepthInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setHeightInPlan-float-">setHeightInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setInformation-java.lang.String-">setInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setLicense-java.lang.String-">setLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelCenteredAtOrigin-boolean-">setModelCenteredAtOrigin</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setName-java.lang.String-">setName</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameAngle-float-">setNameAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameStyle-com.eteks.sweethome3d.model.TextStyle-">setNameStyle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameVisible-boolean-">setNameVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameXOffset-float-">setNameXOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameYOffset-float-">setNameYOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPitch-float-">setPitch</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setRoll-float-">setRoll</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setValueAddedTaxPercentage-java.math.BigDecimal-">setValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setWidthInPlan-float-">setWidthInPlan</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeFurnitureGroup-java.util.List-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFurnitureGroup</h4>
<pre>public&nbsp;HomeFurnitureGroup(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                          java.lang.String&nbsp;name)</pre>
<div class="block">Creates a group from the given <code>furniture</code> list.
 The level of each piece of furniture of the group will be reset to <code>null</code> and if they belong to levels
 with different elevations, their elevation will be updated to be relative to the elevation of the lowest level.</div>
</li>
</ul>
<a name="HomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFurnitureGroup</h4>
<pre>public&nbsp;HomeFurnitureGroup(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                          <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;leadingPiece,
                          java.lang.String&nbsp;name)</pre>
<div class="block">Creates a group from the given <code>furniture</code> list.
 The level of each piece of furniture of the group will be reset to <code>null</code> and if they belong to levels
 with different elevations, their elevation will be updated to be relative to the elevation of the lowest level.
 The angle of the group is the one of the leading piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="HomeFurnitureGroup-java.util.List-float-boolean-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFurnitureGroup</h4>
<pre>public&nbsp;HomeFurnitureGroup(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                          float&nbsp;angle,
                          boolean&nbsp;modelMirrored,
                          java.lang.String&nbsp;name)</pre>
<div class="block">Creates a group from the given <code>furniture</code> list.
 The level of each piece of furniture of the group will be reset to <code>null</code> and if they belong to levels
 with different elevations, their elevation will be updated to be relative to the elevation of the lowest level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="HomeFurnitureGroup-java.lang.String-java.util.List-float-boolean-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeFurnitureGroup</h4>
<pre>public&nbsp;HomeFurnitureGroup(java.lang.String&nbsp;id,
                          java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                          float&nbsp;angle,
                          boolean&nbsp;modelMirrored,
                          java.lang.String&nbsp;name)</pre>
<div class="block">Creates a group from the given <code>furniture</code> list.
 The level of each piece of furniture of the group will be reset to <code>null</code> and if they belong to levels
 with different elevations, their elevation will be updated to be relative to the elevation of the lowest level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAllFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllFurniture</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;getAllFurniture()</pre>
<div class="block">Returns the furniture of this group and of all its subgroups, including the possible child furniture groups.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurniture</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;getFurniture()</pre>
<div class="block">Returns an unmodifiable list of the furniture of this group.</div>
</li>
</ul>
<a name="getCatalogId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCatalogId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCatalogId()</pre>
<div class="block">Returns the catalog ID of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCatalogId--">getCatalogId</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getInformation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInformation</h4>
<pre>public&nbsp;java.lang.String&nbsp;getInformation()</pre>
<div class="block">Returns the information associated with this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getInformation--">getInformation</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="isMovable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMovable</h4>
<pre>public&nbsp;boolean&nbsp;isMovable()</pre>
<div class="block">Returns <code>true</code> if this group is movable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isMovable--">isMovable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setMovable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMovable</h4>
<pre>public&nbsp;void&nbsp;setMovable(boolean&nbsp;movable)</pre>
<div class="block">Sets whether this group is movable or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setMovable-boolean-">setMovable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.1</dd>
</dl>
</li>
</ul>
<a name="isDoorOrWindow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDoorOrWindow</h4>
<pre>public&nbsp;boolean&nbsp;isDoorOrWindow()</pre>
<div class="block">Returns <code>true</code> if all furniture of this group are doors or windows.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="isResizable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isResizable</h4>
<pre>public&nbsp;boolean&nbsp;isResizable()</pre>
<div class="block">Returns <code>true</code> if all furniture of this group are resizable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isResizable--">isResizable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="isDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeformable</h4>
<pre>public&nbsp;boolean&nbsp;isDeformable()</pre>
<div class="block">Returns <code>true</code> if all furniture of this group are deformable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isDeformable--">isDeformable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isTexturable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTexturable</h4>
<pre>public&nbsp;boolean&nbsp;isTexturable()</pre>
<div class="block">Returns <code>true</code> if all furniture of this group are texturable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTexturable--">isTexturable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="isHorizontallyRotatable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHorizontallyRotatable</h4>
<pre>public&nbsp;boolean&nbsp;isHorizontallyRotatable()</pre>
<div class="block">Returns <code>false</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;float&nbsp;getWidth()</pre>
<div class="block">Returns the width of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getWidth--">getWidth</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getWidthInPlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidthInPlan</h4>
<pre>public&nbsp;float&nbsp;getWidthInPlan()</pre>
<div class="block">Returns the width of this group. As a group can't be rotated around an horizontal axis,
 its width in the horizontal plan is equal to its width.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getWidthInPlan--">getWidthInPlan</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getDepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepth</h4>
<pre>public&nbsp;float&nbsp;getDepth()</pre>
<div class="block">Returns the depth of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDepth--">getDepth</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getDepthInPlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepthInPlan</h4>
<pre>public&nbsp;float&nbsp;getDepthInPlan()</pre>
<div class="block">Returns the depth of this group. As a group can't be rotated around an horizontal axis,
 its depth in the horizontal plan is equal to its depth.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDepthInPlan--">getDepthInPlan</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;float&nbsp;getHeight()</pre>
<div class="block">Returns the height of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getHeight--">getHeight</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getHeightInPlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeightInPlan</h4>
<pre>public&nbsp;float&nbsp;getHeightInPlan()</pre>
<div class="block">Returns the height of this group. As a group can't be rotated around an horizontal axis,
 its height in the horizontal plan is equal to its height.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getHeightInPlan--">getHeightInPlan</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isHorizontallyRotated--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHorizontallyRotated</h4>
<pre>public&nbsp;boolean&nbsp;isHorizontallyRotated()</pre>
<div class="block">Returns <code>true</code> if this piece or a child of this group is rotated around an horizontal axis.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isHorizontallyRotated--">isHorizontallyRotated</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getDropOnTopElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDropOnTopElevation</h4>
<pre>public&nbsp;float&nbsp;getDropOnTopElevation()</pre>
<div class="block">Returns the elevation at which should be placed an object dropped on this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a percentage of the height of this piece. A negative value means that the piece
         should be ignored when an object is dropped on it.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getIcon()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getIcon--">getIcon</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setIcon-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIcon</h4>
<pre>public&nbsp;void&nbsp;setIcon(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setIcon-com.eteks.sweethome3d.model.Content-">HomePieceOfFurniture</a></code></span></div>
<div class="block">Sets the icon of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setIcon-com.eteks.sweethome3d.model.Content-">setIcon</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getPlanIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlanIcon</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getPlanIcon()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setPlanIcon-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlanIcon</h4>
<pre>public&nbsp;void&nbsp;setPlanIcon(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPlanIcon-com.eteks.sweethome3d.model.Content-">HomePieceOfFurniture</a></code></span></div>
<div class="block">Sets the plan icon of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPlanIcon-com.eteks.sweethome3d.model.Content-">setPlanIcon</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getModel()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModel--">getModel</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setModel-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModel</h4>
<pre>public&nbsp;void&nbsp;setModel(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModel-com.eteks.sweethome3d.model.Content-">HomePieceOfFurniture</a></code></span></div>
<div class="block">Sets the 3D model of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getModelSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelSize</h4>
<pre>public&nbsp;java.lang.Long&nbsp;getModelSize()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelSize--">getModelSize</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setModelSize-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelSize</h4>
<pre>public&nbsp;void&nbsp;setModelSize(java.lang.Long&nbsp;modelSize)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelSize-java.lang.Long-">HomePieceOfFurniture</a></code></span></div>
<div class="block">Sets the size of the 3D model of this piece of furniture.
 This method should be called only to update a piece created with an older version.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelSize-java.lang.Long-">setModelSize</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getModelRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelRotation</h4>
<pre>public&nbsp;float[][]&nbsp;getModelRotation()</pre>
<div class="block">Returns an identity matrix.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelRotation--">getModelRotation</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setModelRotation-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelRotation</h4>
<pre>public&nbsp;void&nbsp;setModelRotation(float[][]&nbsp;modelRotation)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelRotation-float:A:A-">HomePieceOfFurniture</a></code></span></div>
<div class="block">Sets the rotation 3 by 3 matrix of this piece of furniture and notifies listeners of this change.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelRotation-float:A:A-">setModelRotation</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="isModelCenteredAtOrigin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isModelCenteredAtOrigin</h4>
<pre>public&nbsp;boolean&nbsp;isModelCenteredAtOrigin()</pre>
<div class="block">Returns <code>true</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isModelCenteredAtOrigin--">isModelCenteredAtOrigin</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>false</code> by default if version < 5.5</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getModelFlags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelFlags</h4>
<pre>public&nbsp;int&nbsp;getModelFlags()</pre>
<div class="block">Returns 0.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelFlags--">getModelFlags</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setModelFlags-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelFlags</h4>
<pre>public&nbsp;void&nbsp;setModelFlags(int&nbsp;modelFlags)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelFlags-int-">HomePieceOfFurniture</a></code></span></div>
<div class="block">Sets the flags applied to the piece of furniture model.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelFlags-int-">setModelFlags</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="isBackFaceShown--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackFaceShown</h4>
<pre>public&nbsp;boolean&nbsp;isBackFaceShown()</pre>
<div class="block">Returns <code>false</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setBackFaceShown-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackFaceShown</h4>
<pre>public&nbsp;void&nbsp;setBackFaceShown(boolean&nbsp;backFaceShown)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setBackFaceShown-boolean-">HomePieceOfFurniture</a></code></span></div>
<div class="block">Sets whether the back face of the piece of furniture model should be displayed.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setBackFaceShown-boolean-">setBackFaceShown</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getModelTransformations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelTransformations</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;getModelTransformations()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelTransformations--">getModelTransformations</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the transformations of the 3D model or <code>null</code>
 if the 3D model is not transformed.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelTransformations</h4>
<pre>public&nbsp;void&nbsp;setModelTransformations(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;modelTransformations)</pre>
<div class="block">Sets the transformations of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">setModelTransformations</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelTransformations</code> - the transformations of the 3D model or <code>null</code> if no transformation shouldn't be applied</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;float&nbsp;getPitch()</pre>
<div class="block">Returns 0.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPitch--">getPitch</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getRoll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoll</h4>
<pre>public&nbsp;float&nbsp;getRoll()</pre>
<div class="block">Returns 0.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getRoll--">getRoll</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getStaircaseCutOutShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaircaseCutOutShape</h4>
<pre>public&nbsp;java.lang.String&nbsp;getStaircaseCutOutShape()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="setStaircaseCutOutShape-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaircaseCutOutShape</h4>
<pre>public&nbsp;void&nbsp;setStaircaseCutOutShape(java.lang.String&nbsp;staircaseCutOutShape)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setStaircaseCutOutShape-java.lang.String-">HomePieceOfFurniture</a></code></span></div>
<div class="block">Sets the shape used to cut out upper levels when they intersect with the piece
 like a staircase. Once this piece is updated, listeners added to this piece
 will receive a change notification.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setStaircaseCutOutShape-java.lang.String-">setStaircaseCutOutShape</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getCreator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreator</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCreator()</pre>
<div class="block">Returns the creator set for this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCreator--">getCreator</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="getPrice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrice</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getPrice()</pre>
<div class="block">Returns the price of the furniture of this group with a price.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPrice--">getPrice</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setPrice-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrice</h4>
<pre>public&nbsp;void&nbsp;setPrice(java.math.BigDecimal&nbsp;price)</pre>
<div class="block">Sets the price of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPrice-java.math.BigDecimal-">setPrice</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code> - if the price of one of the pieces is set</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getValueAddedTaxPercentage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueAddedTaxPercentage</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getValueAddedTaxPercentage()</pre>
<div class="block">Returns the VAT percentage of the furniture of this group
 or <code>null</code> if one piece has no VAT percentage
 or has a VAT percentage different from the other furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getCurrency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrency</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCurrency()</pre>
<div class="block">Returns the currency of the furniture of this group
 or <code>null</code> if one piece has no currency
 or has a currency different from the other furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCurrency--">getCurrency</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="getValueAddedTax--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueAddedTax</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getValueAddedTax()</pre>
<div class="block">Returns the VAT of the furniture of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getValueAddedTax--">getValueAddedTax</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getPriceValueAddedTaxIncluded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriceValueAddedTaxIncluded</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getPriceValueAddedTaxIncluded()</pre>
<div class="block">Returns the total price of the furniture of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPriceValueAddedTaxIncluded--">getPriceValueAddedTaxIncluded</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getColor--">getColor</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the color of the piece as RGB code or <code>null</code> if piece color is unchanged.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the <code>color</code> of the furniture of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setColor-java.lang.Integer-">setColor</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the color of this piece of furniture or <code>null</code> if piece color is the default one</dd>
</dl>
</li>
</ul>
<a name="getTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTexture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;getTexture()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getTexture--">getTexture</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the texture of the piece or <code>null</code> if piece texture is unchanged.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="setTexture-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTexture</h4>
<pre>public&nbsp;void&nbsp;setTexture(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</pre>
<div class="block">Sets the <code>texture</code> of the furniture of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setTexture-com.eteks.sweethome3d.model.HomeTexture-">setTexture</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - the texture of this piece of furniture or <code>null</code> if piece texture is the default one</dd>
</dl>
</li>
</ul>
<a name="getModelMaterials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelMaterials</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;getModelMaterials()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelMaterials--">getModelMaterials</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the materials of the 3D model or <code>null</code>
 if the individual materials of the 3D model are not modified.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelMaterials</h4>
<pre>public&nbsp;void&nbsp;setModelMaterials(<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;modelMaterials)</pre>
<div class="block">Sets the materials of the furniture of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">setModelMaterials</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelMaterials</code> - the materials of the 3D model or <code>null</code> if they shouldn't be changed</dd>
</dl>
</li>
</ul>
<a name="getShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShininess</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getShininess()</pre>
<div class="block">Returns <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getShininess--">getShininess</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value between 0 (matt) and 1 (very shiny) or <code>null</code> if piece shininess is unchanged.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="setShininess-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShininess</h4>
<pre>public&nbsp;void&nbsp;setShininess(java.lang.Float&nbsp;shininess)</pre>
<div class="block">Sets the shininess of the furniture of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setShininess-java.lang.Float-">setShininess</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="setAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngle</h4>
<pre>public&nbsp;void&nbsp;setAngle(float&nbsp;angle)</pre>
<div class="block">Sets the <code>angle</code> of the furniture of this group.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setAngle-float-">setAngle</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setX-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public&nbsp;void&nbsp;setX(float&nbsp;x)</pre>
<div class="block">Sets the <code>abscissa</code> of this group and moves its furniture accordingly.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setX-float-">setX</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setY-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public&nbsp;void&nbsp;setY(float&nbsp;y)</pre>
<div class="block">Sets the <code>ordinate</code> of this group and moves its furniture accordingly.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setY-float-">setY</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setWidth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidth</h4>
<pre>public&nbsp;void&nbsp;setWidth(float&nbsp;width)</pre>
<div class="block">Sets the <code>width</code> of this group, then moves and resizes its furniture accordingly.
 This method shouldn't be called on a group that contain furniture rotated around an horizontal axis.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setWidth-float-">setWidth</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setDepth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepth</h4>
<pre>public&nbsp;void&nbsp;setDepth(float&nbsp;depth)</pre>
<div class="block">Sets the <code>depth</code> of this group, then moves and resizes its furniture accordingly.
 This method shouldn't be called on a group that contain furniture rotated around an horizontal axis.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDepth-float-">setDepth</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public&nbsp;void&nbsp;setHeight(float&nbsp;height)</pre>
<div class="block">Sets the <code>height</code> of this group, then moves and resizes its furniture accordingly.
 This method shouldn't be called on a group that contain furniture rotated around an horizontal axis.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setHeight-float-">setHeight</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="scale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scale</h4>
<pre>public&nbsp;void&nbsp;scale(float&nbsp;scale)</pre>
<div class="block">Scales this group and its children with the given <code>ratio</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#scale-float-">scale</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setElevation-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevation</h4>
<pre>public&nbsp;void&nbsp;setElevation(float&nbsp;elevation)</pre>
<div class="block">Sets the <code>elevation</code> of this group, then moves its furniture accordingly.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setElevation-float-">setElevation</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setModelMirrored-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelMirrored</h4>
<pre>public&nbsp;void&nbsp;setModelMirrored(boolean&nbsp;modelMirrored)</pre>
<div class="block">Sets whether the furniture of this group should be mirrored or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelMirrored-boolean-">setModelMirrored</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>public&nbsp;void&nbsp;setVisible(boolean&nbsp;visible)</pre>
<div class="block">Sets whether the furniture of this group should be visible or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setVisible-boolean-">setVisible</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevel</h4>
<pre>public&nbsp;void&nbsp;setLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Set the level of this group and the furniture it contains.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="intersectsRectangle-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersectsRectangle</h4>
<pre>public&nbsp;boolean&nbsp;intersectsRectangle(float&nbsp;x0,
                                   float&nbsp;y0,
                                   float&nbsp;x1,
                                   float&nbsp;y1)</pre>
<div class="block">Returns <code>true</code> if one of the pieces of this group intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="containsPoint-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsPoint</h4>
<pre>public&nbsp;boolean&nbsp;containsPoint(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if one of the pieces of this group contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#containsPoint-float-float-float-">containsPoint</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#containsPoint-float-float-float-">containsPoint</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="duplicate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duplicate</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>&nbsp;duplicate()</pre>
<div class="block">Returns a copy of this object and its children with new ids.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this group with cloned furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeFurnitureGroup.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeFurnitureGroup.html" target="_top">Frames</a></li>
<li><a href="HomeFurnitureGroup.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
