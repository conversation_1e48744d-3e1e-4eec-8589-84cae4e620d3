<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Interface com.eteks.sweethome3d.model.TextureImage (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.eteks.sweethome3d.model.TextureImage (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/TextureImage.html" target="_top">Frames</a></li>
<li><a href="TextureImage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.eteks.sweethome3d.model.TextureImage" class="title">Uses of Interface<br>com.eteks.sweethome3d.model.TextureImage</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that implement <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a></span></code>
<div class="block">A texture in textures catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span></code>
<div class="block">An image used as texture on home 3D objects.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallPattern--">getNewWallPattern</a></span>()</code>
<div class="block">Returns the pattern used for new walls in plan or <code>null</code> if it's not set.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><span class="typeNameLabel">Wall.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html#getPattern--">getPattern</a></span>()</code>
<div class="block">Returns the pattern of this wall in the plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><span class="typeNameLabel">PatternsCatalog.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/PatternsCatalog.html#getPattern-int-">getPattern</a></span>(int&nbsp;index)</code>
<div class="block">Returns the pattern at a given <code>index</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><span class="typeNameLabel">PatternsCatalog.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/PatternsCatalog.html#getPattern-java.lang.String-">getPattern</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the pattern with a given <code>name</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#getWallPattern--">getWallPattern</a></span>()</code>
<div class="block">Returns the wall pattern in plan used by default.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PatternsCatalog.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/PatternsCatalog.html#getPatterns--">getPatterns</a></span>()</code>
<div class="block">Returns the patterns list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentTextures--">getRecentTextures</a></span>()</code>
<div class="block">Returns an unmodifiable list of the recent textures.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallPattern-com.eteks.sweethome3d.model.TextureImage-">setNewWallPattern</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;newWallPattern)</code>
<div class="block">Sets how new walls should be displayed in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Wall.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html#setPattern-com.eteks.sweethome3d.model.TextureImage-">setPattern</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</code>
<div class="block">Sets the pattern of this wall in the plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#setWallPattern-com.eteks.sweethome3d.model.TextureImage-">setWallPattern</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;wallPattern)</code>
<div class="block">Sets how walls should be displayed in plan by default, and notifies
 listeners of this change.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentTextures-java.util.List-">setRecentTextures</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&gt;&nbsp;recentTextures)</code>
<div class="block">Sets the recent colors list and notifies listeners of this change.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-">HomeTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture)</code>
<div class="block">Creates a home texture from an existing one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-">HomeTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;angle)</code>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-boolean-">HomeTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;angle,
           boolean&nbsp;leftToRightOriented)</code>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-boolean-">HomeTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;angle,
           float&nbsp;scale,
           boolean&nbsp;leftToRightOriented)</code>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-float-float-boolean-">HomeTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;xOffset,
           float&nbsp;yOffset,
           float&nbsp;angle,
           float&nbsp;scale,
           boolean&nbsp;leftToRightOriented)</code>
<div class="block">Creates a home texture from an existing one with customized parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-float-float-boolean-boolean-">HomeTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;xOffset,
           float&nbsp;yOffset,
           float&nbsp;angle,
           float&nbsp;scale,
           boolean&nbsp;fittingArea,
           boolean&nbsp;leftToRightOriented)</code>
<div class="block">Creates a home texture from an existing one with customized parameters.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html#Wall-float-float-float-float-float-float-com.eteks.sweethome3d.model.TextureImage-">Wall</a></span>(float&nbsp;xStart,
    float&nbsp;yStart,
    float&nbsp;xEnd,
    float&nbsp;yEnd,
    float&nbsp;thickness,
    float&nbsp;height,
    <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</code>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness, height and pattern.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html#Wall-java.lang.String-float-float-float-float-float-float-com.eteks.sweethome3d.model.TextureImage-">Wall</a></span>(java.lang.String&nbsp;id,
    float&nbsp;xStart,
    float&nbsp;yStart,
    float&nbsp;xEnd,
    float&nbsp;yEnd,
    float&nbsp;thickness,
    float&nbsp;height,
    <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</code>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness, height and pattern.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/PatternsCatalog.html#PatternsCatalog-java.util.List-">PatternsCatalog</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&gt;&nbsp;patterns)</code>
<div class="block">Creates a patterns catalog.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static java.awt.image.BufferedImage</code></td>
<td class="colLast"><span class="typeNameLabel">SwingTools.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingTools.html#getPatternImage-com.eteks.sweethome3d.model.TextureImage-java.awt.Color-java.awt.Color-">getPatternImage</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern,
               java.awt.Color&nbsp;backgroundColor,
               java.awt.Color&nbsp;foregroundColor)</code>
<div class="block">Returns the image matching a given pattern.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferencesController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getNewWallPattern--">getNewWallPattern</a></span>()</code>
<div class="block">Returns the edited new wall top pattern in plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><span class="typeNameLabel">WallController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getPattern--">getPattern</a></span>()</code>
<div class="block">Returns the pattern of edited wall in plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferencesController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getWallPattern--">getWallPattern</a></span>()</code>
<div class="block">Returns the default walls top pattern in plan.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">TextureChoiceController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html#addRecentTexture-com.eteks.sweethome3d.model.TextureImage-">addRecentTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture)</code>
<div class="block">Adds the given <code>texture</code> to the recent textures set.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferencesController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setNewWallPattern-com.eteks.sweethome3d.model.TextureImage-">setNewWallPattern</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;newWallPattern)</code>
<div class="block">Sets the edited new wall top pattern in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">WallController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setPattern-com.eteks.sweethome3d.model.TextureImage-">setPattern</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</code>
<div class="block">Sets the pattern of edited wall in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferencesController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setWallPattern-com.eteks.sweethome3d.model.TextureImage-">setWallPattern</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;wallPattern)</code>
<div class="block">Sets default walls top pattern in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/TextureImage.html" target="_top">Frames</a></li>
<li><a href="TextureImage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
