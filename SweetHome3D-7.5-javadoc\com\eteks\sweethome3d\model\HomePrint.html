<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomePrint (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomePrint (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePrint.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomePrint.html" target="_top">Frames</a></li>
<li><a href="HomePrint.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class HomePrint" class="title">Class HomePrint</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomePrint</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomePrint</span>
extends java.lang.Object
implements java.io.Serializable</pre>
<div class="block">The print attributes for a home.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.HomePrint">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a></span></code>
<div class="block">Paper orientation.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#HomePrint-com.eteks.sweethome3d.model.HomePrint.PaperOrientation-float-float-float-float-float-float-boolean-boolean-boolean-java.lang.Float-java.lang.String-java.lang.String-">HomePrint</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a>&nbsp;paperOrientation,
         float&nbsp;paperWidth,
         float&nbsp;paperHeight,
         float&nbsp;paperTopMargin,
         float&nbsp;paperLeftMargin,
         float&nbsp;paperBottomMargin,
         float&nbsp;paperRightMargin,
         boolean&nbsp;furniturePrinted,
         boolean&nbsp;planPrinted,
         boolean&nbsp;view3DPrinted,
         java.lang.Float&nbsp;planScale,
         java.lang.String&nbsp;headerFormat,
         java.lang.String&nbsp;footerFormat)</code>
<div class="block">Create a print attributes for home from the given parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#HomePrint-com.eteks.sweethome3d.model.HomePrint.PaperOrientation-float-float-float-float-float-float-boolean-boolean-java.util.List-boolean-java.lang.Float-java.lang.String-java.lang.String-">HomePrint</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a>&nbsp;paperOrientation,
         float&nbsp;paperWidth,
         float&nbsp;paperHeight,
         float&nbsp;paperTopMargin,
         float&nbsp;paperLeftMargin,
         float&nbsp;paperBottomMargin,
         float&nbsp;paperRightMargin,
         boolean&nbsp;furniturePrinted,
         boolean&nbsp;planPrinted,
         java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;&nbsp;printedLevels,
         boolean&nbsp;view3DPrinted,
         java.lang.Float&nbsp;planScale,
         java.lang.String&nbsp;headerFormat,
         java.lang.String&nbsp;footerFormat)</code>
<div class="block">Create a print attributes for home from the given parameters.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getFooterFormat--">getFooterFormat</a></span>()</code>
<div class="block">Returns the string format used to print page footers.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getHeaderFormat--">getHeaderFormat</a></span>()</code>
<div class="block">Returns the string format used to print page headers.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPaperBottomMargin--">getPaperBottomMargin</a></span>()</code>
<div class="block">Returns the margin at paper bottom in 1/72nds of an inch.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPaperHeight--">getPaperHeight</a></span>()</code>
<div class="block">Returns the paper height in 1/72nds of an inch.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPaperLeftMargin--">getPaperLeftMargin</a></span>()</code>
<div class="block">Returns the margin at paper left in 1/72nds of an inch.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPaperOrientation--">getPaperOrientation</a></span>()</code>
<div class="block">Returns the paper orientation.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPaperRightMargin--">getPaperRightMargin</a></span>()</code>
<div class="block">Returns the margin at paper right in 1/72nds of an inch.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPaperTopMargin--">getPaperTopMargin</a></span>()</code>
<div class="block">Returns the margin at paper top in 1/72nds of an inch.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPaperWidth--">getPaperWidth</a></span>()</code>
<div class="block">Returns the paper width in 1/72nds of an inch.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPlanScale--">getPlanScale</a></span>()</code>
<div class="block">Returns the scale used to print home plan or
 <code>null</code> if no special scale is desired.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#getPrintedLevels--">getPrintedLevels</a></span>()</code>
<div class="block">Returns the printed levels or <code>null</code> if all levels should be printed.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#isFurniturePrinted--">isFurniturePrinted</a></span>()</code>
<div class="block">Returns whether home furniture should be printed or not.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#isPlanPrinted--">isPlanPrinted</a></span>()</code>
<div class="block">Returns whether home plan should be printed or not.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html#isView3DPrinted--">isView3DPrinted</a></span>()</code>
<div class="block">Returns whether home 3D view should be printed or not.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomePrint-com.eteks.sweethome3d.model.HomePrint.PaperOrientation-float-float-float-float-float-float-boolean-boolean-boolean-java.lang.Float-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomePrint</h4>
<pre>public&nbsp;HomePrint(<a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a>&nbsp;paperOrientation,
                 float&nbsp;paperWidth,
                 float&nbsp;paperHeight,
                 float&nbsp;paperTopMargin,
                 float&nbsp;paperLeftMargin,
                 float&nbsp;paperBottomMargin,
                 float&nbsp;paperRightMargin,
                 boolean&nbsp;furniturePrinted,
                 boolean&nbsp;planPrinted,
                 boolean&nbsp;view3DPrinted,
                 java.lang.Float&nbsp;planScale,
                 java.lang.String&nbsp;headerFormat,
                 java.lang.String&nbsp;footerFormat)</pre>
<div class="block">Create a print attributes for home from the given parameters.</div>
</li>
</ul>
<a name="HomePrint-com.eteks.sweethome3d.model.HomePrint.PaperOrientation-float-float-float-float-float-float-boolean-boolean-java.util.List-boolean-java.lang.Float-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomePrint</h4>
<pre>public&nbsp;HomePrint(<a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a>&nbsp;paperOrientation,
                 float&nbsp;paperWidth,
                 float&nbsp;paperHeight,
                 float&nbsp;paperTopMargin,
                 float&nbsp;paperLeftMargin,
                 float&nbsp;paperBottomMargin,
                 float&nbsp;paperRightMargin,
                 boolean&nbsp;furniturePrinted,
                 boolean&nbsp;planPrinted,
                 java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;&nbsp;printedLevels,
                 boolean&nbsp;view3DPrinted,
                 java.lang.Float&nbsp;planScale,
                 java.lang.String&nbsp;headerFormat,
                 java.lang.String&nbsp;footerFormat)</pre>
<div class="block">Create a print attributes for home from the given parameters.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPaperOrientation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaperOrientation</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a>&nbsp;getPaperOrientation()</pre>
<div class="block">Returns the paper orientation.</div>
</li>
</ul>
<a name="getPaperBottomMargin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaperBottomMargin</h4>
<pre>public&nbsp;float&nbsp;getPaperBottomMargin()</pre>
<div class="block">Returns the margin at paper bottom in 1/72nds of an inch.</div>
</li>
</ul>
<a name="getPaperHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaperHeight</h4>
<pre>public&nbsp;float&nbsp;getPaperHeight()</pre>
<div class="block">Returns the paper height in 1/72nds of an inch.</div>
</li>
</ul>
<a name="getPaperLeftMargin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaperLeftMargin</h4>
<pre>public&nbsp;float&nbsp;getPaperLeftMargin()</pre>
<div class="block">Returns the margin at paper left in 1/72nds of an inch.</div>
</li>
</ul>
<a name="getPaperRightMargin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaperRightMargin</h4>
<pre>public&nbsp;float&nbsp;getPaperRightMargin()</pre>
<div class="block">Returns the margin at paper right in 1/72nds of an inch.</div>
</li>
</ul>
<a name="getPaperTopMargin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaperTopMargin</h4>
<pre>public&nbsp;float&nbsp;getPaperTopMargin()</pre>
<div class="block">Returns the margin at paper top in 1/72nds of an inch.</div>
</li>
</ul>
<a name="getPaperWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaperWidth</h4>
<pre>public&nbsp;float&nbsp;getPaperWidth()</pre>
<div class="block">Returns the paper width in 1/72nds of an inch.</div>
</li>
</ul>
<a name="isFurniturePrinted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFurniturePrinted</h4>
<pre>public&nbsp;boolean&nbsp;isFurniturePrinted()</pre>
<div class="block">Returns whether home furniture should be printed or not.</div>
</li>
</ul>
<a name="isPlanPrinted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPlanPrinted</h4>
<pre>public&nbsp;boolean&nbsp;isPlanPrinted()</pre>
<div class="block">Returns whether home plan should be printed or not.</div>
</li>
</ul>
<a name="getPrintedLevels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintedLevels</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;&nbsp;getPrintedLevels()</pre>
<div class="block">Returns the printed levels or <code>null</code> if all levels should be printed.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isView3DPrinted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isView3DPrinted</h4>
<pre>public&nbsp;boolean&nbsp;isView3DPrinted()</pre>
<div class="block">Returns whether home 3D view should be printed or not.</div>
</li>
</ul>
<a name="getPlanScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlanScale</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getPlanScale()</pre>
<div class="block">Returns the scale used to print home plan or
 <code>null</code> if no special scale is desired.</div>
</li>
</ul>
<a name="getHeaderFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeaderFormat</h4>
<pre>public&nbsp;java.lang.String&nbsp;getHeaderFormat()</pre>
<div class="block">Returns the string format used to print page headers.</div>
</li>
</ul>
<a name="getFooterFormat--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getFooterFormat</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFooterFormat()</pre>
<div class="block">Returns the string format used to print page footers.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePrint.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomePrint.html" target="_top">Frames</a></li>
<li><a href="HomePrint.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
