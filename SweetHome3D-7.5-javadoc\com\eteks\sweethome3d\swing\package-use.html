<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Package com.eteks.sweethome3d.swing (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.eteks.sweethome3d.swing (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.eteks.sweethome3d.swing" class="title">Uses of Package<br>com.eteks.sweethome3d.swing</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> used by <a href="../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/FileContentManager.html#com.eteks.sweethome3d.applet">FileContentManager</a>
<div class="block">Content manager for files with Swing file choosers.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> used by <a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/AutoCommitSpinner.html#com.eteks.sweethome3d.swing">AutoCommitSpinner</a>
<div class="block">A spinner which commits its value during edition and selects
 the value displayed in its editor when it gains focus.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/CatalogItemToolTip.DisplayedInformation.html#com.eteks.sweethome3d.swing">CatalogItemToolTip.DisplayedInformation</a>
<div class="block">Type of information displayed by a tool tip.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/FurnitureTablePanel.html#com.eteks.sweethome3d.swing">FurnitureTablePanel</a>
<div class="block">A panel displaying home furniture table and other information like totals.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/HomeComponent3D.Projection.html#com.eteks.sweethome3d.swing">HomeComponent3D.Projection</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/HomePrintableComponent.Variable.html#com.eteks.sweethome3d.swing">HomePrintableComponent.Variable</a>
<div class="block">List of the variables that the user may insert in header and footer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/IconManager.html#com.eteks.sweethome3d.swing">IconManager</a>
<div class="block">Singleton managing icons cache.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/LocatedTransferHandler.html#com.eteks.sweethome3d.swing">LocatedTransferHandler</a>
<div class="block">Transfer handler that stores the dropped location of mouse pointer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/NullableSpinner.NullableSpinnerNumberModel.html#com.eteks.sweethome3d.swing">NullableSpinner.NullableSpinnerNumberModel</a>
<div class="block">Spinner number model that accepts <code>null</code> values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/PhotoPanel.html#com.eteks.sweethome3d.swing">PhotoPanel</a>
<div class="block">A panel to edit photo creation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/PhotoSizeAndQualityPanel.html#com.eteks.sweethome3d.swing">PhotoSizeAndQualityPanel</a>
<div class="block">A panel to edit photo size and quality.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/PhotosPanel.html#com.eteks.sweethome3d.swing">PhotosPanel</a>
<div class="block">A panel to edit photos created at home points of view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/PlanComponent.IndicatorType.html#com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a>
<div class="block">Indicator types that may be displayed on selected items.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/PlanComponent.PaintMode.html#com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>
<div class="block">The circumstances under which the home items displayed by this component will be painted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/ProportionalLayout.Constraints.html#com.eteks.sweethome3d.swing">ProportionalLayout.Constraints</a>
<div class="block">The two locations where components managed by a <a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html" title="class in com.eteks.sweethome3d.swing"><code>ProportionalLayout</code></a> instance can be placed.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/ResourceAction.html#com.eteks.sweethome3d.swing">ResourceAction</a>
<div class="block">An action with properties read from a resource bundle file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/ResourceAction.MenuItemAction.html#com.eteks.sweethome3d.swing">ResourceAction.MenuItemAction</a>
<div class="block">An action decorator for menu items.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/VideoPanel.html#com.eteks.sweethome3d.swing">VideoPanel</a>
<div class="block">A panel used for video creation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/swing/class-use/VisualTransferHandler.html#com.eteks.sweethome3d.swing">VisualTransferHandler</a>
<div class="block">Transfer handler with visual representation on systems that support it.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
