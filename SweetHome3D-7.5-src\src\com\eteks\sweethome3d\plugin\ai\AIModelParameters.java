/*
 * AIModelParameters.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.io.Serializable;

/**
 * Model-specific parameters for AI requests.
 * These parameters control the behavior and output characteristics of AI models.
 * 
 * <AUTHOR>
 */
public class AIModelParameters implements Serializable {
  private static final long serialVersionUID = 1L;
  
  private double temperature = 0.7;
  private int maxTokens = 2048;
  private double topP = 1.0;
  private int frequencyPenalty = 0;
  private int presencePenalty = 0;
  
  /**
   * Creates default model parameters.
   */
  public AIModelParameters() {
    // Default values are set in field declarations
  }
  
  /**
   * Creates model parameters with specified values.
   */
  public AIModelParameters(double temperature, int maxTokens, double topP, 
                          int frequencyPenalty, int presencePenalty) {
    this.temperature = temperature;
    this.maxTokens = maxTokens;
    this.topP = topP;
    this.frequencyPenalty = frequencyPenalty;
    this.presencePenalty = presencePenalty;
  }
  
  /**
   * Returns the temperature parameter (0.0 to 2.0).
   * Higher values make output more random, lower values more focused.
   */
  public double getTemperature() {
    return temperature;
  }
  
  /**
   * Sets the temperature parameter (0.0 to 2.0).
   */
  public void setTemperature(double temperature) {
    this.temperature = Math.max(0.0, Math.min(2.0, temperature));
  }
  
  /**
   * Returns the maximum number of tokens to generate.
   */
  public int getMaxTokens() {
    return maxTokens;
  }
  
  /**
   * Sets the maximum number of tokens to generate.
   */
  public void setMaxTokens(int maxTokens) {
    this.maxTokens = Math.max(1, maxTokens);
  }
  
  /**
   * Returns the top-p parameter (0.0 to 1.0).
   * Controls nucleus sampling - lower values focus on more likely tokens.
   */
  public double getTopP() {
    return topP;
  }
  
  /**
   * Sets the top-p parameter (0.0 to 1.0).
   */
  public void setTopP(double topP) {
    this.topP = Math.max(0.0, Math.min(1.0, topP));
  }
  
  /**
   * Returns the frequency penalty (-2.0 to 2.0).
   * Positive values decrease likelihood of repeating tokens.
   */
  public int getFrequencyPenalty() {
    return frequencyPenalty;
  }
  
  /**
   * Sets the frequency penalty (-2.0 to 2.0).
   */
  public void setFrequencyPenalty(int frequencyPenalty) {
    this.frequencyPenalty = Math.max(-2, Math.min(2, frequencyPenalty));
  }
  
  /**
   * Returns the presence penalty (-2.0 to 2.0).
   * Positive values increase likelihood of talking about new topics.
   */
  public int getPresencePenalty() {
    return presencePenalty;
  }
  
  /**
   * Sets the presence penalty (-2.0 to 2.0).
   */
  public void setPresencePenalty(int presencePenalty) {
    this.presencePenalty = Math.max(-2, Math.min(2, presencePenalty));
  }
  
  @Override
  public String toString() {
    return "AIModelParameters{" +
           "temperature=" + temperature +
           ", maxTokens=" + maxTokens +
           ", topP=" + topP +
           ", frequencyPenalty=" + frequencyPenalty +
           ", presencePenalty=" + presencePenalty +
           '}';
  }
}
