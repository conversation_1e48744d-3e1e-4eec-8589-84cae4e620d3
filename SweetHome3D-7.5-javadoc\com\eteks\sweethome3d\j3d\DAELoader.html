<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>DAELoader (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DAELoader (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DAELoader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/DimensionLine3D.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/DAELoader.html" target="_top">Frames</a></li>
<li><a href="DAELoader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.sun.j3d.loaders.LoaderBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class DAELoader" class="title">Class DAELoader</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.sun.j3d.loaders.LoaderBase</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.DAELoader</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>com.sun.j3d.loaders.Loader</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DAELoader</span>
extends com.sun.j3d.loaders.LoaderBase
implements com.sun.j3d.loaders.Loader</pre>
<div class="block">A loader for DAE Collada 1.4.1 format as specified by
 <a href="http://www.khronos.org/files/collada_spec_1_4.pdf">http://www.khronos.org/files/collada_spec_1_4.pdf</a>.
 All texture coordinates are considered to belong to the same set (for example UVSET0).<br>
 Note: this class is compatible with Java 3D 1.3.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret, apptaro (bug fixes)</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.sun.j3d.loaders.LoaderBase">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.sun.j3d.loaders.LoaderBase</h3>
<code>basePath, baseUrl, loadFlags</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.sun.j3d.loaders.Loader">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.sun.j3d.loaders.Loader</h3>
<code>LOAD_ALL, LOAD_BACKGROUND_NODES, LOAD_BEHAVIOR_NODES, LOAD_FOG_NODES, LOAD_LIGHT_NODES, LOAD_SOUND_NODES, LOAD_VIEW_GROUPS</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/DAELoader.html#DAELoader--">DAELoader</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>com.sun.j3d.loaders.Scene</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/DAELoader.html#load-java.io.Reader-">load</a></span>(java.io.Reader&nbsp;reader)</code>
<div class="block">Returns the scene described in the given DAE file stream.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>com.sun.j3d.loaders.Scene</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/DAELoader.html#load-java.lang.String-">load</a></span>(java.lang.String&nbsp;file)</code>
<div class="block">Returns the scene described in the given DAE file.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>com.sun.j3d.loaders.Scene</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/DAELoader.html#load-java.net.URL-">load</a></span>(java.net.URL&nbsp;url)</code>
<div class="block">Returns the scene described in the given DAE file url.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/DAELoader.html#setUseCaches-java.lang.Boolean-">setUseCaches</a></span>(java.lang.Boolean&nbsp;useCaches)</code>
<div class="block">Sets whether this loader should try or avoid accessing to URLs with cache.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.sun.j3d.loaders.LoaderBase">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.sun.j3d.loaders.LoaderBase</h3>
<code>getBasePath, getBaseUrl, getFlags, setBasePath, setBaseUrl, setFlags</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.sun.j3d.loaders.Loader">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.sun.j3d.loaders.Loader</h3>
<code>getBasePath, getBaseUrl, getFlags, setBasePath, setBaseUrl, setFlags</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DAELoader--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DAELoader</h4>
<pre>public&nbsp;DAELoader()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setUseCaches-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseCaches</h4>
<pre>public&nbsp;void&nbsp;setUseCaches(java.lang.Boolean&nbsp;useCaches)</pre>
<div class="block">Sets whether this loader should try or avoid accessing to URLs with cache.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>useCaches</code> - <code>Boolean.TRUE</code>, <code>Boolean.FALSE</code>, or
    <code>null</code> then caches will be used according to the value
    returned by <code>URLConnection.getDefaultUseCaches()</code>.</dd>
</dl>
</li>
</ul>
<a name="load-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public&nbsp;com.sun.j3d.loaders.Scene&nbsp;load(java.lang.String&nbsp;file)
                               throws java.io.FileNotFoundException,
                                      com.sun.j3d.loaders.IncorrectFormatException,
                                      com.sun.j3d.loaders.ParsingErrorException</pre>
<div class="block">Returns the scene described in the given DAE file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>load</code>&nbsp;in interface&nbsp;<code>com.sun.j3d.loaders.Loader</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code></dd>
<dd><code>com.sun.j3d.loaders.IncorrectFormatException</code></dd>
<dd><code>com.sun.j3d.loaders.ParsingErrorException</code></dd>
</dl>
</li>
</ul>
<a name="load-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>load</h4>
<pre>public&nbsp;com.sun.j3d.loaders.Scene&nbsp;load(java.net.URL&nbsp;url)
                               throws java.io.FileNotFoundException,
                                      com.sun.j3d.loaders.IncorrectFormatException,
                                      com.sun.j3d.loaders.ParsingErrorException</pre>
<div class="block">Returns the scene described in the given DAE file url.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>load</code>&nbsp;in interface&nbsp;<code>com.sun.j3d.loaders.Loader</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code></dd>
<dd><code>com.sun.j3d.loaders.IncorrectFormatException</code></dd>
<dd><code>com.sun.j3d.loaders.ParsingErrorException</code></dd>
</dl>
</li>
</ul>
<a name="load-java.io.Reader-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>load</h4>
<pre>public&nbsp;com.sun.j3d.loaders.Scene&nbsp;load(java.io.Reader&nbsp;reader)
                               throws java.io.FileNotFoundException,
                                      com.sun.j3d.loaders.IncorrectFormatException,
                                      com.sun.j3d.loaders.ParsingErrorException</pre>
<div class="block">Returns the scene described in the given DAE file stream.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>load</code>&nbsp;in interface&nbsp;<code>com.sun.j3d.loaders.Loader</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code></dd>
<dd><code>com.sun.j3d.loaders.IncorrectFormatException</code></dd>
<dd><code>com.sun.j3d.loaders.ParsingErrorException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DAELoader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/DimensionLine3D.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/DAELoader.html" target="_top">Frames</a></li>
<li><a href="DAELoader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.sun.j3d.loaders.LoaderBase">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
