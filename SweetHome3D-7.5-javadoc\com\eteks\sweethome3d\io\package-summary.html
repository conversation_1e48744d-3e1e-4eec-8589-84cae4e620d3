<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>com.eteks.sweethome3d.io (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.eteks.sweethome3d.io (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/applet/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.eteks.sweethome3d.io</h1>
<div class="docSummary">
<div class="block">Implements how to read and write 
<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/AutoRecoveryManager.html" title="class in com.eteks.sweethome3d.io">AutoRecoveryManager</a></td>
<td class="colLast">
<div class="block">Manager able to automatically save open homes in recovery folder with a timer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/Base64.html" title="class in com.eteks.sweethome3d.io">Base64</a></td>
<td class="colLast">
<div class="block">Encodes and decodes to and from Base64 notation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/ContentDigestManager.html" title="class in com.eteks.sweethome3d.io">ContentDigestManager</a></td>
<td class="colLast">
<div class="block">Manager able to store and compute content digest to compare content data faster.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html" title="class in com.eteks.sweethome3d.io">DefaultFurnitureCatalog</a></td>
<td class="colLast">
<div class="block">Furniture default catalog read from resources localized in <code>.properties</code> files.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io">DefaultHomeInputStream</a></td>
<td class="colLast">
<div class="block">An <code>InputStream</code> filter that reads a home from a stream
 at .sh3d file format.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io">DefaultHomeOutputStream</a></td>
<td class="colLast">
<div class="block">An <code>OutputStream</code> filter that writes a home in a stream
 at .sh3d file format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html" title="class in com.eteks.sweethome3d.io">DefaultTexturesCatalog</a></td>
<td class="colLast">
<div class="block">Textures default catalog read from localized resources.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io">DefaultUserPreferences</a></td>
<td class="colLast">
<div class="block">Default user preferences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html" title="class in com.eteks.sweethome3d.io">FileUserPreferences</a></td>
<td class="colLast">
<div class="block">User preferences initialized from
 <a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io"><code>default user preferences</code></a>
 and stored in user preferences on local file system.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html" title="class in com.eteks.sweethome3d.io">HomeFileRecorder</a></td>
<td class="colLast">
<div class="block">Recorder that stores homes in files with <a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io"><code>DefaultHomeOutputStream</code></a> and
 <a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io"><code>DefaultHomeInputStream</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html" title="class in com.eteks.sweethome3d.io">HomeXMLExporter</a></td>
<td class="colLast">
<div class="block">Exporter for home instances.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a></td>
<td class="colLast">
<div class="block">SAX handler for Sweet Home 3D XML stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io">ObjectXMLExporter</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">Base class used to write objects to XML.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a></td>
<td class="colLast">
<div class="block">A simple XML writer able to write XML elements, their attributes and texts, indenting child elements.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a></td>
<td class="colLast">
<div class="block">Describes how <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">content</a> associated to a home should be managed during recording.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a></td>
<td class="colLast">
<div class="block">The keys of the properties values read in <code>.properties</code> files.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultTexturesCatalog.PropertyKey</a></td>
<td class="colLast">
<div class="block">The keys of the properties values read in <code>.properties</code> files.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/io/DamagedHomeIOException.html" title="class in com.eteks.sweethome3d.io">DamagedHomeIOException</a></td>
<td class="colLast">
<div class="block">Exception thrown when a home data is damaged containing invalid content.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package com.eteks.sweethome3d.io Description">Package com.eteks.sweethome3d.io Description</h2>
<div class="block">Implements how to read and write 
<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.
<p><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io">Default user preferences</a>
are read from resources and written in 
<a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html" title="class in com.eteks.sweethome3d.io">local files and system preferences</a>.
<br>Homes are read and written to <a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html" title="class in com.eteks.sweethome3d.io">files</a>.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/applet/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
