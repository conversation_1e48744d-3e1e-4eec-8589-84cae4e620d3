<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>FileUserPreferences (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileUserPreferences (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FileUserPreferences.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/FileUserPreferences.html" target="_top">Frames</a></li>
<li><a href="FileUserPreferences.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class FileUserPreferences" class="title">Class FileUserPreferences</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.UserPreferences</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.FileUserPreferences</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">FileUserPreferences</span>
extends <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></pre>
<div class="block">User preferences initialized from
 <a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io"><code>default user preferences</code></a>
 and stored in user preferences on local file system.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#PLUGIN_LANGUAGE_LIBRARY_FAMILY">PLUGIN_LANGUAGE_LIBRARY_FAMILY</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#FURNITURE_LIBRARY_TYPE">FURNITURE_LIBRARY_TYPE</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#LANGUAGE_LIBRARY_TYPE">LANGUAGE_LIBRARY_TYPE</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#TEXTURES_LIBRARY_TYPE">TEXTURES_LIBRARY_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#FileUserPreferences--">FileUserPreferences</a></span>()</code>
<div class="block">Creates user preferences read from user preferences in file system,
 and from resource files.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#FileUserPreferences-java.io.File-java.io.File:A-">FileUserPreferences</a></span>(java.io.File&nbsp;preferencesFolder,
                   java.io.File[]&nbsp;applicationFolders)</code>
<div class="block">Creates user preferences stored in the folders given in parameter.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#FileUserPreferences-java.io.File-java.io.File:A-java.util.concurrent.Executor-">FileUserPreferences</a></span>(java.io.File&nbsp;preferencesFolder,
                   java.io.File[]&nbsp;applicationFolders,
                   java.util.concurrent.Executor&nbsp;updater)</code>
<div class="block">Creates user preferences stored in the folders given in parameter.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></span>(java.lang.String&nbsp;furnitureLibraryPath)</code>
<div class="block">Adds the file <code>furnitureLibraryPath</code> to the first furniture libraries folder
 to make the furniture library available to catalog.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></span>(java.lang.String&nbsp;languageLibraryPath)</code>
<div class="block">Adds <code>languageLibraryPath</code> to the first language libraries folder
 to make the language library it contains available to supported languages.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></span>(java.lang.String&nbsp;texturesLibraryPath)</code>
<div class="block">Adds the file <code>texturesLibraryPath</code> to the first textures libraries folder
 to make the textures library available to catalog.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#deleteLibraries-java.util.List-">deleteLibraries</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;libraries)</code>
<div class="block">Deletes the given <code>libraries</code> and updates user preferences.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Returns <code>true</code> if the furniture library at the given <code>location</code> exists
 in the first furniture libraries folder.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.io.File</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getApplicationFolder--">getApplicationFolder</a></span>()</code>
<div class="block">Returns the first Sweet Home 3D application folder.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.io.File[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getApplicationFolders--">getApplicationFolders</a></span>()</code>
<div class="block">Returns Sweet Home 3D application folders.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.io.File[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getApplicationSubfolders-java.lang.String-">getApplicationSubfolders</a></span>(java.lang.String&nbsp;subfolder)</code>
<div class="block">Returns subfolders of Sweet Home 3D application folders of a given name.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected java.io.File[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getFurnitureLibrariesPluginFolders--">getFurnitureLibrariesPluginFolders</a></span>()</code>
<div class="block">Returns the folders where furniture catalog files are placed
 or <code>null</code> if that folder can't be retrieved.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected java.io.File[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getLanguageLibrariesPluginFolders--">getLanguageLibrariesPluginFolders</a></span>()</code>
<div class="block">Returns the folders where language libraries files are placed
 or <code>null</code> if that folder can't be retrieved.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Returns the libraries available in user preferences.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>protected java.util.prefs.Preferences</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getPreferences--">getPreferences</a></span>()</code>
<div class="block">Returns default Java preferences for current system user.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.ClassLoader&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getResourceClassLoaders--">getResourceClassLoaders</a></span>()</code>
<div class="block">Returns the default class loader of user preferences and the class loaders that
 give access to resources in language libraries plugin folder.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected java.io.File[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getTexturesLibrariesPluginFolders--">getTexturesLibrariesPluginFolders</a></span>()</code>
<div class="block">Returns the folders where texture catalog files are placed
 or <code>null</code> if that folder can't be retrieved.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#isActionTipIgnored-java.lang.String-">isActionTipIgnored</a></span>(java.lang.String&nbsp;actionKey)</code>
<div class="block">Returns whether an action tip should be ignored or not.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#isLibraryDeletable-com.eteks.sweethome3d.model.Library-">isLibraryDeletable</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&nbsp;library)</code>
<div class="block">Returns <code>true</code> if the given file <code>library</code> can be deleted.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Returns <code>true</code> if the language library at the given location exists
 in the first language libraries folder.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#readFurnitureCatalogFromResource-java.io.File:A-">readFurnitureCatalogFromResource</a></span>(java.io.File[]&nbsp;furniturePluginFolders)</code>
<div class="block">Returns the furniture catalog contained in resources of the application and in the given plug-in folders.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#readModifiableFurnitureCategory-java.util.prefs.Preferences-int-">readModifiableFurnitureCategory</a></span>(java.util.prefs.Preferences&nbsp;preferences,
                               int&nbsp;index)</code>
<div class="block">Returns the furniture category of a piece at the given <code>index</code>
 read from <code>preferences</code>.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#readModifiablePieceOfFurniture-java.util.prefs.Preferences-int-java.io.File-">readModifiablePieceOfFurniture</a></span>(java.util.prefs.Preferences&nbsp;preferences,
                              int&nbsp;index,
                              java.io.File&nbsp;preferencesFolder)</code>
<div class="block">Returns the modifiable piece of furniture read from <code>preferences</code> at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#readModifiableTexture-java.util.prefs.Preferences-int-java.io.File-">readModifiableTexture</a></span>(java.util.prefs.Preferences&nbsp;preferences,
                     int&nbsp;index,
                     java.io.File&nbsp;preferencesFolder)</code>
<div class="block">Returns the modifiable texture read from <code>preferences</code> at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#readModifiableTextureCategory-java.util.prefs.Preferences-int-">readModifiableTextureCategory</a></span>(java.util.prefs.Preferences&nbsp;preferences,
                             int&nbsp;index)</code>
<div class="block">Returns the category of a texture at the given <code>index</code>
 read from <code>preferences</code>.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#readTexturesCatalogFromResource-java.io.File:A-">readTexturesCatalogFromResource</a></span>(java.io.File[]&nbsp;texturesPluginFolders)</code>
<div class="block">Returns the textures catalog contained in resources of the application and in the given plug-in folders.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#resetIgnoredActionTips--">resetIgnoredActionTips</a></span>()</code>
<div class="block">Resets the display flag of action tips.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#setActionTipIgnored-java.lang.String-">setActionTipIgnored</a></span>(java.lang.String&nbsp;actionKey)</code>
<div class="block">Sets which action tip should be ignored.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Returns <code>true</code> if the textures library at the given <code>location</code> exists
 in the first textures libraries folder.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#write--">write</a></span>()</code>
<div class="block">Writes user preferences in current user preferences in system.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addAutoCompletionString-java.lang.String-java.lang.String-">addAutoCompletionString</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addPropertyChangeListener-com.eteks.sweethome3d.model.UserPreferences.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoCompletedProperties--">getAutoCompletedProperties</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoCompletionStrings-java.lang.String-">getAutoCompletionStrings</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoSaveDelayForRecovery--">getAutoSaveDelayForRecovery</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultFontName--">getDefaultFontName</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultSupportedLanguages--">getDefaultSupportedLanguages</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultTextStyle-java.lang.Class-">getDefaultTextStyle</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultValueAddedTaxPercentage--">getDefaultValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getFurnitureCatalog--">getFurnitureCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getFurnitureModelIconSize--">getFurnitureModelIconSize</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getHomeExamples--">getHomeExamples</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLanguage--">getLanguage</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLengthUnit--">getLengthUnit</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedString-java.lang.Class-java.lang.String-java.lang.Object...-">getLocalizedString</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedString-java.lang.String-java.lang.String-java.lang.Object...-">getLocalizedString</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedStringKeys-java.lang.String-">getLocalizedStringKeys</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewFloorThickness--">getNewFloorThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewRoomFloorColor--">getNewRoomFloorColor</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallBaseboardHeight--">getNewWallBaseboardHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallBaseboardThickness--">getNewWallBaseboardThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallHeight--">getNewWallHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallPattern--">getNewWallPattern</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallThickness--">getNewWallThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getPatternsCatalog--">getPatternsCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getPhotoRenderer--">getPhotoRenderer</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentColors--">getRecentColors</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentHomes--">getRecentHomes</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentHomesMaxCount--">getRecentHomesMaxCount</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentTextures--">getRecentTextures</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getStoredCamerasMaxCount--">getStoredCamerasMaxCount</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getSupportedLanguages--">getSupportedLanguages</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getTexturesCatalog--">getTexturesCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getUpdatesMinimumDate--">getUpdatesMinimumDate</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getWallPattern--">getWallPattern</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isAerialViewCenteredOnSelectionEnabled--">isAerialViewCenteredOnSelectionEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isCheckUpdatesEnabled--">isCheckUpdatesEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isDrawingModeEnabled--">isDrawingModeEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isEditingIn3DViewEnabled--">isEditingIn3DViewEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isFurnitureCatalogViewedInTree--">isFurnitureCatalogViewedInTree</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isFurnitureViewedFromTop--">isFurnitureViewedFromTop</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isGridVisible--">isGridVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isLanguageEditable--">isLanguageEditable</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isMagnetismEnabled--">isMagnetismEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isNavigationPanelVisible--">isNavigationPanelVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isObserverCameraSelectedAtChange--">isObserverCameraSelectedAtChange</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isRoomFloorColoredOrTextured--">isRoomFloorColoredOrTextured</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isRulersVisible--">isRulersVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isValueAddedTaxEnabled--">isValueAddedTaxEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#removePropertyChangeListener-com.eteks.sweethome3d.model.UserPreferences.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAerialViewCenteredOnSelectionEnabled-boolean-">setAerialViewCenteredOnSelectionEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAutoCompletionStrings-java.lang.String-java.util.List-">setAutoCompletionStrings</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAutoSaveDelayForRecovery-int-">setAutoSaveDelayForRecovery</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setCheckUpdatesEnabled-boolean-">setCheckUpdatesEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setCurrency-java.lang.String-">setCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setDefaultFontName-java.lang.String-">setDefaultFontName</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setDefaultValueAddedTaxPercentage-java.math.BigDecimal-">setDefaultValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setEditingIn3DViewEnabled-boolean-">setEditingIn3DViewEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFloorColoredOrTextured-boolean-">setFloorColoredOrTextured</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureCatalog-com.eteks.sweethome3d.model.FurnitureCatalog-">setFurnitureCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureCatalogViewedInTree-boolean-">setFurnitureCatalogViewedInTree</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureModelIconSize-int-">setFurnitureModelIconSize</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureViewedFromTop-boolean-">setFurnitureViewedFromTop</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setGridVisible-boolean-">setGridVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setHomeExamples-java.util.List-">setHomeExamples</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setLanguage-java.lang.String-">setLanguage</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setMagnetismEnabled-boolean-">setMagnetismEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNavigationPanelVisible-boolean-">setNavigationPanelVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewFloorThickness-float-">setNewFloorThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewRoomFloorColor-java.lang.Integer-">setNewRoomFloorColor</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallBaseboardHeight-float-">setNewWallBaseboardHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallBaseboardThickness-float-">setNewWallBaseboardThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallHeight-float-">setNewWallHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallPattern-com.eteks.sweethome3d.model.TextureImage-">setNewWallPattern</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallThickness-float-">setNewWallThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setObserverCameraSelectedAtChange-boolean-">setObserverCameraSelectedAtChange</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setPatternsCatalog-com.eteks.sweethome3d.model.PatternsCatalog-">setPatternsCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setPhotoRenderer-java.lang.String-">setPhotoRenderer</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentColors-java.util.List-">setRecentColors</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentHomes-java.util.List-">setRecentHomes</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentTextures-java.util.List-">setRecentTextures</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRulersVisible-boolean-">setRulersVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setSupportedLanguages-java.lang.String:A-">setSupportedLanguages</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setTexturesCatalog-com.eteks.sweethome3d.model.TexturesCatalog-">setTexturesCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setUnit-com.eteks.sweethome3d.model.LengthUnit-">setUnit</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setUpdatesMinimumDate-java.lang.Long-">setUpdatesMinimumDate</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setValueAddedTaxEnabled-boolean-">setValueAddedTaxEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setWallPattern-com.eteks.sweethome3d.model.TextureImage-">setWallPattern</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="PLUGIN_LANGUAGE_LIBRARY_FAMILY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PLUGIN_LANGUAGE_LIBRARY_FAMILY</h4>
<pre>public static final&nbsp;java.lang.String PLUGIN_LANGUAGE_LIBRARY_FAMILY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.FileUserPreferences.PLUGIN_LANGUAGE_LIBRARY_FAMILY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="FileUserPreferences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FileUserPreferences</h4>
<pre>public&nbsp;FileUserPreferences()</pre>
<div class="block">Creates user preferences read from user preferences in file system,
 and from resource files.</div>
</li>
</ul>
<a name="FileUserPreferences-java.io.File-java.io.File:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FileUserPreferences</h4>
<pre>public&nbsp;FileUserPreferences(java.io.File&nbsp;preferencesFolder,
                           java.io.File[]&nbsp;applicationFolders)</pre>
<div class="block">Creates user preferences stored in the folders given in parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferencesFolder</code> - the folder where preferences files are stored
    or <code>null</code> if this folder is the default one.</dd>
<dd><code>applicationFolders</code> - the folders where application private files are stored
    or <code>null</code> if it's the default one. As the first application folder
    is used as the folder where plug-ins files are imported by the user, it should
    have write access otherwise the user won't be able to import them.</dd>
</dl>
</li>
</ul>
<a name="FileUserPreferences-java.io.File-java.io.File:A-java.util.concurrent.Executor-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FileUserPreferences</h4>
<pre>public&nbsp;FileUserPreferences(java.io.File&nbsp;preferencesFolder,
                           java.io.File[]&nbsp;applicationFolders,
                           java.util.concurrent.Executor&nbsp;updater)</pre>
<div class="block">Creates user preferences stored in the folders given in parameter.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferencesFolder</code> - the folder where preferences files are stored
    or <code>null</code> if this folder is the default one.</dd>
<dd><code>applicationFolders</code> - the folders where application private files are stored
    or <code>null</code> if it's the default one. As the first application folder
    is used as the folder where plug-ins files are imported by the user, it should
    have write access otherwise the user won't be able to import them.</dd>
<dd><code>updater</code> - an executor that will be used to update user preferences for lengthy
    operations. If <code>null</code>, then these operations and
    updates will be executed in the current thread.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getResourceClassLoaders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceClassLoaders</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.ClassLoader&gt;&nbsp;getResourceClassLoaders()</pre>
<div class="block">Returns the default class loader of user preferences and the class loaders that
 give access to resources in language libraries plugin folder.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getResourceClassLoaders--">getResourceClassLoaders</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
</dl>
</li>
</ul>
<a name="readFurnitureCatalogFromResource-java.io.File:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readFurnitureCatalogFromResource</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;readFurnitureCatalogFromResource(java.io.File[]&nbsp;furniturePluginFolders)</pre>
<div class="block">Returns the furniture catalog contained in resources of the application and in the given plug-in folders.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
</li>
</ul>
<a name="readTexturesCatalogFromResource-java.io.File:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTexturesCatalogFromResource</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a>&nbsp;readTexturesCatalogFromResource(java.io.File[]&nbsp;texturesPluginFolders)</pre>
<div class="block">Returns the textures catalog contained in resources of the application and in the given plug-in folders.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
</li>
</ul>
<a name="readModifiablePieceOfFurniture-java.util.prefs.Preferences-int-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readModifiablePieceOfFurniture</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;readModifiablePieceOfFurniture(java.util.prefs.Preferences&nbsp;preferences,
                                                                 int&nbsp;index,
                                                                 java.io.File&nbsp;preferencesFolder)</pre>
<div class="block">Returns the modifiable piece of furniture read from <code>preferences</code> at the given <code>index</code>.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferences</code> - the preferences from which piece of furniture data can be read</dd>
<dd><code>index</code> - the index of the read piece</dd>
<dd><code>preferencesFolder</code> - the folder where piece resources can be stored</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the read piece of furniture or <code>null</code> if the piece at the given index doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="readModifiableFurnitureCategory-java.util.prefs.Preferences-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readModifiableFurnitureCategory</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a>&nbsp;readModifiableFurnitureCategory(java.util.prefs.Preferences&nbsp;preferences,
                                                            int&nbsp;index)</pre>
<div class="block">Returns the furniture category of a piece at the given <code>index</code>
 read from <code>preferences</code>.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferences</code> - the preferences from which piece of furniture data can be read</dd>
<dd><code>index</code> - the index of the read piece</dd>
</dl>
</li>
</ul>
<a name="readModifiableTexture-java.util.prefs.Preferences-int-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readModifiableTexture</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;readModifiableTexture(java.util.prefs.Preferences&nbsp;preferences,
                                               int&nbsp;index,
                                               java.io.File&nbsp;preferencesFolder)</pre>
<div class="block">Returns the modifiable texture read from <code>preferences</code> at the given <code>index</code>.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferences</code> - the preferences from which texture data can be read</dd>
<dd><code>index</code> - the index of the read texture</dd>
<dd><code>preferencesFolder</code> - the folder where textures resources can be stored</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the read texture or <code>null</code> if the texture at the given index doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="readModifiableTextureCategory-java.util.prefs.Preferences-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readModifiableTextureCategory</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a>&nbsp;readModifiableTextureCategory(java.util.prefs.Preferences&nbsp;preferences,
                                                         int&nbsp;index)</pre>
<div class="block">Returns the category of a texture at the given <code>index</code>
 read from <code>preferences</code>.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferences</code> - the preferences from which texture data can be read</dd>
<dd><code>index</code> - the index of the read piece</dd>
</dl>
</li>
</ul>
<a name="write--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write()
           throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Writes user preferences in current user preferences in system.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#write--">write</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code> - if user preferences couldn'y be saved.</dd>
</dl>
</li>
</ul>
<a name="getLanguageLibrariesPluginFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLanguageLibrariesPluginFolders</h4>
<pre>protected&nbsp;java.io.File[]&nbsp;getLanguageLibrariesPluginFolders()</pre>
<div class="block">Returns the folders where language libraries files are placed
 or <code>null</code> if that folder can't be retrieved.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
</li>
</ul>
<a name="getFurnitureLibrariesPluginFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureLibrariesPluginFolders</h4>
<pre>protected&nbsp;java.io.File[]&nbsp;getFurnitureLibrariesPluginFolders()</pre>
<div class="block">Returns the folders where furniture catalog files are placed
 or <code>null</code> if that folder can't be retrieved.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
</li>
</ul>
<a name="getTexturesLibrariesPluginFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTexturesLibrariesPluginFolders</h4>
<pre>protected&nbsp;java.io.File[]&nbsp;getTexturesLibrariesPluginFolders()</pre>
<div class="block">Returns the folders where texture catalog files are placed
 or <code>null</code> if that folder can't be retrieved.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
</li>
</ul>
<a name="getApplicationFolder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplicationFolder</h4>
<pre>public&nbsp;java.io.File&nbsp;getApplicationFolder()
                                  throws java.io.IOException</pre>
<div class="block">Returns the first Sweet Home 3D application folder.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getApplicationFolders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplicationFolders</h4>
<pre>public&nbsp;java.io.File[]&nbsp;getApplicationFolders()
                                     throws java.io.IOException</pre>
<div class="block">Returns Sweet Home 3D application folders.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getApplicationSubfolders-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplicationSubfolders</h4>
<pre>public&nbsp;java.io.File[]&nbsp;getApplicationSubfolders(java.lang.String&nbsp;subfolder)
                                        throws java.io.IOException</pre>
<div class="block">Returns subfolders of Sweet Home 3D application folders of a given name.
 Caution : This method can be called from constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getPreferences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreferences</h4>
<pre>protected&nbsp;java.util.prefs.Preferences&nbsp;getPreferences()</pre>
<div class="block">Returns default Java preferences for current system user.
 Caution : This method is called once in constructor so overriding implementations
 shouldn't be based on the state of their fields.</div>
</li>
</ul>
<a name="setActionTipIgnored-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActionTipIgnored</h4>
<pre>public&nbsp;void&nbsp;setActionTipIgnored(java.lang.String&nbsp;actionKey)</pre>
<div class="block">Sets which action tip should be ignored.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setActionTipIgnored-java.lang.String-">setActionTipIgnored</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
</dl>
</li>
</ul>
<a name="isActionTipIgnored-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isActionTipIgnored</h4>
<pre>public&nbsp;boolean&nbsp;isActionTipIgnored(java.lang.String&nbsp;actionKey)</pre>
<div class="block">Returns whether an action tip should be ignored or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isActionTipIgnored-java.lang.String-">isActionTipIgnored</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
</dl>
</li>
</ul>
<a name="resetIgnoredActionTips--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetIgnoredActionTips</h4>
<pre>public&nbsp;void&nbsp;resetIgnoredActionTips()</pre>
<div class="block">Resets the display flag of action tips.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#resetIgnoredActionTips--">resetIgnoredActionTips</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
</dl>
</li>
</ul>
<a name="languageLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>languageLibraryExists</h4>
<pre>public&nbsp;boolean&nbsp;languageLibraryExists(java.lang.String&nbsp;location)
                              throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns <code>true</code> if the language library at the given location exists
 in the first language libraries folder.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>location</code> - the file path of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="addLanguageLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLanguageLibrary</h4>
<pre>public&nbsp;void&nbsp;addLanguageLibrary(java.lang.String&nbsp;languageLibraryPath)
                        throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Adds <code>languageLibraryPath</code> to the first language libraries folder
 to make the language library it contains available to supported languages.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>languageLibraryPath</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="furnitureLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>furnitureLibraryExists</h4>
<pre>public&nbsp;boolean&nbsp;furnitureLibraryExists(java.lang.String&nbsp;location)
                               throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns <code>true</code> if the furniture library at the given <code>location</code> exists
 in the first furniture libraries folder.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>location</code> - the file path of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="addFurnitureLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurnitureLibrary</h4>
<pre>public&nbsp;void&nbsp;addFurnitureLibrary(java.lang.String&nbsp;furnitureLibraryPath)
                         throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Adds the file <code>furnitureLibraryPath</code> to the first furniture libraries folder
 to make the furniture library available to catalog.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureLibraryPath</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="texturesLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>texturesLibraryExists</h4>
<pre>public&nbsp;boolean&nbsp;texturesLibraryExists(java.lang.String&nbsp;location)
                              throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns <code>true</code> if the textures library at the given <code>location</code> exists
 in the first textures libraries folder.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>location</code> - the file path of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="addTexturesLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTexturesLibrary</h4>
<pre>public&nbsp;void&nbsp;addTexturesLibrary(java.lang.String&nbsp;texturesLibraryPath)
                        throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Adds the file <code>texturesLibraryPath</code> to the first textures libraries folder
 to make the textures library available to catalog.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texturesLibraryPath</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="getLibraries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLibraries</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;getLibraries()</pre>
<div class="block">Returns the libraries available in user preferences.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLibraries--">getLibraries</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="deleteLibraries-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteLibraries</h4>
<pre>public&nbsp;void&nbsp;deleteLibraries(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;libraries)
                     throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Deletes the given <code>libraries</code> and updates user preferences.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="isLibraryDeletable-com.eteks.sweethome3d.model.Library-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isLibraryDeletable</h4>
<pre>public&nbsp;boolean&nbsp;isLibraryDeletable(<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&nbsp;library)</pre>
<div class="block">Returns <code>true</code> if the given file <code>library</code> can be deleted.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FileUserPreferences.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/FileUserPreferences.html" target="_top">Frames</a></li>
<li><a href="FileUserPreferences.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
