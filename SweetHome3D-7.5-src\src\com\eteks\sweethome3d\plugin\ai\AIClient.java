/*
 * AIClient.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Interface for AI clients that can analyze floor plans.
 * Supports OpenAI-compatible endpoints using the Chat Completions API.
 * 
 * <AUTHOR> <PERSON>
 */
public interface AIClient {
  
  /**
   * Analyzes a floor plan using the configured AI model.
   * 
   * @param floorPlanData JSON representation of the floor plan data
   * @param prompt The analysis prompt to send to the AI
   * @return A CompletableFuture containing the AI's analysis response
   */
  CompletableFuture<String> analyzeFloorPlan(String floorPlanData, String prompt);
  
  /**
   * Tests the connection to the AI service.
   * 
   * @return true if the connection is successful, false otherwise
   */
  boolean testConnection();
  
  /**
   * Retrieves the list of available models from the provider.
   * 
   * @return A list of available model names
   */
  List<String> getAvailableModels();
  
  /**
   * Closes the client and releases any resources.
   */
  void close();
}
