<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomeLight (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeLight (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeLight.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeLight.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeLight.html" target="_top">Frames</a></li>
<li><a href="HomeLight.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class HomeLight" class="title">Class HomeLight</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomePieceOfFurniture</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomeLight</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeLight</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>
implements <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a></pre>
<div class="block">A light in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.HomeLight">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.Property.html" title="enum in com.eteks.sweethome3d.model">HomeLight.Property</a></span></code>
<div class="block">The properties of a light that may change.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#EMPTY_PROPERTY_ARRAY">EMPTY_PROPERTY_ARRAY</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#HomeLight-com.eteks.sweethome3d.model.Light-">HomeLight</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>&nbsp;light)</code>
<div class="block">Creates a home light from an existing one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#HomeLight-com.eteks.sweethome3d.model.Light-java.lang.String:A-">HomeLight</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>&nbsp;light,
         java.lang.String[]&nbsp;copiedProperties)</code>
<div class="block">Creates a home light from an existing one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#HomeLight-java.lang.String-com.eteks.sweethome3d.model.Light-">HomeLight</a></span>(java.lang.String&nbsp;id,
         <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>&nbsp;light)</code>
<div class="block">Creates a home light from an existing one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#HomeLight-java.lang.String-com.eteks.sweethome3d.model.Light-java.lang.String:A-">HomeLight</a></span>(java.lang.String&nbsp;id,
         <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>&nbsp;light,
         java.lang.String[]&nbsp;copiedProperties)</code>
<div class="block">Creates a home light from an existing one.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model">HomeLight</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this light.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#getLightSourceMaterialNames--">getLightSourceMaterialNames</a></span>()</code>
<div class="block">Returns the material names of the light sources in the 3D model managed by this light.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#getLightSources--">getLightSources</a></span>()</code>
<div class="block">Returns the sources managed by this light.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#getPower--">getPower</a></span>()</code>
<div class="block">Returns the power of this light.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#setLightSourceMaterialNames-java.lang.String:A-">setLightSourceMaterialNames</a></span>(java.lang.String[]&nbsp;lightSourceMaterialNames)</code>
<div class="block">Sets the material names of the light sources in the 3D model managed by this light.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#setLightSources-com.eteks.sweethome3d.model.LightSource:A-">setLightSources</a></span>(<a href="../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources)</code>
<div class="block">Sets the sources managed by this light.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html#setPower-float-">setPower</a></span>(float&nbsp;power)</code>
<div class="block">Sets the power of this light.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#containsPoint-float-float-float-">containsPoint</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getAngle--">getAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCatalogId--">getCatalogId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getColor--">getColor</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCreator--">getCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDepth--">getDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDepthInPlan--">getDepthInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getFurnitureComparator-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">getFurnitureComparator</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getGroundElevation--">getGroundElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getHeight--">getHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getHeightInPlan--">getHeightInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getIcon--">getIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getInformation--">getInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getLevel--">getLevel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModel--">getModel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelFlags--">getModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelMaterials--">getModelMaterials</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelRotation--">getModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelSize--">getModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelTransformations--">getModelTransformations</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameAngle--">getNameAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameStyle--">getNameStyle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameXOffset--">getNameXOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameYOffset--">getNameYOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPitch--">getPitch</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPlanIcon--">getPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPoints--">getPoints</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPrice--">getPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPriceValueAddedTaxIncluded--">getPriceValueAddedTaxIncluded</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getRoll--">getRoll</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getShininess--">getShininess</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getTexture--">getTexture</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getValueAddedTax--">getValueAddedTax</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getWidth--">getWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getWidthInPlan--">getWidthInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getX--">getX</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getY--">getY</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBottomLeftPointAt-float-float-float-">isBottomLeftPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBottomRightPointAt-float-float-float-">isBottomRightPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isDeformable--">isDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isHorizontallyRotated--">isHorizontallyRotated</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isModelCenteredAtOrigin--">isModelCenteredAtOrigin</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isModelMirrored--">isModelMirrored</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isMovable--">isMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isNameCenterPointAt-float-float-float-">isNameCenterPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isNameVisible--">isNameVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isParallelToWall-com.eteks.sweethome3d.model.Wall-">isParallelToWall</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isPointAt-float-float-float-">isPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTexturable--">isTexturable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTopLeftPointAt-float-float-float-">isTopLeftPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTopRightPointAt-float-float-float-">isTopRightPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isVisible--">isVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#move-float-float-">move</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#scale-float-">scale</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setAngle-float-">setAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setBackFaceShown-boolean-">setBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCatalogId-java.lang.String-">setCatalogId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setColor-java.lang.Integer-">setColor</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCreator-java.lang.String-">setCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCurrency-java.lang.String-">setCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDepth-float-">setDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDepthInPlan-float-">setDepthInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setElevation-float-">setElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setHeight-float-">setHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setHeightInPlan-float-">setHeightInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setIcon-com.eteks.sweethome3d.model.Content-">setIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setInformation-java.lang.String-">setInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setLicense-java.lang.String-">setLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelCenteredAtOrigin-boolean-">setModelCenteredAtOrigin</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelFlags-int-">setModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">setModelMaterials</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelMirrored-boolean-">setModelMirrored</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelRotation-float:A:A-">setModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelSize-java.lang.Long-">setModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">setModelTransformations</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setMovable-boolean-">setMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setName-java.lang.String-">setName</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameAngle-float-">setNameAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameStyle-com.eteks.sweethome3d.model.TextStyle-">setNameStyle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameVisible-boolean-">setNameVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameXOffset-float-">setNameXOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameYOffset-float-">setNameYOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPitch-float-">setPitch</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPlanIcon-com.eteks.sweethome3d.model.Content-">setPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPrice-java.math.BigDecimal-">setPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setRoll-float-">setRoll</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setShininess-java.lang.Float-">setShininess</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setStaircaseCutOutShape-java.lang.String-">setStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setTexture-com.eteks.sweethome3d.model.HomeTexture-">setTexture</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setValueAddedTaxPercentage-java.math.BigDecimal-">setValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setVisible-boolean-">setVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setWidth-float-">setWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setWidthInPlan-float-">setWidthInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setX-float-">setX</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setY-float-">setY</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeLight-com.eteks.sweethome3d.model.Light-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeLight</h4>
<pre>public&nbsp;HomeLight(<a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>&nbsp;light)</pre>
<div class="block">Creates a home light from an existing one.
 No additional properties will be copied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>light</code> - the light from which data are copied</dd>
</dl>
</li>
</ul>
<a name="HomeLight-com.eteks.sweethome3d.model.Light-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeLight</h4>
<pre>public&nbsp;HomeLight(<a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>&nbsp;light,
                 java.lang.String[]&nbsp;copiedProperties)</pre>
<div class="block">Creates a home light from an existing one.
 No additional properties will be copied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>light</code> - the light from which data are copied</dd>
<dd><code>copiedProperties</code> - the names of the additional properties which should be copied from the existing piece
                         or <code>null</code> if all properties should be copied.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="HomeLight-java.lang.String-com.eteks.sweethome3d.model.Light-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeLight</h4>
<pre>public&nbsp;HomeLight(java.lang.String&nbsp;id,
                 <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>&nbsp;light)</pre>
<div class="block">Creates a home light from an existing one.
 No additional properties will be copied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the ID of the light</dd>
<dd><code>light</code> - the light from which data are copied</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="HomeLight-java.lang.String-com.eteks.sweethome3d.model.Light-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeLight</h4>
<pre>public&nbsp;HomeLight(java.lang.String&nbsp;id,
                 <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>&nbsp;light,
                 java.lang.String[]&nbsp;copiedProperties)</pre>
<div class="block">Creates a home light from an existing one.
 No additional properties will be copied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the ID of the light</dd>
<dd><code>light</code> - the light from which data are copied</dd>
<dd><code>copiedProperties</code> - the names of the additional properties which should be copied from the existing piece
                         or <code>null</code> if all properties should be copied.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getLightSources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightSources</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;getLightSources()</pre>
<div class="block">Returns the sources managed by this light. Each light source point
 is a percentage of the width, the depth and the height of this light.
 with the abscissa origin at the left side of the piece,
 the ordinate origin at the front side of the piece
 and the elevation origin at the bottom side of the piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Light.html#getLightSources--">getLightSources</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a copy of light sources array.</dd>
</dl>
</li>
</ul>
<a name="setLightSources-com.eteks.sweethome3d.model.LightSource:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLightSources</h4>
<pre>public&nbsp;void&nbsp;setLightSources(<a href="../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources)</pre>
<div class="block">Sets the sources managed by this light. Once this light is updated,
 listeners added to this light will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lightSources</code> - sources of the light</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getLightSourceMaterialNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightSourceMaterialNames</h4>
<pre>public&nbsp;java.lang.String[]&nbsp;getLightSourceMaterialNames()</pre>
<div class="block">Returns the material names of the light sources in the 3D model managed by this light.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Light.html#getLightSourceMaterialNames--">getLightSourceMaterialNames</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a copy of light source material names array.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setLightSourceMaterialNames-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLightSourceMaterialNames</h4>
<pre>public&nbsp;void&nbsp;setLightSourceMaterialNames(java.lang.String[]&nbsp;lightSourceMaterialNames)</pre>
<div class="block">Sets the material names of the light sources in the 3D model managed by this light.
 Once this light is updated, listeners added to this light will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>lightSourceMaterialNames</code> - material names of the light sources</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPower</h4>
<pre>public&nbsp;float&nbsp;getPower()</pre>
<div class="block">Returns the power of this light.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setPower-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPower</h4>
<pre>public&nbsp;void&nbsp;setPower(float&nbsp;power)</pre>
<div class="block">Sets the power of this light. Once this light is updated,
 listeners added to this light will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>power</code> - power of the light</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model">HomeLight</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this light.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeLight.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeLight.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeLight.html" target="_top">Frames</a></li>
<li><a href="HomeLight.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
