<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomeApplication (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeApplication (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":6,"i5":10,"i6":10,"i7":10,"i8":10,"i9":6,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeApplication.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeApplication.html" target="_top">Frames</a></li>
<li><a href="HomeApplication.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class HomeApplication" class="title">Class HomeApplication</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomeApplication</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/applet/AppletApplication.html" title="class in com.eteks.sweethome3d.applet">AppletApplication</a>, <a href="../../../../com/eteks/sweethome3d/SweetHome3D.html" title="class in com.eteks.sweethome3d">SweetHome3D</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">HomeApplication</span>
extends java.lang.Object</pre>
<div class="block">Application managing a list of homes displayed at screen.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#HomeApplication--">HomeApplication</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#addHome-com.eteks.sweethome3d.model.Home-">addHome</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Adds a given <code>home</code> to the homes list of this application.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#addHomesListener-com.eteks.sweethome3d.model.CollectionListener-">addHomesListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the home <code>listener</code> in parameter to this application.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#createHome--">createHome</a></span>()</code>
<div class="block">Returns a new home.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#deleteHome-com.eteks.sweethome3d.model.Home-">deleteHome</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Removes a given <code>home</code> from the homes list  of this application.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>abstract <a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#getHomeRecorder--">getHomeRecorder</a></span>()</code>
<div class="block">Returns the default recorder able to write and read homes.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#getHomeRecorder-com.eteks.sweethome3d.model.HomeRecorder.Type-">getHomeRecorder</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a>&nbsp;type)</code>
<div class="block">Returns a recorder of a given <code>type</code> able to write and read homes.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#getHomes--">getHomes</a></span>()</code>
<div class="block">Returns an unmodifiable collection of the homes of this application.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#getId--">getId</a></span>()</code>
<div class="block">Returns the id of this application.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this application.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#getUserPreferences--">getUserPreferences</a></span>()</code>
<div class="block">Returns user preferences.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#getVersion--">getVersion</a></span>()</code>
<div class="block">Returns information about the version of this application.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html#removeHomesListener-com.eteks.sweethome3d.model.CollectionListener-">removeHomesListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the home <code>listener</code> in parameter from this application.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeApplication--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeApplication</h4>
<pre>public&nbsp;HomeApplication()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addHomesListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addHomesListener</h4>
<pre>public&nbsp;void&nbsp;addHomesListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the home <code>listener</code> in parameter to this application.</div>
</li>
</ul>
<a name="removeHomesListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeHomesListener</h4>
<pre>public&nbsp;void&nbsp;removeHomesListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the home <code>listener</code> in parameter from this application.</div>
</li>
</ul>
<a name="createHome--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHome</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;createHome()</pre>
<div class="block">Returns a new home.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a new home with wall heights equal to the one in user preferences.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="getHomes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomes</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;&nbsp;getHomes()</pre>
<div class="block">Returns an unmodifiable collection of the homes of this application.</div>
</li>
</ul>
<a name="addHome-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addHome</h4>
<pre>public&nbsp;void&nbsp;addHome(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</pre>
<div class="block">Adds a given <code>home</code> to the homes list of this application.
 Once the <code>home</code> is added, home listeners added 
 to this application will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a> 
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
</li>
</ul>
<a name="deleteHome-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteHome</h4>
<pre>public&nbsp;void&nbsp;deleteHome(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</pre>
<div class="block">Removes a given <code>home</code> from the homes list  of this application.
 Once the <code>home</code> is removed, home listeners added 
 to this application will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a> 
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#DELETE"><code>DELETE</code></a>.</div>
</li>
</ul>
<a name="getHomeRecorder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeRecorder</h4>
<pre>public abstract&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;getHomeRecorder()</pre>
<div class="block">Returns the default recorder able to write and read homes.</div>
</li>
</ul>
<a name="getHomeRecorder-com.eteks.sweethome3d.model.HomeRecorder.Type-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeRecorder</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;getHomeRecorder(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a>&nbsp;type)</pre>
<div class="block">Returns a recorder of a given <code>type</code> able to write and read homes.
 Subclasses may override this method to return a recorder matching <code>type</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - a hint for the application to choose the returned recorder.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the default recorder able to write and read homes.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.8</dd>
</dl>
</li>
</ul>
<a name="getUserPreferences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserPreferences</h4>
<pre>public abstract&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;getUserPreferences()</pre>
<div class="block">Returns user preferences.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this application. Default implementation returns <i>Sweet Home 3D</i>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">Returns information about the version of this application.
 Default implementation returns an empty string.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.6</dd>
</dl>
</li>
</ul>
<a name="getId--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getId()</pre>
<div class="block">Returns the id of this application.
 Default implementation returns null.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeApplication.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeApplication.html" target="_top">Frames</a></li>
<li><a href="HomeApplication.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
