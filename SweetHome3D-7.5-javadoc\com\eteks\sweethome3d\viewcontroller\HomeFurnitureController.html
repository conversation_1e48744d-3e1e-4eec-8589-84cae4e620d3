<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>HomeFurnitureController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeFurnitureController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":42,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeFurnitureController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureHorizontalAxis.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" target="_top">Frames</a></li>
<li><a href="HomeFurnitureController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class HomeFurnitureController" class="title">Class HomeFurnitureController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.HomeFurnitureController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeFurnitureController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for home furniture view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureHorizontalAxis.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureHorizontalAxis</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getHorizontalAxis--">horizontal axis</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurniturePaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurniturePaint</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getPaint--">paint type</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureShininess.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureShininess</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getShininess--">shininess type</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#HomeFurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">HomeFurnitureController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#HomeFurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">HomeFurnitureController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.Map&lt;<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a>,java.lang.Object&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getAdditionalProperties--">getAdditionalProperties</a></span>()</code>
<div class="block">Returns additional edited properties.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getAngle--">getAngle</a></span>()</code>
<div class="block">Returns the edited angle in radians.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getAngleInDegrees--">getAngleInDegrees</a></span>()</code>
<div class="block">Returns the edited angle in degrees.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getBasePlanItem--">getBasePlanItem</a></span>()</code>
<div class="block">Returns whether furniture is a base plan item or not.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the edited color.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getContentManager--">getContentManager</a></span>()</code>
<div class="block">Returns the content manager associated to this controller.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getDepth--">getDepth</a></span>()</code>
<div class="block">Returns the edited depth.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Returns the edited description.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the edited elevation.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the edited height.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureHorizontalAxis.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureHorizontalAxis</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getHorizontalAxis--">getHorizontalAxis</a></span>()</code>
<div class="block">Returns the edited horizontal axis.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the edited icon.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getLightPower--">getLightPower</a></span>()</code>
<div class="block">Returns the edited light power.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getModelMaterialsController--">getModelMaterialsController</a></span>()</code>
<div class="block">Returns the model materials controller of the piece.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getModelMirrored--">getModelMirrored</a></span>()</code>
<div class="block">Returns whether furniture model is mirrored or not.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getModelPresetTransformations-int-">getModelPresetTransformations</a></span>(int&nbsp;index)</code>
<div class="block">Returns the preset model transformations at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getModelPresetTransformationsNames--">getModelPresetTransformationsNames</a></span>()</code>
<div class="block">Returns the names of the available preset model transformations.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getModelTransformations--">getModelTransformations</a></span>()</code>
<div class="block">Returns model transformations.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getName--">getName</a></span>()</code>
<div class="block">Returns the edited name.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getNameVisible--">getNameVisible</a></span>()</code>
<div class="block">Returns whether furniture name should be drawn or not.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurniturePaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurniturePaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getPaint--">getPaint</a></span>()</code>
<div class="block">Returns whether the piece is colored, textured, uses customized materials or unknown painted.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns the edited pitch in radians.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getPrice--">getPrice</a></span>()</code>
<div class="block">Returns the edited price.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getRoll--">getRoll</a></span>()</code>
<div class="block">Returns the edited roll angle in radians.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureShininess.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureShininess</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getShininess--">getShininess</a></span>()</code>
<div class="block">Returns whether the piece is shininess is the default one, matt, shiny or unknown.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getTextureController--">getTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the piece.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></span>()</code>
<div class="block">Returns edited Value Added Tax percentage.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getVisible--">getVisible</a></span>()</code>
<div class="block">Returns whether furniture is visible or not.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the edited width.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getX--">getX</a></span>()</code>
<div class="block">Returns the edited abscissa.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getY--">getY</a></span>()</code>
<div class="block">Returns the edited ordinate.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isBasePlanItemEditable--">isBasePlanItemEditable</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">the method is wrongly named and should be replaced by <code>isBasePlanItemEnabled</code>.</span></div>
</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isBasePlanItemEnabled--">isBasePlanItemEnabled</a></span>()</code>
<div class="block">Returns <code>true</code> if base plan item is an enabled property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isDeformable--">isDeformable</a></span>()</code>
<div class="block">Returns whether furniture model can be deformed or not.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isLightPowerEditable--">isLightPowerEditable</a></span>()</code>
<div class="block">Returns <code>true</code> if light power is an editable property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isPriceEditable--">isPriceEditable</a></span>()</code>
<div class="block">Returns whether the price can be edited or not.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isPropertyEditable-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.Property-">isPropertyEditable</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.Property</a>&nbsp;property)</code>
<div class="block">Returns <code>true</code> if the given <code>property</code> is editable.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isProportional--">isProportional</a></span>()</code>
<div class="block">Returns whether furniture proportions should be kept or not.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isResizable--">isResizable</a></span>()</code>
<div class="block">Returns whether furniture model can be resized or not.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isRollAndPitchEditable--">isRollAndPitchEditable</a></span>()</code>
<div class="block">Returns whether roll and pitch angles can be edited.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isTexturable--">isTexturable</a></span>()</code>
<div class="block">Returns whether the color or the texture of the furniture model can be changed or not.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#isValueAddedTaxPercentageEditable--">isValueAddedTaxPercentageEditable</a></span>()</code>
<div class="block">Returns whether the Value Added Tax percentage can be edited or not.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#modifyFurniture--">modifyFurniture</a></span>()</code>
<div class="block">Controls the modification of selected furniture in the edited home.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setAdditionalProperties-java.util.Map-">setAdditionalProperties</a></span>(java.util.Map&lt;<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a>,java.lang.Object&gt;&nbsp;additionalProperties)</code>
<div class="block">Sets additional edited properties.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setAngle-java.lang.Float-">setAngle</a></span>(java.lang.Float&nbsp;angle)</code>
<div class="block">Sets the edited angle in radians.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setAngle-java.lang.Float-boolean-">setAngle</a></span>(java.lang.Float&nbsp;angle,
        boolean&nbsp;updateAngleInDegrees)</code>&nbsp;</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setAngleInDegrees-java.lang.Integer-">setAngleInDegrees</a></span>(java.lang.Integer&nbsp;angleInDegrees)</code>
<div class="block">Sets the edited angle in degrees.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setBasePlanItem-java.lang.Boolean-">setBasePlanItem</a></span>(java.lang.Boolean&nbsp;basePlanItem)</code>
<div class="block">Sets whether furniture is a base plan item or not.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the edited color.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setDepth-java.lang.Float-">setDepth</a></span>(java.lang.Float&nbsp;depth)</code>
<div class="block">Sets the edited depth.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setDescription-java.lang.String-">setDescription</a></span>(java.lang.String&nbsp;description)</code>
<div class="block">Sets the edited description.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setElevation-java.lang.Float-">setElevation</a></span>(java.lang.Float&nbsp;elevation)</code>
<div class="block">Sets the edited elevation.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setHeight-java.lang.Float-">setHeight</a></span>(java.lang.Float&nbsp;height)</code>
<div class="block">Sets the edited height.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setHorizontalAxis-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.FurnitureHorizontalAxis-">setHorizontalAxis</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureHorizontalAxis.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureHorizontalAxis</a>&nbsp;horizontalAxis)</code>
<div class="block">Sets the edited horizontal axis.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setLightPower-java.lang.Float-">setLightPower</a></span>(java.lang.Float&nbsp;lightPower)</code>
<div class="block">Sets the edited light power.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setModelMirrored-java.lang.Boolean-">setModelMirrored</a></span>(java.lang.Boolean&nbsp;modelMirrored)</code>
<div class="block">Sets whether furniture model is mirrored or not.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">setModelTransformations</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;modelTransformations)</code>
<div class="block">Sets model transformations.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-float-float-float-float-float-float-">setModelTransformations</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;transformations,
                       float&nbsp;x,
                       float&nbsp;y,
                       float&nbsp;elevation,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height)</code>
<div class="block">Sets model transformations and updated dimensions of the edited piece.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Sets the edited name.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setNameVisible-java.lang.Boolean-">setNameVisible</a></span>(java.lang.Boolean&nbsp;nameVisible)</code>
<div class="block">Sets whether furniture name is visible or not.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setPaint-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.FurniturePaint-">setPaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurniturePaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurniturePaint</a>&nbsp;paint)</code>
<div class="block">Sets whether the piece is colored, textured, uses customized materials or unknown painted.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setPitch-java.lang.Float-">setPitch</a></span>(java.lang.Float&nbsp;pitch)</code>
<div class="block">Sets the edited pitch in radians.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setPrice-java.math.BigDecimal-">setPrice</a></span>(java.math.BigDecimal&nbsp;price)</code>
<div class="block">Sets the edited price.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setProportional-boolean-">setProportional</a></span>(boolean&nbsp;proportional)</code>
<div class="block">Sets whether furniture proportions should be kept.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setRoll-java.lang.Float-">setRoll</a></span>(java.lang.Float&nbsp;roll)</code>
<div class="block">Sets the edited roll angle in radians.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setShininess-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.FurnitureShininess-">setShininess</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureShininess.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureShininess</a>&nbsp;shininess)</code>
<div class="block">Sets whether the piece shininess is the default one, matt, shiny or unknown.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setValueAddedTaxPercentage-java.math.BigDecimal-">setValueAddedTaxPercentage</a></span>(java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Sets the edited Value Added Tax percentage.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setVisible-java.lang.Boolean-">setVisible</a></span>(java.lang.Boolean&nbsp;visible)</code>
<div class="block">Sets whether furniture is visible or not.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setWidth-java.lang.Float-">setWidth</a></span>(java.lang.Float&nbsp;width)</code>
<div class="block">Sets the edited width.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setX-java.lang.Float-">setX</a></span>(java.lang.Float&nbsp;x)</code>
<div class="block">Sets the edited abscissa.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#setY-java.lang.Float-">setY</a></span>(java.lang.Float&nbsp;y)</code>
<div class="block">Sets the edited ordinate.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates edited properties from selected furniture in the home edited by this controller.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeFurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFurnitureController</h4>
<pre>public&nbsp;HomeFurnitureController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</li>
</ul>
<a name="HomeFurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeFurnitureController</h4>
<pre>public&nbsp;HomeFurnitureController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getTextureController()</pre>
<div class="block">Returns the texture controller of the piece.</div>
</li>
</ul>
<a name="getModelMaterialsController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelMaterialsController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;getModelMaterialsController()</pre>
<div class="block">Returns the model materials controller of the piece.</div>
</li>
</ul>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="getContentManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentManager</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;getContentManager()</pre>
<div class="block">Returns the content manager associated to this controller.</div>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates edited properties from selected furniture in the home edited by this controller.</div>
</li>
</ul>
<a name="isPropertyEditable-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.Property-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPropertyEditable</h4>
<pre>public&nbsp;boolean&nbsp;isPropertyEditable(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.Property</a>&nbsp;property)</pre>
<div class="block">Returns <code>true</code> if the given <code>property</code> is editable.
 Depending on whether a property is editable or not, the view associated to this controller
 may render it differently.</div>
</li>
</ul>
<a name="getIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getIcon()</pre>
<div class="block">Returns the edited icon.</div>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the edited name.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the edited name.</div>
</li>
</ul>
<a name="setNameVisible-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameVisible</h4>
<pre>public&nbsp;void&nbsp;setNameVisible(java.lang.Boolean&nbsp;nameVisible)</pre>
<div class="block">Sets whether furniture name is visible or not.</div>
</li>
</ul>
<a name="getNameVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameVisible</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getNameVisible()</pre>
<div class="block">Returns whether furniture name should be drawn or not.</div>
</li>
</ul>
<a name="setDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescription</h4>
<pre>public&nbsp;void&nbsp;setDescription(java.lang.String&nbsp;description)</pre>
<div class="block">Sets the edited description.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDescription()</pre>
<div class="block">Returns the edited description.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setAdditionalProperties-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAdditionalProperties</h4>
<pre>public&nbsp;void&nbsp;setAdditionalProperties(java.util.Map&lt;<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a>,java.lang.Object&gt;&nbsp;additionalProperties)</pre>
<div class="block">Sets additional edited properties.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getAdditionalProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdditionalProperties</h4>
<pre>public&nbsp;java.util.Map&lt;<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a>,java.lang.Object&gt;&nbsp;getAdditionalProperties()</pre>
<div class="block">Returns additional edited properties.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setPrice-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrice</h4>
<pre>public&nbsp;void&nbsp;setPrice(java.math.BigDecimal&nbsp;price)</pre>
<div class="block">Sets the edited price.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getPrice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrice</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getPrice()</pre>
<div class="block">Returns the edited price.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="isPriceEditable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPriceEditable</h4>
<pre>public&nbsp;boolean&nbsp;isPriceEditable()</pre>
<div class="block">Returns whether the price can be edited or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setValueAddedTaxPercentage-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValueAddedTaxPercentage</h4>
<pre>public&nbsp;void&nbsp;setValueAddedTaxPercentage(java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Sets the edited Value Added Tax percentage.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getValueAddedTaxPercentage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueAddedTaxPercentage</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getValueAddedTaxPercentage()</pre>
<div class="block">Returns edited Value Added Tax percentage.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="isValueAddedTaxPercentageEditable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isValueAddedTaxPercentageEditable</h4>
<pre>public&nbsp;boolean&nbsp;isValueAddedTaxPercentageEditable()</pre>
<div class="block">Returns whether the Value Added Tax percentage can be edited or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setX-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public&nbsp;void&nbsp;setX(java.lang.Float&nbsp;x)</pre>
<div class="block">Sets the edited abscissa.</div>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getX()</pre>
<div class="block">Returns the edited abscissa.</div>
</li>
</ul>
<a name="setY-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public&nbsp;void&nbsp;setY(java.lang.Float&nbsp;y)</pre>
<div class="block">Sets the edited ordinate.</div>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getY()</pre>
<div class="block">Returns the edited ordinate.</div>
</li>
</ul>
<a name="setElevation-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevation</h4>
<pre>public&nbsp;void&nbsp;setElevation(java.lang.Float&nbsp;elevation)</pre>
<div class="block">Sets the edited elevation.</div>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getElevation()</pre>
<div class="block">Returns the edited elevation.</div>
</li>
</ul>
<a name="setAngleInDegrees-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngleInDegrees</h4>
<pre>public&nbsp;void&nbsp;setAngleInDegrees(java.lang.Integer&nbsp;angleInDegrees)</pre>
<div class="block">Sets the edited angle in degrees.</div>
</li>
</ul>
<a name="getAngleInDegrees--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAngleInDegrees</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getAngleInDegrees()</pre>
<div class="block">Returns the edited angle in degrees.</div>
</li>
</ul>
<a name="setAngle-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngle</h4>
<pre>public&nbsp;void&nbsp;setAngle(java.lang.Float&nbsp;angle)</pre>
<div class="block">Sets the edited angle in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="setAngle-java.lang.Float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngle</h4>
<pre>public&nbsp;void&nbsp;setAngle(java.lang.Float&nbsp;angle,
                     boolean&nbsp;updateAngleInDegrees)</pre>
</li>
</ul>
<a name="getAngle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAngle</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getAngle()</pre>
<div class="block">Returns the edited angle in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="isRollAndPitchEditable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRollAndPitchEditable</h4>
<pre>public&nbsp;boolean&nbsp;isRollAndPitchEditable()</pre>
<div class="block">Returns whether roll and pitch angles can be edited.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setRoll-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoll</h4>
<pre>public&nbsp;void&nbsp;setRoll(java.lang.Float&nbsp;roll)</pre>
<div class="block">Sets the edited roll angle in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getRoll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoll</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getRoll()</pre>
<div class="block">Returns the edited roll angle in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setPitch-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitch</h4>
<pre>public&nbsp;void&nbsp;setPitch(java.lang.Float&nbsp;pitch)</pre>
<div class="block">Sets the edited pitch in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getPitch()</pre>
<div class="block">Returns the edited pitch in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setHorizontalAxis-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.FurnitureHorizontalAxis-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHorizontalAxis</h4>
<pre>public&nbsp;void&nbsp;setHorizontalAxis(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureHorizontalAxis.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureHorizontalAxis</a>&nbsp;horizontalAxis)</pre>
<div class="block">Sets the edited horizontal axis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getHorizontalAxis--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHorizontalAxis</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureHorizontalAxis.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureHorizontalAxis</a>&nbsp;getHorizontalAxis()</pre>
<div class="block">Returns the edited horizontal axis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isBasePlanItemEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBasePlanItemEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isBasePlanItemEnabled()</pre>
<div class="block">Returns <code>true</code> if base plan item is an enabled property.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="isBasePlanItemEditable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBasePlanItemEditable</h4>
<pre>public&nbsp;boolean&nbsp;isBasePlanItemEditable()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">the method is wrongly named and should be replaced by <code>isBasePlanItemEnabled</code>.</span></div>
<div class="block">Returns <code>true</code> if base plan item is an enabled property.</div>
</li>
</ul>
<a name="setBasePlanItem-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBasePlanItem</h4>
<pre>public&nbsp;void&nbsp;setBasePlanItem(java.lang.Boolean&nbsp;basePlanItem)</pre>
<div class="block">Sets whether furniture is a base plan item or not.</div>
</li>
</ul>
<a name="getBasePlanItem--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBasePlanItem</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getBasePlanItem()</pre>
<div class="block">Returns whether furniture is a base plan item or not.</div>
</li>
</ul>
<a name="setWidth-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidth</h4>
<pre>public&nbsp;void&nbsp;setWidth(java.lang.Float&nbsp;width)</pre>
<div class="block">Sets the edited width.</div>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getWidth()</pre>
<div class="block">Returns the edited width.</div>
</li>
</ul>
<a name="setDepth-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepth</h4>
<pre>public&nbsp;void&nbsp;setDepth(java.lang.Float&nbsp;depth)</pre>
<div class="block">Sets the edited depth.</div>
</li>
</ul>
<a name="getDepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepth</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getDepth()</pre>
<div class="block">Returns the edited depth.</div>
</li>
</ul>
<a name="setHeight-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public&nbsp;void&nbsp;setHeight(java.lang.Float&nbsp;height)</pre>
<div class="block">Sets the edited height.</div>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getHeight()</pre>
<div class="block">Returns the edited height.</div>
</li>
</ul>
<a name="setProportional-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProportional</h4>
<pre>public&nbsp;void&nbsp;setProportional(boolean&nbsp;proportional)</pre>
<div class="block">Sets whether furniture proportions should be kept.</div>
</li>
</ul>
<a name="isProportional--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProportional</h4>
<pre>public&nbsp;boolean&nbsp;isProportional()</pre>
<div class="block">Returns whether furniture proportions should be kept or not.</div>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the edited color.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the edited color.</div>
</li>
</ul>
<a name="setPaint-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.FurniturePaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPaint</h4>
<pre>public&nbsp;void&nbsp;setPaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurniturePaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurniturePaint</a>&nbsp;paint)</pre>
<div class="block">Sets whether the piece is colored, textured, uses customized materials or unknown painted.</div>
</li>
</ul>
<a name="getPaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurniturePaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurniturePaint</a>&nbsp;getPaint()</pre>
<div class="block">Returns whether the piece is colored, textured, uses customized materials or unknown painted.</div>
</li>
</ul>
<a name="setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelTransformations</h4>
<pre>public&nbsp;void&nbsp;setModelTransformations(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;modelTransformations)</pre>
<div class="block">Sets model transformations.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelTransformations</h4>
<pre>public&nbsp;void&nbsp;setModelTransformations(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;transformations,
                                    float&nbsp;x,
                                    float&nbsp;y,
                                    float&nbsp;elevation,
                                    float&nbsp;width,
                                    float&nbsp;depth,
                                    float&nbsp;height)</pre>
<div class="block">Sets model transformations and updated dimensions of the edited piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getModelTransformations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelTransformations</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;getModelTransformations()</pre>
<div class="block">Returns model transformations.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getModelPresetTransformationsNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelPresetTransformationsNames</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getModelPresetTransformationsNames()</pre>
<div class="block">Returns the names of the available preset model transformations.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getModelPresetTransformations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelPresetTransformations</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;getModelPresetTransformations(int&nbsp;index)</pre>
<div class="block">Returns the preset model transformations at the given <code>index</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setShininess-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController.FurnitureShininess-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShininess</h4>
<pre>public&nbsp;void&nbsp;setShininess(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureShininess.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureShininess</a>&nbsp;shininess)</pre>
<div class="block">Sets whether the piece shininess is the default one, matt, shiny or unknown.</div>
</li>
</ul>
<a name="getShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShininess</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureShininess.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureShininess</a>&nbsp;getShininess()</pre>
<div class="block">Returns whether the piece is shininess is the default one, matt, shiny or unknown.</div>
</li>
</ul>
<a name="setVisible-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>public&nbsp;void&nbsp;setVisible(java.lang.Boolean&nbsp;visible)</pre>
<div class="block">Sets whether furniture is visible or not.</div>
</li>
</ul>
<a name="getVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVisible</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getVisible()</pre>
<div class="block">Returns whether furniture is visible or not.</div>
</li>
</ul>
<a name="setModelMirrored-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelMirrored</h4>
<pre>public&nbsp;void&nbsp;setModelMirrored(java.lang.Boolean&nbsp;modelMirrored)</pre>
<div class="block">Sets whether furniture model is mirrored or not.</div>
</li>
</ul>
<a name="getModelMirrored--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelMirrored</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getModelMirrored()</pre>
<div class="block">Returns whether furniture model is mirrored or not.</div>
</li>
</ul>
<a name="isLightPowerEditable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLightPowerEditable</h4>
<pre>public&nbsp;boolean&nbsp;isLightPowerEditable()</pre>
<div class="block">Returns <code>true</code> if light power is an editable property.</div>
</li>
</ul>
<a name="getLightPower--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightPower</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getLightPower()</pre>
<div class="block">Returns the edited light power.</div>
</li>
</ul>
<a name="setLightPower-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLightPower</h4>
<pre>public&nbsp;void&nbsp;setLightPower(java.lang.Float&nbsp;lightPower)</pre>
<div class="block">Sets the edited light power.</div>
</li>
</ul>
<a name="isResizable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isResizable</h4>
<pre>public&nbsp;boolean&nbsp;isResizable()</pre>
<div class="block">Returns whether furniture model can be resized or not.</div>
</li>
</ul>
<a name="isDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeformable</h4>
<pre>public&nbsp;boolean&nbsp;isDeformable()</pre>
<div class="block">Returns whether furniture model can be deformed or not.</div>
</li>
</ul>
<a name="isTexturable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTexturable</h4>
<pre>public&nbsp;boolean&nbsp;isTexturable()</pre>
<div class="block">Returns whether the color or the texture of the furniture model can be changed or not.</div>
</li>
</ul>
<a name="modifyFurniture--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>modifyFurniture</h4>
<pre>public&nbsp;void&nbsp;modifyFurniture()</pre>
<div class="block">Controls the modification of selected furniture in the edited home.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeFurnitureController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureHorizontalAxis.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" target="_top">Frames</a></li>
<li><a href="HomeFurnitureController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
