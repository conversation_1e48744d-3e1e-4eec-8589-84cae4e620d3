<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.model.HomePieceOfFurniture (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.model.HomePieceOfFurniture (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.model.HomePieceOfFurniture" class="title">Uses of Class<br>com.eteks.sweethome3d.model.HomePieceOfFurniture</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d</a></td>
<td class="colLast">
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLHandler.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setPieceOfFurnitureAttributes-com.eteks.sweethome3d.model.HomePieceOfFurniture-java.lang.String-java.util.Map-">setPieceOfFurnitureAttributes</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                             java.lang.String&nbsp;elementName,
                             java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>piece</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.PieceOfFurnitureExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePieceOfFurniture-">writeAttributes</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
               <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.PieceOfFurnitureExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePieceOfFurniture-">writeChildren</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writePieceOfFurniture-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePieceOfFurniture-">writePieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                     <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Writes in XML the <code>piece</code> object with the given <code>writer</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.j3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a> in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.awt.geom.Area</code></td>
<td class="colLast"><span class="typeNameLabel">ModelManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getAreaOnFloor-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getAreaOnFloor</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;staircase)</code>
<div class="block">Returns the area on the floor of the given staircase.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html#HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-">HomePieceOfFurniture3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                      <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html#HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-boolean-boolean-">HomePieceOfFurniture3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                      <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      boolean&nbsp;ignoreDrawingMode,
                      boolean&nbsp;waitModelAndTextureLoadingEnd)</code>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html#HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-boolean-">HomePieceOfFurniture3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                      <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.lang.Object&nbsp;context,
                      boolean&nbsp;ignoreDrawingMode,
                      boolean&nbsp;waitModelAndTextureLoadingEnd)</code>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">HomeDoorOrWindow</a></span></code>
<div class="block">A door or a window in <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></span></code>
<div class="block">A group of furniture of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model">HomeLight</a></span></code>
<div class="block">A light in <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeShelfUnit.html" title="class in com.eteks.sweethome3d.model">HomeShelfUnit</a></span></code>
<div class="block">A shelf unit in <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this piece.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getAllFurniture--">getAllFurniture</a></span>()</code>
<div class="block">Returns the furniture of this group and of all its subgroups, including the possible child furniture groups.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getFurniture--">getFurniture</a></span>()</code>
<div class="block">Returns an unmodifiable list of the furniture of this group.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurniture--">getFurniture</a></span>()</code>
<div class="block">Returns an unmodifiable list of the furniture managed by this home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.Comparator&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getFurnitureComparator-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">getFurnitureComparator</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;property)</code>
<div class="block">Returns a comparator which compares furniture on a given <code>property</code> in ascending order.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSubList-java.util.List-">getFurnitureSubList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only home furniture.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">addPieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Adds the <code>piece</code> in parameter to this home at the end of the furniture list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-int-">addPieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                   int&nbsp;index)</code>
<div class="block">Adds the <code>piece</code> in parameter at a given <code>index</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#addPieceOfFurnitureToGroup-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.HomeFurnitureGroup-int-">addPieceOfFurnitureToGroup</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                          <a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;group,
                          int&nbsp;index)</code>
<div class="block">Adds the <code>piece</code> in parameter at the <code>index</code> in the given <code>group</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#deletePieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">deletePieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Deletes the <code>piece</code> in parameter from this home.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#addFurnitureListener-com.eteks.sweethome3d.model.CollectionListener-">addFurnitureListener</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the furniture <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#removeFurnitureListener-com.eteks.sweethome3d.model.CollectionListener-">removeFurnitureListener</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the furniture <code>listener</code> in parameter from this home.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-java.lang.String-">HomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;leadingPiece,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#Home-java.util.List-">Home</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</code>
<div class="block">Creates a home with the given <code>furniture</code>,
 no walls and a height equal to 250 cm.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.util.List-float-boolean-java.lang.String-">HomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  float&nbsp;angle,
                  boolean&nbsp;modelMirrored,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-java.lang.String-">HomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;leadingPiece,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.util.List-java.lang.String-">HomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#HomeFurnitureGroup-java.lang.String-java.util.List-float-boolean-java.lang.String-">HomeFurnitureGroup</a></span>(java.lang.String&nbsp;id,
                  java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                  float&nbsp;angle,
                  boolean&nbsp;modelMirrored,
                  java.lang.String&nbsp;name)</code>
<div class="block">Creates a group from the given <code>furniture</code> list.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getPieceOfFurnitureSizeInPlan</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan,
 or <code>null</code> if the view isn't able to compute such a value.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getPieceOfFurnitureSizeInPlan</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomePieceOfFurniture-com.eteks.sweethome3d.model.PieceOfFurniture-">createHomePieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns a new home piece of furniture created from an other given <code>piece</code> of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getHighestSurroundingPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getHighestSurroundingPieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the highest piece of furniture that includes the given <code>piece</code>
 with a margin error of 5% of the smallest side length.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getFurnitureInSameGroup-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getFurnitureInSameGroup</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the furniture list of the given <code>piece</code> which belongs to same group
 or home furniture if it doesn't belong to home furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getSurroundingFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getSurroundingFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the shelf units which include the given <code>piece</code>
 with a margin error of 20% of the smallest side length.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurniture-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">addFurniture</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
            <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;beforePiece)</code>
<div class="block">Controls new furniture added to home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ImportedFurnitureWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">addPieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Controls new piece added to home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#adjustMagnetizedPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-float-float-">adjustMagnetizedPieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                                float&nbsp;x,
                                float&nbsp;y)</code>
<div class="block">Attempts to modify <code>piece</code> location depending of its context.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">createHomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                        <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;leadingPiece)</code>
<div class="block">Returns a new furniture group for the given furniture list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getFurnitureInSameGroup-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getFurnitureInSameGroup</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the furniture list of the given <code>piece</code> which belongs to same group
 or home furniture if it doesn't belong to home furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getHighestSurroundingPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getHighestSurroundingPieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the highest piece of furniture that includes the given <code>piece</code>
 with a margin error of 5% of the smallest side length.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><span class="typeNameLabel">PlanView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getPieceOfFurnitureSizeInPlan</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getSurroundingFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getSurroundingFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the shelf units which include the given <code>piece</code>
 with a margin error of 20% of the smallest side length.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureView.FurnitureFilter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html#include-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomePieceOfFurniture-">include</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
       <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given <code>piece</code> should be shown,
 otherwise returns <code>false</code> if the <code>piece</code> should be hidden.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureDeletable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureDeletable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given <code>piece</code> may be deleted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureMovable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureMovable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given <code>piece</code> may be moved.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurniturePartOfBasePlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurniturePartOfBasePlan</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given <code>piece</code> isn't movable.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureVisibleAtSelectedLevel-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureVisibleAtSelectedLevel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given piece is viewable and
 its height and elevation make it viewable at the selected level in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#moveSelectedFurnitureBefore-com.eteks.sweethome3d.model.HomePieceOfFurniture-">moveSelectedFurnitureBefore</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;beforePiece)</code>
<div class="block">Reorders the selected furniture in home to place it before the given piece.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurniture-java.util.List-">addFurniture</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</code>
<div class="block">Controls new furniture added to home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addFurniture-java.util.List-">addFurniture</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</code>
<div class="block">Adds furniture to home and updates door and window flags if they intersect with walls and magnetism is enabled.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurniture-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">addFurniture</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
            <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;beforePiece)</code>
<div class="block">Controls new furniture added to home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurnitureToGroup-java.util.List-com.eteks.sweethome3d.model.HomeFurnitureGroup-">addFurnitureToGroup</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                   <a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;group)</code>
<div class="block">Controls new furniture added to the given group.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomeFurnitureGroup-java.util.List-">createHomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</code>
<div class="block">Returns a new furniture group for the given furniture list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">createHomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                        <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;leadingPiece)</code>
<div class="block">Returns a new furniture group for the given furniture list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#deleteFurniture-java.util.List-">deleteFurniture</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;deletedFurniture)</code>
<div class="block">Deletes the furniture of <code>deletedFurniture</code> from home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setSelectedFurniture-java.util.List-">setSelectedFurniture</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;selectedFurniture)</code>
<div class="block">Updates the selected furniture in home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setSelectedFurniture-java.util.List-boolean-">setSelectedFurniture</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;selectedFurniture,
                    boolean&nbsp;resetSelection)</code>
<div class="block">Updates the selected furniture in home, unselecting all other kinds of selected objects
 when <code>resetSelection</code> is <code>true</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
