<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:49 CEST 2024 -->
<title>ModelPreviewComponent (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ModelPreviewComponent (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ModelPreviewComponent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/ModelMaterialsComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ModelPreviewComponent.html" target="_top">Frames</a></li>
<li><a href="ModelPreviewComponent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JComponent">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class ModelPreviewComponent" class="title">Class ModelPreviewComponent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>javax.swing.JComponent</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.ModelPreviewComponent</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.awt.image.ImageObserver, java.awt.MenuContainer, java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ModelPreviewComponent</span>
extends javax.swing.JComponent</pre>
<div class="block">Super class of 3D preview component for model.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.ModelPreviewComponent">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JComponent</h3>
<code>javax.swing.JComponent.AccessibleJComponent</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JComponent</h3>
<code>listenerList, TOOL_TIP_TEXT_KEY, ui, UNDEFINED_CONDITION, WHEN_ANCESTOR_OF_FOCUSED_COMPONENT, WHEN_FOCUSED, WHEN_IN_FOCUSED_WINDOW</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>accessibleContext, BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#ModelPreviewComponent--">ModelPreviewComponent</a></span>()</code>
<div class="block">Returns an 3D model preview component that lets the user change its yaw.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#ModelPreviewComponent-boolean-">ModelPreviewComponent</a></span>(boolean&nbsp;pitchAndScaleChangeSupported)</code>
<div class="block">Returns an 3D model preview component that lets the user change its pitch and scale
 if <code>pitchAndScaleChangeSupported</code> is <code>true</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#ModelPreviewComponent-boolean-boolean-boolean-">ModelPreviewComponent</a></span>(boolean&nbsp;yawChangeSupported,
                     boolean&nbsp;pitchChangeSupported,
                     boolean&nbsp;scaleChangeSupported)</code>
<div class="block">Returns an 3D model preview component that lets the user change its yaw, pitch and scale
 according to parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#ModelPreviewComponent-boolean-boolean-boolean-boolean-">ModelPreviewComponent</a></span>(boolean&nbsp;yawChangeSupported,
                     boolean&nbsp;pitchChangeSupported,
                     boolean&nbsp;scaleChangeSupported,
                     boolean&nbsp;transformationsChangeSupported)</code>
<div class="block">Returns an 3D model preview component that lets the user change its yaw, pitch and scale
 according to parameters.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#addMouseListener-java.awt.event.MouseListener-">addMouseListener</a></span>(java.awt.event.MouseListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#addMouseMotionListener-java.awt.event.MouseMotionListener-">addMouseMotionListener</a></span>(java.awt.event.MouseMotionListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getIcon-int-">getIcon</a></span>(int&nbsp;maxWaitingDelay)</code>
<div class="block">Returns a temporary content of the icon matching the displayed view.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model content displayed by this component.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getPickedMaterial--">getPickedMaterial</a></span>()</code>
<div class="block">Returns the material of the shape last picked by the user.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getPreferredSize--">getPreferredSize</a></span>()</code>
<div class="block">Returns component preferred size.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getViewPitch--">getViewPitch</a></span>()</code>
<div class="block">Returns the <code>pitch</code> angle used by view platform transform.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getViewScale--">getViewScale</a></span>()</code>
<div class="block">Returns the zoom factor used by view platform transform.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getViewYaw--">getViewYaw</a></span>()</code>
<div class="block">Returns the <code>yaw</code> angle used by view platform transform.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#isParallelProjection--">isParallelProjection</a></span>()</code>
<div class="block">Returns <code>true</code> if the component 3D uses parallel projection.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setBackFaceShown-boolean-">setBackFaceShown</a></span>(boolean&nbsp;backFaceShown)</code>
<div class="block">Sets the back face visibility of the children nodes of the displayed 3D model.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setBackground-java.awt.Color-">setBackground</a></span>(java.awt.Color&nbsp;backgroundColor)</code>
<div class="block">Sets the background color.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setEdgeColorMaterialHidden-boolean-">setEdgeColorMaterialHidden</a></span>(boolean&nbsp;edgeColorMaterialHidden)</code>
<div class="block">Sets the visibility of edge color materials of the children nodes of the displayed 3D model.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Sets the 3D model content displayed by this component.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setModelColor-java.lang.Integer-">setModelColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the color applied to 3D model.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setModelFlags-int-">setModelFlags</a></span>(int&nbsp;modelFlags)</code>
<div class="block">Sets the model flags of the preview piece.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">setModelMaterials</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;materials)</code>
<div class="block">Sets the materials applied to 3D model.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setModelRotation-float:A:A-">setModelRotation</a></span>(float[][]&nbsp;modelRotation)</code>
<div class="block">Updates the rotation of the 3D model displayed by this component.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setModelRotationAndSize-float:A:A-float-float-float-">setModelRotationAndSize</a></span>(float[][]&nbsp;modelRotation,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height)</code>
<div class="block">Updates the rotation and the size of the 3D model displayed by this component.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">setModelTransformations</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;transformations)</code>
<div class="block">Sets the transformations applied to 3D model.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setParallelProjection-boolean-">setParallelProjection</a></span>(boolean&nbsp;parallelProjection)</code>
<div class="block">Sets whether the component 3D should use parallel or perspective projection.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setViewPitch-float-">setViewPitch</a></span>(float&nbsp;viewPitch)</code>
<div class="block">Sets the <code>pitch</code> angle used by view platform transform.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setViewScale-float-">setViewScale</a></span>(float&nbsp;viewScale)</code>
<div class="block">Sets the zoom factor used by view platform transform.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setViewYaw-float-">setViewYaw</a></span>(float&nbsp;viewYaw)</code>
<div class="block">Sets the <code>yaw</code> angle used by view platform transform.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JComponent</h3>
<code>addAncestorListener, addNotify, addVetoableChangeListener, computeVisibleRect, contains, createToolTip, disable, enable, firePropertyChange, firePropertyChange, firePropertyChange, fireVetoableChange, getActionForKeyStroke, getActionMap, getAlignmentX, getAlignmentY, getAncestorListeners, getAutoscrolls, getBaseline, getBaselineResizeBehavior, getBorder, getBounds, getClientProperty, getComponentGraphics, getComponentPopupMenu, getConditionForKeyStroke, getDebugGraphicsOptions, getDefaultLocale, getFontMetrics, getGraphics, getHeight, getInheritsPopupMenu, getInputMap, getInputMap, getInputVerifier, getInsets, getInsets, getListeners, getLocation, getMaximumSize, getMinimumSize, getNextFocusableComponent, getPopupLocation, getRegisteredKeyStrokes, getRootPane, getSize, getToolTipLocation, getToolTipText, getToolTipText, getTopLevelAncestor, getTransferHandler, getUIClassID, getVerifyInputWhenFocusTarget, getVetoableChangeListeners, getVisibleRect, getWidth, getX, getY, grabFocus, hide, isDoubleBuffered, isLightweightComponent, isManagingFocus, isOpaque, isOptimizedDrawingEnabled, isPaintingForPrint, isPaintingOrigin, isPaintingTile, isRequestFocusEnabled, isValidateRoot, paint, paintBorder, paintChildren, paintComponent, paintImmediately, paintImmediately, paramString, print, printAll, printBorder, printChildren, printComponent, processComponentKeyEvent, processKeyBinding, processKeyEvent, processMouseEvent, processMouseMotionEvent, putClientProperty, registerKeyboardAction, registerKeyboardAction, removeAncestorListener, removeNotify, removeVetoableChangeListener, repaint, repaint, requestDefaultFocus, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, resetKeyboardActions, reshape, revalidate, scrollRectToVisible, setActionMap, setAlignmentX, setAlignmentY, setAutoscrolls, setBorder, setComponentPopupMenu, setDebugGraphicsOptions, setDefaultLocale, setDoubleBuffered, setEnabled, setFocusTraversalKeys, setFont, setForeground, setInheritsPopupMenu, setInputMap, setInputVerifier, setMaximumSize, setMinimumSize, setNextFocusableComponent, setOpaque, setPreferredSize, setRequestFocusEnabled, setToolTipText, setTransferHandler, setUI, setVerifyInputWhenFocusTarget, setVisible, unregisterKeyboardAction, update, updateUI</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addImpl, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, doLayout, findComponentAt, findComponentAt, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getLayout, getMousePosition, insets, invalidate, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paintComponents, preferredSize, printComponents, processContainerEvent, processEvent, remove, remove, removeAll, removeContainerListener, setComponentZOrder, setFocusCycleRoot, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setLayout, transferFocusDownCycle, validate, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addFocusListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, createImage, createImage, createVolatileImage, createVolatileImage, disableEvents, dispatchEvent, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getAccessibleContext, getBackground, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getCursor, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getForeground, getGraphicsConfiguration, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocale, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getToolkit, getTreeLock, gotFocus, handleEvent, hasFocus, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseListener, removeMouseMotionListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, resize, resize, setBounds, setBounds, setComponentOrientation, setCursor, setDropTarget, setFocusable, setFocusTraversalKeysEnabled, setIgnoreRepaint, setLocale, setLocation, setLocation, setName, setSize, setSize, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ModelPreviewComponent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModelPreviewComponent</h4>
<pre>public&nbsp;ModelPreviewComponent()</pre>
<div class="block">Returns an 3D model preview component that lets the user change its yaw.</div>
</li>
</ul>
<a name="ModelPreviewComponent-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModelPreviewComponent</h4>
<pre>public&nbsp;ModelPreviewComponent(boolean&nbsp;pitchAndScaleChangeSupported)</pre>
<div class="block">Returns an 3D model preview component that lets the user change its pitch and scale
 if <code>pitchAndScaleChangeSupported</code> is <code>true</code>.</div>
</li>
</ul>
<a name="ModelPreviewComponent-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ModelPreviewComponent</h4>
<pre>public&nbsp;ModelPreviewComponent(boolean&nbsp;yawChangeSupported,
                             boolean&nbsp;pitchChangeSupported,
                             boolean&nbsp;scaleChangeSupported)</pre>
<div class="block">Returns an 3D model preview component that lets the user change its yaw, pitch and scale
 according to parameters.</div>
</li>
</ul>
<a name="ModelPreviewComponent-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ModelPreviewComponent</h4>
<pre>public&nbsp;ModelPreviewComponent(boolean&nbsp;yawChangeSupported,
                             boolean&nbsp;pitchChangeSupported,
                             boolean&nbsp;scaleChangeSupported,
                             boolean&nbsp;transformationsChangeSupported)</pre>
<div class="block">Returns an 3D model preview component that lets the user change its yaw, pitch and scale
 according to parameters.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPreferredSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreferredSize</h4>
<pre>public&nbsp;java.awt.Dimension&nbsp;getPreferredSize()</pre>
<div class="block">Returns component preferred size.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getPreferredSize</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="addMouseMotionListener-java.awt.event.MouseMotionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMouseMotionListener</h4>
<pre>public&nbsp;void&nbsp;addMouseMotionListener(java.awt.event.MouseMotionListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>addMouseMotionListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="addMouseListener-java.awt.event.MouseListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMouseListener</h4>
<pre>public&nbsp;void&nbsp;addMouseListener(java.awt.event.MouseListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>addMouseListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="getViewYaw--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getViewYaw</h4>
<pre>protected&nbsp;float&nbsp;getViewYaw()</pre>
<div class="block">Returns the <code>yaw</code> angle used by view platform transform.</div>
</li>
</ul>
<a name="setViewYaw-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewYaw</h4>
<pre>protected&nbsp;void&nbsp;setViewYaw(float&nbsp;viewYaw)</pre>
<div class="block">Sets the <code>yaw</code> angle used by view platform transform.</div>
</li>
</ul>
<a name="getViewScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getViewScale</h4>
<pre>protected&nbsp;float&nbsp;getViewScale()</pre>
<div class="block">Returns the zoom factor used by view platform transform.</div>
</li>
</ul>
<a name="setViewScale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewScale</h4>
<pre>protected&nbsp;void&nbsp;setViewScale(float&nbsp;viewScale)</pre>
<div class="block">Sets the zoom factor used by view platform transform.</div>
</li>
</ul>
<a name="getViewPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getViewPitch</h4>
<pre>protected&nbsp;float&nbsp;getViewPitch()</pre>
<div class="block">Returns the <code>pitch</code> angle used by view platform transform.</div>
</li>
</ul>
<a name="setViewPitch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setViewPitch</h4>
<pre>protected&nbsp;void&nbsp;setViewPitch(float&nbsp;viewPitch)</pre>
<div class="block">Sets the <code>pitch</code> angle used by view platform transform.</div>
</li>
</ul>
<a name="setParallelProjection-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setParallelProjection</h4>
<pre>protected&nbsp;void&nbsp;setParallelProjection(boolean&nbsp;parallelProjection)</pre>
<div class="block">Sets whether the component 3D should use parallel or perspective projection.</div>
</li>
</ul>
<a name="isParallelProjection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isParallelProjection</h4>
<pre>protected&nbsp;boolean&nbsp;isParallelProjection()</pre>
<div class="block">Returns <code>true</code> if the component 3D uses parallel projection.</div>
</li>
</ul>
<a name="setBackground-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackground</h4>
<pre>public&nbsp;void&nbsp;setBackground(java.awt.Color&nbsp;backgroundColor)</pre>
<div class="block">Sets the background color.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setBackground</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="getModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getModel()</pre>
<div class="block">Returns the 3D model content displayed by this component.</div>
</li>
</ul>
<a name="setModel-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModel</h4>
<pre>public&nbsp;void&nbsp;setModel(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</pre>
<div class="block">Sets the 3D model content displayed by this component.
 The model is shown at its default orientation and in a box 1 unit wide.</div>
</li>
</ul>
<a name="setBackFaceShown-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackFaceShown</h4>
<pre>protected&nbsp;void&nbsp;setBackFaceShown(boolean&nbsp;backFaceShown)</pre>
<div class="block">Sets the back face visibility of the children nodes of the displayed 3D model.</div>
</li>
</ul>
<a name="setEdgeColorMaterialHidden-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeColorMaterialHidden</h4>
<pre>protected&nbsp;void&nbsp;setEdgeColorMaterialHidden(boolean&nbsp;edgeColorMaterialHidden)</pre>
<div class="block">Sets the visibility of edge color materials of the children nodes of the displayed 3D model.</div>
</li>
</ul>
<a name="setModelFlags-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelFlags</h4>
<pre>public&nbsp;void&nbsp;setModelFlags(int&nbsp;modelFlags)</pre>
<div class="block">Sets the model flags of the preview piece.</div>
</li>
</ul>
<a name="setModelRotation-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelRotation</h4>
<pre>protected&nbsp;void&nbsp;setModelRotation(float[][]&nbsp;modelRotation)</pre>
<div class="block">Updates the rotation of the 3D model displayed by this component.
 The model is shown at its default size.</div>
</li>
</ul>
<a name="setModelRotationAndSize-float:A:A-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelRotationAndSize</h4>
<pre>protected&nbsp;void&nbsp;setModelRotationAndSize(float[][]&nbsp;modelRotation,
                                       float&nbsp;width,
                                       float&nbsp;depth,
                                       float&nbsp;height)</pre>
<div class="block">Updates the rotation and the size of the 3D model displayed by this component.</div>
</li>
</ul>
<a name="setModelColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelColor</h4>
<pre>protected&nbsp;void&nbsp;setModelColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the color applied to 3D model.</div>
</li>
</ul>
<a name="setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelMaterials</h4>
<pre>public&nbsp;void&nbsp;setModelMaterials(<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;materials)</pre>
<div class="block">Sets the materials applied to 3D model.</div>
</li>
</ul>
<a name="setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelTransformations</h4>
<pre>public&nbsp;void&nbsp;setModelTransformations(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;transformations)</pre>
<div class="block">Sets the transformations applied to 3D model.</div>
</li>
</ul>
<a name="getPickedMaterial--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPickedMaterial</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>&nbsp;getPickedMaterial()</pre>
<div class="block">Returns the material of the shape last picked by the user.</div>
</li>
</ul>
<a name="getIcon-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getIcon(int&nbsp;maxWaitingDelay)
                throws java.io.IOException</pre>
<div class="block">Returns a temporary content of the icon matching the displayed view.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ModelPreviewComponent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/ModelMaterialsComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ModelPreviewComponent.html" target="_top">Frames</a></li>
<li><a href="ModelPreviewComponent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JComponent">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
