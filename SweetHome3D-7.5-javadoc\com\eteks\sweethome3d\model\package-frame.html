<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>com.eteks.sweethome3d.model (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../../com/eteks/sweethome3d/model/package-summary.html" target="classFrame">com.eteks.sweethome3d.model</a></h1>
<div class="indexContainer">
<h2 title="Interfaces">Interfaces</h2>
<ul title="Interfaces">
<li><a href="CatalogItem.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">CatalogItem</span></a></li>
<li><a href="CollectionListener.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">CollectionListener</span></a></li>
<li><a href="Content.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">Content</span></a></li>
<li><a href="DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">DoorOrWindow</span></a></li>
<li><a href="Elevatable.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">Elevatable</span></a></li>
<li><a href="HomeRecorder.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">HomeRecorder</span></a></li>
<li><a href="Library.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">Library</span></a></li>
<li><a href="Light.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">Light</span></a></li>
<li><a href="PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">PieceOfFurniture</span></a></li>
<li><a href="Selectable.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">Selectable</span></a></li>
<li><a href="SelectionListener.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">SelectionListener</span></a></li>
<li><a href="ShelfUnit.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">ShelfUnit</span></a></li>
<li><a href="TextureImage.html" title="interface in com.eteks.sweethome3d.model" target="classFrame"><span class="interfaceName">TextureImage</span></a></li>
</ul>
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="BackgroundImage.html" title="class in com.eteks.sweethome3d.model" target="classFrame">BackgroundImage</a></li>
<li><a href="Baseboard.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Baseboard</a></li>
<li><a href="BoxBounds.html" title="class in com.eteks.sweethome3d.model" target="classFrame">BoxBounds</a></li>
<li><a href="Camera.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Camera</a></li>
<li><a href="CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model" target="classFrame">CatalogDoorOrWindow</a></li>
<li><a href="CatalogLight.html" title="class in com.eteks.sweethome3d.model" target="classFrame">CatalogLight</a></li>
<li><a href="CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model" target="classFrame">CatalogPieceOfFurniture</a></li>
<li><a href="CatalogShelfUnit.html" title="class in com.eteks.sweethome3d.model" target="classFrame">CatalogShelfUnit</a></li>
<li><a href="CatalogTexture.html" title="class in com.eteks.sweethome3d.model" target="classFrame">CatalogTexture</a></li>
<li><a href="CollectionChangeSupport.html" title="class in com.eteks.sweethome3d.model" target="classFrame">CollectionChangeSupport</a></li>
<li><a href="CollectionEvent.html" title="class in com.eteks.sweethome3d.model" target="classFrame">CollectionEvent</a></li>
<li><a href="Compass.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Compass</a></li>
<li><a href="DimensionLine.html" title="class in com.eteks.sweethome3d.model" target="classFrame">DimensionLine</a></li>
<li><a href="FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model" target="classFrame">FurnitureCatalog</a></li>
<li><a href="FurnitureCategory.html" title="class in com.eteks.sweethome3d.model" target="classFrame">FurnitureCategory</a></li>
<li><a href="Home.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Home</a></li>
<li><a href="HomeApplication.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeApplication</a></li>
<li><a href="HomeDescriptor.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeDescriptor</a></li>
<li><a href="HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeDoorOrWindow</a></li>
<li><a href="HomeEnvironment.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeEnvironment</a></li>
<li><a href="HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeFurnitureGroup</a></li>
<li><a href="HomeLight.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeLight</a></li>
<li><a href="HomeMaterial.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeMaterial</a></li>
<li><a href="HomeObject.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeObject</a></li>
<li><a href="HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomePieceOfFurniture</a></li>
<li><a href="HomePrint.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomePrint</a></li>
<li><a href="HomeShelfUnit.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeShelfUnit</a></li>
<li><a href="HomeTexture.html" title="class in com.eteks.sweethome3d.model" target="classFrame">HomeTexture</a></li>
<li><a href="Label.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Label</a></li>
<li><a href="Level.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Level</a></li>
<li><a href="LightSource.html" title="class in com.eteks.sweethome3d.model" target="classFrame">LightSource</a></li>
<li><a href="ObjectProperty.html" title="class in com.eteks.sweethome3d.model" target="classFrame">ObjectProperty</a></li>
<li><a href="ObserverCamera.html" title="class in com.eteks.sweethome3d.model" target="classFrame">ObserverCamera</a></li>
<li><a href="PatternsCatalog.html" title="class in com.eteks.sweethome3d.model" target="classFrame">PatternsCatalog</a></li>
<li><a href="Polyline.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Polyline</a></li>
<li><a href="Room.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Room</a></li>
<li><a href="Sash.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Sash</a></li>
<li><a href="SelectionEvent.html" title="class in com.eteks.sweethome3d.model" target="classFrame">SelectionEvent</a></li>
<li><a href="TextStyle.html" title="class in com.eteks.sweethome3d.model" target="classFrame">TextStyle</a></li>
<li><a href="TexturesCatalog.html" title="class in com.eteks.sweethome3d.model" target="classFrame">TexturesCatalog</a></li>
<li><a href="TexturesCategory.html" title="class in com.eteks.sweethome3d.model" target="classFrame">TexturesCategory</a></li>
<li><a href="Transformation.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Transformation</a></li>
<li><a href="UserPreferences.html" title="class in com.eteks.sweethome3d.model" target="classFrame">UserPreferences</a></li>
<li><a href="Wall.html" title="class in com.eteks.sweethome3d.model" target="classFrame">Wall</a></li>
</ul>
<h2 title="Enums">Enums</h2>
<ul title="Enums">
<li><a href="AspectRatio.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">AspectRatio</a></li>
<li><a href="Camera.Lens.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Camera.Lens</a></li>
<li><a href="Camera.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Camera.Property</a></li>
<li><a href="CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">CollectionEvent.Type</a></li>
<li><a href="Compass.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Compass.Property</a></li>
<li><a href="DimensionLine.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">DimensionLine.Property</a></li>
<li><a href="Home.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Home.Property</a></li>
<li><a href="HomeDoorOrWindow.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomeDoorOrWindow.Property</a></li>
<li><a href="HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomeEnvironment.DrawingMode</a></li>
<li><a href="HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomeEnvironment.Property</a></li>
<li><a href="HomeLight.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomeLight.Property</a></li>
<li><a href="HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomePieceOfFurniture.Property</a></li>
<li><a href="HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomePieceOfFurniture.SortableProperty</a></li>
<li><a href="HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomePrint.PaperOrientation</a></li>
<li><a href="HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomeRecorder.Type</a></li>
<li><a href="HomeShelfUnit.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">HomeShelfUnit.Property</a></li>
<li><a href="Label.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Label.Property</a></li>
<li><a href="LengthUnit.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">LengthUnit</a></li>
<li><a href="Level.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Level.Property</a></li>
<li><a href="ObjectProperty.Type.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">ObjectProperty.Type</a></li>
<li><a href="ObserverCamera.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">ObserverCamera.Property</a></li>
<li><a href="Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Polyline.ArrowStyle</a></li>
<li><a href="Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Polyline.CapStyle</a></li>
<li><a href="Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Polyline.DashStyle</a></li>
<li><a href="Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Polyline.JoinStyle</a></li>
<li><a href="Polyline.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Polyline.Property</a></li>
<li><a href="Room.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Room.Property</a></li>
<li><a href="TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">TextStyle.Alignment</a></li>
<li><a href="UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">UserPreferences.Property</a></li>
<li><a href="Wall.Property.html" title="enum in com.eteks.sweethome3d.model" target="classFrame">Wall.Property</a></li>
</ul>
<h2 title="Exceptions">Exceptions</h2>
<ul title="Exceptions">
<li><a href="DamagedHomeRecorderException.html" title="class in com.eteks.sweethome3d.model" target="classFrame">DamagedHomeRecorderException</a></li>
<li><a href="IllegalHomonymException.html" title="class in com.eteks.sweethome3d.model" target="classFrame">IllegalHomonymException</a></li>
<li><a href="InterruptedRecorderException.html" title="class in com.eteks.sweethome3d.model" target="classFrame">InterruptedRecorderException</a></li>
<li><a href="NotEnoughSpaceRecorderException.html" title="class in com.eteks.sweethome3d.model" target="classFrame">NotEnoughSpaceRecorderException</a></li>
<li><a href="RecorderException.html" title="class in com.eteks.sweethome3d.model" target="classFrame">RecorderException</a></li>
</ul>
</div>
</body>
</html>
