<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Home3DAttributesController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Home3DAttributesController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Home3DAttributesController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" target="_top">Frames</a></li>
<li><a href="Home3DAttributesController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class Home3DAttributesController" class="title">Class Home3DAttributesController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.Home3DAttributesController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Home3DAttributesController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for home 3D attributes view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getGroundPaint--">ground paint type</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#Home3DAttributesController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">Home3DAttributesController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                          <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                          <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                          javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of 3D view with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getGroundColor--">getGroundColor</a></span>()</code>
<div class="block">Returns the edited ground color.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getGroundPaint--">getGroundPaint</a></span>()</code>
<div class="block">Returns whether the ground is colored or textured.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getGroundTextureController--">getGroundTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the ground.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getLightColor--">getLightColor</a></span>()</code>
<div class="block">Returns the edited light color.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getSkyColor--">getSkyColor</a></span>()</code>
<div class="block">Returns the edited sky color.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getSkyPaint--">getSkyPaint</a></span>()</code>
<div class="block">Returns whether the sky is colored or textured.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getSkyTextureController--">getSkyTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the sky.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getWallsAlpha--">getWallsAlpha</a></span>()</code>
<div class="block">Returns the edited walls transparency alpha.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#isBackgroundImageVisibleOnGround3D--">isBackgroundImageVisibleOnGround3D</a></span>()</code>
<div class="block">Returns <code>true</code> if the background image should be displayed on the ground in 3D.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#modify3DAttributes--">modify3DAttributes</a></span>()</code>
<div class="block">Controls the modification of the 3D attributes of the edited home.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#setBackgroundImageVisibleOnGround3D-boolean-">setBackgroundImageVisibleOnGround3D</a></span>(boolean&nbsp;backgroundImageVisibleOnGround3D)</code>
<div class="block">Sets whether the background image should be displayed on the ground in 3D.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#setGroundColor-int-">setGroundColor</a></span>(int&nbsp;groundColor)</code>
<div class="block">Sets the edited ground color.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#setGroundPaint-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController.EnvironmentPaint-">setGroundPaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a>&nbsp;groundPaint)</code>
<div class="block">Sets whether the ground is colored or textured.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#setLightColor-int-">setLightColor</a></span>(int&nbsp;lightColor)</code>
<div class="block">Sets the edited light color.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#setSkyColor-int-">setSkyColor</a></span>(int&nbsp;skyColor)</code>
<div class="block">Sets the edited sky color.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#setSkyPaint-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController.EnvironmentPaint-">setSkyPaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a>&nbsp;skyPaint)</code>
<div class="block">Sets whether the sky is colored or textured.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#setWallsAlpha-float-">setWallsAlpha</a></span>(float&nbsp;wallsAlpha)</code>
<div class="block">Sets the edited walls transparency alpha.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates edited properties from the 3D attributes of the home edited by this controller.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Home3DAttributesController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Home3DAttributesController</h4>
<pre>public&nbsp;Home3DAttributesController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                  <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                  <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                  <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                                  javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of 3D view with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getGroundTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroundTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getGroundTextureController()</pre>
<div class="block">Returns the texture controller of the ground.</div>
</li>
</ul>
<a name="getSkyTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSkyTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getSkyTextureController()</pre>
<div class="block">Returns the texture controller of the sky.</div>
</li>
</ul>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates edited properties from the 3D attributes of the home edited by this controller.</div>
</li>
</ul>
<a name="setGroundColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroundColor</h4>
<pre>public&nbsp;void&nbsp;setGroundColor(int&nbsp;groundColor)</pre>
<div class="block">Sets the edited ground color.</div>
</li>
</ul>
<a name="getGroundColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroundColor</h4>
<pre>public&nbsp;int&nbsp;getGroundColor()</pre>
<div class="block">Returns the edited ground color.</div>
</li>
</ul>
<a name="setGroundPaint-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController.EnvironmentPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroundPaint</h4>
<pre>public&nbsp;void&nbsp;setGroundPaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a>&nbsp;groundPaint)</pre>
<div class="block">Sets whether the ground is colored or textured.</div>
</li>
</ul>
<a name="getGroundPaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroundPaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a>&nbsp;getGroundPaint()</pre>
<div class="block">Returns whether the ground is colored or textured.</div>
</li>
</ul>
<a name="isBackgroundImageVisibleOnGround3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackgroundImageVisibleOnGround3D</h4>
<pre>public&nbsp;boolean&nbsp;isBackgroundImageVisibleOnGround3D()</pre>
<div class="block">Returns <code>true</code> if the background image should be displayed on the ground in 3D.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setBackgroundImageVisibleOnGround3D-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackgroundImageVisibleOnGround3D</h4>
<pre>public&nbsp;void&nbsp;setBackgroundImageVisibleOnGround3D(boolean&nbsp;backgroundImageVisibleOnGround3D)</pre>
<div class="block">Sets whether the background image should be displayed on the ground in 3D.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setSkyColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSkyColor</h4>
<pre>public&nbsp;void&nbsp;setSkyColor(int&nbsp;skyColor)</pre>
<div class="block">Sets the edited sky color.</div>
</li>
</ul>
<a name="getSkyColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSkyColor</h4>
<pre>public&nbsp;int&nbsp;getSkyColor()</pre>
<div class="block">Returns the edited sky color.</div>
</li>
</ul>
<a name="setSkyPaint-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController.EnvironmentPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSkyPaint</h4>
<pre>public&nbsp;void&nbsp;setSkyPaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a>&nbsp;skyPaint)</pre>
<div class="block">Sets whether the sky is colored or textured.</div>
</li>
</ul>
<a name="getSkyPaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSkyPaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a>&nbsp;getSkyPaint()</pre>
<div class="block">Returns whether the sky is colored or textured.</div>
</li>
</ul>
<a name="setLightColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLightColor</h4>
<pre>public&nbsp;void&nbsp;setLightColor(int&nbsp;lightColor)</pre>
<div class="block">Sets the edited light color.</div>
</li>
</ul>
<a name="getLightColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightColor</h4>
<pre>public&nbsp;int&nbsp;getLightColor()</pre>
<div class="block">Returns the edited light color.</div>
</li>
</ul>
<a name="setWallsAlpha-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallsAlpha</h4>
<pre>public&nbsp;void&nbsp;setWallsAlpha(float&nbsp;wallsAlpha)</pre>
<div class="block">Sets the edited walls transparency alpha.</div>
</li>
</ul>
<a name="getWallsAlpha--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallsAlpha</h4>
<pre>public&nbsp;float&nbsp;getWallsAlpha()</pre>
<div class="block">Returns the edited walls transparency alpha.</div>
</li>
</ul>
<a name="modify3DAttributes--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>modify3DAttributes</h4>
<pre>public&nbsp;void&nbsp;modify3DAttributes()</pre>
<div class="block">Controls the modification of the 3D attributes of the edited home.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Home3DAttributesController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" target="_top">Frames</a></li>
<li><a href="Home3DAttributesController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
