<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>DoorOrWindow (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DoorOrWindow (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DoorOrWindow.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/DoorOrWindow.html" target="_top">Frames</a></li>
<li><a href="DoorOrWindow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Interface DoorOrWindow" class="title">Interface DoorOrWindow</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">CatalogDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">HomeDoorOrWindow</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">DoorOrWindow</span>
extends <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></pre>
<div class="block">A piece of furniture used as a door or a window.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getCutOutShape--">getCutOutShape</a></span>()</code>
<div class="block">Returns the shape used to cut out walls that intersect this new door or window.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getSashes--">getSashes</a></span>()</code>
<div class="block">Returns a copy of the sashes attached to this door or window.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getWallDistance--">getWallDistance</a></span>()</code>
<div class="block">Returns the default distance that should lie outside of this door or window.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getWallThickness--">getWallThickness</a></span>()</code>
<div class="block">Returns the default thickness of the wall in which this door or window should be placed.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#isWallCutOutOnBothSides--">isWallCutOutOnBothSides</a></span>()</code>
<div class="block">Returns <code>true</code> if this door or window should cut out the both sides
 of the walls it intersects, even if its front or back side are within the wall thickness.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></span>()</code>
<div class="block">Returns <code>false</code> if the width and depth of the new door or window may 
 not be changed independently from each other.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getWallThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallThickness</h4>
<pre>float&nbsp;getWallThickness()</pre>
<div class="block">Returns the default thickness of the wall in which this door or window should be placed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value in percentage of the depth of this door or window.</dd>
</dl>
</li>
</ul>
<a name="getWallDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallDistance</h4>
<pre>float&nbsp;getWallDistance()</pre>
<div class="block">Returns the default distance that should lie outside of this door or window.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a distance in percentage of the depth of this door or the window.</dd>
</dl>
</li>
</ul>
<a name="getSashes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSashes</h4>
<pre><a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;getSashes()</pre>
<div class="block">Returns a copy of the sashes attached to this door or window.
 If no sash is defined an empty array is returned.</div>
</li>
</ul>
<a name="getCutOutShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCutOutShape</h4>
<pre>java.lang.String&nbsp;getCutOutShape()</pre>
<div class="block">Returns the shape used to cut out walls that intersect this new door or window.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>null</code> or a shape defined with the syntax of the d attribute of a 
 <a href="http://www.w3.org/TR/SVG/paths.html">SVG path element</a>
 that fits in a square spreading from (0, 0) to (1, 1) which will be 
 scaled afterwards to the real size of this door or window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="isWallCutOutOnBothSides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWallCutOutOnBothSides</h4>
<pre>boolean&nbsp;isWallCutOutOnBothSides()</pre>
<div class="block">Returns <code>true</code> if this door or window should cut out the both sides
 of the walls it intersects, even if its front or back side are within the wall thickness.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isWidthDepthDeformable--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isWidthDepthDeformable</h4>
<pre>boolean&nbsp;isWidthDepthDeformable()</pre>
<div class="block">Returns <code>false</code> if the width and depth of the new door or window may 
 not be changed independently from each other. When <code>false</code>, this door or window
 will also make a hole in the wall when it's placed whatever its depth if its 
 <code>bouldToWall</code> flag is <code>true</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DoorOrWindow.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/DoorOrWindow.html" target="_top">Frames</a></li>
<li><a href="DoorOrWindow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
