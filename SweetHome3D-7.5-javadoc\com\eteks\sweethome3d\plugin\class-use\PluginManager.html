<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.plugin.PluginManager (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.plugin.PluginManager (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/plugin/class-use/PluginManager.html" target="_top">Frames</a></li>
<li><a href="PluginManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.plugin.PluginManager" class="title">Uses of Class<br>com.eteks.sweethome3d.plugin.PluginManager</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d">com.eteks.sweethome3d</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as a stand-alone application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.plugin">com.eteks.sweethome3d.plugin</a></td>
<td class="colLast">
<div class="block">Describes the super classes required to create Sweet Home 3D plug-ins.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a> in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a> that return <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a></code></td>
<td class="colLast"><span class="typeNameLabel">SweetHome3D.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/SweetHome3D.html#getPluginManager--">getPluginManager</a></span>()</code>
<div class="block">Returns the plugin manager of this application.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/HomeFrameController.html#HomeFrameController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-">HomeFrameController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                   <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a> in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> that return <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a></code></td>
<td class="colLast"><span class="typeNameLabel">AppletApplication.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletApplication.html#getPluginManager--">getPluginManager</a></span>()</code>
<div class="block">Returns the plugin manager of this application.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#HomeAppletController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-boolean-boolean-boolean-boolean-">HomeAppletController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                    <a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                    <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager,
                    boolean&nbsp;newHomeEnabled,
                    boolean&nbsp;openEnabled,
                    boolean&nbsp;saveEnabled,
                    boolean&nbsp;saveAsEnabled)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#HomeAppletController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-boolean-boolean-boolean-boolean-long-">HomeAppletController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                    <a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                    <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager,
                    boolean&nbsp;newHomeEnabled,
                    boolean&nbsp;openEnabled,
                    boolean&nbsp;saveEnabled,
                    boolean&nbsp;saveAsEnabled,
                    long&nbsp;homeMaximumLength)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.plugin">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a> in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/plugin/HomePluginController.html#HomePluginController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-">HomePluginController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                    <a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                    <a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager)</code>
<div class="block">Creates the controller of home view.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/plugin/class-use/PluginManager.html" target="_top">Frames</a></li>
<li><a href="PluginManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
