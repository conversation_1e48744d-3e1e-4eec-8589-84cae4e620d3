<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>com.eteks.sweethome3d.model (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.eteks.sweethome3d.model (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.eteks.sweethome3d.model</h1>
<div class="docSummary">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a></td>
<td class="colLast">
<div class="block">An item of a catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">A listener notified when items are added or removed from a collection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></td>
<td class="colLast">
<div class="block">Content for files, images...</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></td>
<td class="colLast">
<div class="block">A piece of furniture used as a door or a window.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></td>
<td class="colLast">
<div class="block">An object that belongs to a level.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></td>
<td class="colLast">
<div class="block">Homes recorder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></td>
<td class="colLast">
<div class="block">A library able to provide additional capabilities to Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a></td>
<td class="colLast">
<div class="block">A piece of furniture that contains one or more light sources.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></td>
<td class="colLast">
<div class="block">A piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></td>
<td class="colLast">
<div class="block">An object that is selectable in home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/SelectionListener.html" title="interface in com.eteks.sweethome3d.model">SelectionListener</a></td>
<td class="colLast">
<div class="block">Listener implemented to receive notifications of selection changes in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model"><code>Home</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html" title="interface in com.eteks.sweethome3d.model">ShelfUnit</a></td>
<td class="colLast">
<div class="block">A piece of furniture whith shelves.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></td>
<td class="colLast">
<div class="block">An image used as texture.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a></td>
<td class="colLast">
<div class="block">The image displayed in background of the plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a></td>
<td class="colLast">
<div class="block">A baseboard associated to wall.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/BoxBounds.html" title="class in com.eteks.sweethome3d.model">BoxBounds</a></td>
<td class="colLast">
<div class="block">Bounds of a box.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a></td>
<td class="colLast">
<div class="block">Camera characteristics in home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">CatalogDoorOrWindow</a></td>
<td class="colLast">
<div class="block">A door or a window of the catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CatalogLight.html" title="class in com.eteks.sweethome3d.model">CatalogLight</a></td>
<td class="colLast">
<div class="block">A light of the catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a></td>
<td class="colLast">
<div class="block">A catalog piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html" title="class in com.eteks.sweethome3d.model">CatalogShelfUnit</a></td>
<td class="colLast">
<div class="block">A catalog piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a></td>
<td class="colLast">
<div class="block">A texture in textures catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="class in com.eteks.sweethome3d.model">CollectionChangeSupport</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">A helper class for <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model"><code>CollectionListener</code></a> management.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="class in com.eteks.sweethome3d.model">CollectionEvent</a>&lt;T&gt;</td>
<td class="colLast">
<div class="block">Type of event notified when an item is added or deleted from a list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a></td>
<td class="colLast">
<div class="block">A compass used to locate where a home is located and how it's oriented towards North.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a></td>
<td class="colLast">
<div class="block">A dimension line in plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a></td>
<td class="colLast">
<div class="block">Furniture catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a></td>
<td class="colLast">
<div class="block">Category of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></td>
<td class="colLast">
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a></td>
<td class="colLast">
<div class="block">Application managing a list of homes displayed at screen.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model">HomeDescriptor</a></td>
<td class="colLast">
<div class="block">A descriptor that gives access to some data of a home not yet created.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">HomeDoorOrWindow</a></td>
<td class="colLast">
<div class="block">A door or a window in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model">HomeEnvironment</a></td>
<td class="colLast">
<div class="block">The environment attributes of a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></td>
<td class="colLast">
<div class="block">A group of furniture of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model">HomeLight</a></td>
<td class="colLast">
<div class="block">A light in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a></td>
<td class="colLast">
<div class="block">The color and other properties of a material.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></td>
<td class="colLast">
<div class="block">An object with and ID and data where users can stored their own properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></td>
<td class="colLast">
<div class="block">A piece of furniture in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a></td>
<td class="colLast">
<div class="block">The print attributes for a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.html" title="class in com.eteks.sweethome3d.model">HomeShelfUnit</a></td>
<td class="colLast">
<div class="block">A shelf unit in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></td>
<td class="colLast">
<div class="block">An image used as texture on home 3D objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a></td>
<td class="colLast">
<div class="block">A free label.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></td>
<td class="colLast">
<div class="block">A level in a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a></td>
<td class="colLast">
<div class="block">A light source of a <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">light</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a></td>
<td class="colLast">
<div class="block">Information about additional property.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/ObserverCamera.html" title="class in com.eteks.sweethome3d.model">ObserverCamera</a></td>
<td class="colLast">
<div class="block">Observer camera characteristics in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/PatternsCatalog.html" title="class in com.eteks.sweethome3d.model">PatternsCatalog</a></td>
<td class="colLast">
<div class="block">A catalog of texture images used as patterns to fill plan areas.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a></td>
<td class="colLast">
<div class="block">A polyline or a polygon in a home plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a></td>
<td class="colLast">
<div class="block">A room or a polygon in a home plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a></td>
<td class="colLast">
<div class="block">A sash (moving part) of a door or a window.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/SelectionEvent.html" title="class in com.eteks.sweethome3d.model">SelectionEvent</a></td>
<td class="colLast">
<div class="block">Type of event notified when selection changes in home or furniture catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></td>
<td class="colLast">
<div class="block">The different attributes that define a text style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a></td>
<td class="colLast">
<div class="block">Textures catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a></td>
<td class="colLast">
<div class="block">Category of textures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a></td>
<td class="colLast">
<div class="block">The transformation applied to some model parts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></td>
<td class="colLast">
<div class="block">User preferences.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a></td>
<td class="colLast">
<div class="block">A wall of a home plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a></td>
<td class="colLast">
<div class="block">The aspect ratio of pictures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a></td>
<td class="colLast">
<div class="block">The kind of lens that can be used with a camera.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Camera.Property.html" title="enum in com.eteks.sweethome3d.model">Camera.Property</a></td>
<td class="colLast">
<div class="block">The properties of a camera that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a></td>
<td class="colLast">
<div class="block">The type of change in the collection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Compass.Property.html" title="enum in com.eteks.sweethome3d.model">Compass.Property</a></td>
<td class="colLast">
<div class="block">The properties of a compass that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.Property.html" title="enum in com.eteks.sweethome3d.model">DimensionLine.Property</a></td>
<td class="colLast">
<div class="block">The properties of a dimension line that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model">Home.Property</a></td>
<td class="colLast">
<div class="block">The properties of a home that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.Property.html" title="enum in com.eteks.sweethome3d.model">HomeDoorOrWindow.Property</a></td>
<td class="colLast">
<div class="block">The properties of a door or window that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.DrawingMode</a></td>
<td class="colLast">
<div class="block">The various modes used to draw home in 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.Property</a></td>
<td class="colLast">
<div class="block">The environment properties that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeLight.Property.html" title="enum in com.eteks.sweethome3d.model">HomeLight.Property</a></td>
<td class="colLast">
<div class="block">The properties of a light that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a></td>
<td class="colLast">
<div class="block">The properties of a piece of furniture that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></td>
<td class="colLast">
<div class="block">The properties on which home furniture may be sorted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a></td>
<td class="colLast">
<div class="block">Paper orientation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a></td>
<td class="colLast">
<div class="block">Recorder type used as a hint to select a home recorder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.Property.html" title="enum in com.eteks.sweethome3d.model">HomeShelfUnit.Property</a></td>
<td class="colLast">
<div class="block">The properties of a shelf unit that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Label.Property.html" title="enum in com.eteks.sweethome3d.model">Label.Property</a></td>
<td class="colLast">
<div class="block">The properties of a label that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a></td>
<td class="colLast">
<div class="block">Unit used for lengths.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Level.Property.html" title="enum in com.eteks.sweethome3d.model">Level.Property</a></td>
<td class="colLast">
<div class="block">The properties of a level that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.Type.html" title="enum in com.eteks.sweethome3d.model">ObjectProperty.Type</a></td>
<td class="colLast">
<div class="block">Property type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/ObserverCamera.Property.html" title="enum in com.eteks.sweethome3d.model">ObserverCamera.Property</a></td>
<td class="colLast">
<div class="block">The additional properties of an observer camera that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Polyline.Property.html" title="enum in com.eteks.sweethome3d.model">Polyline.Property</a></td>
<td class="colLast">
<div class="block">The properties of a polyline that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Room.Property.html" title="enum in com.eteks.sweethome3d.model">Room.Property</a></td>
<td class="colLast">
<div class="block">The properties of a room that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a></td>
<td class="colLast">
<div class="block">The properties of user preferences that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/Wall.Property.html" title="enum in com.eteks.sweethome3d.model">Wall.Property</a></td>
<td class="colLast">
<div class="block">The properties of a wall that may change.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/DamagedHomeRecorderException.html" title="class in com.eteks.sweethome3d.model">DamagedHomeRecorderException</a></td>
<td class="colLast">
<div class="block">Exception thrown when a home data read in IO layer is damaged with with possible invalid content in dependencies.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/IllegalHomonymException.html" title="class in com.eteks.sweethome3d.model">IllegalHomonymException</a></td>
<td class="colLast">
<div class="block">Exception thrown when a method didn't accept an homonym.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/InterruptedRecorderException.html" title="class in com.eteks.sweethome3d.model">InterruptedRecorderException</a></td>
<td class="colLast">
<div class="block">Exception thrown when a thread is interrupted during an access to data in IO layer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/NotEnoughSpaceRecorderException.html" title="class in com.eteks.sweethome3d.model">NotEnoughSpaceRecorderException</a></td>
<td class="colLast">
<div class="block">Exception thrown when there's not enough space to save a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></td>
<td class="colLast">
<div class="block">Exception thrown by methods that access to data in IO layer.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package com.eteks.sweethome3d.model Description">Package com.eteks.sweethome3d.model Description</h2>
<div class="block">Describes model classes of Sweet Home 3D. This is the core package of Sweet Home 3D 
upon which all the other packages are dependent.
<p>The <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model"><code>HomeApplication</code></a> abstract class is the 
entry point of this package, and gives access to 
<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a>, to the list of created 
<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a>, with their 
<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">walls</a>, 
<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">furniture</a>,... and how to 
<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">read and write homes</a>.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
