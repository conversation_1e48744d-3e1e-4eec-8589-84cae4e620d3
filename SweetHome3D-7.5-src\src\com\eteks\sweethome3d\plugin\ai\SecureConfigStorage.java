/*
 * SecureConfigStorage.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.Arrays;
import java.util.Base64;
import java.util.prefs.Preferences;

/**
 * Provides secure storage for sensitive configuration data like API keys.
 * Uses basic obfuscation for protection while maintaining compatibility.
 * 
 * <AUTHOR>
 */
public class SecureConfigStorage {
  private static final String API_KEY_PREF = "aiApiKeyObfuscated";
  private static final byte[] OBFUSCATION_KEY = {0x53, 0x48, 0x33, 0x44, 0x41, 0x49}; // "SH3DAI"
  
  private final Preferences preferences;
  
  /**
   * Creates a new secure config storage instance.
   */
  public SecureConfigStorage() {
    this.preferences = Preferences.userNodeForPackage(SecureConfigStorage.class);
  }
  
  /**
   * Stores an API key using basic obfuscation.
   */
  public void storeApiKey(String apiKey) {
    if (apiKey == null || apiKey.isEmpty()) {
      clearApiKey();
      return;
    }
    
    try {
      // Simple obfuscation (not encryption) for basic protection
      byte[] obfuscated = obfuscate(apiKey.getBytes("UTF-8"));
      String encoded = Base64.getEncoder().encodeToString(obfuscated);
      preferences.put(API_KEY_PREF, encoded);
    } catch (Exception e) {
      // Fallback to plain storage if obfuscation fails
      preferences.put(API_KEY_PREF, apiKey);
    }
  }
  
  /**
   * Retrieves the stored API key.
   */
  public String retrieveApiKey() {
    String stored = preferences.get(API_KEY_PREF, "");
    if (stored.isEmpty()) {
      return "";
    }
    
    try {
      // Try to decode as obfuscated data
      byte[] decoded = Base64.getDecoder().decode(stored);
      byte[] deobfuscated = deobfuscate(decoded);
      return new String(deobfuscated, "UTF-8");
    } catch (Exception e) {
      // Fallback: assume it's plain text (for backward compatibility)
      return stored;
    }
  }
  
  /**
   * Clears the stored API key.
   */
  public void clearApiKey() {
    preferences.remove(API_KEY_PREF);
  }
  
  /**
   * Simple obfuscation using XOR with a key.
   */
  private byte[] obfuscate(byte[] data) {
    byte[] result = new byte[data.length];
    for (int i = 0; i < data.length; i++) {
      result[i] = (byte) (data[i] ^ OBFUSCATION_KEY[i % OBFUSCATION_KEY.length]);
    }
    return result;
  }
  
  /**
   * Deobfuscation (same as obfuscation for XOR).
   */
  private byte[] deobfuscate(byte[] data) {
    return obfuscate(data); // XOR is its own inverse
  }
}
