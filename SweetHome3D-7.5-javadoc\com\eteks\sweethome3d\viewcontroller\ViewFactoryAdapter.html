<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>ViewFactoryAdapter (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ViewFactoryAdapter (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ViewFactoryAdapter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html" target="_top">Frames</a></li>
<li><a href="ViewFactoryAdapter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class ViewFactoryAdapter" class="title">Class ViewFactoryAdapter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.ViewFactoryAdapter</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ViewFactoryAdapter</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></pre>
<div class="block">A view factory with all its methods throwing
 <code>UnsupportedOperationException</code> exception.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#ViewFactoryAdapter--">ViewFactoryAdapter</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">createBackgroundImageWizardStepsView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                                    <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;backgroundImageWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to choose a background image.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">createBaseboardChoiceView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;baseboardChoiceController)</code>
<div class="block">Returns a new view that edits the baseboard of its controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">createCompassView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;compassController)</code>
<div class="block">Returns a new view that edits compass values.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">createDimensionLineView</a></span>(boolean&nbsp;modification,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;dimensionLineController)</code>
<div class="block">Returns a new view that edits dimension line values.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">createFurnitureCatalogView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                          <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;furnitureCatalogController)</code>
<div class="block">Returns a new view that displays furniture <code>catalog</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController)</code>
<div class="block">Returns a new view that displays <code>home</code> furniture list.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHelpView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HelpController-">createHelpView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html" title="class in com.eteks.sweethome3d.viewcontroller">HelpController</a>&nbsp;helpController)</code>
<div class="block">Returns a new view that displays Sweet Home 3D help.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">createHome3DAttributesView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;home3DAttributesController)</code>
<div class="block">Returns a new view that edits 3D attributes.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">createHomeFurnitureView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;homeFurnitureController)</code>
<div class="block">Returns a new view that edits furniture values.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHomeView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">createHomeView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</code>
<div class="block">Returns a new view that displays <code>home</code> and its sub views.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createImportedFurnitureWizardStepsView-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController-">createImportedFurnitureWizardStepsView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                      java.lang.String&nbsp;modelName,
                                      boolean&nbsp;importHomePiece,
                                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController</a>&nbsp;importedFurnitureWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to import furniture.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">createImportedTextureWizardStepsView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                                    java.lang.String&nbsp;textureName,
                                    <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;importedTextureWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to import a texture.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">createLabelView</a></span>(boolean&nbsp;modification,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;labelController)</code>
<div class="block">Returns a new view that edits label values.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">createLevelView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;levelController)</code>
<div class="block">Returns a new view that edits level values.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">createModelMaterialsView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;modelMaterialsController)</code>
<div class="block">Returns a new view that edits the materials of its controller.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">createObserverCameraView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;home3dAttributesController)</code>
<div class="block">Returns a new view that edits observer camera values.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">createPageSetupView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;pageSetupController)</code>
<div class="block">Creates a new view that edits page setup.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">createPhotosView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;photosController)</code>
<div class="block">Returns a new view able to compute a photos of a home from its stored points of view.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">createPhotoView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;photoController)</code>
<div class="block">Returns a new view able to compute a photo realistic image of a home.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPlanView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">createPlanView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;planController)</code>
<div class="block">Returns a new view that displays <code>home</code> on a plan.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">createPolylineView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;polylineController)</code>
<div class="block">Returns a new view that edits polyline values.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">createPrintPreviewView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</code>
<div class="block">Returns a new view that displays home print preview.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">createRoomView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;roomController)</code>
<div class="block">Returns a new view that edits room values.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TextureChoiceView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createTextureChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.TextureChoiceController-">createTextureChoiceView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;textureChoiceController)</code>
<div class="block">Returns a new view that edits the texture of its controller.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createThreadedTaskView-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController-">createThreadedTaskView</a></span>(java.lang.String&nbsp;taskMessage,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html" title="class in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController</a>&nbsp;controller)</code>
<div class="block">Returns a new view that displays message for a threaded task.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">createUserPreferencesView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;userPreferencesController)</code>
<div class="block">Returns a new view that edits user preferences.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">createVideoView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;videoController)</code>
<div class="block">Returns a new view able to compute a 3D video of a home.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">createView3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
            <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
            <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Returns a new view that displays <code>home</code> in 3D.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">createWallView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;wallController)</code>
<div class="block">Returns a new view that edits wall values.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">createWizardView</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;wizardController)</code>
<div class="block">Returns a new view that displays a wizard.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ViewFactoryAdapter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ViewFactoryAdapter</h4>
<pre>public&nbsp;ViewFactoryAdapter()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBackgroundImageWizardStepsView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;createBackgroundImageWizardStepsView(<a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                                                 <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;backgroundImageWizardController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays the different steps that helps the user to choose a background image.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">createBackgroundImageWizardStepsView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createFurnitureCatalogView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;createFurnitureCatalogView(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;furnitureCatalogController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays furniture <code>catalog</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">createFurnitureCatalogView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createFurnitureView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;createFurnitureView(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays <code>home</code> furniture list.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createHelpView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HelpController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHelpView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a>&nbsp;createHelpView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html" title="class in com.eteks.sweethome3d.viewcontroller">HelpController</a>&nbsp;helpController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHelpView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HelpController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays Sweet Home 3D help.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHelpView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HelpController-">createHelpView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHome3DAttributesView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createHome3DAttributesView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                             <a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;home3DAttributesController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits 3D attributes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">createHome3DAttributesView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createLevelView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createLevelView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                  <a href="../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;levelController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits level values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">createLevelView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHomeFurnitureView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createHomeFurnitureView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                          <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;homeFurnitureController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits furniture values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">createHomeFurnitureView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createHomeView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHomeView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a>&nbsp;createHomeView(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHomeView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays <code>home</code> and its sub views.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHomeView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">createHomeView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createImportedFurnitureWizardStepsView-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createImportedFurnitureWizardStepsView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a>&nbsp;createImportedFurnitureWizardStepsView(<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                                                               java.lang.String&nbsp;modelName,
                                                                               boolean&nbsp;importHomePiece,
                                                                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                                                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController</a>&nbsp;importedFurnitureWizardController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createImportedFurnitureWizardStepsView-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays the different steps that helps the user to import furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createImportedFurnitureWizardStepsView-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController-">createImportedFurnitureWizardStepsView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createImportedTextureWizardStepsView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;createImportedTextureWizardStepsView(<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                                                 java.lang.String&nbsp;textureName,
                                                 <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;importedTextureWizardController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays the different steps that helps the user to import a texture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">createImportedTextureWizardStepsView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createLabelView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createLabelView(boolean&nbsp;modification,
                                  <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                  <a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;labelController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits label values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">createLabelView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPageSetupView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createPageSetupView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;pageSetupController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">ViewFactory</a></code></span></div>
<div class="block">Creates a new view that edits page setup.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">createPageSetupView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createPlanView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPlanView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a>&nbsp;createPlanView(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;planController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPlanView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays <code>home</code> on a plan.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPlanView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">createPlanView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPrintPreviewView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createPrintPreviewView(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                         <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays home print preview.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">createPrintPreviewView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoomView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createRoomView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;roomController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits room values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">createRoomView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPolylineView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createPolylineView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                     <a href="../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;polylineController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits polyline values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">createPolylineView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCompassView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createCompassView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;compassController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits compass values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">createCompassView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createObserverCameraView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createObserverCameraView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                           <a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;home3dAttributesController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits observer camera values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">createObserverCameraView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createTextureChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.TextureChoiceController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTextureChoiceView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TextureChoiceView</a>&nbsp;createTextureChoiceView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;textureChoiceController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createTextureChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.TextureChoiceController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits the texture of its controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createTextureChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.TextureChoiceController-">createTextureChoiceView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createBaseboardChoiceView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;createBaseboardChoiceView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;baseboardChoiceController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits the baseboard of its controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">createBaseboardChoiceView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createModelMaterialsView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;createModelMaterialsView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                     <a href="../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;modelMaterialsController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits the materials of its controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">createModelMaterialsView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createThreadedTaskView-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createThreadedTaskView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskView</a>&nbsp;createThreadedTaskView(java.lang.String&nbsp;taskMessage,
                                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html" title="class in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController</a>&nbsp;controller)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createThreadedTaskView-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays message for a threaded task.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createThreadedTaskView-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController-">createThreadedTaskView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUserPreferencesView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createUserPreferencesView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;userPreferencesController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits user preferences.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">createUserPreferencesView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createView3D</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;createView3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                         <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays <code>home</code> in 3D.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">createView3D</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWallView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createWallView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;wallController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits wall values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">createWallView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWizardView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createWizardView(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;wizardController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that displays a wizard.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">createWizardView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPhotoView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createPhotoView(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                  <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                  <a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;photoController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view able to compute a photo realistic image of a home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">createPhotoView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPhotosView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createPhotosView(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                   <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;photosController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view able to compute a photos of a home from its stored points of view.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">createPhotosView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createVideoView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createVideoView(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                  <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                  <a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;videoController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view able to compute a 3D video of a home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">createVideoView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
<a name="createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>createDimensionLineView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;createDimensionLineView(boolean&nbsp;modification,
                                          <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                          <a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;dimensionLineController)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">ViewFactory</a></code></span></div>
<div class="block">Returns a new view that edits dimension line values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">createDimensionLineView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.UnsupportedOperationException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ViewFactoryAdapter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html" target="_top">Frames</a></li>
<li><a href="ViewFactoryAdapter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
