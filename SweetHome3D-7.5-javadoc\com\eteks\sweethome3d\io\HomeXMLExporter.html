<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>HomeXMLExporter (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeXMLExporter (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeXMLExporter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/HomeXMLExporter.html" target="_top">Frames</a></li>
<li><a href="HomeXMLExporter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class HomeXMLExporter" class="title">Class HomeXMLExporter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io">com.eteks.sweethome3d.io.ObjectXMLExporter</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.HomeXMLExporter</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeXMLExporter</span>
extends <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io">ObjectXMLExporter</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;</pre>
<div class="block">Exporter for home instances. Homes will be written using the DTD given in <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io"><code>HomeXMLHandler</code></a> class.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html" title="class in com.eteks.sweethome3d.io">HomeXMLExporter.PieceOfFurnitureExporter</a></span></code>
<div class="block">Default exporter class used to write a piece of furniture in XML.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#HomeXMLExporter--">HomeXMLExporter</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#getExportedContentName-java.lang.Object-com.eteks.sweethome3d.model.Content-">getExportedContentName</a></span>(java.lang.Object&nbsp;owner,
                      <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns the saved name of the given <code>content</code> owned by an object.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#getId-java.lang.Object-">getId</a></span>(java.lang.Object&nbsp;object)</code>
<div class="block">Returns the XML id of the given <code>object</code> that can be referenced by other elements.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">writeAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
               <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Writes as XML attributes some data of <code>home</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeBackgroundImage-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.BackgroundImage-">writeBackgroundImage</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                    <a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage)</code>
<div class="block">Writes in XML the <code>background</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeBaseboard-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Baseboard-java.lang.String-">writeBaseboard</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
              <a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;baseboard,
              java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>baseboard</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeCamera-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Camera-java.lang.String-">writeCamera</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
           <a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
           java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>camera</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">writeChildren</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Writes as XML elements some objects that depends on of <code>home</code> with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeCompass-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Compass-">writeCompass</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a>&nbsp;compass)</code>
<div class="block">Writes in XML the <code>compass</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeDimensionLine-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.DimensionLine-">writeDimensionLine</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                  <a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine)</code>
<div class="block">Writes in XML the <code>dimensionLine</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeElement-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">writeElement</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Writes in XML the <code>home</code> object and the objects that depends on it with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeEnvironment-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeEnvironment-">writeEnvironment</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                <a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model">HomeEnvironment</a>&nbsp;environment)</code>
<div class="block">Writes in XML the <code>environment</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeLabel-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Label-">writeLabel</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
          <a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)</code>
<div class="block">Writes in XML the <code>label</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeLevel-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Level-">writeLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
          <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Writes in XML the <code>level</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeMaterial-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeMaterial-com.eteks.sweethome3d.model.Content-">writeMaterial</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>&nbsp;material,
             <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Writes in XML the <code>material</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writePieceOfFurniture-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePieceOfFurniture-">writePieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                     <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Writes in XML the <code>piece</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writePolyline-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Polyline-">writePolyline</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline)</code>
<div class="block">Writes in XML the <code>polyline</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writePrint-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePrint-">writePrint</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
          <a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a>&nbsp;print)</code>
<div class="block">Writes in XML the <code>print</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeRoom-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Room-">writeRoom</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
         <a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room)</code>
<div class="block">Writes in XML the <code>room</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeTextStyle-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.TextStyle-java.lang.String-">writeTextStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
              <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle,
              java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>textStyle</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeTexture-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeTexture-java.lang.String-">writeTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
            java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>texture</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeWall-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Wall-">writeWall</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
         <a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)</code>
<div class="block">Writes in XML the <code>wall</code> object with the given <code>writer</code>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.io.ObjectXMLExporter">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.io.<a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io">ObjectXMLExporter</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#getTag-T-">getTag</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeXMLExporter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeXMLExporter</h4>
<pre>public&nbsp;HomeXMLExporter()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getId-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre>protected&nbsp;java.lang.String&nbsp;getId(java.lang.Object&nbsp;object)</pre>
<div class="block">Returns the XML id of the given <code>object</code> that can be referenced by other elements.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if the <code>object</code> has no associated id.</dd>
</dl>
</li>
</ul>
<a name="writeElement-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeElement</h4>
<pre>public&nbsp;void&nbsp;writeElement(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                         <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)
                  throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>home</code> object and the objects that depends on it with the given <code>writer</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeElement-com.eteks.sweethome3d.io.XMLWriter-T-">writeElement</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io">ObjectXMLExporter</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeAttributes-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAttributes</h4>
<pre>protected&nbsp;void&nbsp;writeAttributes(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                               <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)
                        throws java.io.IOException</pre>
<div class="block">Writes as XML attributes some data of <code>home</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-T-">writeAttributes</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io">ObjectXMLExporter</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeChildren-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeChildren</h4>
<pre>protected&nbsp;void&nbsp;writeChildren(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                             <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)
                      throws java.io.IOException</pre>
<div class="block">Writes as XML elements some objects that depends on of <code>home</code> with the given <code>writer</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-T-">writeChildren</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io">ObjectXMLExporter</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&gt;</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeEnvironment-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeEnvironment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeEnvironment</h4>
<pre>protected&nbsp;void&nbsp;writeEnvironment(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                                <a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model">HomeEnvironment</a>&nbsp;environment)
                         throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>environment</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeBackgroundImage-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.BackgroundImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeBackgroundImage</h4>
<pre>protected&nbsp;void&nbsp;writeBackgroundImage(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                                    <a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage)
                             throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>background</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writePrint-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePrint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writePrint</h4>
<pre>protected&nbsp;void&nbsp;writePrint(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                          <a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a>&nbsp;print)
                   throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>print</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeCompass-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Compass-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeCompass</h4>
<pre>protected&nbsp;void&nbsp;writeCompass(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                            <a href="../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a>&nbsp;compass)
                     throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>compass</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeCamera-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Camera-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeCamera</h4>
<pre>protected&nbsp;void&nbsp;writeCamera(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                           <a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
                           java.lang.String&nbsp;attributeName)
                    throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>camera</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeLevel-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeLevel</h4>
<pre>protected&nbsp;void&nbsp;writeLevel(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                          <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)
                   throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>level</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writePieceOfFurniture-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writePieceOfFurniture</h4>
<pre>protected&nbsp;void&nbsp;writePieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                                     <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)
                              throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>piece</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeMaterial-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeMaterial-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeMaterial</h4>
<pre>protected&nbsp;void&nbsp;writeMaterial(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                             <a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>&nbsp;material,
                             <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)
                      throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>material</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeWall-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Wall-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeWall</h4>
<pre>protected&nbsp;void&nbsp;writeWall(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                         <a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)
                  throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>wall</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeRoom-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Room-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeRoom</h4>
<pre>protected&nbsp;void&nbsp;writeRoom(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                         <a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room)
                  throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>room</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writePolyline-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Polyline-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writePolyline</h4>
<pre>protected&nbsp;void&nbsp;writePolyline(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                             <a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline)
                      throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>polyline</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeDimensionLine-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.DimensionLine-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeDimensionLine</h4>
<pre>protected&nbsp;void&nbsp;writeDimensionLine(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                                  <a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine)
                           throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>dimensionLine</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeLabel-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Label-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeLabel</h4>
<pre>protected&nbsp;void&nbsp;writeLabel(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                          <a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)
                   throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>label</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeTextStyle-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.TextStyle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeTextStyle</h4>
<pre>protected&nbsp;void&nbsp;writeTextStyle(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                              <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle,
                              java.lang.String&nbsp;attributeName)
                       throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>textStyle</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeBaseboard-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Baseboard-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeBaseboard</h4>
<pre>protected&nbsp;void&nbsp;writeBaseboard(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                              <a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;baseboard,
                              java.lang.String&nbsp;attributeName)
                       throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>baseboard</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeTexture-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeTexture-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeTexture</h4>
<pre>protected&nbsp;void&nbsp;writeTexture(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                            <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
                            java.lang.String&nbsp;attributeName)
                     throws java.io.IOException</pre>
<div class="block">Writes in XML the <code>texture</code> object with the given <code>writer</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getExportedContentName-java.lang.Object-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getExportedContentName</h4>
<pre>protected&nbsp;java.lang.String&nbsp;getExportedContentName(java.lang.Object&nbsp;owner,
                                                  <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</pre>
<div class="block">Returns the saved name of the given <code>content</code> owned by an object.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeXMLExporter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/HomeXMLExporter.html" target="_top">Frames</a></li>
<li><a href="HomeXMLExporter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
