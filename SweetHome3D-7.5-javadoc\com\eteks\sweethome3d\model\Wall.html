<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>Wall (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Wall (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":9,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Wall.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Wall.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Wall.html" target="_top">Frames</a></li>
<li><a href="Wall.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class Wall" class="title">Class Wall</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.Wall</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Wall</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>
implements <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, <a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></pre>
<div class="block">A wall of a home plan.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.Wall">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.Property.html" title="enum in com.eteks.sweethome3d.model">Wall.Property</a></span></code>
<div class="block">The properties of a wall that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#Wall-float-float-float-float-float-">Wall</a></span>(float&nbsp;xStart,
    float&nbsp;yStart,
    float&nbsp;xEnd,
    float&nbsp;yEnd,
    float&nbsp;thickness)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">specify a height with the <a href="../../../../com/eteks/sweethome3d/model/Wall.html#Wall-float-float-float-float-float-float-">other constructor</a>.</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#Wall-float-float-float-float-float-float-">Wall</a></span>(float&nbsp;xStart,
    float&nbsp;yStart,
    float&nbsp;xEnd,
    float&nbsp;yEnd,
    float&nbsp;thickness,
    float&nbsp;height)</code>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness and height.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#Wall-float-float-float-float-float-float-com.eteks.sweethome3d.model.TextureImage-">Wall</a></span>(float&nbsp;xStart,
    float&nbsp;yStart,
    float&nbsp;xEnd,
    float&nbsp;yEnd,
    float&nbsp;thickness,
    float&nbsp;height,
    <a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</code>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness, height and pattern.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#Wall-java.lang.String-float-float-float-float-float-float-">Wall</a></span>(java.lang.String&nbsp;id,
    float&nbsp;xStart,
    float&nbsp;yStart,
    float&nbsp;xEnd,
    float&nbsp;yEnd,
    float&nbsp;thickness,
    float&nbsp;height)</code>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness and height.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#Wall-java.lang.String-float-float-float-float-float-float-com.eteks.sweethome3d.model.TextureImage-">Wall</a></span>(java.lang.String&nbsp;id,
    float&nbsp;xStart,
    float&nbsp;yStart,
    float&nbsp;xEnd,
    float&nbsp;yEnd,
    float&nbsp;thickness,
    float&nbsp;height,
    <a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</code>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness, height and pattern.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this wall expected
 its wall at start and wall at end aren't copied.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#clone-java.util.List-">clone</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;walls)</code>
<div class="block">Returns a clone of the <code>walls</code> list.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#containsPoint-float-float-boolean-float-">containsPoint</a></span>(float&nbsp;x,
             float&nbsp;y,
             boolean&nbsp;includeBaseboards,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this wall contains the point at (<code>x</code>, <code>y</code>)
 possibly including its baseboards, with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#containsPoint-float-float-float-">containsPoint</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this wall contains the point at (<code>x</code>, <code>y</code>)
 not including its baseboards, with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#containsWallEndAt-float-float-float-">containsWallEndAt</a></span>(float&nbsp;x,
                 float&nbsp;y,
                 float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this wall end line contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code> around the wall end line.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#containsWallStartAt-float-float-float-">containsWallStartAt</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this wall start line contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code> around the wall start line.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#duplicate-java.util.List-">duplicate</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;walls)</code>
<div class="block">Returns a duplicate of the <code>walls</code> list.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getArcExtent--">getArcExtent</a></span>()</code>
<div class="block">Returns the arc extent of a round wall or <code>null</code> if this wall isn't round.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height of this wall.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getHeightAtEnd--">getHeightAtEnd</a></span>()</code>
<div class="block">Returns the height of this wall at its end point.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getLeftSideBaseboard--">getLeftSideBaseboard</a></span>()</code>
<div class="block">Returns the left side baseboard of this wall.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getLeftSideColor--">getLeftSideColor</a></span>()</code>
<div class="block">Returns left side color of this wall.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getLeftSideShininess--">getLeftSideShininess</a></span>()</code>
<div class="block">Returns the left side shininess of this wall.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getLeftSideTexture--">getLeftSideTexture</a></span>()</code>
<div class="block">Returns the left side texture of this wall.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getLength--">getLength</a></span>()</code>
<div class="block">Returns the length of this wall.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getLevel--">getLevel</a></span>()</code>
<div class="block">Returns the level which this wall belongs to.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getPattern--">getPattern</a></span>()</code>
<div class="block">Returns the pattern of this wall in the plan.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getPoints--">getPoints</a></span>()</code>
<div class="block">Returns the points of each corner of a wall not including its baseboards.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getPoints-boolean-">getPoints</a></span>(boolean&nbsp;includeBaseboards)</code>
<div class="block">Returns the points of each corner of a wall possibly including its baseboards.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getRightSideBaseboard--">getRightSideBaseboard</a></span>()</code>
<div class="block">Returns the right side baseboard of this wall.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getRightSideColor--">getRightSideColor</a></span>()</code>
<div class="block">Returns right side color of this wall.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getRightSideShininess--">getRightSideShininess</a></span>()</code>
<div class="block">Returns the right side shininess of this wall.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getRightSideTexture--">getRightSideTexture</a></span>()</code>
<div class="block">Returns the right side texture of this wall.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getStartPointToEndPointDistance--">getStartPointToEndPointDistance</a></span>()</code>
<div class="block">Returns the distance from the start point of this wall to its end point.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getThickness--">getThickness</a></span>()</code>
<div class="block">Returns the thickness of this wall.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getTopColor--">getTopColor</a></span>()</code>
<div class="block">Returns the color of the top of this wall in the 3D view.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getWallAtEnd--">getWallAtEnd</a></span>()</code>
<div class="block">Returns the wall joined to this wall at end point.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getWallAtStart--">getWallAtStart</a></span>()</code>
<div class="block">Returns the wall joined to this wall at start point.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getXArcCircleCenter--">getXArcCircleCenter</a></span>()</code>
<div class="block">Returns the abscissa of the arc circle center of this wall.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getXEnd--">getXEnd</a></span>()</code>
<div class="block">Returns the end point abscissa of this wall.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getXStart--">getXStart</a></span>()</code>
<div class="block">Returns the start point abscissa of this wall.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getYArcCircleCenter--">getYArcCircleCenter</a></span>()</code>
<div class="block">Returns the ordinate of the arc circle center of this wall.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getYEnd--">getYEnd</a></span>()</code>
<div class="block">Returns the end point ordinate of this wall.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#getYStart--">getYStart</a></span>()</code>
<div class="block">Returns the start point ordinate of this wall.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></span>(float&nbsp;x0,
                   float&nbsp;y0,
                   float&nbsp;x1,
                   float&nbsp;y1)</code>
<div class="block">Returns <code>true</code> if this wall intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Returns <code>true</code> if this wall is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index
 or if the elevation of its highest point is higher than <code>level</code> elevation.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#isMiddlePointAt-float-float-float-">isMiddlePointAt</a></span>(float&nbsp;x,
               float&nbsp;y,
               float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the middle point of this wall is the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#isTrapezoidal--">isTrapezoidal</a></span>()</code>
<div class="block">Returns <code>true</code> if the height of this wall is different
 at its start and end points.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#move-float-float-">move</a></span>(float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Moves this wall of (<code>dx</code>, <code>dy</code>) units.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setArcExtent-java.lang.Float-">setArcExtent</a></span>(java.lang.Float&nbsp;arcExtent)</code>
<div class="block">Sets the arc extent of a round wall.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setHeight-java.lang.Float-">setHeight</a></span>(java.lang.Float&nbsp;height)</code>
<div class="block">Sets the height of this wall.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setHeightAtEnd-java.lang.Float-">setHeightAtEnd</a></span>(java.lang.Float&nbsp;heightAtEnd)</code>
<div class="block">Sets the height of this wall at its end point.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setLeftSideBaseboard-com.eteks.sweethome3d.model.Baseboard-">setLeftSideBaseboard</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;leftSideBaseboard)</code>
<div class="block">Sets the left side baseboard of this wall.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setLeftSideColor-java.lang.Integer-">setLeftSideColor</a></span>(java.lang.Integer&nbsp;leftSideColor)</code>
<div class="block">Sets left side color of this wall.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setLeftSideShininess-float-">setLeftSideShininess</a></span>(float&nbsp;leftSideShininess)</code>
<div class="block">Sets the left side shininess of this wall.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setLeftSideTexture-com.eteks.sweethome3d.model.HomeTexture-">setLeftSideTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;leftSideTexture)</code>
<div class="block">Sets the left side texture of this wall.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Sets the level of this wall.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setPattern-com.eteks.sweethome3d.model.TextureImage-">setPattern</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</code>
<div class="block">Sets the pattern of this wall in the plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setRightSideBaseboard-com.eteks.sweethome3d.model.Baseboard-">setRightSideBaseboard</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;rightSideBaseboard)</code>
<div class="block">Sets the right side baseboard of this wall.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setRightSideColor-java.lang.Integer-">setRightSideColor</a></span>(java.lang.Integer&nbsp;rightSideColor)</code>
<div class="block">Sets right side color of this wall.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setRightSideShininess-float-">setRightSideShininess</a></span>(float&nbsp;rightSideShininess)</code>
<div class="block">Sets the right side shininess of this wall.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setRightSideTexture-com.eteks.sweethome3d.model.HomeTexture-">setRightSideTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;rightSideTexture)</code>
<div class="block">Sets the right side texture of this wall.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setThickness-float-">setThickness</a></span>(float&nbsp;thickness)</code>
<div class="block">Sets wall thickness.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setTopColor-java.lang.Integer-">setTopColor</a></span>(java.lang.Integer&nbsp;topColor)</code>
<div class="block">Sets the color of the top of this wall in the 3D view, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setWallAtEnd-com.eteks.sweethome3d.model.Wall-">setWallAtEnd</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wallAtEnd)</code>
<div class="block">Sets the wall joined to this wall at end point.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setWallAtStart-com.eteks.sweethome3d.model.Wall-">setWallAtStart</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wallAtStart)</code>
<div class="block">Sets the wall joined to this wall at start point.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setXEnd-float-">setXEnd</a></span>(float&nbsp;xEnd)</code>
<div class="block">Sets the end point abscissa of this wall.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setXStart-float-">setXStart</a></span>(float&nbsp;xStart)</code>
<div class="block">Sets the start point abscissa of this wall.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setYEnd-float-">setYEnd</a></span>(float&nbsp;yEnd)</code>
<div class="block">Sets the end point ordinate of this wall.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Wall.html#setYStart-float-">setYStart</a></span>(float&nbsp;yStart)</code>
<div class="block">Sets the start point ordinate of this wall.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Wall-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wall</h4>
<pre>public&nbsp;Wall(float&nbsp;xStart,
            float&nbsp;yStart,
            float&nbsp;xEnd,
            float&nbsp;yEnd,
            float&nbsp;thickness)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">specify a height with the <a href="../../../../com/eteks/sweethome3d/model/Wall.html#Wall-float-float-float-float-float-float-">other constructor</a>.</span></div>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness. Height, left and right colors are <code>null</code>.</div>
</li>
</ul>
<a name="Wall-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wall</h4>
<pre>public&nbsp;Wall(float&nbsp;xStart,
            float&nbsp;yStart,
            float&nbsp;xEnd,
            float&nbsp;yEnd,
            float&nbsp;thickness,
            float&nbsp;height)</pre>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness and height. Pattern, left and right colors are <code>null</code>.</div>
</li>
</ul>
<a name="Wall-java.lang.String-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wall</h4>
<pre>public&nbsp;Wall(java.lang.String&nbsp;id,
            float&nbsp;xStart,
            float&nbsp;yStart,
            float&nbsp;xEnd,
            float&nbsp;yEnd,
            float&nbsp;thickness,
            float&nbsp;height)</pre>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness and height. Pattern, left and right colors are <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="Wall-float-float-float-float-float-float-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Wall</h4>
<pre>public&nbsp;Wall(float&nbsp;xStart,
            float&nbsp;yStart,
            float&nbsp;xEnd,
            float&nbsp;yEnd,
            float&nbsp;thickness,
            float&nbsp;height,
            <a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</pre>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness, height and pattern.
 Colors are <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="Wall-java.lang.String-float-float-float-float-float-float-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Wall</h4>
<pre>public&nbsp;Wall(java.lang.String&nbsp;id,
            float&nbsp;xStart,
            float&nbsp;yStart,
            float&nbsp;xEnd,
            float&nbsp;yEnd,
            float&nbsp;thickness,
            float&nbsp;height,
            <a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</pre>
<div class="block">Creates a wall from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>),
 with given thickness, height and pattern.
 Colors are <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getXStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXStart</h4>
<pre>public&nbsp;float&nbsp;getXStart()</pre>
<div class="block">Returns the start point abscissa of this wall.</div>
</li>
</ul>
<a name="setXStart-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXStart</h4>
<pre>public&nbsp;void&nbsp;setXStart(float&nbsp;xStart)</pre>
<div class="block">Sets the start point abscissa of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getYStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYStart</h4>
<pre>public&nbsp;float&nbsp;getYStart()</pre>
<div class="block">Returns the start point ordinate of this wall.</div>
</li>
</ul>
<a name="setYStart-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYStart</h4>
<pre>public&nbsp;void&nbsp;setYStart(float&nbsp;yStart)</pre>
<div class="block">Sets the start point ordinate of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getXEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXEnd</h4>
<pre>public&nbsp;float&nbsp;getXEnd()</pre>
<div class="block">Returns the end point abscissa of this wall.</div>
</li>
</ul>
<a name="setXEnd-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXEnd</h4>
<pre>public&nbsp;void&nbsp;setXEnd(float&nbsp;xEnd)</pre>
<div class="block">Sets the end point abscissa of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getYEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYEnd</h4>
<pre>public&nbsp;float&nbsp;getYEnd()</pre>
<div class="block">Returns the end point ordinate of this wall.</div>
</li>
</ul>
<a name="setYEnd-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYEnd</h4>
<pre>public&nbsp;void&nbsp;setYEnd(float&nbsp;yEnd)</pre>
<div class="block">Sets the end point ordinate of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLength</h4>
<pre>public&nbsp;float&nbsp;getLength()</pre>
<div class="block">Returns the length of this wall.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getStartPointToEndPointDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartPointToEndPointDistance</h4>
<pre>public&nbsp;float&nbsp;getStartPointToEndPointDistance()</pre>
<div class="block">Returns the distance from the start point of this wall to its end point.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setArcExtent-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArcExtent</h4>
<pre>public&nbsp;void&nbsp;setArcExtent(java.lang.Float&nbsp;arcExtent)</pre>
<div class="block">Sets the arc extent of a round wall.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getArcExtent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArcExtent</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getArcExtent()</pre>
<div class="block">Returns the arc extent of a round wall or <code>null</code> if this wall isn't round.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getXArcCircleCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXArcCircleCenter</h4>
<pre>public&nbsp;float&nbsp;getXArcCircleCenter()</pre>
<div class="block">Returns the abscissa of the arc circle center of this wall.
 If the wall isn't round, the return abscissa is at the middle of the wall.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getYArcCircleCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYArcCircleCenter</h4>
<pre>public&nbsp;float&nbsp;getYArcCircleCenter()</pre>
<div class="block">Returns the ordinate of the arc circle center of this wall.
 If the wall isn't round, the return ordinate is at the middle of the wall.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getWallAtStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallAtStart</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;getWallAtStart()</pre>
<div class="block">Returns the wall joined to this wall at start point.</div>
</li>
</ul>
<a name="setWallAtStart-com.eteks.sweethome3d.model.Wall-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallAtStart</h4>
<pre>public&nbsp;void&nbsp;setWallAtStart(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wallAtStart)</pre>
<div class="block">Sets the wall joined to this wall at start point. Once this wall is updated,
 listeners added to this wall will receive a change notification.
 If the start point of this wall is attached to an other wall, it will be detached
 from this wall, and wall listeners will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallAtStart</code> - a wall or <code>null</code> to detach this wall
          from any wall it was attached to before.</dd>
</dl>
</li>
</ul>
<a name="getWallAtEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallAtEnd</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;getWallAtEnd()</pre>
<div class="block">Returns the wall joined to this wall at end point.</div>
</li>
</ul>
<a name="setWallAtEnd-com.eteks.sweethome3d.model.Wall-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallAtEnd</h4>
<pre>public&nbsp;void&nbsp;setWallAtEnd(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wallAtEnd)</pre>
<div class="block">Sets the wall joined to this wall at end point. Once this wall is updated,
 listeners added to this wall will receive a change notification.
 If the end point of this wall is attached to an other wall, it will be detached
 from this wall, and wall listeners will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallAtEnd</code> - a wall or <code>null</code> to detach this wall
          from any wall it was attached to before.</dd>
</dl>
</li>
</ul>
<a name="getThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThickness</h4>
<pre>public&nbsp;float&nbsp;getThickness()</pre>
<div class="block">Returns the thickness of this wall.</div>
</li>
</ul>
<a name="setThickness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThickness</h4>
<pre>public&nbsp;void&nbsp;setThickness(float&nbsp;thickness)</pre>
<div class="block">Sets wall thickness. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getHeight()</pre>
<div class="block">Returns the height of this wall. If <a href="../../../../com/eteks/sweethome3d/model/Wall.html#getHeightAtEnd--"><code>getHeightAtEnd</code></a>
 returns a value not <code>null</code>, the returned height should be
 considered as the height of this wall at its start point.</div>
</li>
</ul>
<a name="setHeight-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public&nbsp;void&nbsp;setHeight(java.lang.Float&nbsp;height)</pre>
<div class="block">Sets the height of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getHeightAtEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeightAtEnd</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getHeightAtEnd()</pre>
<div class="block">Returns the height of this wall at its end point.</div>
</li>
</ul>
<a name="setHeightAtEnd-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeightAtEnd</h4>
<pre>public&nbsp;void&nbsp;setHeightAtEnd(java.lang.Float&nbsp;heightAtEnd)</pre>
<div class="block">Sets the height of this wall at its end point. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="isTrapezoidal--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTrapezoidal</h4>
<pre>public&nbsp;boolean&nbsp;isTrapezoidal()</pre>
<div class="block">Returns <code>true</code> if the height of this wall is different
 at its start and end points.</div>
</li>
</ul>
<a name="getLeftSideColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSideColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getLeftSideColor()</pre>
<div class="block">Returns left side color of this wall. This is the color of the left side
 of this wall when you go through wall from start point to end point.</div>
</li>
</ul>
<a name="setLeftSideColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeftSideColor</h4>
<pre>public&nbsp;void&nbsp;setLeftSideColor(java.lang.Integer&nbsp;leftSideColor)</pre>
<div class="block">Sets left side color of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getRightSideColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSideColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getRightSideColor()</pre>
<div class="block">Returns right side color of this wall. This is the color of the right side
 of this wall when you go through wall from start point to end point.</div>
</li>
</ul>
<a name="setRightSideColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightSideColor</h4>
<pre>public&nbsp;void&nbsp;setRightSideColor(java.lang.Integer&nbsp;rightSideColor)</pre>
<div class="block">Sets right side color of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getLeftSideTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSideTexture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;getLeftSideTexture()</pre>
<div class="block">Returns the left side texture of this wall.</div>
</li>
</ul>
<a name="setLeftSideTexture-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeftSideTexture</h4>
<pre>public&nbsp;void&nbsp;setLeftSideTexture(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;leftSideTexture)</pre>
<div class="block">Sets the left side texture of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getRightSideTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSideTexture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;getRightSideTexture()</pre>
<div class="block">Returns the right side texture of this wall.</div>
</li>
</ul>
<a name="setRightSideTexture-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightSideTexture</h4>
<pre>public&nbsp;void&nbsp;setRightSideTexture(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;rightSideTexture)</pre>
<div class="block">Sets the right side texture of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
</li>
</ul>
<a name="getLeftSideShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSideShininess</h4>
<pre>public&nbsp;float&nbsp;getLeftSideShininess()</pre>
<div class="block">Returns the left side shininess of this wall.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value between 0 (matt) and 1 (very shiny)</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setLeftSideShininess-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeftSideShininess</h4>
<pre>public&nbsp;void&nbsp;setLeftSideShininess(float&nbsp;leftSideShininess)</pre>
<div class="block">Sets the left side shininess of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getRightSideShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSideShininess</h4>
<pre>public&nbsp;float&nbsp;getRightSideShininess()</pre>
<div class="block">Returns the right side shininess of this wall.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value between 0 (matt) and 1 (very shiny)</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setRightSideShininess-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightSideShininess</h4>
<pre>public&nbsp;void&nbsp;setRightSideShininess(float&nbsp;rightSideShininess)</pre>
<div class="block">Sets the right side shininess of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getLeftSideBaseboard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSideBaseboard</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;getLeftSideBaseboard()</pre>
<div class="block">Returns the left side baseboard of this wall.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setLeftSideBaseboard-com.eteks.sweethome3d.model.Baseboard-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeftSideBaseboard</h4>
<pre>public&nbsp;void&nbsp;setLeftSideBaseboard(<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;leftSideBaseboard)</pre>
<div class="block">Sets the left side baseboard of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getRightSideBaseboard--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSideBaseboard</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;getRightSideBaseboard()</pre>
<div class="block">Returns the right side baseboard of this wall.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setRightSideBaseboard-com.eteks.sweethome3d.model.Baseboard-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightSideBaseboard</h4>
<pre>public&nbsp;void&nbsp;setRightSideBaseboard(<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;rightSideBaseboard)</pre>
<div class="block">Sets the right side baseboard of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPattern</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;getPattern()</pre>
<div class="block">Returns the pattern of this wall in the plan.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="setPattern-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPattern</h4>
<pre>public&nbsp;void&nbsp;setPattern(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</pre>
<div class="block">Sets the pattern of this wall in the plan, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.3</dd>
</dl>
</li>
</ul>
<a name="getTopColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTopColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getTopColor()</pre>
<div class="block">Returns the color of the top of this wall in the 3D view.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setTopColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTopColor</h4>
<pre>public&nbsp;void&nbsp;setTopColor(java.lang.Integer&nbsp;topColor)</pre>
<div class="block">Sets the color of the top of this wall in the 3D view, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;getLevel()</pre>
<div class="block">Returns the level which this wall belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#getLevel--">getLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevel</h4>
<pre>public&nbsp;void&nbsp;setLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Sets the level of this wall. Once this wall is updated,
 listeners added to this wall will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isAtLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAtLevel</h4>
<pre>public&nbsp;boolean&nbsp;isAtLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Returns <code>true</code> if this wall is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index
 or if the elevation of its highest point is higher than <code>level</code> elevation.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public&nbsp;float[][]&nbsp;getPoints()</pre>
<div class="block">Returns the points of each corner of a wall not including its baseboards.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#getPoints--">getPoints</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of the (x,y) coordinates of the wall corners.
    For a straight wall, the points at index 0 and 3 indicates the start of the wall,
    while the points at index 1 and 2 indicates the end of the wall.</dd>
</dl>
</li>
</ul>
<a name="getPoints-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public&nbsp;float[][]&nbsp;getPoints(boolean&nbsp;includeBaseboards)</pre>
<div class="block">Returns the points of each corner of a wall possibly including its baseboards.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="intersectsRectangle-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersectsRectangle</h4>
<pre>public&nbsp;boolean&nbsp;intersectsRectangle(float&nbsp;x0,
                                   float&nbsp;y0,
                                   float&nbsp;x1,
                                   float&nbsp;y1)</pre>
<div class="block">Returns <code>true</code> if this wall intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="containsPoint-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsPoint</h4>
<pre>public&nbsp;boolean&nbsp;containsPoint(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this wall contains the point at (<code>x</code>, <code>y</code>)
 not including its baseboards, with a given <code>margin</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#containsPoint-float-float-float-">containsPoint</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="containsPoint-float-float-boolean-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsPoint</h4>
<pre>public&nbsp;boolean&nbsp;containsPoint(float&nbsp;x,
                             float&nbsp;y,
                             boolean&nbsp;includeBaseboards,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this wall contains the point at (<code>x</code>, <code>y</code>)
 possibly including its baseboards, with a given <code>margin</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="isMiddlePointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMiddlePointAt</h4>
<pre>public&nbsp;boolean&nbsp;isMiddlePointAt(float&nbsp;x,
                               float&nbsp;y,
                               float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the middle point of this wall is the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</li>
</ul>
<a name="containsWallStartAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsWallStartAt</h4>
<pre>public&nbsp;boolean&nbsp;containsWallStartAt(float&nbsp;x,
                                   float&nbsp;y,
                                   float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this wall start line contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code> around the wall start line.</div>
</li>
</ul>
<a name="containsWallEndAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsWallEndAt</h4>
<pre>public&nbsp;boolean&nbsp;containsWallEndAt(float&nbsp;x,
                                 float&nbsp;y,
                                 float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this wall end line contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code> around the wall end line.</div>
</li>
</ul>
<a name="move-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>move</h4>
<pre>public&nbsp;void&nbsp;move(float&nbsp;dx,
                 float&nbsp;dy)</pre>
<div class="block">Moves this wall of (<code>dx</code>, <code>dy</code>) units.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#move-float-float-">move</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="duplicate-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duplicate</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;duplicate(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;walls)</pre>
<div class="block">Returns a duplicate of the <code>walls</code> list. All existing walls
 are copied and their wall at start and end point are set with copied
 walls only if they belong to the returned list.
 The id of duplicated walls are regenerated.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="clone-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;clone(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;walls)</pre>
<div class="block">Returns a clone of the <code>walls</code> list. All existing walls
 are copied and their wall at start and end point are set with copied
 walls only if they belong to the returned list.</div>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this wall expected
 its wall at start and wall at end aren't copied.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Wall.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Wall.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Wall.html" target="_top">Frames</a></li>
<li><a href="Wall.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
