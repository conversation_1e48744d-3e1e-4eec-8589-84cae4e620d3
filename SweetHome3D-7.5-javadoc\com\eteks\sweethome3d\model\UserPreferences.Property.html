<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>UserPreferences.Property (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UserPreferences.Property (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UserPreferences.Property.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/UserPreferences.Property.html" target="_top">Frames</a></li>
<li><a href="UserPreferences.Property.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Enum UserPreferences.Property" class="title">Enum UserPreferences.Property</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.UserPreferences.Property</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>&gt;</dd>
</dl>
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></dd>
</dl>
<hr>
<br>
<pre>public static enum <span class="typeNameLabel">UserPreferences.Property</span>
extends java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>&gt;</pre>
<div class="block">The properties of user preferences that may change. <code>PropertyChangeListener</code>s added
 to user preferences will be notified under a property name equal to the string value of one these properties.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#AERIAL_VIEW_CENTERED_ON_SELECTION_ENABLED">AERIAL_VIEW_CENTERED_ON_SELECTION_ENABLED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#AUTO_COMPLETION_STRINGS">AUTO_COMPLETION_STRINGS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#AUTO_SAVE_DELAY_FOR_RECOVERY">AUTO_SAVE_DELAY_FOR_RECOVERY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#CHECK_UPDATES_ENABLED">CHECK_UPDATES_ENABLED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#CURRENCY">CURRENCY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#DEFAULT_FONT_NAME">DEFAULT_FONT_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#DEFAULT_VALUE_ADDED_TAX_PERCENTAGE">DEFAULT_VALUE_ADDED_TAX_PERCENTAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#EDITING_IN_3D_VIEW_ENABLED">EDITING_IN_3D_VIEW_ENABLED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#FURNITURE_CATALOG_VIEWED_IN_TREE">FURNITURE_CATALOG_VIEWED_IN_TREE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#FURNITURE_MODEL_ICON_SIZE">FURNITURE_MODEL_ICON_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#FURNITURE_VIEWED_FROM_TOP">FURNITURE_VIEWED_FROM_TOP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#GRID_VISIBLE">GRID_VISIBLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#HOME_EXAMPLES">HOME_EXAMPLES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#IGNORED_ACTION_TIP">IGNORED_ACTION_TIP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#LANGUAGE">LANGUAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#MAGNETISM_ENABLED">MAGNETISM_ENABLED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#NAVIGATION_PANEL_VISIBLE">NAVIGATION_PANEL_VISIBLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#NEW_FLOOR_THICKNESS">NEW_FLOOR_THICKNESS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#NEW_ROOM_FLOOR_COLOR">NEW_ROOM_FLOOR_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#NEW_WALL_HEIGHT">NEW_WALL_HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#NEW_WALL_PATTERN">NEW_WALL_PATTERN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#NEW_WALL_SIDEBOARD_HEIGHT">NEW_WALL_SIDEBOARD_HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#NEW_WALL_SIDEBOARD_THICKNESS">NEW_WALL_SIDEBOARD_THICKNESS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#NEW_WALL_THICKNESS">NEW_WALL_THICKNESS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#OBSERVER_CAMERA_SELECTED_AT_CHANGE">OBSERVER_CAMERA_SELECTED_AT_CHANGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#PHOTO_RENDERER">PHOTO_RENDERER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#RECENT_COLORS">RECENT_COLORS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#RECENT_HOMES">RECENT_HOMES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#RECENT_TEXTURES">RECENT_TEXTURES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#ROOM_FLOOR_COLORED_OR_TEXTURED">ROOM_FLOOR_COLORED_OR_TEXTURED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#RULERS_VISIBLE">RULERS_VISIBLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#SUPPORTED_LANGUAGES">SUPPORTED_LANGUAGES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#UNIT">UNIT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#UPDATES_MINIMUM_DATE">UPDATES_MINIMUM_DATE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#VALUE_ADDED_TAX_ENABLED">VALUE_ADDED_TAX_ENABLED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#WALL_PATTERN">WALL_PATTERN</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="LANGUAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LANGUAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> LANGUAGE</pre>
</li>
</ul>
<a name="SUPPORTED_LANGUAGES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUPPORTED_LANGUAGES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> SUPPORTED_LANGUAGES</pre>
</li>
</ul>
<a name="UNIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNIT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> UNIT</pre>
</li>
</ul>
<a name="CURRENCY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CURRENCY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> CURRENCY</pre>
</li>
</ul>
<a name="VALUE_ADDED_TAX_ENABLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VALUE_ADDED_TAX_ENABLED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> VALUE_ADDED_TAX_ENABLED</pre>
</li>
</ul>
<a name="DEFAULT_VALUE_ADDED_TAX_PERCENTAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_VALUE_ADDED_TAX_PERCENTAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> DEFAULT_VALUE_ADDED_TAX_PERCENTAGE</pre>
</li>
</ul>
<a name="MAGNETISM_ENABLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAGNETISM_ENABLED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> MAGNETISM_ENABLED</pre>
</li>
</ul>
<a name="RULERS_VISIBLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RULERS_VISIBLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> RULERS_VISIBLE</pre>
</li>
</ul>
<a name="GRID_VISIBLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRID_VISIBLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> GRID_VISIBLE</pre>
</li>
</ul>
<a name="DEFAULT_FONT_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_FONT_NAME</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> DEFAULT_FONT_NAME</pre>
</li>
</ul>
<a name="FURNITURE_VIEWED_FROM_TOP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FURNITURE_VIEWED_FROM_TOP</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> FURNITURE_VIEWED_FROM_TOP</pre>
</li>
</ul>
<a name="FURNITURE_MODEL_ICON_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FURNITURE_MODEL_ICON_SIZE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> FURNITURE_MODEL_ICON_SIZE</pre>
</li>
</ul>
<a name="ROOM_FLOOR_COLORED_OR_TEXTURED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROOM_FLOOR_COLORED_OR_TEXTURED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> ROOM_FLOOR_COLORED_OR_TEXTURED</pre>
</li>
</ul>
<a name="WALL_PATTERN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WALL_PATTERN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> WALL_PATTERN</pre>
</li>
</ul>
<a name="NEW_WALL_PATTERN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_WALL_PATTERN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> NEW_WALL_PATTERN</pre>
</li>
</ul>
<a name="NEW_WALL_THICKNESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_WALL_THICKNESS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> NEW_WALL_THICKNESS</pre>
</li>
</ul>
<a name="NEW_WALL_HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_WALL_HEIGHT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> NEW_WALL_HEIGHT</pre>
</li>
</ul>
<a name="NEW_WALL_SIDEBOARD_THICKNESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_WALL_SIDEBOARD_THICKNESS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> NEW_WALL_SIDEBOARD_THICKNESS</pre>
</li>
</ul>
<a name="NEW_WALL_SIDEBOARD_HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_WALL_SIDEBOARD_HEIGHT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> NEW_WALL_SIDEBOARD_HEIGHT</pre>
</li>
</ul>
<a name="NEW_ROOM_FLOOR_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_ROOM_FLOOR_COLOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> NEW_ROOM_FLOOR_COLOR</pre>
</li>
</ul>
<a name="NEW_FLOOR_THICKNESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_FLOOR_THICKNESS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> NEW_FLOOR_THICKNESS</pre>
</li>
</ul>
<a name="RECENT_HOMES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECENT_HOMES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> RECENT_HOMES</pre>
</li>
</ul>
<a name="IGNORED_ACTION_TIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IGNORED_ACTION_TIP</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> IGNORED_ACTION_TIP</pre>
</li>
</ul>
<a name="FURNITURE_CATALOG_VIEWED_IN_TREE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FURNITURE_CATALOG_VIEWED_IN_TREE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> FURNITURE_CATALOG_VIEWED_IN_TREE</pre>
</li>
</ul>
<a name="NAVIGATION_PANEL_VISIBLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAVIGATION_PANEL_VISIBLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> NAVIGATION_PANEL_VISIBLE</pre>
</li>
</ul>
<a name="AERIAL_VIEW_CENTERED_ON_SELECTION_ENABLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AERIAL_VIEW_CENTERED_ON_SELECTION_ENABLED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> AERIAL_VIEW_CENTERED_ON_SELECTION_ENABLED</pre>
</li>
</ul>
<a name="OBSERVER_CAMERA_SELECTED_AT_CHANGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBSERVER_CAMERA_SELECTED_AT_CHANGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> OBSERVER_CAMERA_SELECTED_AT_CHANGE</pre>
</li>
</ul>
<a name="EDITING_IN_3D_VIEW_ENABLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EDITING_IN_3D_VIEW_ENABLED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> EDITING_IN_3D_VIEW_ENABLED</pre>
</li>
</ul>
<a name="CHECK_UPDATES_ENABLED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CHECK_UPDATES_ENABLED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> CHECK_UPDATES_ENABLED</pre>
</li>
</ul>
<a name="UPDATES_MINIMUM_DATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UPDATES_MINIMUM_DATE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> UPDATES_MINIMUM_DATE</pre>
</li>
</ul>
<a name="AUTO_SAVE_DELAY_FOR_RECOVERY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AUTO_SAVE_DELAY_FOR_RECOVERY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> AUTO_SAVE_DELAY_FOR_RECOVERY</pre>
</li>
</ul>
<a name="AUTO_COMPLETION_STRINGS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AUTO_COMPLETION_STRINGS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> AUTO_COMPLETION_STRINGS</pre>
</li>
</ul>
<a name="RECENT_COLORS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECENT_COLORS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> RECENT_COLORS</pre>
</li>
</ul>
<a name="RECENT_TEXTURES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECENT_TEXTURES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> RECENT_TEXTURES</pre>
</li>
</ul>
<a name="HOME_EXAMPLES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HOME_EXAMPLES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> HOME_EXAMPLES</pre>
</li>
</ul>
<a name="PHOTO_RENDERER">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PHOTO_RENDERER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a> PHOTO_RENDERER</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (UserPreferences.Property c : UserPreferences.Property.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UserPreferences.Property.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/UserPreferences.Property.html" target="_top">Frames</a></li>
<li><a href="UserPreferences.Property.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
