<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>LengthUnit (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LengthUnit (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":6,"i3":9,"i4":6,"i5":6,"i6":6,"i7":6,"i8":10,"i9":6,"i10":6,"i11":6,"i12":6,"i13":9,"i14":10,"i15":6,"i16":9,"i17":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LengthUnit.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/Label.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/LengthUnit.html" target="_top">Frames</a></li>
<li><a href="LengthUnit.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Enum LengthUnit" class="title">Enum LengthUnit</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.LengthUnit</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public enum <span class="typeNameLabel">LengthUnit</span>
extends java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&gt;</pre>
<div class="block">Unit used for lengths.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#CENTIMETER">CENTIMETER</a></span></code>
<div class="block">Centimeter unit.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#FOOT_DECIMALS">FOOT_DECIMALS</a></span></code>
<div class="block">Foot unit with decimals.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#INCH">INCH</a></span></code>
<div class="block">Foot/Inch unit followed by fraction.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#INCH_DECIMALS">INCH_DECIMALS</a></span></code>
<div class="block">Inch unit with decimals.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#INCH_FRACTION">INCH_FRACTION</a></span></code>
<div class="block">Inch unit followed by fraction.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#METER">METER</a></span></code>
<div class="block">Meter unit.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#MILLIMETER">MILLIMETER</a></span></code>
<div class="block">Millimeter unit.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#centimeterToFoot-float-">centimeterToFoot</a></span>(float&nbsp;length)</code>
<div class="block">Returns the <code>length</code> given in centimeters converted to feet.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#centimeterToInch-float-">centimeterToInch</a></span>(float&nbsp;length)</code>
<div class="block">Returns the <code>length</code> given in centimeters converted to inches.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>abstract float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#centimeterToUnit-float-">centimeterToUnit</a></span>(float&nbsp;length)</code>
<div class="block">Returns the <code>length</code> given in centimeters converted
 to a value expressed in this unit.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#footToCentimeter-float-">footToCentimeter</a></span>(float&nbsp;length)</code>
<div class="block">Returns the <code>length</code> given in feet converted to centimeters.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>abstract java.text.Format</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getAreaFormatWithUnit--">getAreaFormatWithUnit</a></span>()</code>
<div class="block">Returns a format able to format areas with their localized unit.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>abstract java.text.Format</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getFormat--">getFormat</a></span>()</code>
<div class="block">Returns a format able to format lengths.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>abstract java.text.Format</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getFormatWithUnit--">getFormatWithUnit</a></span>()</code>
<div class="block">Returns a format able to format lengths with their localized unit.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>abstract float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getMagnetizedLength-float-float-">getMagnetizedLength</a></span>(float&nbsp;length,
                   float&nbsp;maxDelta)</code>
<div class="block">Returns the value close to the given <code>length</code> in centimeter under magnetism.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getMaximumElevation--">getMaximumElevation</a></span>()</code>
<div class="block">Returns the maximum value for elevation in centimeter.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>abstract float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getMaximumLength--">getMaximumLength</a></span>()</code>
<div class="block">Returns the maximum value for length in centimeter.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>abstract float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getMinimumLength--">getMinimumLength</a></span>()</code>
<div class="block">Returns the minimum value for length in centimeter.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>abstract java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getName--">getName</a></span>()</code>
<div class="block">Returns a localized name of this unit.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>abstract float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#getStepSize--">getStepSize</a></span>()</code>
<div class="block">Returns the preferred step value for length in centimeter.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#inchToCentimeter-float-">inchToCentimeter</a></span>(float&nbsp;length)</code>
<div class="block">Returns the <code>length</code> given in inches converted to centimeters.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#isMetric--">isMetric</a></span>()</code>
<div class="block">Returns <code>true</code> if this unit belongs to Metric system.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>abstract float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#unitToCentimeter-float-">unitToCentimeter</a></span>(float&nbsp;length)</code>
<div class="block">Returns the <code>length</code> given in this unit converted
 to a value expressed in centimeter.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="MILLIMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MILLIMETER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a> MILLIMETER</pre>
<div class="block">Millimeter unit.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="CENTIMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CENTIMETER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a> CENTIMETER</pre>
<div class="block">Centimeter unit.</div>
</li>
</ul>
<a name="METER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>METER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a> METER</pre>
<div class="block">Meter unit.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="INCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INCH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a> INCH</pre>
<div class="block">Foot/Inch unit followed by fraction.</div>
</li>
</ul>
<a name="INCH_FRACTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INCH_FRACTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a> INCH_FRACTION</pre>
<div class="block">Inch unit followed by fraction.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="INCH_DECIMALS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INCH_DECIMALS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a> INCH_DECIMALS</pre>
<div class="block">Inch unit with decimals.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="FOOT_DECIMALS">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FOOT_DECIMALS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a> FOOT_DECIMALS</pre>
<div class="block">Foot unit with decimals.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (LengthUnit c : LengthUnit.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="centimeterToInch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>centimeterToInch</h4>
<pre>public static&nbsp;float&nbsp;centimeterToInch(float&nbsp;length)</pre>
<div class="block">Returns the <code>length</code> given in centimeters converted to inches.</div>
</li>
</ul>
<a name="centimeterToFoot-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>centimeterToFoot</h4>
<pre>public static&nbsp;float&nbsp;centimeterToFoot(float&nbsp;length)</pre>
<div class="block">Returns the <code>length</code> given in centimeters converted to feet.</div>
</li>
</ul>
<a name="inchToCentimeter-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>inchToCentimeter</h4>
<pre>public static&nbsp;float&nbsp;inchToCentimeter(float&nbsp;length)</pre>
<div class="block">Returns the <code>length</code> given in inches converted to centimeters.</div>
</li>
</ul>
<a name="footToCentimeter-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>footToCentimeter</h4>
<pre>public static&nbsp;float&nbsp;footToCentimeter(float&nbsp;length)</pre>
<div class="block">Returns the <code>length</code> given in feet converted to centimeters.</div>
</li>
</ul>
<a name="getFormatWithUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormatWithUnit</h4>
<pre>public abstract&nbsp;java.text.Format&nbsp;getFormatWithUnit()</pre>
<div class="block">Returns a format able to format lengths with their localized unit.</div>
</li>
</ul>
<a name="getFormat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFormat</h4>
<pre>public abstract&nbsp;java.text.Format&nbsp;getFormat()</pre>
<div class="block">Returns a format able to format lengths.</div>
</li>
</ul>
<a name="getAreaFormatWithUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaFormatWithUnit</h4>
<pre>public abstract&nbsp;java.text.Format&nbsp;getAreaFormatWithUnit()</pre>
<div class="block">Returns a format able to format areas with their localized unit.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public abstract&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns a localized name of this unit.</div>
</li>
</ul>
<a name="getMagnetizedLength-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMagnetizedLength</h4>
<pre>public abstract&nbsp;float&nbsp;getMagnetizedLength(float&nbsp;length,
                                          float&nbsp;maxDelta)</pre>
<div class="block">Returns the value close to the given <code>length</code> in centimeter under magnetism.</div>
</li>
</ul>
<a name="getMinimumLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinimumLength</h4>
<pre>public abstract&nbsp;float&nbsp;getMinimumLength()</pre>
<div class="block">Returns the minimum value for length in centimeter.</div>
</li>
</ul>
<a name="getMaximumLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaximumLength</h4>
<pre>public abstract&nbsp;float&nbsp;getMaximumLength()</pre>
<div class="block">Returns the maximum value for length in centimeter.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getStepSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStepSize</h4>
<pre>public abstract&nbsp;float&nbsp;getStepSize()</pre>
<div class="block">Returns the preferred step value for length in centimeter.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getMaximumElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaximumElevation</h4>
<pre>public&nbsp;float&nbsp;getMaximumElevation()</pre>
<div class="block">Returns the maximum value for elevation in centimeter.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="centimeterToUnit-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>centimeterToUnit</h4>
<pre>public abstract&nbsp;float&nbsp;centimeterToUnit(float&nbsp;length)</pre>
<div class="block">Returns the <code>length</code> given in centimeters converted
 to a value expressed in this unit.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="unitToCentimeter-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unitToCentimeter</h4>
<pre>public abstract&nbsp;float&nbsp;unitToCentimeter(float&nbsp;length)</pre>
<div class="block">Returns the <code>length</code> given in this unit converted
 to a value expressed in centimeter.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="isMetric--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isMetric</h4>
<pre>public&nbsp;boolean&nbsp;isMetric()</pre>
<div class="block">Returns <code>true</code> if this unit belongs to Metric system.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LengthUnit.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/Label.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/LengthUnit.html" target="_top">Frames</a></li>
<li><a href="LengthUnit.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
