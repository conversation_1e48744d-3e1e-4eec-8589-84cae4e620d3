<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:48 CEST 2024 -->
<title>HomeComponent3D (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeComponent3D (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":42,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeComponent3D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/Home3DAttributesPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.Projection.html" title="enum in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/HomeComponent3D.html" target="_top">Frames</a></li>
<li><a href="HomeComponent3D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class HomeComponent3D" class="title">Class HomeComponent3D</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>javax.swing.JComponent</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.HomeComponent3D</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html" title="interface in com.eteks.sweethome3d.viewcontroller">View3D</a>, java.awt.image.ImageObserver, java.awt.MenuContainer, java.awt.print.Printable, java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeComponent3D</span>
extends javax.swing.JComponent
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html" title="interface in com.eteks.sweethome3d.viewcontroller">View3D</a>, java.awt.print.Printable</pre>
<div class="block">A component that displays home walls, rooms and furniture with Java 3D.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.HomeComponent3D">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.Projection.html" title="enum in com.eteks.sweethome3d.swing">HomeComponent3D.Projection</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JComponent</h3>
<code>javax.swing.JComponent.AccessibleJComponent</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JComponent</h3>
<code>listenerList, TOOL_TIP_TEXT_KEY, ui, UNDEFINED_CONDITION, WHEN_ANCESTOR_OF_FOCUSED_COMPONENT, WHEN_FOCUSED, WHEN_IN_FOCUSED_WINDOW</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>accessibleContext, BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.print.Printable">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.print.Printable</h3>
<code>NO_SUCH_PAGE, PAGE_EXISTS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-">HomeComponent3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture,
 with no controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-boolean-">HomeComponent3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               boolean&nbsp;displayShadowOnFloor)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture,
 with shadows on the floor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-boolean-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
               boolean&nbsp;displayShadowOnFloor,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.swing.HomeComponent3D.Projection-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
               <a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.Projection.html" title="enum in com.eteks.sweethome3d.swing">HomeComponent3D.Projection</a>&nbsp;projection,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#addMouseListener-java.awt.event.MouseListener-">addMouseListener</a></span>(java.awt.event.MouseListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#addMouseMotionListener-java.awt.event.MouseMotionListener-">addMouseMotionListener</a></span>(java.awt.event.MouseMotionListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#convertPixelLocationToVirtualWorld-int-int-">convertPixelLocationToVirtualWorld</a></span>(int&nbsp;x,
                                  int&nbsp;y)</code>
<div class="block">Returns the 3D point matching the point (x, y) in component coordinates space.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#endOffscreenImagesCreation--">endOffscreenImagesCreation</a></span>()</code>
<div class="block">Frees unnecessary resources after the creation of a sequence of multiple offscreen images.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#getClosestItemAt-int-int-">getClosestItemAt</a></span>(int&nbsp;x,
                int&nbsp;y)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use rather getClosestSelectableItemAt.</span></div>
</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#getClosestSelectableItemAt-int-int-">getClosestSelectableItemAt</a></span>(int&nbsp;x,
                          int&nbsp;y)</code>
<div class="block">Returns the closest <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model"><code>Selectable</code></a> object at component coordinates (x, y),
 or <code>null</code> if not found.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#getObject3DFactory--">getObject3DFactory</a></span>()</code>
<div class="block">Returns the object factory used to create 3D objects.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#getOffScreenImage-int-int-">getOffScreenImage</a></span>(int&nbsp;width,
                 int&nbsp;height)</code>
<div class="block">Returns an image of the home viewed by this component at the given size.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#getVirtualWorldPointAt-int-int-float-">getVirtualWorldPointAt</a></span>(int&nbsp;x,
                      int&nbsp;y,
                      float&nbsp;elevation)</code>
<div class="block">Returns the coordinates intersecting the floor of the selected level in the direction
 joining camera location and component coordinates (x, y).</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#print-java.awt.Graphics-java.awt.print.PageFormat-int-">print</a></span>(java.awt.Graphics&nbsp;g,
     java.awt.print.PageFormat&nbsp;pageFormat,
     int&nbsp;pageIndex)</code>
<div class="block">Prints this component to make it fill <code>pageFormat</code> imageable size.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#removeMouseListener-java.awt.event.MouseListener-">removeMouseListener</a></span>(java.awt.event.MouseListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#removeMouseMotionListener-java.awt.event.MouseMotionListener-">removeMouseMotionListener</a></span>(java.awt.event.MouseMotionListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#setVisible-boolean-">setVisible</a></span>(boolean&nbsp;visible)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#startOffscreenImagesCreation--">startOffscreenImagesCreation</a></span>()</code>
<div class="block">Optimizes this component for the creation of a sequence of multiple off screen images.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JComponent</h3>
<code>addAncestorListener, addNotify, addVetoableChangeListener, computeVisibleRect, contains, createToolTip, disable, enable, firePropertyChange, firePropertyChange, firePropertyChange, fireVetoableChange, getActionForKeyStroke, getActionMap, getAlignmentX, getAlignmentY, getAncestorListeners, getAutoscrolls, getBaseline, getBaselineResizeBehavior, getBorder, getBounds, getClientProperty, getComponentGraphics, getComponentPopupMenu, getConditionForKeyStroke, getDebugGraphicsOptions, getDefaultLocale, getFontMetrics, getGraphics, getHeight, getInheritsPopupMenu, getInputMap, getInputMap, getInputVerifier, getInsets, getInsets, getListeners, getLocation, getMaximumSize, getMinimumSize, getNextFocusableComponent, getPopupLocation, getPreferredSize, getRegisteredKeyStrokes, getRootPane, getSize, getToolTipLocation, getToolTipText, getToolTipText, getTopLevelAncestor, getTransferHandler, getUIClassID, getVerifyInputWhenFocusTarget, getVetoableChangeListeners, getVisibleRect, getWidth, getX, getY, grabFocus, hide, isDoubleBuffered, isLightweightComponent, isManagingFocus, isOpaque, isOptimizedDrawingEnabled, isPaintingForPrint, isPaintingOrigin, isPaintingTile, isRequestFocusEnabled, isValidateRoot, paint, paintBorder, paintChildren, paintComponent, paintImmediately, paintImmediately, paramString, print, printAll, printBorder, printChildren, printComponent, processComponentKeyEvent, processKeyBinding, processKeyEvent, processMouseEvent, processMouseMotionEvent, putClientProperty, registerKeyboardAction, registerKeyboardAction, removeAncestorListener, removeNotify, removeVetoableChangeListener, repaint, repaint, requestDefaultFocus, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, resetKeyboardActions, reshape, revalidate, scrollRectToVisible, setActionMap, setAlignmentX, setAlignmentY, setAutoscrolls, setBackground, setBorder, setComponentPopupMenu, setDebugGraphicsOptions, setDefaultLocale, setDoubleBuffered, setEnabled, setFocusTraversalKeys, setFont, setForeground, setInheritsPopupMenu, setInputMap, setInputVerifier, setMaximumSize, setMinimumSize, setNextFocusableComponent, setOpaque, setPreferredSize, setRequestFocusEnabled, setToolTipText, setTransferHandler, setUI, setVerifyInputWhenFocusTarget, unregisterKeyboardAction, update, updateUI</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addImpl, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, doLayout, findComponentAt, findComponentAt, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getLayout, getMousePosition, insets, invalidate, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paintComponents, preferredSize, printComponents, processContainerEvent, processEvent, remove, remove, removeAll, removeContainerListener, setComponentZOrder, setFocusCycleRoot, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setLayout, transferFocusDownCycle, validate, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addFocusListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, createImage, createImage, createVolatileImage, createVolatileImage, disableEvents, dispatchEvent, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getAccessibleContext, getBackground, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getCursor, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getForeground, getGraphicsConfiguration, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocale, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getToolkit, getTreeLock, gotFocus, handleEvent, hasFocus, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, resize, resize, setBounds, setBounds, setComponentOrientation, setCursor, setDropTarget, setFocusable, setFocusTraversalKeysEnabled, setIgnoreRepaint, setLocale, setLocation, setLocation, setName, setSize, setSize, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeComponent3D-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeComponent3D</h4>
<pre>public&nbsp;HomeComponent3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</pre>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture,
 with no controller.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the 3D component couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.HomeController3D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeComponent3D</h4>
<pre>public&nbsp;HomeComponent3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</pre>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the 3D component couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeComponent3D</h4>
<pre>public&nbsp;HomeComponent3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       boolean&nbsp;displayShadowOnFloor)</pre>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture,
 with shadows on the floor.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the 3D component couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeComponent3D</h4>
<pre>public&nbsp;HomeComponent3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</pre>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the 3D component couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.HomeController3D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeComponent3D</h4>
<pre>public&nbsp;HomeComponent3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</pre>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home to display in this component</dd>
<dd><code>preferences</code> - user preferences</dd>
<dd><code>object3dFactory</code> - a factory able to create 3D objects from <code>home</code> items.
            The <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html#createObject3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.Selectable-boolean-"><code>createObject3D</code></a> of
            this factory is expected to return an instance of <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d"><code>Object3DBranch</code></a> in current implementation.</dd>
<dd><code>controller</code> - the controller that manages modifications in <code>home</code>.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the 3D component couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-boolean-com.eteks.sweethome3d.viewcontroller.HomeController3D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeComponent3D</h4>
<pre>public&nbsp;HomeComponent3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
                       boolean&nbsp;displayShadowOnFloor,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</pre>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the 3D component couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.swing.HomeComponent3D.Projection-com.eteks.sweethome3d.viewcontroller.HomeController3D-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeComponent3D</h4>
<pre>public&nbsp;HomeComponent3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
                       <a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.Projection.html" title="enum in com.eteks.sweethome3d.swing">HomeComponent3D.Projection</a>&nbsp;projection,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</pre>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the 3D component couldn't be created.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>public&nbsp;void&nbsp;setVisible(boolean&nbsp;visible)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setVisible</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="print-java.awt.Graphics-java.awt.print.PageFormat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;int&nbsp;print(java.awt.Graphics&nbsp;g,
                 java.awt.print.PageFormat&nbsp;pageFormat,
                 int&nbsp;pageIndex)</pre>
<div class="block">Prints this component to make it fill <code>pageFormat</code> imageable size.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>print</code>&nbsp;in interface&nbsp;<code>java.awt.print.Printable</code></dd>
</dl>
</li>
</ul>
<a name="startOffscreenImagesCreation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startOffscreenImagesCreation</h4>
<pre>public&nbsp;void&nbsp;startOffscreenImagesCreation()</pre>
<div class="block">Optimizes this component for the creation of a sequence of multiple off screen images.
 Once off screen images are generated with <a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#getOffScreenImage-int-int-"><code>getOffScreenImage</code></a>,
 call <a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#endOffscreenImagesCreation--"><code>endOffscreenImagesCreation</code></a> method to free resources.</div>
</li>
</ul>
<a name="getOffScreenImage-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOffScreenImage</h4>
<pre>public&nbsp;java.awt.image.BufferedImage&nbsp;getOffScreenImage(int&nbsp;width,
                                                      int&nbsp;height)</pre>
<div class="block">Returns an image of the home viewed by this component at the given size.</div>
</li>
</ul>
<a name="endOffscreenImagesCreation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endOffscreenImagesCreation</h4>
<pre>public&nbsp;void&nbsp;endOffscreenImagesCreation()</pre>
<div class="block">Frees unnecessary resources after the creation of a sequence of multiple offscreen images.</div>
</li>
</ul>
<a name="addMouseMotionListener-java.awt.event.MouseMotionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMouseMotionListener</h4>
<pre>public&nbsp;void&nbsp;addMouseMotionListener(java.awt.event.MouseMotionListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>addMouseMotionListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="removeMouseMotionListener-java.awt.event.MouseMotionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeMouseMotionListener</h4>
<pre>public&nbsp;void&nbsp;removeMouseMotionListener(java.awt.event.MouseMotionListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>removeMouseMotionListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="addMouseListener-java.awt.event.MouseListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMouseListener</h4>
<pre>public&nbsp;void&nbsp;addMouseListener(java.awt.event.MouseListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>addMouseListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="removeMouseListener-java.awt.event.MouseListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeMouseListener</h4>
<pre>public&nbsp;void&nbsp;removeMouseListener(java.awt.event.MouseListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>removeMouseListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="getClosestItemAt-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClosestItemAt</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;getClosestItemAt(int&nbsp;x,
                                   int&nbsp;y)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use rather getClosestSelectableItemAt.</span></div>
<div class="block">Returns the closest <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model"><code>Selectable</code></a> object at component coordinates (x, y),
 or <code>null</code> if not found.</div>
</li>
</ul>
<a name="getClosestSelectableItemAt-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClosestSelectableItemAt</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;getClosestSelectableItemAt(int&nbsp;x,
                                             int&nbsp;y)</pre>
<div class="block">Returns the closest <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model"><code>Selectable</code></a> object at component coordinates (x, y),
 or <code>null</code> if not found.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html#getClosestSelectableItemAt-int-int-">getClosestSelectableItemAt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html" title="interface in com.eteks.sweethome3d.viewcontroller">View3D</a></code></dd>
</dl>
</li>
</ul>
<a name="convertPixelLocationToVirtualWorld-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertPixelLocationToVirtualWorld</h4>
<pre>public&nbsp;float[]&nbsp;convertPixelLocationToVirtualWorld(int&nbsp;x,
                                                  int&nbsp;y)</pre>
<div class="block">Returns the 3D point matching the point (x, y) in component coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html#convertPixelLocationToVirtualWorld-int-int-">convertPixelLocationToVirtualWorld</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html" title="interface in com.eteks.sweethome3d.viewcontroller">View3D</a></code></dd>
</dl>
</li>
</ul>
<a name="getVirtualWorldPointAt-int-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVirtualWorldPointAt</h4>
<pre>public&nbsp;float[]&nbsp;getVirtualWorldPointAt(int&nbsp;x,
                                      int&nbsp;y,
                                      float&nbsp;elevation)</pre>
<div class="block">Returns the coordinates intersecting the floor of the selected level in the direction
 joining camera location and component coordinates (x, y).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html#getVirtualWorldPointAt-int-int-float-">getVirtualWorldPointAt</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html" title="interface in com.eteks.sweethome3d.viewcontroller">View3D</a></code></dd>
</dl>
</li>
</ul>
<a name="getObject3DFactory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getObject3DFactory</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;getObject3DFactory()</pre>
<div class="block">Returns the object factory used to create 3D objects.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeComponent3D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/Home3DAttributesPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.Projection.html" title="enum in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/HomeComponent3D.html" target="_top">Frames</a></li>
<li><a href="HomeComponent3D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
