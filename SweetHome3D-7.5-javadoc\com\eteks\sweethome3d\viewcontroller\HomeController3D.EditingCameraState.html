<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>HomeController3D.EditingCameraState (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeController3D.EditingCameraState (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeController3D.EditingCameraState.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html" target="_top">Frames</a></li>
<li><a href="HomeController3D.EditingCameraState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class HomeController3D.EditingCameraState" class="title">Class HomeController3D.EditingCameraState</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller.HomeController3D.CameraControllerState</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.HomeController3D.EditingCameraState</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a></dd>
</dl>
<hr>
<br>
<pre>protected abstract class <span class="typeNameLabel">HomeController3D.EditingCameraState</span>
extends <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></pre>
<div class="block">Controller state handling mouse events to edit home items.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colLast" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#EditingCameraState--">EditingCameraState</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#elevateCamera-float-">elevateCamera</a></span>(float&nbsp;delta)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#escape--">escape</a></span>()</code>
<div class="block">Escapes of current editing action.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#goToCamera-com.eteks.sweethome3d.model.Camera-">goToCamera</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#isEditingState--">isEditingState</a></span>()</code>
<div class="block">Returns <code>true</code> if this controller is moving items.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#modifyFieldOfView-float-">modifyFieldOfView</a></span>(float&nbsp;delta)</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#moveCamera-float-">moveCamera</a></span>(float&nbsp;delta)</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#moveCameraSideways-float-">moveCameraSideways</a></span>(float&nbsp;delta)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#moveMouse-float-float-">moveMouse</a></span>(float&nbsp;x,
         float&nbsp;y)</code>
<div class="block">Processes a mouse button moved event.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#pressMouse-float-float-int-boolean-boolean-boolean-boolean-com.eteks.sweethome3d.viewcontroller.View.PointerType-">pressMouse</a></span>(float&nbsp;x,
          float&nbsp;y,
          int&nbsp;clickCount,
          boolean&nbsp;shiftDown,
          boolean&nbsp;alignmentActivated,
          boolean&nbsp;duplicationActivated,
          boolean&nbsp;magnetismToggled,
          <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a>&nbsp;pointerType)</code>
<div class="block">Processes a mouse button pressed event.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#releaseMouse-float-float-">releaseMouse</a></span>(float&nbsp;x,
            float&nbsp;y)</code>
<div class="block">Processes a mouse button released event.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#rotateCameraPitch-float-">rotateCameraPitch</a></span>(float&nbsp;delta)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#rotateCameraYaw-float-">rotateCameraYaw</a></span>(float&nbsp;delta)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#setAlignmentActivated-boolean-">setAlignmentActivated</a></span>(boolean&nbsp;alignmentActivated)</code>
<div class="block">Activates or deactivates alignment feature during editing action.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#setDuplicationActivated-boolean-">setDuplicationActivated</a></span>(boolean&nbsp;duplicationActivated)</code>
<div class="block">Activates or deactivates duplication feature during editing action.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html#toggleMagnetism-boolean-">toggleMagnetism</a></span>(boolean&nbsp;magnetismToggled)</code>
<div class="block">Toggles temporary magnetism feature of user preferences during editing action.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.HomeController3D.CameraControllerState">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#enter--">enter</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#exit--">exit</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#setEditionActivated-boolean-">setEditionActivated</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="EditingCameraState--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EditingCameraState</h4>
<pre>protected&nbsp;EditingCameraState()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isEditingState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditingState</h4>
<pre>public&nbsp;boolean&nbsp;isEditingState()</pre>
<div class="block">Returns <code>true</code> if this controller is moving items.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#isEditingState--">isEditingState</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="moveCamera-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveCamera</h4>
<pre>public&nbsp;void&nbsp;moveCamera(float&nbsp;delta)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#moveCamera-float-">moveCamera</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="moveCameraSideways-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveCameraSideways</h4>
<pre>public&nbsp;void&nbsp;moveCameraSideways(float&nbsp;delta)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#moveCameraSideways-float-">moveCameraSideways</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="elevateCamera-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>elevateCamera</h4>
<pre>public&nbsp;void&nbsp;elevateCamera(float&nbsp;delta)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#elevateCamera-float-">elevateCamera</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="rotateCameraYaw-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rotateCameraYaw</h4>
<pre>public&nbsp;void&nbsp;rotateCameraYaw(float&nbsp;delta)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#rotateCameraYaw-float-">rotateCameraYaw</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="rotateCameraPitch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rotateCameraPitch</h4>
<pre>public&nbsp;void&nbsp;rotateCameraPitch(float&nbsp;delta)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#rotateCameraPitch-float-">rotateCameraPitch</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="modifyFieldOfView-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifyFieldOfView</h4>
<pre>public&nbsp;void&nbsp;modifyFieldOfView(float&nbsp;delta)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#modifyFieldOfView-float-">modifyFieldOfView</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="goToCamera-com.eteks.sweethome3d.model.Camera-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>goToCamera</h4>
<pre>public&nbsp;void&nbsp;goToCamera(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#goToCamera-com.eteks.sweethome3d.model.Camera-">goToCamera</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="pressMouse-float-float-int-boolean-boolean-boolean-boolean-com.eteks.sweethome3d.viewcontroller.View.PointerType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pressMouse</h4>
<pre>public&nbsp;void&nbsp;pressMouse(float&nbsp;x,
                       float&nbsp;y,
                       int&nbsp;clickCount,
                       boolean&nbsp;shiftDown,
                       boolean&nbsp;alignmentActivated,
                       boolean&nbsp;duplicationActivated,
                       boolean&nbsp;magnetismToggled,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a>&nbsp;pointerType)</pre>
<div class="block">Processes a mouse button pressed event.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#pressMouse-float-float-int-boolean-boolean-boolean-boolean-com.eteks.sweethome3d.viewcontroller.View.PointerType-">pressMouse</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="releaseMouse-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>releaseMouse</h4>
<pre>public&nbsp;void&nbsp;releaseMouse(float&nbsp;x,
                         float&nbsp;y)</pre>
<div class="block">Processes a mouse button released event.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#releaseMouse-float-float-">releaseMouse</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="moveMouse-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveMouse</h4>
<pre>public&nbsp;void&nbsp;moveMouse(float&nbsp;x,
                      float&nbsp;y)</pre>
<div class="block">Processes a mouse button moved event.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#moveMouse-float-float-">moveMouse</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="escape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>escape</h4>
<pre>public&nbsp;void&nbsp;escape()</pre>
<div class="block">Escapes of current editing action.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#escape--">escape</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="toggleMagnetism-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleMagnetism</h4>
<pre>public&nbsp;void&nbsp;toggleMagnetism(boolean&nbsp;magnetismToggled)</pre>
<div class="block">Toggles temporary magnetism feature of user preferences during editing action.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#toggleMagnetism-boolean-">toggleMagnetism</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>magnetismToggled</code> - if <code>true</code> then magnetism feature is toggled.</dd>
</dl>
</li>
</ul>
<a name="setAlignmentActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignmentActivated</h4>
<pre>public&nbsp;void&nbsp;setAlignmentActivated(boolean&nbsp;alignmentActivated)</pre>
<div class="block">Activates or deactivates alignment feature during editing action.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#setAlignmentActivated-boolean-">setAlignmentActivated</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignmentActivated</code> - if <code>true</code> then alignment is active.</dd>
</dl>
</li>
</ul>
<a name="setDuplicationActivated-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setDuplicationActivated</h4>
<pre>public&nbsp;void&nbsp;setDuplicationActivated(boolean&nbsp;duplicationActivated)</pre>
<div class="block">Activates or deactivates duplication feature during editing action.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html#setDuplicationActivated-boolean-">setDuplicationActivated</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duplicationActivated</code> - if <code>true</code> then duplication is active.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeController3D.EditingCameraState.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html" target="_top">Frames</a></li>
<li><a href="HomeController3D.EditingCameraState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
