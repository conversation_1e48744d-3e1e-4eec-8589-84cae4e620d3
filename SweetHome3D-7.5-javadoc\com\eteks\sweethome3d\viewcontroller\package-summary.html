<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>com.eteks.sweethome3d.viewcontroller (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.eteks.sweethome3d.viewcontroller (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/tools/package-summary.html">Prev&nbsp;Package</a></li>
<li>Next&nbsp;Package</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.eteks.sweethome3d.viewcontroller</h1>
<div class="docSummary">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></td>
<td class="colLast">
<div class="block">Content manager.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></td>
<td class="colLast">
<div class="block">A MVC controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></td>
<td class="colLast">
<div class="block">The view that displays a pane in a dialog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></td>
<td class="colLast">
<div class="block">A view able to export data in an output stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a></td>
<td class="colLast">
<div class="block">The view that displays the furniture of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView.FurnitureFilter</a></td>
<td class="colLast">
<div class="block">The super type used to specify how furniture should be filtered in viewed furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a></td>
<td class="colLast">
<div class="block">A view that displays Sweet Home 3D help.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></td>
<td class="colLast">
<div class="block">The main view that displays a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a></td>
<td class="colLast">
<div class="block">A view that displays the different steps that helps the user to import a piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a></td>
<td class="colLast">
<div class="block">A factory that specifies how to create the 3D objects from Sweet Home 3D model objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></td>
<td class="colLast">
<div class="block">The view that displays the plan of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TextureChoiceView</a></td>
<td class="colLast">
<div class="block">A view that edits the texture of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.ExceptionHandler.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController.ExceptionHandler</a></td>
<td class="colLast">
<div class="block">Handles exception that may happen during the execution of a threaded task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskView</a></td>
<td class="colLast">
<div class="block">A view of a threaded task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></td>
<td class="colLast">
<div class="block">A view able to transfer data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView.TransferObserver</a></td>
<td class="colLast">
<div class="block">An observer to follow the data created for transfer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></td>
<td class="colLast">
<div class="block">An MVC view created and controlled by a controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html" title="interface in com.eteks.sweethome3d.viewcontroller">View3D</a></td>
<td class="colLast">
<div class="block">The view that displays the 3D view of a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a></td>
<td class="colLast">
<div class="block">A factory that specifies how to create the views displayed in Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/AbstractPhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">AbstractPhotoController</a></td>
<td class="colLast">
<div class="block">The base class for controllers of photo creation views.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a></td>
<td class="colLast">
<div class="block">Wizard controller for background image in plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a></td>
<td class="colLast">
<div class="block">A MVC controller for baseboard choice view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a></td>
<td class="colLast">
<div class="block">A MVC controller for the compass view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a></td>
<td class="colLast">
<div class="block">A MVC controller for dimension line view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a></td>
<td class="colLast">
<div class="block">Data types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a></td>
<td class="colLast">
<div class="block">A MVC controller for the furniture catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a></td>
<td class="colLast">
<div class="block">A MVC controller for the home furniture table.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html" title="class in com.eteks.sweethome3d.viewcontroller">HelpController</a></td>
<td class="colLast">
<div class="block">A MVC controller for Sweet Home 3D help view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a></td>
<td class="colLast">
<div class="block">A MVC controller for home 3D attributes view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></td>
<td class="colLast">
<div class="block">A MVC controller for the home view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a></td>
<td class="colLast">
<div class="block">A MVC controller for the home 3D view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></td>
<td class="colLast">
<div class="block">Controller state classes super class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a></td>
<td class="colLast">
<div class="block">A MVC controller for home furniture view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController</a></td>
<td class="colLast">
<div class="block">Wizard controller to manage furniture importation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a></td>
<td class="colLast">
<div class="block">Wizard controller for background image in plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a></td>
<td class="colLast">
<div class="block">A MVC controller for label view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a></td>
<td class="colLast">
<div class="block">A MVC controller for home levels view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a></td>
<td class="colLast">
<div class="block">A MVC controller for model materials choice.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a></td>
<td class="colLast">
<div class="block">A MVC controller for observer camera attributes view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a></td>
<td class="colLast">
<div class="block">A MVC controller for home page setup view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a></td>
<td class="colLast">
<div class="block">The controller of the photo creation view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a></td>
<td class="colLast">
<div class="block">The controller of multiple photos creation view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a></td>
<td class="colLast">
<div class="block">A MVC controller for the plan view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></td>
<td class="colLast">
<div class="block">Controller state classes super class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerStateDecorator</a></td>
<td class="colLast">
<div class="block">A decorator on controller state, useful to change the behavior of an existing state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></td>
<td class="colLast">
<div class="block">Selectable modes in controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a></td>
<td class="colLast">
<div class="block">A MVC controller for polyline view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a></td>
<td class="colLast">
<div class="block">A MVC controller for home print preview view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a></td>
<td class="colLast">
<div class="block">A MVC controller for room view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></td>
<td class="colLast">
<div class="block">A MVC controller for texture choice.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html" title="class in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController</a></td>
<td class="colLast">
<div class="block">A MVC controller used to execute a particular task in a separate thread.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a></td>
<td class="colLast">
<div class="block">Data types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a></td>
<td class="colLast">
<div class="block">A MVC controller for user preferences view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a></td>
<td class="colLast">
<div class="block">The controller of the video creation view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html" title="class in com.eteks.sweethome3d.viewcontroller">ViewFactoryAdapter</a></td>
<td class="colLast">
<div class="block">A view factory with all its methods throwing
 <code>UnsupportedOperationException</code> exception.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a></td>
<td class="colLast">
<div class="block">A MVC controller for wall view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a></td>
<td class="colLast">
<div class="block">An abstract MVC for a wizard view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></td>
<td class="colLast">
<div class="block">State of a step in wizard.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/AbstractPhotoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">AbstractPhotoController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController.Property</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController.Step</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.BaseboardPaint</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getPaint--">paint type</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/CompassController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">CompassController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.DimensionLineOrientation.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.DimensionLineOrientation</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getOrientation--">dimension line type</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HelpController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.EnvironmentPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.EnvironmentPaint</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getGroundPaint--">ground paint type</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureHorizontalAxis.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureHorizontalAxis</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getHorizontalAxis--">horizontal axis</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurniturePaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurniturePaint</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getPaint--">paint type</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.FurnitureShininess.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.FurnitureShininess</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getShininess--">shininess type</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a></td>
<td class="colLast">
<div class="block">The actions proposed by the view to user.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.SaveAnswer</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Property</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Step</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController.Property</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController.Step</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">LabelController.Property</a></td>
<td class="colLast">
<div class="block">The property that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LevelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">LevelController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController.Property</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ObserverCameraController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PageSetupController.Property</a></td>
<td class="colLast">
<div class="block">The property that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PhotoController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PhotosController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a></td>
<td class="colLast">
<div class="block">Fields that can be edited in plan view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.Property</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a></td>
<td class="colLast">
<div class="block">The cursor types available in plan view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PolylineController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getFloorPaint--">room paint type</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">TextureChoiceController.Property</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">UserPreferencesController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">VideoController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></td>
<td class="colLast">
<div class="block">The pointer types that the user may use to interact with the plan</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.Property</a></td>
<td class="colLast">
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getLeftSidePaint--">wall paint type</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallShape.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallShape</a></td>
<td class="colLast">
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getShape--">wall shape</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WizardController.Property</a></td>
<td class="colLast">
<div class="block">The properties that the view associated to this controller needs.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package com.eteks.sweethome3d.viewcontroller Description">Package com.eteks.sweethome3d.viewcontroller Description</h2>
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D. This package controls the edition 
of the data stored by the <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.
<p>The <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller"><code>HomeController</code></a> class is the 
main <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">controller</a> 
of a home edited in Sweet Home 3D. This controller manages all the 
<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">actions</a> proposed to the end user, 
creates various subcontrollers that manage 
<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">furniture catalog</a>,
<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">home furniture</a>,
<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">plan</a>...
<br>All the controllers of this package create their associated 
<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">view</a> thanks to a 
<a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">view factory</a>, and this abstract
factory is implemented in Sweet Home 3D with Swing components in 
<a href="../../../../com/eteks/sweethome3d/swing/package-summary.html"><code>com.eteks.sweethome3d.swing</code></a> package.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/tools/package-summary.html">Prev&nbsp;Package</a></li>
<li>Next&nbsp;Package</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
