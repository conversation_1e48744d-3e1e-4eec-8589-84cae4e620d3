<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:48 CEST 2024 -->
<title>IconManager (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="IconManager (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":9,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IconManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/HomeTransferableList.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ImportedFurnitureWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/IconManager.html" target="_top">Frames</a></li>
<li><a href="IconManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class IconManager" class="title">Class IconManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.IconManager</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">IconManager</span>
extends java.lang.Object</pre>
<div class="block">Singleton managing icons cache.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#clear--">clear</a></span>()</code>
<div class="block">Clears the loaded resources cache and shutdowns the multithreaded service
 that loads icons.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>javax.swing.Icon</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#getErrorIcon--">getErrorIcon</a></span>()</code>
<div class="block">Returns the icon displayed for wrong content.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>javax.swing.Icon</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#getErrorIcon-int-">getErrorIcon</a></span>(int&nbsp;height)</code>
<div class="block">Returns the icon displayed for wrong content resized at a given height.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>javax.swing.Icon</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#getIcon-com.eteks.sweethome3d.model.Content-java.awt.Component-">getIcon</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
       java.awt.Component&nbsp;waitingComponent)</code>
<div class="block">Returns an icon read from <code>content</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>javax.swing.Icon</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#getIcon-com.eteks.sweethome3d.model.Content-int-java.awt.Component-">getIcon</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
       int&nbsp;height,
       java.awt.Component&nbsp;waitingComponent)</code>
<div class="block">Returns an icon read from <code>content</code> and rescaled at a given <code>height</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/IconManager.html" title="class in com.eteks.sweethome3d.swing">IconManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#getInstance--">getInstance</a></span>()</code>
<div class="block">Returns an instance of this singleton.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>javax.swing.Icon</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#getWaitIcon--">getWaitIcon</a></span>()</code>
<div class="block">Returns the icon displayed while a content is loaded.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>javax.swing.Icon</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#getWaitIcon-int-">getWaitIcon</a></span>(int&nbsp;height)</code>
<div class="block">Returns the icon displayed while a content is loaded resized at a given height.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#isErrorIcon-javax.swing.Icon-">isErrorIcon</a></span>(javax.swing.Icon&nbsp;icon)</code>
<div class="block">Returns <code>true</code> if the given <code>icon</code> is the error icon
 used by this manager to indicate it couldn't load an icon.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html#isWaitIcon-javax.swing.Icon-">isWaitIcon</a></span>(javax.swing.Icon&nbsp;icon)</code>
<div class="block">Returns <code>true</code> if the given <code>icon</code> is the wait icon
 used by this manager to indicate it's currently loading an icon.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/IconManager.html" title="class in com.eteks.sweethome3d.swing">IconManager</a>&nbsp;getInstance()</pre>
<div class="block">Returns an instance of this singleton.</div>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public&nbsp;void&nbsp;clear()</pre>
<div class="block">Clears the loaded resources cache and shutdowns the multithreaded service
 that loads icons.</div>
</li>
</ul>
<a name="getErrorIcon-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorIcon</h4>
<pre>public&nbsp;javax.swing.Icon&nbsp;getErrorIcon(int&nbsp;height)</pre>
<div class="block">Returns the icon displayed for wrong content resized at a given height.</div>
</li>
</ul>
<a name="getErrorIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorIcon</h4>
<pre>public&nbsp;javax.swing.Icon&nbsp;getErrorIcon()</pre>
<div class="block">Returns the icon displayed for wrong content.</div>
</li>
</ul>
<a name="isErrorIcon-javax.swing.Icon-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isErrorIcon</h4>
<pre>public&nbsp;boolean&nbsp;isErrorIcon(javax.swing.Icon&nbsp;icon)</pre>
<div class="block">Returns <code>true</code> if the given <code>icon</code> is the error icon
 used by this manager to indicate it couldn't load an icon.</div>
</li>
</ul>
<a name="getWaitIcon-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWaitIcon</h4>
<pre>public&nbsp;javax.swing.Icon&nbsp;getWaitIcon(int&nbsp;height)</pre>
<div class="block">Returns the icon displayed while a content is loaded resized at a given height.</div>
</li>
</ul>
<a name="getWaitIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWaitIcon</h4>
<pre>public&nbsp;javax.swing.Icon&nbsp;getWaitIcon()</pre>
<div class="block">Returns the icon displayed while a content is loaded.</div>
</li>
</ul>
<a name="isWaitIcon-javax.swing.Icon-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWaitIcon</h4>
<pre>public&nbsp;boolean&nbsp;isWaitIcon(javax.swing.Icon&nbsp;icon)</pre>
<div class="block">Returns <code>true</code> if the given <code>icon</code> is the wait icon
 used by this manager to indicate it's currently loading an icon.</div>
</li>
</ul>
<a name="getIcon-com.eteks.sweethome3d.model.Content-java.awt.Component-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;javax.swing.Icon&nbsp;getIcon(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                                java.awt.Component&nbsp;waitingComponent)</pre>
<div class="block">Returns an icon read from <code>content</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - an object containing an image</dd>
<dd><code>waitingComponent</code> - a waiting component. If <code>null</code>, the returned icon will
            be read immediately in the current thread.</dd>
</dl>
</li>
</ul>
<a name="getIcon-com.eteks.sweethome3d.model.Content-int-java.awt.Component-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;javax.swing.Icon&nbsp;getIcon(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                                int&nbsp;height,
                                java.awt.Component&nbsp;waitingComponent)</pre>
<div class="block">Returns an icon read from <code>content</code> and rescaled at a given <code>height</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - an object containing an image</dd>
<dd><code>height</code> - the desired height of the returned icon</dd>
<dd><code>waitingComponent</code> - a waiting component. If <code>null</code>, the returned icon will
            be read immediately in the current thread.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/IconManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/HomeTransferableList.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ImportedFurnitureWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/IconManager.html" target="_top">Frames</a></li>
<li><a href="IconManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
