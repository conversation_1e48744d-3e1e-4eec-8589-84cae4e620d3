<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:50 CEST 2024 -->
<title>PlanTransferHandler (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PlanTransferHandler (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanTransferHandler.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PlanRulerComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/PolylinePanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/PlanTransferHandler.html" target="_top">Frames</a></li>
<li><a href="PlanTransferHandler.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.TransferHandler">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.TransferHandler">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class PlanTransferHandler" class="title">Class PlanTransferHandler</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>javax.swing.TransferHandler</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html" title="class in com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing.LocatedTransferHandler</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.PlanTransferHandler</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PlanTransferHandler</span>
extends <a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html" title="class in com.eteks.sweethome3d.swing">LocatedTransferHandler</a></pre>
<div class="block">Plan transfer handler.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.PlanTransferHandler">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.TransferHandler">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.TransferHandler</h3>
<code>javax.swing.TransferHandler.DropLocation, javax.swing.TransferHandler.TransferSupport</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.TransferHandler">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.TransferHandler</h3>
<code>COPY, COPY_OR_MOVE, LINK, MOVE, NONE</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#PlanTransferHandler-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.viewcontroller.HomeController-">PlanTransferHandler</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</code>
<div class="block">Creates a handler able to transfer furniture and walls in plan.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#canImportFlavor-java.awt.datatransfer.DataFlavor:A-">canImportFlavor</a></span>(java.awt.datatransfer.DataFlavor[]&nbsp;flavors)</code>
<div class="block">Returns <code>true</code> if <code>flavors</code> contains
 <a href="../../../../com/eteks/sweethome3d/swing/HomeTransferableList.html#HOME_FLAVOR"><code>HOME_FLAVOR</code></a> flavor
 or <code>DataFlavor.javaFileListFlavor</code> flavor.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected java.awt.datatransfer.Transferable</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#createTransferable-javax.swing.JComponent-">createTransferable</a></span>(javax.swing.JComponent&nbsp;source)</code>
<div class="block">Returns a transferable object that contains a copy of the selected items in home
 and an image of the selected items.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#dragEntered-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">dragEntered</a></span>(javax.swing.JComponent&nbsp;destination,
           java.awt.datatransfer.Transferable&nbsp;transferable,
           int&nbsp;dragAction)</code>
<div class="block">Notifies home controller that a drag operation started if
 <code>transferable</code> data contains <a href="../../../../com/eteks/sweethome3d/swing/HomeTransferableList.html#HOME_FLAVOR"><code>HOME_FLAVOR</code></a>
 flavor and destination is a plan.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#dragExited-javax.swing.JComponent-">dragExited</a></span>(javax.swing.JComponent&nbsp;destination)</code>
<div class="block">Called once the cursor left <code>destination</code> component during a drag and drop operation.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#dragMoved-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">dragMoved</a></span>(javax.swing.JComponent&nbsp;destination,
         java.awt.datatransfer.Transferable&nbsp;transferable,
         int&nbsp;dragAction)</code>
<div class="block">Called when <code>transferable</code> data moved in <code>destination</code> component
 during a drag and drop operation.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#exportDone-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">exportDone</a></span>(javax.swing.JComponent&nbsp;source,
          java.awt.datatransfer.Transferable&nbsp;data,
          int&nbsp;action)</code>
<div class="block">Removes the copied element once moved.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#getSourceActions-javax.swing.JComponent-">getSourceActions</a></span>(javax.swing.JComponent&nbsp;source)</code>
<div class="block">Returns <code>COPY_OR_MOVE</code>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html#importData-javax.swing.JComponent-java.awt.datatransfer.Transferable-">importData</a></span>(javax.swing.JComponent&nbsp;destination,
          java.awt.datatransfer.Transferable&nbsp;transferable)</code>
<div class="block">Adds items contained in <code>transferable</code> to home.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.swing.LocatedTransferHandler">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.swing.<a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html" title="class in com.eteks.sweethome3d.swing">LocatedTransferHandler</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#canImport-javax.swing.JComponent-java.awt.datatransfer.DataFlavor:A-">canImport</a>, <a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#getDropLocation--">getDropLocation</a>, <a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#getModelContents-java.util.List-com.eteks.sweethome3d.viewcontroller.ContentManager-">getModelContents</a>, <a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#isDrop--">isDrop</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.TransferHandler">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.TransferHandler</h3>
<code>canImport, exportAsDrag, exportToClipboard, getCopyAction, getCutAction, getDragImage, getDragImageOffset, getPasteAction, getVisualRepresentation, importData, setDragImage, setDragImageOffset</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PlanTransferHandler-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.viewcontroller.HomeController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PlanTransferHandler</h4>
<pre>public&nbsp;PlanTransferHandler(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                           <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                           <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</pre>
<div class="block">Creates a handler able to transfer furniture and walls in plan.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSourceActions-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceActions</h4>
<pre>public&nbsp;int&nbsp;getSourceActions(javax.swing.JComponent&nbsp;source)</pre>
<div class="block">Returns <code>COPY_OR_MOVE</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getSourceActions</code>&nbsp;in class&nbsp;<code>javax.swing.TransferHandler</code></dd>
</dl>
</li>
</ul>
<a name="createTransferable-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTransferable</h4>
<pre>protected&nbsp;java.awt.datatransfer.Transferable&nbsp;createTransferable(javax.swing.JComponent&nbsp;source)</pre>
<div class="block">Returns a transferable object that contains a copy of the selected items in home
 and an image of the selected items.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>createTransferable</code>&nbsp;in class&nbsp;<code>javax.swing.TransferHandler</code></dd>
</dl>
</li>
</ul>
<a name="exportDone-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportDone</h4>
<pre>protected&nbsp;void&nbsp;exportDone(javax.swing.JComponent&nbsp;source,
                          java.awt.datatransfer.Transferable&nbsp;data,
                          int&nbsp;action)</pre>
<div class="block">Removes the copied element once moved.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>exportDone</code>&nbsp;in class&nbsp;<code>javax.swing.TransferHandler</code></dd>
</dl>
</li>
</ul>
<a name="canImportFlavor-java.awt.datatransfer.DataFlavor:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>canImportFlavor</h4>
<pre>protected&nbsp;boolean&nbsp;canImportFlavor(java.awt.datatransfer.DataFlavor[]&nbsp;flavors)</pre>
<div class="block">Returns <code>true</code> if <code>flavors</code> contains
 <a href="../../../../com/eteks/sweethome3d/swing/HomeTransferableList.html#HOME_FLAVOR"><code>HOME_FLAVOR</code></a> flavor
 or <code>DataFlavor.javaFileListFlavor</code> flavor.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#canImportFlavor-java.awt.datatransfer.DataFlavor:A-">canImportFlavor</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html" title="class in com.eteks.sweethome3d.swing">LocatedTransferHandler</a></code></dd>
</dl>
</li>
</ul>
<a name="dragEntered-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dragEntered</h4>
<pre>protected&nbsp;void&nbsp;dragEntered(javax.swing.JComponent&nbsp;destination,
                           java.awt.datatransfer.Transferable&nbsp;transferable,
                           int&nbsp;dragAction)</pre>
<div class="block">Notifies home controller that a drag operation started if
 <code>transferable</code> data contains <a href="../../../../com/eteks/sweethome3d/swing/HomeTransferableList.html#HOME_FLAVOR"><code>HOME_FLAVOR</code></a>
 flavor and destination is a plan.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#dragEntered-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">dragEntered</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html" title="class in com.eteks.sweethome3d.swing">LocatedTransferHandler</a></code></dd>
<dd><code>dragAction</code> - the current drag action (<code>TransferHandler.COPY</code>, <code>TransferHandler.MOVE</code>
    or <code>TransferHandler.LINK</code>)</dd>
</dl>
</li>
</ul>
<a name="dragMoved-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dragMoved</h4>
<pre>protected&nbsp;void&nbsp;dragMoved(javax.swing.JComponent&nbsp;destination,
                         java.awt.datatransfer.Transferable&nbsp;transferable,
                         int&nbsp;dragAction)</pre>
<div class="block">Called when <code>transferable</code> data moved in <code>destination</code> component
 during a drag and drop operation. Subclasses should override this method if they are
 interested by this event.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#dragMoved-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">dragMoved</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html" title="class in com.eteks.sweethome3d.swing">LocatedTransferHandler</a></code></dd>
<dd><code>dragAction</code> - the current drag action (<code>TransferHandler.COPY</code>, <code>TransferHandler.MOVE</code>
    or <code>TransferHandler.LINK</code>)</dd>
</dl>
</li>
</ul>
<a name="dragExited-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dragExited</h4>
<pre>protected&nbsp;void&nbsp;dragExited(javax.swing.JComponent&nbsp;destination)</pre>
<div class="block">Called once the cursor left <code>destination</code> component during a drag and drop operation.
 Subclasses should override this method if they are interested by this event.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#dragExited-javax.swing.JComponent-">dragExited</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html" title="class in com.eteks.sweethome3d.swing">LocatedTransferHandler</a></code></dd>
</dl>
</li>
</ul>
<a name="importData-javax.swing.JComponent-java.awt.datatransfer.Transferable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>importData</h4>
<pre>public&nbsp;boolean&nbsp;importData(javax.swing.JComponent&nbsp;destination,
                          java.awt.datatransfer.Transferable&nbsp;transferable)</pre>
<div class="block">Adds items contained in <code>transferable</code> to home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>importData</code>&nbsp;in class&nbsp;<code>javax.swing.TransferHandler</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanTransferHandler.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PlanRulerComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/PolylinePanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/PlanTransferHandler.html" target="_top">Frames</a></li>
<li><a href="PlanTransferHandler.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.TransferHandler">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.TransferHandler">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
