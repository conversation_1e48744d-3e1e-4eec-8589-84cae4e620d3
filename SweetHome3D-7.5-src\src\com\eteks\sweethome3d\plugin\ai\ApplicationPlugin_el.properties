# ApplicationPlugin_el.properties
# Sweet Home 3D AI Plugin Configuration - Greek
# Copyright (c) 2025 Samuel <PERSON>

# Plugin identification
name=AI Ανάλυση Κατόψεων
description=Αναλύστε κατόψεις χρησιμοποιώντας τεχνητή νοημοσύνη για να παρέχετε γνώσεις και προτάσεις βελτίωσης
provider=<PERSON>na

# Features
features=AI Ανάλυση, Γνώσεις Κατόψεων, Πολλαπλοί Πάροχοι AI, Έλεγχοι Ιδιωτικότητας

# Requirements
requirements=Σύνδεση στο διαδίκτυο για παρόχους AI cloud (προαιρετικό για τοπικούς παρόχους)

# UI Strings for internationalization
# Action properties
AIAction.Name=AI Ανάλυση
AIAction.ShortDescription=Ανάλυση κάτοψης με AI
AIAction.Menu=Εργαλεία

# Dialog titles
AIChatDialog.title=AI Ανάλυση Κατόψεων
AISettingsDialog.title=Ρυθμίσεις AI

# Button labels
button.send=Αποστολή
button.newAnalysis=Νέα Ανάλυση
button.settings=Ρυθμίσεις
button.testConnection=Δοκιμή Σύνδεσης
button.save=Αποθήκευση
button.cancel=Ακύρωση

# Labels
label.provider=Πάροχος:
label.baseUrl=Βασικό URL:
label.apiKey=Κλειδί API:
label.model=Μοντέλο:
label.temperature=Θερμοκρασία:
label.maxTokens=Μέγιστα Tokens:
label.status=Κατάσταση:

# Messages
message.analyzing=Ανάλυση κάτοψης...
message.processingQuestion=Επεξεργασία ερώτησης...
message.testingConnection=Δοκιμή σύνδεσης...
message.connectionSuccessful=Επιτυχής σύνδεση!
message.connectionFailed=Αποτυχία σύνδεσης: {0}
message.configurationSaved=Η διαμόρφωση αποθηκεύτηκε επιτυχώς
message.validationError=Σφάλματα διαμόρφωσης:\n{0}
message.noConfiguration=Ο πάροχος AI δεν έχει διαμορφωθεί. Παρακαλώ διαμορφώστε πρώτα τις ρυθμίσεις.

# Analysis prompt
analysis.prompt=Παρακαλώ αναλύστε αυτή την κάτοψη και παρέχετε ολοκληρωμένες γνώσεις συμπεριλαμβανομένων:\n1. Αποδοτικότητα διάταξης και χρήση χώρου\n2. Ροή κίνησης και μοτίβα κυκλοφορίας\n3. Ευκαιρίες φυσικού φωτισμού και αερισμού\n4. Σκέψεις προσβασιμότητας\n5. Λειτουργικές σχέσεις μεταξύ χώρων\n6. Προτάσεις βελτίωσης\n7. Συμμόρφωση με κοινά οικοδομικά πρότυπα\n8. Σκέψεις ενεργειακής αποδοτικότητας\n\nΠαρακαλώ παρέχετε συγκεκριμένες, εφαρμόσιμες συστάσεις που θα βελτίωναν τη λειτουργικότητα, την άνεση και την αισθητική ελκυστικότητα αυτού του χώρου.

# Error messages
error.analysisError=Σφάλμα ανάλυσης: {0}
error.configurationError=Σφάλμα διαμόρφωσης: {0}
error.connectionError=Σφάλμα σύνδεσης: {0}
error.invalidConfiguration=Μη έγκυρη διαμόρφωση
error.missingApiKey=Απαιτείται κλειδί API
error.missingBaseUrl=Απαιτείται βασικό URL
error.missingModel=Απαιτείται επιλογή μοντέλου

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Τοπικό)
provider.lmstudio=LM Studio (Τοπικό)
provider.anythingllm=AnythingLLM (Τοπικό)
provider.jan=Jan (Τοπικό)
provider.custom=Προσαρμοσμένο
