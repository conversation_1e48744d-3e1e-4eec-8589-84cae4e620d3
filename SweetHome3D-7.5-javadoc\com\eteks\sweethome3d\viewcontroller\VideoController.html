<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>VideoController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VideoController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":42,"i27":10,"i28":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/VideoController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/VideoController.html" target="_top">Frames</a></li>
<li><a href="VideoController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class VideoController" class="title">Class VideoController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.VideoController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">VideoController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">The controller of the video creation view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">VideoController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#VideoController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">VideoController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.VideoController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">VideoController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getAspectRatio--">getAspectRatio</a></span>()</code>
<div class="block">Returns the aspect ratio of the video.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getCameraPath--">getCameraPath</a></span>()</code>
<div class="block">Returns the camera path of the video.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getCeilingLightColor--">getCeilingLightColor</a></span>()</code>
<div class="block">Returns the edited ceiling light color.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getContentManager--">getContentManager</a></span>()</code>
<div class="block">Returns the content manager of this controller.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getFrameRate--">getFrameRate</a></span>()</code>
<div class="block">Returns the frame rate of the video.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height of the video.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getQuality--">getQuality</a></span>()</code>
<div class="block">Returns the rendering quality of the video.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getQualityLevelCount--">getQualityLevelCount</a></span>()</code>
<div class="block">Returns the maximum value for quality.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getRenderer--">getRenderer</a></span>()</code>
<div class="block">Returns the edited camera rendering engine.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getSpeed--">getSpeed</a></span>()</code>
<div class="block">Returns the preferred speed of movements in the video in m/s.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getTime--">getTime</a></span>()</code>
<div class="block">Returns the edited time in UTC time zone.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the width of the video.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.VideoController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">VideoController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setAspectRatio-com.eteks.sweethome3d.model.AspectRatio-">setAspectRatio</a></span>(<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;aspectRatio)</code>
<div class="block">Sets the aspect ratio of the video.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setCameraPath-java.util.List-">setCameraPath</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;cameraPath)</code>
<div class="block">Sets the camera locations of the video.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setCeilingLightColor-int-">setCeilingLightColor</a></span>(int&nbsp;ceilingLightColor)</code>
<div class="block">Sets the edited ceiling light color.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setFrameRate-int-">setFrameRate</a></span>(int&nbsp;frameRate)</code>
<div class="block">Sets the frame rate of the video.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setHeight-int-">setHeight</a></span>(int&nbsp;height)</code>
<div class="block">Sets the height of the video.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setHomeProperty-java.lang.String-java.lang.String-">setHomeProperty</a></span>(java.lang.String&nbsp;propertyName,
               java.lang.String&nbsp;propertyValue)</code>
<div class="block">Controls the change of value of a property in home.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setQuality-int-">setQuality</a></span>(int&nbsp;quality)</code>
<div class="block">Sets the rendering quality of the video.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setRenderer-java.lang.String-">setRenderer</a></span>(java.lang.String&nbsp;renderer)</code>
<div class="block">Sets the edited camera rendering engine.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setSpeed-float-">setSpeed</a></span>(float&nbsp;speed)</code>
<div class="block">Sets the preferred speed of movements in the video in m/s.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setTime-long-">setTime</a></span>(long&nbsp;time)</code>
<div class="block">Sets the edited time in UTC time zone.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setVisualProperty-java.lang.String-java.lang.Object-">setVisualProperty</a></span>(java.lang.String&nbsp;propertyName,
                 java.lang.Object&nbsp;propertyValue)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty</code></a> should be replaced by a call to
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setHomeProperty-java.lang.String-java.lang.String-"><code>setHomeProperty</code></a> to ensure the property can be easily saved and read.</span></div>
</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setWidth-int-">setWidth</a></span>(int&nbsp;width)</code>
<div class="block">Sets the width of the video.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates edited properties from the video creation preferences.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="VideoController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VideoController</h4>
<pre>public&nbsp;VideoController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="getContentManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentManager</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;getContentManager()</pre>
<div class="block">Returns the content manager of this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.VideoController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">VideoController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.VideoController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">VideoController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates edited properties from the video creation preferences.</div>
</li>
</ul>
<a name="setAspectRatio-com.eteks.sweethome3d.model.AspectRatio-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAspectRatio</h4>
<pre>public&nbsp;void&nbsp;setAspectRatio(<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;aspectRatio)</pre>
<div class="block">Sets the aspect ratio of the video.</div>
</li>
</ul>
<a name="getAspectRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAspectRatio</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;getAspectRatio()</pre>
<div class="block">Returns the aspect ratio of the video.</div>
</li>
</ul>
<a name="setFrameRate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrameRate</h4>
<pre>public&nbsp;void&nbsp;setFrameRate(int&nbsp;frameRate)</pre>
<div class="block">Sets the frame rate of the video.</div>
</li>
</ul>
<a name="getFrameRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrameRate</h4>
<pre>public&nbsp;int&nbsp;getFrameRate()</pre>
<div class="block">Returns the frame rate of the video.</div>
</li>
</ul>
<a name="setWidth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidth</h4>
<pre>public&nbsp;void&nbsp;setWidth(int&nbsp;width)</pre>
<div class="block">Sets the width of the video.</div>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;int&nbsp;getWidth()</pre>
<div class="block">Returns the width of the video.</div>
</li>
</ul>
<a name="setHeight-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public&nbsp;void&nbsp;setHeight(int&nbsp;height)</pre>
<div class="block">Sets the height of the video.</div>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;int&nbsp;getHeight()</pre>
<div class="block">Returns the height of the video.</div>
</li>
</ul>
<a name="setQuality-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setQuality</h4>
<pre>public&nbsp;void&nbsp;setQuality(int&nbsp;quality)</pre>
<div class="block">Sets the rendering quality of the video.</div>
</li>
</ul>
<a name="getQuality--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQuality</h4>
<pre>public&nbsp;int&nbsp;getQuality()</pre>
<div class="block">Returns the rendering quality of the video.</div>
</li>
</ul>
<a name="setSpeed-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpeed</h4>
<pre>public&nbsp;void&nbsp;setSpeed(float&nbsp;speed)</pre>
<div class="block">Sets the preferred speed of movements in the video in m/s.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getSpeed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpeed</h4>
<pre>public&nbsp;float&nbsp;getSpeed()</pre>
<div class="block">Returns the preferred speed of movements in the video in m/s.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getQualityLevelCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQualityLevelCount</h4>
<pre>public&nbsp;int&nbsp;getQualityLevelCount()</pre>
<div class="block">Returns the maximum value for quality.</div>
</li>
</ul>
<a name="getCameraPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCameraPath</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;getCameraPath()</pre>
<div class="block">Returns the camera path of the video.</div>
</li>
</ul>
<a name="setCameraPath-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCameraPath</h4>
<pre>public&nbsp;void&nbsp;setCameraPath(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;cameraPath)</pre>
<div class="block">Sets the camera locations of the video.</div>
</li>
</ul>
<a name="setTime-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTime</h4>
<pre>public&nbsp;void&nbsp;setTime(long&nbsp;time)</pre>
<div class="block">Sets the edited time in UTC time zone.</div>
</li>
</ul>
<a name="getTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTime</h4>
<pre>public&nbsp;long&nbsp;getTime()</pre>
<div class="block">Returns the edited time in UTC time zone.</div>
</li>
</ul>
<a name="setRenderer-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRenderer</h4>
<pre>public&nbsp;void&nbsp;setRenderer(java.lang.String&nbsp;renderer)</pre>
<div class="block">Sets the edited camera rendering engine.</div>
</li>
</ul>
<a name="getRenderer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRenderer</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRenderer()</pre>
<div class="block">Returns the edited camera rendering engine.</div>
</li>
</ul>
<a name="setCeilingLightColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingLightColor</h4>
<pre>public&nbsp;void&nbsp;setCeilingLightColor(int&nbsp;ceilingLightColor)</pre>
<div class="block">Sets the edited ceiling light color.</div>
</li>
</ul>
<a name="getCeilingLightColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingLightColor</h4>
<pre>public&nbsp;int&nbsp;getCeilingLightColor()</pre>
<div class="block">Returns the edited ceiling light color.</div>
</li>
</ul>
<a name="setVisualProperty-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisualProperty</h4>
<pre>public&nbsp;void&nbsp;setVisualProperty(java.lang.String&nbsp;propertyName,
                              java.lang.Object&nbsp;propertyValue)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty</code></a> should be replaced by a call to
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#setHomeProperty-java.lang.String-java.lang.String-"><code>setHomeProperty</code></a> to ensure the property can be easily saved and read.</span></div>
<div class="block">Controls the change of value of a visual property in home.</div>
</li>
</ul>
<a name="setHomeProperty-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setHomeProperty</h4>
<pre>public&nbsp;void&nbsp;setHomeProperty(java.lang.String&nbsp;propertyName,
                            java.lang.String&nbsp;propertyValue)</pre>
<div class="block">Controls the change of value of a property in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/VideoController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/VideoController.html" target="_top">Frames</a></li>
<li><a href="VideoController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
