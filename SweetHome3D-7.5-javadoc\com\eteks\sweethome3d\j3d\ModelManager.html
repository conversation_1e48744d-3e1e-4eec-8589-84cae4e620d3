<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>ModelManager (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ModelManager (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":9,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ModelManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Max3DSLoader.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/ModelManager.html" target="_top">Frames</a></li>
<li><a href="ModelManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class ModelManager" class="title">Class ModelManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.ModelManager</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ModelManager</span>
extends java.lang.Object</pre>
<div class="block">Singleton managing 3D models cache.
 This manager supports 3D models with an OBJ, DAE, 3DS or LWS format by default.
 Additional classes implementing Java 3D <code>Loader</code> interface may be
 specified in the <code>com.eteks.sweethome3d.j3d.additionalLoaderClasses</code>
 (separated by a space or a colon :) to enable the support of other formats.<br>
 Note: this class is compatible with Java 3D 1.3.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a></span></code>
<div class="block">An observer that receives model loading notifications.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#ARM_ON_BALL_PREFIX">ARM_ON_BALL_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#BALL_PREFIX">BALL_PREFIX</a></span></code>
<div class="block"><code>Node</code> user data prefix for ball / rotating  joints.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#DEFORMABLE_TRANSFORM_GROUP_SUFFIX">DEFORMABLE_TRANSFORM_GROUP_SUFFIX</a></span></code>
<div class="block">Deformable group suffix.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#EDGE_COLOR_MATERIAL_PREFIX">EDGE_COLOR_MATERIAL_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#HINGE_PREFIX">HINGE_PREFIX</a></span></code>
<div class="block"><code>Node</code> user data prefix for hinge / rotating opening joints.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#LIGHT_SHAPE_PREFIX">LIGHT_SHAPE_PREFIX</a></span></code>
<div class="block"><code>Shape3D</code> user data prefix for lights.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_ABDOMEN_CHEST_PREFIX">MANNEQUIN_ABDOMEN_CHEST_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_ABDOMEN_PELVIS_PREFIX">MANNEQUIN_ABDOMEN_PELVIS_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_ABDOMEN_PREFIX">MANNEQUIN_ABDOMEN_PREFIX</a></span></code>
<div class="block"><code>Node</code> user data prefix for mannequin parts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_CHEST_PREFIX">MANNEQUIN_CHEST_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_HEAD_PREFIX">MANNEQUIN_HEAD_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_ANKLE_PREFIX">MANNEQUIN_LEFT_ANKLE_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_ARM_PREFIX">MANNEQUIN_LEFT_ARM_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_ELBOW_PREFIX">MANNEQUIN_LEFT_ELBOW_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_FOOT_PREFIX">MANNEQUIN_LEFT_FOOT_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_FOREARM_PREFIX">MANNEQUIN_LEFT_FOREARM_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_HAND_PREFIX">MANNEQUIN_LEFT_HAND_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_HIP_PREFIX">MANNEQUIN_LEFT_HIP_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_KNEE_PREFIX">MANNEQUIN_LEFT_KNEE_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_LEG_PREFIX">MANNEQUIN_LEFT_LEG_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_SHOULDER_PREFIX">MANNEQUIN_LEFT_SHOULDER_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_THIGH_PREFIX">MANNEQUIN_LEFT_THIGH_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_LEFT_WRIST_PREFIX">MANNEQUIN_LEFT_WRIST_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_NECK_PREFIX">MANNEQUIN_NECK_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_PELVIS_PREFIX">MANNEQUIN_PELVIS_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_ANKLE_PREFIX">MANNEQUIN_RIGHT_ANKLE_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_ARM_PREFIX">MANNEQUIN_RIGHT_ARM_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_ELBOW_PREFIX">MANNEQUIN_RIGHT_ELBOW_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_FOOT_PREFIX">MANNEQUIN_RIGHT_FOOT_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_FOREARM_PREFIX">MANNEQUIN_RIGHT_FOREARM_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_HAND_PREFIX">MANNEQUIN_RIGHT_HAND_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_HIP_PREFIX">MANNEQUIN_RIGHT_HIP_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_KNEE_PREFIX">MANNEQUIN_RIGHT_KNEE_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_LEG_PREFIX">MANNEQUIN_RIGHT_LEG_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_SHOULDER_PREFIX">MANNEQUIN_RIGHT_SHOULDER_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_THIGH_PREFIX">MANNEQUIN_RIGHT_THIGH_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MANNEQUIN_RIGHT_WRIST_PREFIX">MANNEQUIN_RIGHT_WRIST_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MIRROR_ON_HINGE_PREFIX">MIRROR_ON_HINGE_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MIRROR_ON_RAIL_PREFIX">MIRROR_ON_RAIL_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#MIRROR_SHAPE_PREFIX">MIRROR_SHAPE_PREFIX</a></span></code>
<div class="block"><code>Shape3D</code> user data prefix for mirror shapes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#OPENING_ON_HINGE_PREFIX">OPENING_ON_HINGE_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#OPENING_ON_RAIL_PREFIX">OPENING_ON_RAIL_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#RAIL_PREFIX">RAIL_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#SPECIAL_SHAPE_PREFIX">SPECIAL_SHAPE_PREFIX</a></span></code>
<div class="block">Special shapes prefix;</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#SUB_TRANSFORMATION_SEPARATOR">SUB_TRANSFORMATION_SEPARATOR</a></span></code>
<div class="block"><code>Node</code> user data separator for sub transformations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#UNIQUE_RAIL_PREFIX">UNIQUE_RAIL_PREFIX</a></span></code>
<div class="block"><code>Node</code> user data prefix for rail / sliding opening joints.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#WINDOW_PANE_ON_HINGE_PREFIX">WINDOW_PANE_ON_HINGE_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#WINDOW_PANE_ON_RAIL_PREFIX">WINDOW_PANE_ON_RAIL_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#WINDOW_PANE_SHAPE_PREFIX">WINDOW_PANE_SHAPE_PREFIX</a></span></code>
<div class="block"><code>Shape3D</code> user data prefix for window pane shapes.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#checkAppearancesName-javax.media.j3d.Node-">checkAppearancesName</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Ensures that all the appearance of the children shapes of the
 given <code>node</code> have a name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#clear--">clear</a></span>()</code>
<div class="block">Shutdowns the multithreaded service that load models and clears loaded models cache.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>javax.media.j3d.Node</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#cloneNode-javax.media.j3d.Node-">cloneNode</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Returns a clone of the given <code>node</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#containsDeformableNode-javax.media.j3d.Node-">containsDeformableNode</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Returns <code>true</code> if the given <code>node</code> or its children contains at least a deformable group.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#containsNode-javax.media.j3d.Node-java.lang.String-">containsNode</a></span>(javax.media.j3d.Node&nbsp;node,
            java.lang.String&nbsp;prefix)</code>
<div class="block">Returns <code>true</code> if the given <code>node</code> or a node in its hierarchy
 contains a node which name, stored in user data, starts with <code>prefix</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.awt.geom.Area</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getAreaOnFloor-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getAreaOnFloor</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;staircase)</code>
<div class="block">Returns the area on the floor of the given staircase.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.awt.geom.Area</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getAreaOnFloor-javax.media.j3d.Node-">getAreaOnFloor</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Returns the 2D area of the 3D shapes children of the given <code>node</code>
 projected on the floor (plan y = 0).</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>javax.media.j3d.BoundingBox</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getBounds-javax.media.j3d.Node-">getBounds</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Returns the bounds of the 3D shapes of <code>node</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>javax.media.j3d.BoundingBox</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getBounds-javax.media.j3d.Node-javax.media.j3d.Transform3D-">getBounds</a></span>(javax.media.j3d.Node&nbsp;node,
         javax.media.j3d.Transform3D&nbsp;transformation)</code>
<div class="block">Returns the bounds of the 3D shapes of <code>node</code> with an additional <code>transformation</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>javax.vecmath.Point3f</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getCenter-javax.media.j3d.Node-">getCenter</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Returns the center of the bounds of <code>node</code> 3D shapes.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html" title="class in com.eteks.sweethome3d.j3d">ModelManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getInstance--">getInstance</a></span>()</code>
<div class="block">Returns an instance of this singleton.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getMaterials-javax.media.j3d.Node-">getMaterials</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Returns the materials used by the children shapes of the given <code>node</code>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getMaterials-javax.media.j3d.Node-boolean-java.lang.String-">getMaterials</a></span>(javax.media.j3d.Node&nbsp;node,
            boolean&nbsp;ignoreEdgeColorMaterial,
            java.lang.String&nbsp;creator)</code>
<div class="block">Returns the materials used by the children shapes of the given <code>node</code>,
 attributing their <code>creator</code> to them.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getMaterials-javax.media.j3d.Node-java.lang.String-">getMaterials</a></span>(javax.media.j3d.Node&nbsp;node,
            java.lang.String&nbsp;creator)</code>
<div class="block">Returns the materials used by the children shapes of the given <code>node</code>,
 attributing their <code>creator</code> to them.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>javax.media.j3d.Transform3D</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getNormalizedTransform-javax.media.j3d.Node-float:A:A-float-">getNormalizedTransform</a></span>(javax.media.j3d.Node&nbsp;node,
                      float[][]&nbsp;modelRotation,
                      float&nbsp;width)</code>
<div class="block">Returns a transform that will transform the model <code>node</code>
 to let it fill a box of the given <code>width</code> centered on the origin.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>javax.media.j3d.Transform3D</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getNormalizedTransform-javax.media.j3d.Node-float:A:A-float-boolean-">getNormalizedTransform</a></span>(javax.media.j3d.Node&nbsp;node,
                      float[][]&nbsp;modelRotation,
                      float&nbsp;width,
                      boolean&nbsp;modelCenteredAtOrigin)</code>
<div class="block">Returns a transform that will transform the model <code>node</code>
 to let it fill a box of the given <code>width</code> centered on the origin.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>javax.media.j3d.TransformGroup</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getNormalizedTransformGroup-javax.media.j3d.Node-float:A:A-float-">getNormalizedTransformGroup</a></span>(javax.media.j3d.Node&nbsp;node,
                           float[][]&nbsp;modelRotation,
                           float&nbsp;width)</code>
<div class="block">Returns a transform group that will transform the model <code>node</code>
 to let it fill a box of the given <code>width</code> centered on the origin.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>javax.media.j3d.TransformGroup</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getNormalizedTransformGroup-javax.media.j3d.Node-float:A:A-float-boolean-">getNormalizedTransformGroup</a></span>(javax.media.j3d.Node&nbsp;node,
                           float[][]&nbsp;modelRotation,
                           float&nbsp;width,
                           boolean&nbsp;modelCenteredAtOrigin)</code>
<div class="block">Returns a transform group that will transform the model <code>node</code>
 to let it fill a box of the given <code>width</code> centered on the origin.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.awt.Shape</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getShape-java.lang.String-">getShape</a></span>(java.lang.String&nbsp;svgPathShape)</code>
<div class="block">Returns the AWT shape matching the given <a href="http://www.w3.org/TR/SVG/paths.html">SVG path shape</a>.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>javax.vecmath.Vector3f</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getSize-javax.media.j3d.Node-">getSize</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Returns the size of 3D shapes of <code>node</code>.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>javax.vecmath.Vector3f</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#getSize-javax.media.j3d.Node-javax.media.j3d.Transform3D-">getSize</a></span>(javax.media.j3d.Node&nbsp;node,
       javax.media.j3d.Transform3D&nbsp;transformation)</code>
<div class="block">Returns the size of 3D shapes of <code>node</code> after an additional <code>transformation</code>.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>javax.media.j3d.BranchGroup</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#loadModel-com.eteks.sweethome3d.model.Content-">loadModel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns the node loaded synchronously from <code>content</code> with supported loaders.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#loadModel-com.eteks.sweethome3d.model.Content-boolean-com.eteks.sweethome3d.j3d.ModelManager.ModelObserver-">loadModel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
         boolean&nbsp;synchronous,
         <a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a>&nbsp;modelObserver)</code>
<div class="block">Reads a 3D node from <code>content</code> with supported loaders
 and notifies the loaded model to the given <code>modelObserver</code> once available.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html#loadModel-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.j3d.ModelManager.ModelObserver-">loadModel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
         <a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a>&nbsp;modelObserver)</code>
<div class="block">Reads asynchronously a 3D node from <code>content</code> with supported loaders
 and notifies the loaded model to the given <code>modelObserver</code> once available.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="SPECIAL_SHAPE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPECIAL_SHAPE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String SPECIAL_SHAPE_PREFIX</pre>
<div class="block">Special shapes prefix;</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.SPECIAL_SHAPE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="WINDOW_PANE_SHAPE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WINDOW_PANE_SHAPE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String WINDOW_PANE_SHAPE_PREFIX</pre>
<div class="block"><code>Shape3D</code> user data prefix for window pane shapes.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.WINDOW_PANE_SHAPE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MIRROR_SHAPE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MIRROR_SHAPE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MIRROR_SHAPE_PREFIX</pre>
<div class="block"><code>Shape3D</code> user data prefix for mirror shapes.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MIRROR_SHAPE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LIGHT_SHAPE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LIGHT_SHAPE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String LIGHT_SHAPE_PREFIX</pre>
<div class="block"><code>Shape3D</code> user data prefix for lights.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.LIGHT_SHAPE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_ABDOMEN_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_ABDOMEN_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_ABDOMEN_PREFIX</pre>
<div class="block"><code>Node</code> user data prefix for mannequin parts.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_ABDOMEN_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_CHEST_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_CHEST_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_CHEST_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_CHEST_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_PELVIS_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_PELVIS_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_PELVIS_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_PELVIS_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_NECK_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_NECK_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_NECK_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_NECK_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_HEAD_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_HEAD_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_HEAD_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_HEAD_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_SHOULDER_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_SHOULDER_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_SHOULDER_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_SHOULDER_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_ARM_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_ARM_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_ARM_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_ARM_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_ELBOW_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_ELBOW_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_ELBOW_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_ELBOW_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_FOREARM_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_FOREARM_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_FOREARM_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_FOREARM_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_WRIST_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_WRIST_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_WRIST_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_WRIST_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_HAND_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_HAND_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_HAND_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_HAND_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_HIP_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_HIP_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_HIP_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_HIP_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_THIGH_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_THIGH_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_THIGH_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_THIGH_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_KNEE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_KNEE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_KNEE_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_KNEE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_LEG_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_LEG_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_LEG_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_LEG_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_ANKLE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_ANKLE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_ANKLE_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_ANKLE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_LEFT_FOOT_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_LEFT_FOOT_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_LEFT_FOOT_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_LEFT_FOOT_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_SHOULDER_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_SHOULDER_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_SHOULDER_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_SHOULDER_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_ARM_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_ARM_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_ARM_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_ARM_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_ELBOW_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_ELBOW_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_ELBOW_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_ELBOW_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_FOREARM_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_FOREARM_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_FOREARM_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_FOREARM_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_WRIST_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_WRIST_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_WRIST_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_WRIST_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_HAND_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_HAND_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_HAND_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_HAND_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_HIP_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_HIP_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_HIP_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_HIP_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_THIGH_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_THIGH_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_THIGH_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_THIGH_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_KNEE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_KNEE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_KNEE_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_KNEE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_LEG_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_LEG_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_LEG_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_LEG_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_ANKLE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_ANKLE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_ANKLE_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_ANKLE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_RIGHT_FOOT_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_RIGHT_FOOT_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_RIGHT_FOOT_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_RIGHT_FOOT_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_ABDOMEN_CHEST_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_ABDOMEN_CHEST_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_ABDOMEN_CHEST_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_ABDOMEN_CHEST_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MANNEQUIN_ABDOMEN_PELVIS_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MANNEQUIN_ABDOMEN_PELVIS_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MANNEQUIN_ABDOMEN_PELVIS_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MANNEQUIN_ABDOMEN_PELVIS_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="BALL_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BALL_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String BALL_PREFIX</pre>
<div class="block"><code>Node</code> user data prefix for ball / rotating  joints.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.BALL_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ARM_ON_BALL_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ARM_ON_BALL_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String ARM_ON_BALL_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.ARM_ON_BALL_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HINGE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HINGE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String HINGE_PREFIX</pre>
<div class="block"><code>Node</code> user data prefix for hinge / rotating opening joints.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.HINGE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENING_ON_HINGE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENING_ON_HINGE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String OPENING_ON_HINGE_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.OPENING_ON_HINGE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="WINDOW_PANE_ON_HINGE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WINDOW_PANE_ON_HINGE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String WINDOW_PANE_ON_HINGE_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.WINDOW_PANE_ON_HINGE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MIRROR_ON_HINGE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MIRROR_ON_HINGE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MIRROR_ON_HINGE_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MIRROR_ON_HINGE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UNIQUE_RAIL_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNIQUE_RAIL_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String UNIQUE_RAIL_PREFIX</pre>
<div class="block"><code>Node</code> user data prefix for rail / sliding opening joints.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.UNIQUE_RAIL_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RAIL_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RAIL_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String RAIL_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.RAIL_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="OPENING_ON_RAIL_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPENING_ON_RAIL_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String OPENING_ON_RAIL_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.OPENING_ON_RAIL_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="WINDOW_PANE_ON_RAIL_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WINDOW_PANE_ON_RAIL_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String WINDOW_PANE_ON_RAIL_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.WINDOW_PANE_ON_RAIL_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MIRROR_ON_RAIL_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MIRROR_ON_RAIL_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String MIRROR_ON_RAIL_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.MIRROR_ON_RAIL_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="SUB_TRANSFORMATION_SEPARATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SUB_TRANSFORMATION_SEPARATOR</h4>
<pre>public static final&nbsp;java.lang.String SUB_TRANSFORMATION_SEPARATOR</pre>
<div class="block"><code>Node</code> user data separator for sub transformations.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.SUB_TRANSFORMATION_SEPARATOR">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DEFORMABLE_TRANSFORM_GROUP_SUFFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFORMABLE_TRANSFORM_GROUP_SUFFIX</h4>
<pre>public static final&nbsp;java.lang.String DEFORMABLE_TRANSFORM_GROUP_SUFFIX</pre>
<div class="block">Deformable group suffix.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.DEFORMABLE_TRANSFORM_GROUP_SUFFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="EDGE_COLOR_MATERIAL_PREFIX">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EDGE_COLOR_MATERIAL_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String EDGE_COLOR_MATERIAL_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.j3d.ModelManager.EDGE_COLOR_MATERIAL_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html" title="class in com.eteks.sweethome3d.j3d">ModelManager</a>&nbsp;getInstance()</pre>
<div class="block">Returns an instance of this singleton.</div>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public&nbsp;void&nbsp;clear()</pre>
<div class="block">Shutdowns the multithreaded service that load models and clears loaded models cache.</div>
</li>
</ul>
<a name="getSize-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSize</h4>
<pre>public&nbsp;javax.vecmath.Vector3f&nbsp;getSize(javax.media.j3d.Node&nbsp;node)</pre>
<div class="block">Returns the size of 3D shapes of <code>node</code>.
 This method computes the exact box that contains all the shapes,
 contrary to <code>node.getBounds()</code> that returns a bounding
 sphere for a scene.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model</dd>
</dl>
</li>
</ul>
<a name="getSize-javax.media.j3d.Node-javax.media.j3d.Transform3D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSize</h4>
<pre>public&nbsp;javax.vecmath.Vector3f&nbsp;getSize(javax.media.j3d.Node&nbsp;node,
                                      javax.media.j3d.Transform3D&nbsp;transformation)</pre>
<div class="block">Returns the size of 3D shapes of <code>node</code> after an additional <code>transformation</code>.
 This method computes the exact box that contains all the shapes,
 contrary to <code>node.getBounds()</code> that returns a bounding
 sphere for a scene.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model</dd>
<dd><code>transformation</code> - the transformation applied to the model
                 or <code>null</code> if no transformation should be applied to node.</dd>
</dl>
</li>
</ul>
<a name="getCenter-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCenter</h4>
<pre>public&nbsp;javax.vecmath.Point3f&nbsp;getCenter(javax.media.j3d.Node&nbsp;node)</pre>
<div class="block">Returns the center of the bounds of <code>node</code> 3D shapes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model</dd>
</dl>
</li>
</ul>
<a name="getBounds-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBounds</h4>
<pre>public&nbsp;javax.media.j3d.BoundingBox&nbsp;getBounds(javax.media.j3d.Node&nbsp;node)</pre>
<div class="block">Returns the bounds of the 3D shapes of <code>node</code>.
 This method computes the exact box that contains all the shapes,
 contrary to <code>node.getBounds()</code> that returns a bounding
 sphere for a scene.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model</dd>
</dl>
</li>
</ul>
<a name="getBounds-javax.media.j3d.Node-javax.media.j3d.Transform3D-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBounds</h4>
<pre>public&nbsp;javax.media.j3d.BoundingBox&nbsp;getBounds(javax.media.j3d.Node&nbsp;node,
                                             javax.media.j3d.Transform3D&nbsp;transformation)</pre>
<div class="block">Returns the bounds of the 3D shapes of <code>node</code> with an additional <code>transformation</code>.
 This method computes the exact box that contains all the shapes, contrary to <code>node.getBounds()</code>
 that returns a bounding sphere for a scene.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model</dd>
<dd><code>transformation</code> - the transformation applied to the model
                 or <code>null</code> if no transformation should be applied to node.</dd>
</dl>
</li>
</ul>
<a name="getNormalizedTransformGroup-javax.media.j3d.Node-float:A:A-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNormalizedTransformGroup</h4>
<pre>public&nbsp;javax.media.j3d.TransformGroup&nbsp;getNormalizedTransformGroup(javax.media.j3d.Node&nbsp;node,
                                                                  float[][]&nbsp;modelRotation,
                                                                  float&nbsp;width)</pre>
<div class="block">Returns a transform group that will transform the model <code>node</code>
 to let it fill a box of the given <code>width</code> centered on the origin.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model with any size and location</dd>
<dd><code>modelRotation</code> - the rotation applied to the model before normalization
                 or <code>null</code> if no transformation should be applied to node</dd>
<dd><code>width</code> - the width of the box</dd>
</dl>
</li>
</ul>
<a name="getNormalizedTransformGroup-javax.media.j3d.Node-float:A:A-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNormalizedTransformGroup</h4>
<pre>public&nbsp;javax.media.j3d.TransformGroup&nbsp;getNormalizedTransformGroup(javax.media.j3d.Node&nbsp;node,
                                                                  float[][]&nbsp;modelRotation,
                                                                  float&nbsp;width,
                                                                  boolean&nbsp;modelCenteredAtOrigin)</pre>
<div class="block">Returns a transform group that will transform the model <code>node</code>
 to let it fill a box of the given <code>width</code> centered on the origin.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model with any size and location</dd>
<dd><code>modelRotation</code> - the rotation applied to the model before normalization
                 or <code>null</code> if no transformation should be applied to node</dd>
<dd><code>width</code> - the width of the box</dd>
<dd><code>modelCenteredAtOrigin</code> - if <code>true</code> center will be moved to match the origin
                 after the model rotation is applied</dd>
</dl>
</li>
</ul>
<a name="getNormalizedTransform-javax.media.j3d.Node-float:A:A-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNormalizedTransform</h4>
<pre>public&nbsp;javax.media.j3d.Transform3D&nbsp;getNormalizedTransform(javax.media.j3d.Node&nbsp;node,
                                                          float[][]&nbsp;modelRotation,
                                                          float&nbsp;width)</pre>
<div class="block">Returns a transform that will transform the model <code>node</code>
 to let it fill a box of the given <code>width</code> centered on the origin.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model with any size and location</dd>
<dd><code>modelRotation</code> - the rotation applied to the model before normalization
                 or <code>null</code> if no transformation should be applied to node</dd>
<dd><code>width</code> - the width of the box</dd>
</dl>
</li>
</ul>
<a name="getNormalizedTransform-javax.media.j3d.Node-float:A:A-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNormalizedTransform</h4>
<pre>public&nbsp;javax.media.j3d.Transform3D&nbsp;getNormalizedTransform(javax.media.j3d.Node&nbsp;node,
                                                          float[][]&nbsp;modelRotation,
                                                          float&nbsp;width,
                                                          boolean&nbsp;modelCenteredAtOrigin)</pre>
<div class="block">Returns a transform that will transform the model <code>node</code>
 to let it fill a box of the given <code>width</code> centered on the origin.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model with any size and location</dd>
<dd><code>modelRotation</code> - the rotation applied to the model before normalization
                 or <code>null</code> if no transformation should be applied to node</dd>
<dd><code>width</code> - the width of the box</dd>
<dd><code>modelCenteredAtOrigin</code> - if <code>true</code> center will be moved to match the origin
                 after the model rotation is applied</dd>
</dl>
</li>
</ul>
<a name="loadModel-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.j3d.ModelManager.ModelObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadModel</h4>
<pre>public&nbsp;void&nbsp;loadModel(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                      <a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a>&nbsp;modelObserver)</pre>
<div class="block">Reads asynchronously a 3D node from <code>content</code> with supported loaders
 and notifies the loaded model to the given <code>modelObserver</code> once available.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - an object containing a model</dd>
<dd><code>modelObserver</code> - the observer that will be notified once the model is available
    or if an error happens</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if the current thread isn't the Event Dispatch Thread.</dd>
</dl>
</li>
</ul>
<a name="loadModel-com.eteks.sweethome3d.model.Content-boolean-com.eteks.sweethome3d.j3d.ModelManager.ModelObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadModel</h4>
<pre>public&nbsp;void&nbsp;loadModel(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                      boolean&nbsp;synchronous,
                      <a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a>&nbsp;modelObserver)</pre>
<div class="block">Reads a 3D node from <code>content</code> with supported loaders
 and notifies the loaded model to the given <code>modelObserver</code> once available.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - an object containing a model</dd>
<dd><code>synchronous</code> - if <code>true</code>, this method will return only once model content is loaded</dd>
<dd><code>modelObserver</code> - the observer that will be notified once the model is available
    or if an error happens. When the model is loaded synchronously, the observer will be notified
    in the same thread as the caller, otherwise the observer will be notified in the Event
    Dispatch Thread and this method must be called in Event Dispatch Thread too.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if synchronous is <code>false</code> and the current thread isn't
    the Event Dispatch Thread.</dd>
</dl>
</li>
</ul>
<a name="cloneNode-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cloneNode</h4>
<pre>public&nbsp;javax.media.j3d.Node&nbsp;cloneNode(javax.media.j3d.Node&nbsp;node)</pre>
<div class="block">Returns a clone of the given <code>node</code>.
 All the children and the attributes of the given node are duplicated except the geometries
 and the texture images of shapes.</div>
</li>
</ul>
<a name="loadModel-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadModel</h4>
<pre>public&nbsp;javax.media.j3d.BranchGroup&nbsp;loadModel(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)
                                      throws java.io.IOException</pre>
<div class="block">Returns the node loaded synchronously from <code>content</code> with supported loaders.
 This method is threadsafe and may be called from any thread.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - an object containing a model</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="containsNode-javax.media.j3d.Node-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsNode</h4>
<pre>public&nbsp;boolean&nbsp;containsNode(javax.media.j3d.Node&nbsp;node,
                            java.lang.String&nbsp;prefix)</pre>
<div class="block">Returns <code>true</code> if the given <code>node</code> or a node in its hierarchy
 contains a node which name, stored in user data, starts with <code>prefix</code>.</div>
</li>
</ul>
<a name="containsDeformableNode-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsDeformableNode</h4>
<pre>public&nbsp;boolean&nbsp;containsDeformableNode(javax.media.j3d.Node&nbsp;node)</pre>
<div class="block">Returns <code>true</code> if the given <code>node</code> or its children contains at least a deformable group.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - the root of a model</dd>
</dl>
</li>
</ul>
<a name="checkAppearancesName-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkAppearancesName</h4>
<pre>public&nbsp;void&nbsp;checkAppearancesName(javax.media.j3d.Node&nbsp;node)</pre>
<div class="block">Ensures that all the appearance of the children shapes of the
 given <code>node</code> have a name.</div>
</li>
</ul>
<a name="getMaterials-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaterials</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;getMaterials(javax.media.j3d.Node&nbsp;node)</pre>
<div class="block">Returns the materials used by the children shapes of the given <code>node</code>.</div>
</li>
</ul>
<a name="getMaterials-javax.media.j3d.Node-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaterials</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;getMaterials(javax.media.j3d.Node&nbsp;node,
                                   java.lang.String&nbsp;creator)</pre>
<div class="block">Returns the materials used by the children shapes of the given <code>node</code>,
 attributing their <code>creator</code> to them.</div>
</li>
</ul>
<a name="getMaterials-javax.media.j3d.Node-boolean-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaterials</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;getMaterials(javax.media.j3d.Node&nbsp;node,
                                   boolean&nbsp;ignoreEdgeColorMaterial,
                                   java.lang.String&nbsp;creator)</pre>
<div class="block">Returns the materials used by the children shapes of the given <code>node</code>,
 attributing their <code>creator</code> to them.</div>
</li>
</ul>
<a name="getAreaOnFloor-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaOnFloor</h4>
<pre>public&nbsp;java.awt.geom.Area&nbsp;getAreaOnFloor(javax.media.j3d.Node&nbsp;node)</pre>
<div class="block">Returns the 2D area of the 3D shapes children of the given <code>node</code>
 projected on the floor (plan y = 0).</div>
</li>
</ul>
<a name="getAreaOnFloor-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaOnFloor</h4>
<pre>public&nbsp;java.awt.geom.Area&nbsp;getAreaOnFloor(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;staircase)</pre>
<div class="block">Returns the area on the floor of the given staircase.</div>
</li>
</ul>
<a name="getShape-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getShape</h4>
<pre>public&nbsp;java.awt.Shape&nbsp;getShape(java.lang.String&nbsp;svgPathShape)</pre>
<div class="block">Returns the AWT shape matching the given <a href="http://www.w3.org/TR/SVG/paths.html">SVG path shape</a>.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ModelManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Max3DSLoader.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/ModelManager.html" target="_top">Frames</a></li>
<li><a href="ModelManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
