<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>CollectionChangeSupport (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CollectionChangeSupport (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CollectionChangeSupport.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/CollectionChangeSupport.html" target="_top">Frames</a></li>
<li><a href="CollectionChangeSupport.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class CollectionChangeSupport" class="title">Class CollectionChangeSupport&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.CollectionChangeSupport&lt;T&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">CollectionChangeSupport&lt;T&gt;</span>
extends java.lang.Object</pre>
<div class="block">A helper class for <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model"><code>CollectionListener</code></a> management.
 <code>T</code> is the type of item stored in the collection.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html#CollectionChangeSupport-java.lang.Object-">CollectionChangeSupport</a></span>(java.lang.Object&nbsp;source)</code>
<div class="block">Creates a collection change support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html#addCollectionListener-com.eteks.sweethome3d.model.CollectionListener-">addCollectionListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="type parameter in CollectionChangeSupport">T</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the <code>listener</code> in parameter to the list of listeners that may be notified.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html#fireCollectionChanged-T-com.eteks.sweethome3d.model.CollectionEvent.Type-">fireCollectionChanged</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="type parameter in CollectionChangeSupport">T</a>&nbsp;item,
                     <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;eventType)</code>
<div class="block">Fires a collection event about <code>item</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html#fireCollectionChanged-T-int-com.eteks.sweethome3d.model.CollectionEvent.Type-">fireCollectionChanged</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="type parameter in CollectionChangeSupport">T</a>&nbsp;item,
                     int&nbsp;index,
                     <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;eventType)</code>
<div class="block">Fires a collection event about <code>item</code> at a given <code>index</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html#removeCollectionListener-com.eteks.sweethome3d.model.CollectionListener-">removeCollectionListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="type parameter in CollectionChangeSupport">T</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the <code>listener</code> in parameter to the list of listeners that may be notified.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CollectionChangeSupport-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CollectionChangeSupport</h4>
<pre>public&nbsp;CollectionChangeSupport(java.lang.Object&nbsp;source)</pre>
<div class="block">Creates a collection change support.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - the collection to which data will be added.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addCollectionListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addCollectionListener</h4>
<pre>public&nbsp;void&nbsp;addCollectionListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="type parameter in CollectionChangeSupport">T</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the <code>listener</code> in parameter to the list of listeners that may be notified.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="removeCollectionListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeCollectionListener</h4>
<pre>public&nbsp;void&nbsp;removeCollectionListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="type parameter in CollectionChangeSupport">T</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the <code>listener</code> in parameter to the list of listeners that may be notified.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove. If it doesn't exist, it's simply ignored.</dd>
</dl>
</li>
</ul>
<a name="fireCollectionChanged-java.lang.Object-com.eteks.sweethome3d.model.CollectionEvent.Type-">
<!--   -->
</a><a name="fireCollectionChanged-T-com.eteks.sweethome3d.model.CollectionEvent.Type-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireCollectionChanged</h4>
<pre>public&nbsp;void&nbsp;fireCollectionChanged(<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="type parameter in CollectionChangeSupport">T</a>&nbsp;item,
                                  <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;eventType)</pre>
<div class="block">Fires a collection event about <code>item</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>item</code> - the added ore deleted item</dd>
<dd><code>eventType</code> - <code>CollectionEvent.Type.ADD</code> or <code>CollectionEvent.Type.DELETE</code></dd>
</dl>
</li>
</ul>
<a name="fireCollectionChanged-java.lang.Object-int-com.eteks.sweethome3d.model.CollectionEvent.Type-">
<!--   -->
</a><a name="fireCollectionChanged-T-int-com.eteks.sweethome3d.model.CollectionEvent.Type-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>fireCollectionChanged</h4>
<pre>public&nbsp;void&nbsp;fireCollectionChanged(<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="type parameter in CollectionChangeSupport">T</a>&nbsp;item,
                                  int&nbsp;index,
                                  <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;eventType)</pre>
<div class="block">Fires a collection event about <code>item</code> at a given <code>index</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>item</code> - the added ore deleted item</dd>
<dd><code>index</code> - the optional index at which the item was added or deleted</dd>
<dd><code>eventType</code> - <code>CollectionEvent.Type.ADD</code> or <code>CollectionEvent.Type.DELETE</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CollectionChangeSupport.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/CollectionChangeSupport.html" target="_top">Frames</a></li>
<li><a href="CollectionChangeSupport.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
