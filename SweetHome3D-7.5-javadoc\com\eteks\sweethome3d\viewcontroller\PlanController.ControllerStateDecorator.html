<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>PlanController.ControllerStateDecorator (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PlanController.ControllerStateDecorator (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanController.ControllerStateDecorator.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html" target="_top">Frames</a></li>
<li><a href="PlanController.ControllerStateDecorator.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class PlanController.ControllerStateDecorator" class="title">Class PlanController.ControllerStateDecorator</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.PlanController.ControllerStateDecorator</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a></dd>
</dl>
<hr>
<br>
<pre>protected abstract static class <span class="typeNameLabel">PlanController.ControllerStateDecorator</span>
extends <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></pre>
<div class="block">A decorator on controller state, useful to change the behavior of an existing state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#ControllerStateDecorator-com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState-">ControllerStateDecorator</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;state)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#deleteSelection--">deleteSelection</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#enter--">enter</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#escape--">escape</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#exit--">exit</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#getMode--">getMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#isBasePlanModificationState--">isBasePlanModificationState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#isModificationState--">isModificationState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#moveMouse-float-float-">moveMouse</a></span>(float&nbsp;x,
         float&nbsp;y)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#moveSelection-float-float-">moveSelection</a></span>(float&nbsp;dx,
             float&nbsp;dy)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#pressMouse-float-float-int-boolean-boolean-">pressMouse</a></span>(float&nbsp;x,
          float&nbsp;y,
          int&nbsp;clickCount,
          boolean&nbsp;shiftDown,
          boolean&nbsp;duplicationActivated)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#releaseMouse-float-float-">releaseMouse</a></span>(float&nbsp;x,
            float&nbsp;y)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#setAlignmentActivated-boolean-">setAlignmentActivated</a></span>(boolean&nbsp;alignmentActivated)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#setDuplicationActivated-boolean-">setDuplicationActivated</a></span>(boolean&nbsp;duplicationActivated)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#setEditionActivated-boolean-">setEditionActivated</a></span>(boolean&nbsp;editionActivated)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#toggleMagnetism-boolean-">toggleMagnetism</a></span>(boolean&nbsp;magnetismToggled)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#updateEditableProperty-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">updateEditableProperty</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;editableField,
                      java.lang.Object&nbsp;value)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#zoom-float-">zoom</a></span>(float&nbsp;factor)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ControllerStateDecorator-com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ControllerStateDecorator</h4>
<pre>public&nbsp;ControllerStateDecorator(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;state)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="enter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enter</h4>
<pre>public&nbsp;void&nbsp;enter()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#enter--">enter</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="exit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exit</h4>
<pre>public&nbsp;void&nbsp;exit()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#exit--">exit</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;getMode()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#getMode--">getMode</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMode</h4>
<pre>public&nbsp;void&nbsp;setMode(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="isModificationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isModificationState</h4>
<pre>public&nbsp;boolean&nbsp;isModificationState()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#isModificationState--">isModificationState</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="isBasePlanModificationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBasePlanModificationState</h4>
<pre>public&nbsp;boolean&nbsp;isBasePlanModificationState()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#isBasePlanModificationState--">isBasePlanModificationState</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="deleteSelection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteSelection</h4>
<pre>public&nbsp;void&nbsp;deleteSelection()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#deleteSelection--">deleteSelection</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="escape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>escape</h4>
<pre>public&nbsp;void&nbsp;escape()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#escape--">escape</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="moveSelection-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveSelection</h4>
<pre>public&nbsp;void&nbsp;moveSelection(float&nbsp;dx,
                          float&nbsp;dy)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#moveSelection-float-float-">moveSelection</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="toggleMagnetism-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleMagnetism</h4>
<pre>public&nbsp;void&nbsp;toggleMagnetism(boolean&nbsp;magnetismToggled)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#toggleMagnetism-boolean-">toggleMagnetism</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="setAlignmentActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignmentActivated</h4>
<pre>public&nbsp;void&nbsp;setAlignmentActivated(boolean&nbsp;alignmentActivated)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#setAlignmentActivated-boolean-">setAlignmentActivated</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="setDuplicationActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuplicationActivated</h4>
<pre>public&nbsp;void&nbsp;setDuplicationActivated(boolean&nbsp;duplicationActivated)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#setDuplicationActivated-boolean-">setDuplicationActivated</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="setEditionActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditionActivated</h4>
<pre>public&nbsp;void&nbsp;setEditionActivated(boolean&nbsp;editionActivated)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#setEditionActivated-boolean-">setEditionActivated</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="updateEditableProperty-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateEditableProperty</h4>
<pre>public&nbsp;void&nbsp;updateEditableProperty(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;editableField,
                                   java.lang.Object&nbsp;value)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#updateEditableProperty-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">updateEditableProperty</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="pressMouse-float-float-int-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pressMouse</h4>
<pre>public&nbsp;void&nbsp;pressMouse(float&nbsp;x,
                       float&nbsp;y,
                       int&nbsp;clickCount,
                       boolean&nbsp;shiftDown,
                       boolean&nbsp;duplicationActivated)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#pressMouse-float-float-int-boolean-boolean-">pressMouse</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="releaseMouse-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>releaseMouse</h4>
<pre>public&nbsp;void&nbsp;releaseMouse(float&nbsp;x,
                         float&nbsp;y)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#releaseMouse-float-float-">releaseMouse</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="moveMouse-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveMouse</h4>
<pre>public&nbsp;void&nbsp;moveMouse(float&nbsp;x,
                      float&nbsp;y)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#moveMouse-float-float-">moveMouse</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
<a name="zoom-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>zoom</h4>
<pre>public&nbsp;void&nbsp;zoom(float&nbsp;factor)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#zoom-float-">zoom</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanController.ControllerStateDecorator.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html" target="_top">Frames</a></li>
<li><a href="PlanController.ControllerStateDecorator.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
