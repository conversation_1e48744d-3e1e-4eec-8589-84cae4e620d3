# ApplicationPlugin_hu.properties
# Sweet Home 3D AI Plugin Configuration - Hungarian
# Copyright (c) 2025 Samuel <PERSON>

# Plugin identification
name=AI Alaprajz Elemzés
description=Elemezze az alaprajzokat mesterséges intelligencia segítségével, hogy betekintést és fejlesztési javaslatokat kapjon
provider=<PERSON>

# Features
features=AI Elemzés, Alaprajz Betekintések, Több AI Szolgáltató, Adatvédelmi Vezérlők

# Requirements
requirements=Internetkapcsolat szükséges a felhő AI szolgáltatókhoz (opcionális a helyi szolgáltatókhoz)

# UI Strings for internationalization
# Action properties
AIAction.Name=AI Elemzés
AIAction.ShortDescription=Alaprajz elemzése AI-val
AIAction.Menu=Eszközök

# Dialog titles
AIChatDialog.title=AI Alaprajz Elemzés
AISettingsDialog.title=AI Beállítások

# Button labels
button.send=Küldés
button.newAnalysis=<PERSON>j Elemzés
button.settings=Beáll<PERSON>tások
button.testConnection=Kapcsolat Tesztelése
button.save=Mentés
button.cancel=Mégse

# Labels
label.provider=Szolgáltató:
label.baseUrl=Alap URL:
label.apiKey=API Kulcs:
label.model=Modell:
label.temperature=Hőmérséklet:
label.maxTokens=Max Tokenek:
label.status=Állapot:

# Messages
message.analyzing=Alaprajz elemzése...
message.processingQuestion=Kérdés feldolgozása...
message.testingConnection=Kapcsolat tesztelése...
message.connectionSuccessful=Kapcsolat sikeres!
message.connectionFailed=Kapcsolat sikertelen: {0}
message.configurationSaved=Konfiguráció sikeresen mentve
message.validationError=Konfigurációs hibák:\n{0}
message.noConfiguration=AI szolgáltató nincs konfigurálva. Kérjük, először konfigurálja a beállításokat.

# Analysis prompt
analysis.prompt=Kérjük, elemezze ezt az alaprajzot és nyújtson átfogó betekintést, beleértve:\n1. Elrendezés hatékonyság és térhasználat\n2. Forgalmi áramlás és keringési minták\n3. Természetes világítási és szellőzési lehetőségek\n4. Akadálymentességi megfontolások\n5. Funkcionális kapcsolatok a terek között\n6. Fejlesztési javaslatok\n7. Közös építési szabványoknak való megfelelés\n8. Energiahatékonysági megfontolások\n\nKérjük, adjon konkrét, végrehajtható ajánlásokat, amelyek javítanák ennek a térnek a funkcionalitását, kényelmét és esztétikai vonzerejét.

# Error messages
error.analysisError=Elemzési hiba: {0}
error.configurationError=Konfigurációs hiba: {0}
error.connectionError=Kapcsolati hiba: {0}
error.invalidConfiguration=Érvénytelen konfiguráció
error.missingApiKey=API kulcs szükséges
error.missingBaseUrl=Alap URL szükséges
error.missingModel=Modell kiválasztása szükséges

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Helyi)
provider.lmstudio=LM Studio (Helyi)
provider.anythingllm=AnythingLLM (Helyi)
provider.jan=Jan (Helyi)
provider.custom=Egyéni
