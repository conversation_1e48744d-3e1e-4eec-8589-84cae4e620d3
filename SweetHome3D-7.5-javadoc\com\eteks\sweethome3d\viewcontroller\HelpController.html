<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>HelpController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HelpController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HelpController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HelpController.html" target="_top">Frames</a></li>
<li><a href="HelpController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class HelpController" class="title">Class HelpController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.HelpController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HelpController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for Sweet Home 3D help view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HelpController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#HelpController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">HelpController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.HelpController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HelpController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#displayView--">displayView</a></span>()</code>
<div class="block">Displays the help view controlled by this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.net.URL</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#getBrowserPage--">getBrowserPage</a></span>()</code>
<div class="block">Returns the browser page.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.net.URL</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#getHelpPage--">getHelpPage</a></span>()</code>
<div class="block">Returns the current page.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#getHighlightedText--">getHighlightedText</a></span>()</code>
<div class="block">Returns the highlighted text.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#isBrowserPage-java.net.URL-">isBrowserPage</a></span>(java.net.URL&nbsp;page)</code>
<div class="block">Returns <code>true</code> if the given <code>page</code> should be displayed
 by the system browser rather than by the help view.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#isNextPageEnabled--">isNextPageEnabled</a></span>()</code>
<div class="block">Returns whether a next page is available or not.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#isPreviousPageEnabled--">isPreviousPageEnabled</a></span>()</code>
<div class="block">Returns whether a previous page is available or not.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.HelpController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HelpController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#search-java.lang.String-">search</a></span>(java.lang.String&nbsp;searchedText)</code>
<div class="block">Searches <code>searchedText</code> in help documents and displays
 the result.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#setHighlightedText-java.lang.String-">setHighlightedText</a></span>(java.lang.String&nbsp;highlightedText)</code>
<div class="block">Sets the highlighted text.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#showNext--">showNext</a></span>()</code>
<div class="block">Controls the display of next page.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#showPage-java.net.URL-">showPage</a></span>(java.net.URL&nbsp;page)</code>
<div class="block">Controls the display of the given <code>page</code>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#showPrevious--">showPrevious</a></span>()</code>
<div class="block">Controls the display of previous page.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HelpController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HelpController</h4>
<pre>public&nbsp;HelpController(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView()</pre>
<div class="block">Displays the help view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.HelpController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HelpController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.HelpController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">HelpController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="getHelpPage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHelpPage</h4>
<pre>public&nbsp;java.net.URL&nbsp;getHelpPage()</pre>
<div class="block">Returns the current page.</div>
</li>
</ul>
<a name="getBrowserPage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBrowserPage</h4>
<pre>public&nbsp;java.net.URL&nbsp;getBrowserPage()</pre>
<div class="block">Returns the browser page.</div>
</li>
</ul>
<a name="isPreviousPageEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPreviousPageEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isPreviousPageEnabled()</pre>
<div class="block">Returns whether a previous page is available or not.</div>
</li>
</ul>
<a name="isNextPageEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNextPageEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isNextPageEnabled()</pre>
<div class="block">Returns whether a next page is available or not.</div>
</li>
</ul>
<a name="setHighlightedText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHighlightedText</h4>
<pre>public&nbsp;void&nbsp;setHighlightedText(java.lang.String&nbsp;highlightedText)</pre>
<div class="block">Sets the highlighted text.</div>
</li>
</ul>
<a name="getHighlightedText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHighlightedText</h4>
<pre>public&nbsp;java.lang.String&nbsp;getHighlightedText()</pre>
<div class="block">Returns the highlighted text.</div>
</li>
</ul>
<a name="showPrevious--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showPrevious</h4>
<pre>public&nbsp;void&nbsp;showPrevious()</pre>
<div class="block">Controls the display of previous page.</div>
</li>
</ul>
<a name="showNext--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showNext</h4>
<pre>public&nbsp;void&nbsp;showNext()</pre>
<div class="block">Controls the display of next page.</div>
</li>
</ul>
<a name="showPage-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showPage</h4>
<pre>public&nbsp;void&nbsp;showPage(java.net.URL&nbsp;page)</pre>
<div class="block">Controls the display of the given <code>page</code>.</div>
</li>
</ul>
<a name="isBrowserPage-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBrowserPage</h4>
<pre>protected&nbsp;boolean&nbsp;isBrowserPage(java.net.URL&nbsp;page)</pre>
<div class="block">Returns <code>true</code> if the given <code>page</code> should be displayed
 by the system browser rather than by the help view.
 By default, it returns <code>true</code> if the <code>page</code> protocol is http or https.</div>
</li>
</ul>
<a name="search-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>search</h4>
<pre>public&nbsp;void&nbsp;search(java.lang.String&nbsp;searchedText)</pre>
<div class="block">Searches <code>searchedText</code> in help documents and displays
 the result.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HelpController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HelpController.html" target="_top">Frames</a></li>
<li><a href="HelpController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
