This guide details how to develop plug-ins for Sweet Home 3D, enabling Java programmers to add new features without modifying the core application.

Key steps and information include:
- **Development Tools**: Requires Java knowledge and an IDE (Eclipse recommended). Set up Eclipse, create a Java project, and add the `SweetHome3D.jar` library to the build path.
- **Programming a Plug-in**: Create a class extending `com.eteks.sweethome3d.plugin.Plugin` and an inner class extending `com.eteks.sweethome3d.plugin.PluginAction`. Implement the `execute()` method for the plug-in's logic and configure UI properties (e.g., menu item, button). Create an `ApplicationPlugin.properties` file for plug-in metadata.
- **Building and Testing**: Export the compiled classes and `properties` file into a JAR. Place the JAR in the Sweet Home 3D `plugins` folder. The guide provides instructions for running and debugging the plug-in within Sweet Home 3D from Eclipse.
- **Deployment**: Plug-ins can be deployed by copying the JAR to the user's `plugins` folder or by using `.sh3p` files. Emphasizes maintaining Java 5 (or 1.8+) compatibility.
- **Going Further**: Provides links to Sweet Home 3D API Javadoc, an explanation of the MVC Model layer architecture, plug-in class architecture, best practices for localization, and how to contribute plug-ins to the community.

----

[Introduction](#introduction)  
[Installing development tools](#installingTools)  
[Programming a plug-in](#programmingPlugin)  
[Going further](#goingFurther)

### ![](https://www.sweethome3d.com/images/bullet.gif)Introduction

> From version 1.5, it's possible to add new features to [Sweet Home 3D](https://www.sweethome3d.com/index.jsp) with plug-in files placed in your [plug-ins folder](#deployingPlugin). This allows Java programmers to develop and distribute new features for Sweet Home 3D without modifying the source files of the current version (which is good for upward compatibility), and without delivering a full version of the program (which is good for delivery size).  
> This document describes the [tools](#installingTools) required to create plug-ins, then shows how to [program a plug-in](#programmingPlugin) that computes the maximum volume of the movable furniture added to a home, and finally gives some [additional information](#goingFurther) that will help you to go further.

### ![](https://www.sweethome3d.com/images/bullet.gif)Installing development tools

> If Sweet Home 3D targets a general audience, developing plug-ins requires special skills, and you should know how to program in [Java](https://java.sun.com/) with an IDE, before going further. This guide shows how to build a plug-in with [Eclipse](https://www.eclipse.org/), but you can use the IDE of your choice, or no IDE at all.
> 
> #### Download and install Eclipse
> 
> First download Eclipse from [https://www.eclipse.org/](https://www.eclipse.org/). The version named *Eclipse IDE for Java Developers* is enough to develop a plug-in, but you can download any version for Java development.  
> Once downloaded, installing Eclipse is very simple: just uncompress the archive you'll get, open the eclipse folder and depending on your system, run the file named `eclipse.exe` (under Windows), `eclipse.app` (under Mac OS X) or `eclipse` (under Linux).  
> At the first run, Eclipse will require you to choose a *workspace* folder, where will be stored plug-in projects.  
> Once done, choose *File > New > Project* out of the menu to create a new project, select *Java > Java project* in the *New project* wizard that will be displayed, enter VolumePlugin as project name and click on *Finish* button. Finally, close the *Welcome* tab to discover your workspace as shown in figure 1.
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/eclipse.png)  
> *Figure 1. Eclipse workspace*
> 
> #### Download and install Sweet Home 3D library
> 
> The development of a plug-in is based on some classes of Sweet Home 3D that Eclipse must know to be able to build your project. The easiest way to add Sweet Home 3D classes to Eclipse is to download the JAR executable version of Sweet Home 3D available at [https://sourceforge.net/projects/sweethome3d/files/SweetHome3D/SweetHome3D-7.5/SweetHome3D-7.5.jar/download](https://sourceforge.net/projects/sweethome3d/files/SweetHome3D/SweetHome3D-7.5/SweetHome3D-7.5.jar/download). Once downloaded, drag and drop the file SweetHome3D-7.5.jar on the *VolumePlugin* project icon in the *Package Explorer* view of Eclipse, and choose the item *Build Path > Add to Build Path* in the contextual menu of SweetHome3D-7.5.jar file, as shown in figure 2.
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/addToBuildPath.png)  
> *Figure 2. Adding SweetHome3D-7.5.jar to Build Path*

### ![](https://www.sweethome3d.com/images/bullet.gif)Programming a plug-in

> Now that you installed the required tools, let's see how you can program your first plug-in for Sweet Home 3D.
> 
> #### Creating the plug-in class
> 
> First, create a new subclass of com.eteks.sweethome3d.plugin.Plugin by choosing *File > New > Class* menu item in Eclipse.
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/newJavaClass.png)  
> *Figure 3. Creating a new class*
> 
> In the *New Java Class* dialog, enter VolumePlugin as the class name, enter a package (here the chosen package was com.eteks.test), and choose com.eteks.sweethome3d.plugin.Plugin as the super class of VolumePlugin. Once done, click on *Finish*. Eclipse will create the file of the new class with the following content:
> 
> package com.eteks.test;  
> import com.eteks.sweethome3d.plugin.Plugin;  
> import com.eteks.sweethome3d.plugin.PluginAction;  
> public class **VolumePlugin** extends **Plugin** {  
>     @Override  
>     public PluginAction\[\] **getActions**() {  
>         // TODO Auto-generated method stub        return null;  
>     }  
> }
> 
> As you can guess from the TODO comment, you must now change the implementation of the getActions method to return a plug-in action able to compute the volume of the movable furniture. Replace return null; by the following statement:
> 
>         return new **PluginAction** \[\] {new **VolumeAction**()}; 
> 
> and choose *Edition > Quick Fix* out of Eclipse menu to create the missing class VolumeAction, as shown in figure 4.
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/quickFix.png)  
> *Figure 4. Using Quick fix to generate a missing class*
> 
> In the *New Java Class* dialog that appears, select the *Enclosing type* check box to create an inner class of VolumePlugin and click on *Finish*. This will create the class VolumeAction that inherits from com.eteks.sweethome3d.plugin.PluginAction class and contains an empty execute method:
> 
>     public class **VolumeAction** extends **PluginAction** {  
>         @Override  
>         public void **execute**() {  
>             // TODO Auto-generated method stub  
>         }  
>     }
> 
> This method is the one that Sweet Home 3D will call when the user will launch the plug-in action ; thus this is the place where you must implement how to compute the volume of the furniture and display it:
> 
>     public class **VolumeAction** extends **PluginAction** {  
>         @Override  
>         public void **execute**() {
>             float volumeInCm3 = 0;  
>             // Compute the sum of the volume of the bounding box of   
>             // each movable piece of furniture in home  
>             for (PieceOfFurniture piece : **getHome**().**getFurniture**()) {  
>                 if (piece.**isMovable**()) {  
>                     volumeInCm3 += piece.**getWidth**()   
>                                    \* piece.**getDepth**()   
>                                    \* piece.**getHeight**();  
>                 }  
>             }  
>               
>             // Display the result in a message box (\\u00b3 is for 3 in supercript)  
>             String message = String.**format**(  
>                 "The maximum volume of the movable furniture in home is %.2f m\\u00b3.",   
>                 volumeInCm3 / 1000000);  
>             JOptionPane.**showMessageDialog**(null, message);
>         }  
>     }
> 
> Now that you specified what you want the plug-in to do, you must describe how the user will launch this new action. You have the choice between adding a new **menu item** to a menu, and/or a new **button** to the tools bar. This choice is done by setting the appropriate properties of the plug-in action at its creation. For example, if you want the users to launch the volume action with the menu item *Compute volume* found in the *Tools* menu, you'll add the following constructor to VolumnAction class:
> 
>         public **VolumeAction**() {  
>            **putPropertyValue**(Property.NAME, "Compute volume");  
>            **putPropertyValue**(Property.MENU, "Tools");
>            // Enables the action by default           **setEnabled**(true);  
>         }
> 
> The [VolumePlugin](https://www.sweethome3d.com/examples/VolumePlugin/VolumePlugin.java) plug-in class is now programmed, and almost ready to work as a plug-in in Sweet Home 3D. The two last things to do are:
> 
> - creating an ApplicationPlugin.properties description file,
> - putting the files together in a JAR file.
> 
> #### Creating the plug-in description file
> 
> An ApplicationPlugin.properties file describes the plug-in name, its class, the Sweet Home 3D and Java minimum versions under which it is supported, and legal stuff. Choose *File > New > File* from Eclipse menu, enter the file name ApplicationPlugin.properties and click on *Finish*, as shown in figure 5.
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/newFile.png)  
> *Figure 5. Creating a new file*
> 
> Then enter the [following description](https://www.sweethome3d.com/examples/VolumePlugin/ApplicationPlugin.properties) in the new file and save it:
> 
> **name**\=Movable furniture volume  
> **class**\=com.eteks.test.VolumePlugin  
> **description**\=Computes the volume of the movable furniture in home  
> **version**\=1.0  
> **license**\=GNU GPL  
> **provider**\=(C) Copyrights 2024 Space Mushrooms  
> **applicationMinimumVersion**\=1.5  
> **javaMinimumVersion**\=1.5
> 
> #### Creating the plug-in JAR
> 
> The plug-in JAR contains the class files created from the compilation of the VolumePlugin.java file, and the ApplicationPlugin.properties file. As Eclipse compiles a Java file as soon as you save it, you just have to choose *File > Export...* from the menu and select *Java > JAR file* in the *Export* dialog that will be displayed. In the *Jar Export* wizard that appears as shown in figure 6, select the project check box and enter the path of a JAR file placed in the Sweet Home 3D plug-ins folder. This appropriate folder depends on your system as follows:
> 
> - under Windows Vista / 7 / 8 / 10 / 11, this folder is C:\\Users\\<USER>\\AppData\\Roaming\\eTeks\\Sweet Home 3D\\plugins,
> - under Windows XP and previous versions of Windows, this folder is C:\\Documents and Settings\\*user*\\Application Data\\eTeks\\Sweet Home 3D\\plugins,
> - under macOS, it's the subfolder Library/Application Support/eTeks/Sweet Home 3D/plugins of your user folder,
> - under Linux and other Unix, it's the subfolder .eteks/sweethome3d/plugins of your user folder.
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/jarExport.png)  
> *Figure 6. Exporting to a JAR file*
> 
> #### Testing the plug-in
> 
> The [plug-in](https://www.sweethome3d.com/examples/VolumePlugin/VolumePlugin.jar) you developed will run in Sweet Home 3D, either with the [Java Web Start](https://www.sweethome3d.com/SweetHome3D.jnlp) version, the [installers](https://www.sweethome3d.com/%20https://downloads.sourceforge.net/sweethome3d/) version, or the [SweetHome3D-7.5.jar](https://sourceforge.net/projects/sweethome3d/files/SweetHome3D/SweetHome3D-7.5/SweetHome3D-7.5.jar/download) you downloaded previously. As the latest one is an executable JAR, you can run it by double-clicking on it or with the following command:
> 
> java -jar /*path*/*to*/SweetHome3D-7.5.jar
> 
> As long as you're testing, you will probably prefer to run Sweet Home 3D with this command, to be able to read in the console the stack trace of the exceptions thrown during the execution of your plug-in.
> 
> Once Sweet Home 3D is launched, you'll see the new menu and its item appear as shown in figure 7:
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/pluginMenu.png)  
> *Figure 7. Plug-in menu*
> 
> If you choose the new menu item for the [home example](https://www.sweethome3d.com/examples/userGuideExample.sh3d) created in [user's guide](https://www.sweethome3d.com/userGuide.jsp), you'll get the following result:
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/pluginInAction.png)  
> *Figure 8. Plug-in in action*
> 
> #### Debugging the plug-in
> 
> If you need to debug your plug-in from Eclipse, create a debug configuration by following these steps:
> 
> - Choose *Run > Debug Configurations...* from the menu, select *Java Application* item in the available configurations list of the *Debug configurations* dialog box, click on the *New* button at the top left and enter a name for the configuration.
> - Click on the *Search...* button at the right of the *Main class* text field and double click on the *SweetHome3DBootstrap* class among the proposed classes.
> 
> ![Debug configuration](https://www.sweethome3d.com/images/pluginDeveloperGuide/debugConfiguration.png)  
> *Figure 9. Creating a debug configuration*
> 
> - Click on the *Classpath* tab, select the *VolumePlugin (default classpath)* sub item of the *User Entries* item in the *Classpath* list and click on the *Remove* button.
> - Click on the *User Entries* item in the *Classpath* list, click on the *Add JARs...* button, select SweetHome3D-7.5.jar item and confirm your choice.
> 
> ![Classpath configuration](https://www.sweethome3d.com/images/pluginDeveloperGuide/classpathConfiguration.png)  
> *Figure 10. Setting the classpath of the debug configuration*
> 
> - Select the *Source* tab, click on the *Add...* button, double click on the *Java Project* item in the *Add Source* dialog box, select the *VolumePlugin* item in the *Project Selection* popup and confirm your choice.
> 
> ![Sourcepath configuration](https://www.sweethome3d.com/images/pluginDeveloperGuide/sourcepathConfiguration.png)  
> *Figure 11. Setting the source path of the debug configuration*
> 
> - Finally, click on the *Debug* button to launch Sweet Home 3D in debug mode. Once the program is running, open the VolumePlugin.java file, set a breakpoint in the *execute* method and choose *Tools > Compute volume* from Sweet Home 3D menu. Eclipse will stop on the selected breakpoint to let you execute the program step by step and inspect variables value.
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/eclipseDebug.png)  
> *Figure 12. Eclipse debug perspective*
> 
> | ![!](https://www.sweethome3d.com/images/warning.gif) | Each time you modify the source code of your plug-in, don't forget to [generate the plug-in JAR](#creatingPluginJAR) before launching the debug configuration you created. To speed up the JAR export process in eclipse, go to the second step of the JAR export wizard and select the option *Save the description of this JAR in the workspace*. This will add a new item in the project with a contextual *Create JAR* menu item. |
> | --- | --- |
> 
> #### Deploying the plug-in
> 
> Once ready, your plug-in may be deployed on the computer of other Sweet Home 3D users by simply copying it in their [plug-ins folder](#creatingPluginJAR). From version 1.6, a plug-in file may be also installed in the plug-ins folder of Sweet Home 3D by double-clicking on it, if its extension is SH3P (simply change the file extension from .zip to .sh3p). If double-clicking on a .sh3p file doesn't launch Sweet Home 3D (most chances under Linux), you can also install a plug-in with the following command in a *Terminal* window (where `SweetHome3D` is the name of the executable file provided with Sweet Home 3D installers):
> 
> */path/to/*SweetHome3D */path/to/*plugin.sh3p
> 
> To stop using a plug-in, remove its file from the plug-ins folder and relaunch Sweet Home 3D.
> 
> | ![!](https://www.sweethome3d.com/images/warning.gif) | If you want your plug-in to be able to run with all [Sweet Home 3D installers](https://www.sweethome3d.com/download.jsp) available on this web site, take care to keep it compliant with Java 5, by selecting `1.5` in the field *Compiler compliance level* available in the *Java Compiler* section of the dialog box shown by the *Project > Properties* menu item of Eclipse.   If you use a Java compiler version where Java 1.5 compatibility is not available anymore, try to target at least Java 1.8 still used in recent versions of Sweet Home 3D and set `javaMinimumVersion` in the `ApplicationPlugin.properties` file of your plug-in accordingly. |
> | --- | --- |

### ![](https://www.sweethome3d.com/images/bullet.gif)Going further

> The programming of the first plug-in showed you the big picture. Here's some additional information that will help you to go further.
> 
> #### Sweet Home 3D API - Javadoc
> 
> The most useful documentation to develop a new plug-in is the [Sweet Home 3D API](./SweetHome3D-7.5-javadocindex.html) (Application Programming Interface), generated with javadoc tool.  
> Use only the classes of [com.eteks.sweethome3d.plugin](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/package-summary.html), [com.eteks.sweethome3d.model](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/package-summary.html), [com.eteks.sweethome3d.tools](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/tools/package-summary.html) and [com.eteks.sweethome3d.viewcontroller](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/viewcontroller/package-summary.html) packages in your plug-in if you want it to be upward compatible with future versions of Sweet Home 3D. This will be largely enough to program any plug-in that works on the home data available in Sweet Home 3D.  
> The packages matching the other layers of the program are included in the Javadoc for information purpose only. Don't rely on their API, as it may still change in the future with no guarantee of upward compatibility (anyway you'll see no reference to a class of [com.eteks.sweethome3d.swing](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/swing/package-summary.html), [com.eteks.sweethome3d.j3d](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/j3d/package-summary.html), [com.eteks.sweethome3d.io](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/io/package-summary.html) or [com.eteks.sweethome3d](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/package-summary.html) packages in the aforementioned packages).
> 
> #### Model classes architecture
> 
> Sweet Home 3D is based on a MVC (Model View Controller) architecture, so understanding how is organized its Model layer is essential. The figure 13 (available also at [PDF format](https://www.sweethome3d.com/images/pluginDeveloperGuide/modelClassesDiagram.pdf)) presents almost all the classes and interfaces available in the version 1.5 of [com.eteks.sweethome3d.model](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/package-summary.html) package that matches this Model layer.
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/modelClassesDiagram.png)                             

```mermaid
---
config:
  layout: elk
---
classDiagram
    direction TD
    class Selectable {
        <<Interface>>
        +getPoints(): float[][]
        +intersectsRectangle(x0: float, y0: float, x1: float, y1: float): boolean
        +containsPoint(x: float, y: float, margin: float): boolean
        +move(dx: float, dy: float)
        +clone(): Selectable
    }
    class DimensionLine {
        +getXStart(): float
        +getYStart(): float
        +getXEnd(): float
        +getYEnd(): float
        +getLength(): float
        +getOffset(): float
        +getLengthStyle(): TextStyle
    }
    class Label {
        +getText(): String
        +getX(): float
        +getY(): float
        +getStyle(): TextStyle
    }
    class ObserverCamera {
    }
    class Camera {
        +getX(): float
        +getY(): float
        +getZ(): float
        +getFieldOfView(): float
        +getPitch(): float
        +getYaw(): float
    }
    class Room {
        +getName(): String
        +getNameStyle(): TextStyle
        +getNameXOffset(): float
        +getNameYOffset(): float
        +getArea(): float
        +getAreaStyle(): TextStyle
        +getAreaXOffset(): float
        +getAreaYOffset(): float
        +getFloorColor(): Integer
        +getFloorTexture(): HomeTexture
        +getCeilingColor(): Integer
        +getCeilingTexture(): HomeTexture
        +isAreaVisible(): boolean
        +isFloorVisible(): boolean
        +isCeilingVisible(): boolean
    }
    class TextStyle {
        +getFontSize(): float
        +isBold(): boolean
        +isItalic(): boolean
    }
    class Home {
        +getName(): String
        +getFurniture(): List~HomePieceOfFurniture~
        +getRooms(): List~Room~
        +getDimensionLines(): Collection~DimensionLine~
        +getWalls(): Collection~Wall~
        +getLabels(): Collection~Label~
        +getCamera(): Camera
        +getSelectedItems(): List~Selectable~
        +addSelectionListener(l: SelectionListener)
        +removeSelectionListener(l: SelectionListener)
    }
    class SelectionListener {
        <<Interface>>
        +selectionChanged(ev: SelectionEvent)
    }
    class SelectionEvent {
        +getSelectedItems(): List
    }
    class CollectionListener~T~ {
        <<Interface>>
        +collectionChanged(ev: CollectionEvent~T~)
    }
    class CollectionEvent~T~ {
        +getType(): Type
        +getItem(): T
        +getIndex(): int
    }
    class HomeTexture {
    }
    class Wall {
        +getXStart(): float
        +getYStart(): float
        +getXEnd(): float
        +getYEnd(): float
        +getWallAtStart(): Wall
        +getWallAtEnd(): Wall
        +getThickness(): float
        +getHeight(): float
        +getHeightAtEnd(): float
        +getLeftSideColor(): Integer
        +getLeftSideTexture(): HomeTexture
        +getRightSideColor(): Integer
        +getRightSideTexture(): HomeTexture
    }
    class HomeApplication {
        +getHomes(): List~Home~
        +getUserPreferences(): UserPreferences
    }
    class UserPreferences {
        +getFurnitureCatalog(): FurnitureCatalog
        +getTexturesCatalog(): TexturesCatalog
        +getLanguage(): String
        +getLengthUnit(): LengthUnit
        +isGridVisible(): boolean
        +isRulersVisible(): boolean
    }
    class LengthUnit {
        <<Enumeration>>
        CENTIMETER
        INCH
        +getName(): String
        +getFormat(): Format
        +getFormatWithUnit(): Format
        +getAreaFormatWithUnit(): Format
    }
    class HomePieceOfFurniture {
        +getCatalogId(): String
        +getX(): float
        +getY(): float
        +getAngle(): float
        +getNameStyle(): TextStyle
        +getNameXOffset(): float
        +getNameYOffset(): float
    }
    class FurnitureCatalog {
        +getCategories(): List~FurnitureCategory~
    }
    class TexturesCatalog {
        +getCategories(): List~TexturesCategory~
    }
    class PieceOfFurniture {
        <<Interface>>
        +getName(): String
        +getCreator(): String
        +getIcon(): Content
        +getModel(): Content
        +getWidth(): float
        +getDepth(): float
        +getHeight(): float
        +getElevation(): float
        +getColor(): Integer
        +isMovable(): boolean
        +isDoorOrWindow(): boolean
    }
    class FurnitureCategory {
        +getName(): String
        +getFurniture(): List~CatalogPieceOfFurniture~
    }
    class CatalogPieceOfFurniture {
        +getId(): String
        +isModifiable(): boolean
    }
    class Content {
        <<Interface>>
        +openStream(): InputStream
    }
    class TexturesCategory {
        +getName(): String
        +getTextures(): List~CatalogTexture~
    }
    class CatalogTexture {
        +isModifiable(): boolean
    }
    class TextureImage {
        <<Interface>>
        +getName(): String
        +getImage(): Content
        +getWidth(): float
        +getHeight(): float
    }
    ObserverCamera --|> Camera
    DimensionLine ..|> Selectable
    Room ..|> Selectable
    Wall ..|> Selectable
    HomePieceOfFurniture ..|> Selectable
    HomePieceOfFurniture ..|> PieceOfFurniture
    CatalogPieceOfFurniture ..|> PieceOfFurniture
    HomeTexture ..|> TextureImage
    CatalogTexture ..|> TextureImage
    HomeApplication "1" *-- "1..*" Home
    HomeApplication "1" *-- "1" UserPreferences
    UserPreferences "1" *-- "0..1" FurnitureCatalog
    UserPreferences "1" *-- "0..1" TexturesCatalog
    FurnitureCatalog "1" *-- "0..*" FurnitureCategory
    FurnitureCategory "1" *-- "0..*" CatalogPieceOfFurniture
    TexturesCatalog "1" *-- "0..*" TexturesCategory
    TexturesCategory "1" *-- "0..*" CatalogTexture
    Home "1" o-- "0..*" DimensionLine
    Home "1" o-- "0..*" Label
    Home "1" o-- "0..*" Room
    Home "1" o-- "0..*" Wall
    Home "1" o-- "0..*" HomePieceOfFurniture
    Home "1" o-- "1" Camera
    Home "1" o-- "0..*" Selectable : selectedItems
    DimensionLine "0..*" --> "0..1" TextStyle
    Label "0..*" --> "1" TextStyle
    Room ..> TextStyle
    HomePieceOfFurniture ..> TextStyle
    Room "0..*" --> "0..*" HomeTexture
    Wall "0..*" --> "0..*" HomeTexture
    UserPreferences --> LengthUnit
    CatalogPieceOfFurniture ..> Content
    TextureImage ..> Content
    Home ..> SelectionListener
    SelectionListener ..> SelectionEvent
    Home ..> CollectionListener
    CollectionListener ..> CollectionEvent

```

> *Figure 13. UML diagram of com.eteks.sweethome3d.model package  
> (click on a class to view its javadoc)*
> 
> The central class in the Model layer is the [HomeApplication](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/HomeApplication.html) class (10), the abstract super class of [SweetHome3D](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/SweetHome3D.html) application main class. The instance of this class gives access to the [Home](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/Home.html) instances (7) currently edited, and to the [UserPreferences](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/UserPreferences.html) object (11) that stores the [length unit](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/LengthUnit.html) in use (12), the [furniture catalog](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/FurnitureCatalog.html) (14) and the [textures catalog](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/TexturesCatalog.html) (15) from which the user chooses [pieces of furniture](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/CatalogPieceOfFurniture.html) (17) and [textures](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/CatalogTexture.html) (18).  
> A [Home](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/Home.html) instance (7) stores all the objects the user created in the home plan:
> 
> - the list of [HomePieceOfFurniture](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/HomePieceOfFurniture.html) objects (13) which implement the interface [PieceOfFurniture](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/PieceOfFurniture.html) (16),
> - the collection of [Wall](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/Wall.html) objects (9),
> - the list of [Room](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/Wall.html) objects (5),
> - the collection of [DimensionLine](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/DimensionLine.html) objects (2),
> - the collection of [Label](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/Label.html) objects (3).
> 
> These objects implement the [Selectable](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/Selectable.html) interface (1) as well as the [ObserverCamera](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/ObserverCamera.html) object (4), that stores the location of the camera in the *Virtual visitor* mode. All the external information managed by Model objects, like the icon and the 3D model of a [piece of furniture](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/PieceOfFurniture.html) (16), or the image of a [texture](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/TextureImage.html) (20) is accessed through the [Content](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/Content.html) interface (19), implemented by the [URLContent](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/tools/URLContent.html) class and other classes of the [com.eteks.sweethome3d.tools](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/tools/package-summary.html) package.
> 
> This UML diagram should help you understand which classes are available in the Sweet Home 3D model and how you can access to them, but you'll probably notice that no constructors and no mutators (or setters if you prefer) are cited in it. It's just by lack of room but you can use them with no problem in a plug-in class. Note also that any modification of an existing object of the model will be notified to the displayed components either with [PropertyChangeEvent](https://java.sun.com/j2se/1.5.0/docs/api/java/beans/PropertyChangeEvent.html)s, with [CollectionEvent](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/CollectionEvent.html)s (8) or with [SelectionEvent](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/model/SelectionEvent.html)s (6), thus allowing all changes to be reflected immediately on screen.
> 
> | ![!](https://www.sweethome3d.com/images/warning.gif) | Sweet Home 3D model **isn't** thread safe for performance reasons. All **modifications** of an object belonging to the model should be done in the Event Dispatch Thread. |
> | --- | --- |
> 
> #### Plug-in classes architecture
> 
> The architecture of plug-in classes is much more simple to understand than the Model layer's one. The [com.eteks.sweethome3d.plugin](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/package-summary.html) package contains only three classes among which you're supposed to use only [Plugin](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/Plugin.html) and [PluginAction](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/PluginAction.html) classes, as shown in figure 14 (also available at [PDF format](https://www.sweethome3d.com/images/pluginDeveloperGuide/pluginClassesDiagram.pdf)).
> 
> ![](https://www.sweethome3d.com/images/pluginDeveloperGuide/pluginClassesDiagram.png)  

```mermaid
---
config:
  layout: elk
---
classDiagram
    class PluginManager {
        +PluginManager(pluginFolder: File)
        +PluginManager(pluginFolder: URL[])
        +getPlugins(application: HomeApplication, home: Home, preferences: UserPreferences, undoSupport: UndoableEditSupport): List~Plugin~
    }
    class Property {
        <<enumeration>>
        ENABLED
        MENU
        MNEMONIC
        NAME
        SHORT_DESCRIPTION
        SMALL_ICON
        TOOL_BAR
    }
    class Plugin {
        +Plugin()
        +getName(): String
        +getDescription(): String
        +getVersion(): String
        +getLicense(): String
        +getProvider(): String
        +getActions(): PluginAction[]
        +getHome(): Home
        +getPluginClassLoader(): ClassLoader
        +getUndoableEditSupport(): UndoableEditSupport
        +getUserPreferences(): UserPreferences
        +destroy()
    }
    class PluginAction {
        +PluginAction()
        +PluginAction(resourceBaseName: String, actionPrefix: String, pluginClassLoader: ClassLoader)
        +PluginAction(resourceBaseName: String, actionPrefix: String, pluginClassLoader: ClassLoader, enabled: boolean)
        +execute()
        +getPropertyValue(property: Property): Object
        +putPropertyValue(property: Property, value: Object)
        +isEnabled(): boolean
        +setEnabled(enabled: boolean)
        +addPropertyChangeListener(l: PropertyChangeListener)
        +removePropertyChangeListener(l: PropertyChangeListener)
    }
    PluginManager "1" *-- "0..*" Plugin
    Plugin ..> PluginAction
    PluginAction ..> Property

```

> *Figure 14. UML diagram of com.eteks.sweethome3d.plugin package  
> (click on a class to view its javadoc)*    
> 
> A [PluginManager](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/PluginManager.html) instance (1) is created at application launch and searchs the plug-ins installed in user's [plug-ins folder](#creatingPluginJAR). Each time a new home is edited, this manager instanciates and configures a [Plugin](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/Plugin.html) object (3) for each plug-in found at launch time. Then, it calls the [getActions](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/Plugin.html#getActions\(\)) method to retrieve all the [actions](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/PluginAction.html) (4) that will be added as menu items and/or tool bar buttons in the home window. Each action is an instance of [PluginAction](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/PluginAction.html), which looks like [Action](https://java.sun.com/j2se/1.5.0/docs/api/javax/swing/Action.html) class, with its [execute](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/PluginAction.html#execute\(\)) method and its modifiable [properties](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/PluginAction.Property.html) (2).
> 
> Note that the [Plugin](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/Plugin.html) class gives you access to an [UndoableEditSupport](https://java.sun.com/j2se/1.5.0/docs/api/javax/swing/undo/UndoableEditSupport.html) instance through its [getUndoableEditSupport](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/Plugin.html#getUndoableEditSupport\(\)) method. As soon as you modify a home or its objects (furniture, walls...) in the execute method of a [PluginAction](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/PluginAction.html) instance, you should also post an [UndoableEdit](https://java.sun.com/j2se/1.5.0/docs/api/javax/swing/undo/UndoableEdit.html) object to the undoable edit support returned by getUndoableEditSupport method, otherwise users won't be able to undo/redo correctly the changes you made.
> 
> #### Localization

> If you plan to develop a plug-in for Sweet Home 3D users community, try to localize the strings it displays either in actions name and menu or in dialogs you'll create (or at least prepare its localization). Two [constructors of the PluginAction](./SweetHome3D-7.5-javadoccom/eteks/sweethome3d/plugin/PluginAction.html#PluginAction\(java.lang.String,%20java.lang.String,%20java.lang.ClassLoader\)) class will help you to organize the translation of actions properties with .properties files, and if you need to translate other strings in your plug-in (like the one in the dialog shown by the [tested plug-in](#testingPlugin)) reuse these .properties files with [ResourceBundle](https://java.sun.com/j2se/1.5.0/docs/api/java/util/ResourceBundle.html) Java class.  
> If you prefer to limit the number of properties files, you might even write the values of the action properties and other strings in the ApplicationPlugin.properties [description file](#creatingPluginDescriptionFile) of your plug-in.
> 
> If you want an example that uses this architecture, download the *Export to SH3F* plug-in available at [https://www.sweethome3d.com/plugins/ExportToSH3F-1.0.sh3p](https://www.sweethome3d.com/plugins/ExportToSH3F-1.0.sh3p), and unzip it (this plug-in file contains also the source code of the plug-in).  
> As described in [Help forum](https://sourceforge.net/forum/message.php?msg_id=5837358), this plug-in creates a SH3F file that contains all the furniture you imported in the furniture catalog of Sweet Home 3D.
> 
> #### Contributing plug-ins
> 
> You can post the plug-ins you programmed in [Plug-ins Contributions](https://sourceforge.net/p/sweethome3d/plug-ins/) Tracking System to share them with Sweet Home 3D users community.  
> Many features can be added to Sweet Home 3D thanks to plug-ins, from importers to exporters, but also plug-ins able to modify the data of a home like the [Home Rotator Plug-in](https://sourceforge.net/p/sweethome3d/plug-ins/1/) developed by Michel Mbem and others listed in the [Tutorial for Plug-ins and Extensions](https://www.sweethome3d.com/pluginsUserGuide.pdf) (PDF) written by Hans Dirkse and in the [Plug-ins and tools](https://www.sweethome3d.com/plugins.jsp) page.

*Last update : August 20, 2024*