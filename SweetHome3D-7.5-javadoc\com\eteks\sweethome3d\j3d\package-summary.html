<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>com.eteks.sweethome3d.j3d (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.eteks.sweethome3d.j3d (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.eteks.sweethome3d.j3d</h1>
<div class="docSummary">
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Interface Summary table, listing interfaces, and an explanation">
<caption><span>Interface Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Interface</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingErrorObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingErrorObserver</a></td>
<td class="colLast">
<div class="block">An observer that receives error notifications in Java 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingObserver</a></td>
<td class="colLast">
<div class="block">An observer that receives notifications during the different steps
 of the loop rendering a canvas 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a></td>
<td class="colLast">
<div class="block">An observer that receives model loading notifications.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a></td>
<td class="colLast">
<div class="block">An observer that receives texture loading notifications.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></td>
<td class="colLast">
<div class="block">A renderer able to create a photo realistic image of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html" title="class in com.eteks.sweethome3d.j3d">Component3DManager</a></td>
<td class="colLast">
<div class="block">Manager of <code>Canvas3D</code> instantiations and Java 3D error listeners.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/DAELoader.html" title="class in com.eteks.sweethome3d.j3d">DAELoader</a></td>
<td class="colLast">
<div class="block">A loader for DAE Collada 1.4.1 format as specified by
 <a href="http://www.khronos.org/files/collada_spec_1_4.pdf">http://www.khronos.org/files/collada_spec_1_4.pdf</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/DimensionLine3D.html" title="class in com.eteks.sweethome3d.j3d">DimensionLine3D</a></td>
<td class="colLast">
<div class="block">Root of a dimension line branch.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Ground3D.html" title="class in com.eteks.sweethome3d.j3d">Ground3D</a></td>
<td class="colLast">
<div class="block">Root of a the 3D ground.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html" title="class in com.eteks.sweethome3d.j3d">HomePieceOfFurniture3D</a></td>
<td class="colLast">
<div class="block">Root of piece of furniture branch.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Label3D.html" title="class in com.eteks.sweethome3d.j3d">Label3D</a></td>
<td class="colLast">
<div class="block">Root of a label branch.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Max3DSLoader.html" title="class in com.eteks.sweethome3d.j3d">Max3DSLoader</a></td>
<td class="colLast">
<div class="block">A loader for 3DS streams.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.html" title="class in com.eteks.sweethome3d.j3d">ModelManager</a></td>
<td class="colLast">
<div class="block">Singleton managing 3D models cache.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d">Object3DBranch</a></td>
<td class="colLast">
<div class="block">Root of a branch that matches a home object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranchFactory.html" title="class in com.eteks.sweethome3d.j3d">Object3DBranchFactory</a></td>
<td class="colLast">
<div class="block">A factory able to create instances of <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d"><code>Object3DBranch</code></a> class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/OBJLoader.html" title="class in com.eteks.sweethome3d.j3d">OBJLoader</a></td>
<td class="colLast">
<div class="block">An OBJ + MTL loader.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html" title="class in com.eteks.sweethome3d.j3d">OBJMaterial</a></td>
<td class="colLast">
<div class="block">A material with additional parameters useful for raytracing rendering.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html" title="class in com.eteks.sweethome3d.j3d">OBJWriter</a></td>
<td class="colLast">
<div class="block">An output stream that writes Java 3D nodes at OBJ + MTL format.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/PhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">PhotoRenderer</a></td>
<td class="colLast">
<div class="block">A renderer able to create a photo realistic image of a home based on SunFlow rendering engine.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/PhotoRenderer.SphereLightWithNoRepresentation.html" title="class in com.eteks.sweethome3d.j3d">PhotoRenderer.SphereLightWithNoRepresentation</a></td>
<td class="colLast">
<div class="block">A SunFlow sphere light with no representation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/PhotoRenderer.TriangleMeshLightWithNoRepresentation.html" title="class in com.eteks.sweethome3d.j3d">PhotoRenderer.TriangleMeshLightWithNoRepresentation</a></td>
<td class="colLast">
<div class="block">A SunFlow triangle mesh light with no representation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Polyline3D.html" title="class in com.eteks.sweethome3d.j3d">Polyline3D</a></td>
<td class="colLast">
<div class="block">Root of a polyline branch.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Room3D.html" title="class in com.eteks.sweethome3d.j3d">Room3D</a></td>
<td class="colLast">
<div class="block">Root of room branch.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/ShapeTools.html" title="class in com.eteks.sweethome3d.j3d">ShapeTools</a></td>
<td class="colLast">
<div class="block">Gathers some useful tools for shapes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html" title="class in com.eteks.sweethome3d.j3d">TextureManager</a></td>
<td class="colLast">
<div class="block">Singleton managing texture image cache.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/Wall3D.html" title="class in com.eteks.sweethome3d.j3d">Wall3D</a></td>
<td class="colLast">
<div class="block">Root of wall branch.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html" title="class in com.eteks.sweethome3d.j3d">YafarayRenderer</a></td>
<td class="colLast">
<div class="block">A renderer implemented with YafaRay rendering engine called with JNI.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer.Quality</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/j3d/PhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d">PhotoRenderer.Quality</a></td>
<td class="colLast">Deprecated
<div class="block"><span class="deprecationComment">From version 7.0, prefer use <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d"><code>AbstractPhotoRenderer.Quality</code></a> enum.</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package com.eteks.sweethome3d.j3d Description">Package com.eteks.sweethome3d.j3d Description</h2>
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
