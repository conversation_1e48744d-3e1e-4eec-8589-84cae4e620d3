<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>AppletContentManager (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AppletContentManager (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AppletContentManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/applet/AppletApplication.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/applet/AppletContentManager.html" target="_top">Frames</a></li>
<li><a href="AppletContentManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.applet</div>
<h2 title="Class AppletContentManager" class="title">Class AppletContentManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html" title="class in com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing.FileContentManager</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.applet.AppletContentManager</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">AppletContentManager</span>
extends <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html" title="class in com.eteks.sweethome3d.swing">FileContentManager</a></pre>
<div class="block">Content manager for Sweet Home 3D files stored on server.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.ContentManager">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#AppletContentManager-com.eteks.sweethome3d.model.HomeRecorder-com.eteks.sweethome3d.model.UserPreferences-">AppletContentManager</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;recorder,
                    <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#AppletContentManager-com.eteks.sweethome3d.model.HomeRecorder-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">AppletContentManager</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;recorder,
                    <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#getPresentationName-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getPresentationName</a></span>(java.lang.String&nbsp;contentName,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the name of the content in parameter.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#isAcceptable-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">isAcceptable</a></span>(java.lang.String&nbsp;contentName,
            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns <code>true</code> if the content name in parameter is accepted
 for <code>contentType</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">showOpenDialog</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the name chosen by user with an open dialog.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">showSaveDialog</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
              java.lang.String&nbsp;name)</code>
<div class="block">Returns the name chosen by user with a save dialog.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.swing.FileContentManager">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.swing.<a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html" title="class in com.eteks.sweethome3d.swing">FileContentManager</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#confirmOverwrite-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-">confirmOverwrite</a>, <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getContent-java.lang.String-">getContent</a>, <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getDefaultFileExtension-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getDefaultFileExtension</a>, <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getFileDialogTitle-boolean-">getFileDialogTitle</a>, <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getFileExtensions-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getFileExtensions</a>, <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getFileFilter-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getFileFilter</a>, <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getLastDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getLastDirectory</a>, <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#isDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">isDirectory</a>, <a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#setLastDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.io.File-">setLastDirectory</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AppletContentManager-com.eteks.sweethome3d.model.HomeRecorder-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>AppletContentManager</h4>
<pre>public&nbsp;AppletContentManager(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;recorder,
                            <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</pre>
</li>
</ul>
<a name="AppletContentManager-com.eteks.sweethome3d.model.HomeRecorder-com.eteks.sweethome3d.model.UserPreferences-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AppletContentManager</h4>
<pre>public&nbsp;AppletContentManager(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;recorder,
                            <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPresentationName-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentationName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPresentationName(java.lang.String&nbsp;contentName,
                                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns the name of the content in parameter.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#getPresentationName-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getPresentationName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getPresentationName-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getPresentationName</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html" title="class in com.eteks.sweethome3d.swing">FileContentManager</a></code></dd>
</dl>
</li>
</ul>
<a name="isAcceptable-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAcceptable</h4>
<pre>public&nbsp;boolean&nbsp;isAcceptable(java.lang.String&nbsp;contentName,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns <code>true</code> if the content name in parameter is accepted
 for <code>contentType</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#isAcceptable-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">isAcceptable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#isAcceptable-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">isAcceptable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html" title="class in com.eteks.sweethome3d.swing">FileContentManager</a></code></dd>
</dl>
</li>
</ul>
<a name="showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showOpenDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showOpenDialog(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
                                       java.lang.String&nbsp;dialogTitle,
                                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns the name chosen by user with an open dialog.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">showOpenDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">showOpenDialog</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html" title="class in com.eteks.sweethome3d.swing">FileContentManager</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the name or <code>null</code> if user canceled its choice.</dd>
</dl>
</li>
</ul>
<a name="showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>showSaveDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showSaveDialog(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
                                       java.lang.String&nbsp;dialogTitle,
                                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
                                       java.lang.String&nbsp;name)</pre>
<div class="block">Returns the name chosen by user with a save dialog.
 If this name already exists, the user will be prompted whether
 he wants to overwrite this existing name.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">showSaveDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">showSaveDialog</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html" title="class in com.eteks.sweethome3d.swing">FileContentManager</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the chosen name or <code>null</code> if user canceled its choice.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AppletContentManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/applet/AppletApplication.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/applet/AppletContentManager.html" target="_top">Frames</a></li>
<li><a href="AppletContentManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
