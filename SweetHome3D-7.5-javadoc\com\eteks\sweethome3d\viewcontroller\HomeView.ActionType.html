<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>HomeView.ActionType (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeView.ActionType (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeView.ActionType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" target="_top">Frames</a></li>
<li><a href="HomeView.ActionType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Enum HomeView.ActionType" class="title">Enum HomeView.ActionType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.HomeView.ActionType</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>&gt;</dd>
</dl>
<dl>
<dt>Enclosing interface:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></dd>
</dl>
<hr>
<br>
<pre>public static enum <span class="typeNameLabel">HomeView.ActionType</span>
extends java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>&gt;</pre>
<div class="block">The actions proposed by the view to user.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ABOUT">ABOUT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ADD_FURNITURE_TO_GROUP">ADD_FURNITURE_TO_GROUP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ADD_HOME_FURNITURE">ADD_HOME_FURNITURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ADD_LEVEL">ADD_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ADD_LEVEL_AT_SAME_ELEVATION">ADD_LEVEL_AT_SAME_ELEVATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ADD_ROOM_POINT">ADD_ROOM_POINT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_ON_BACK_SIDE">ALIGN_FURNITURE_ON_BACK_SIDE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_ON_BOTTOM">ALIGN_FURNITURE_ON_BOTTOM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_ON_FRONT_SIDE">ALIGN_FURNITURE_ON_FRONT_SIDE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_ON_LEFT">ALIGN_FURNITURE_ON_LEFT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_ON_LEFT_SIDE">ALIGN_FURNITURE_ON_LEFT_SIDE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_ON_RIGHT">ALIGN_FURNITURE_ON_RIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_ON_RIGHT_SIDE">ALIGN_FURNITURE_ON_RIGHT_SIDE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_ON_TOP">ALIGN_FURNITURE_ON_TOP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ALIGN_FURNITURE_SIDE_BY_SIDE">ALIGN_FURNITURE_SIDE_BY_SIDE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ATTACH_3D_VIEW">ATTACH_3D_VIEW</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CLOSE">CLOSE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#COPY">COPY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CREATE_DIMENSION_LINES">CREATE_DIMENSION_LINES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CREATE_LABELS">CREATE_LABELS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CREATE_PHOTO">CREATE_PHOTO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CREATE_PHOTOS_AT_POINTS_OF_VIEW">CREATE_PHOTOS_AT_POINTS_OF_VIEW</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CREATE_POLYLINES">CREATE_POLYLINES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CREATE_ROOMS">CREATE_ROOMS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CREATE_VIDEO">CREATE_VIDEO</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CREATE_WALLS">CREATE_WALLS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#CUT">CUT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DECREASE_TEXT_SIZE">DECREASE_TEXT_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DELETE">DELETE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DELETE_BACKGROUND_IMAGE">DELETE_BACKGROUND_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DELETE_HOME_FURNITURE">DELETE_HOME_FURNITURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DELETE_LEVEL">DELETE_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DELETE_POINTS_OF_VIEW">DELETE_POINTS_OF_VIEW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DELETE_RECENT_HOMES">DELETE_RECENT_HOMES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DELETE_ROOM_POINT">DELETE_ROOM_POINT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DELETE_SELECTION">DELETE_SELECTION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DETACH_3D_VIEW">DETACH_3D_VIEW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISABLE_MAGNETISM">DISABLE_MAGNETISM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_ALL_LEVELS">DISPLAY_ALL_LEVELS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_ANGLE">DISPLAY_HOME_FURNITURE_ANGLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_CATALOG_ID">DISPLAY_HOME_FURNITURE_CATALOG_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_COLOR">DISPLAY_HOME_FURNITURE_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_CREATOR">DISPLAY_HOME_FURNITURE_CREATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_DEPTH">DISPLAY_HOME_FURNITURE_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_DESCRIPTION">DISPLAY_HOME_FURNITURE_DESCRIPTION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_DOOR_OR_WINDOW">DISPLAY_HOME_FURNITURE_DOOR_OR_WINDOW</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_ELEVATION">DISPLAY_HOME_FURNITURE_ELEVATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_HEIGHT">DISPLAY_HOME_FURNITURE_HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_LEVEL">DISPLAY_HOME_FURNITURE_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_LICENSE">DISPLAY_HOME_FURNITURE_LICENSE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_MODEL_SIZE">DISPLAY_HOME_FURNITURE_MODEL_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_MOVABLE">DISPLAY_HOME_FURNITURE_MOVABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_NAME">DISPLAY_HOME_FURNITURE_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_PRICE">DISPLAY_HOME_FURNITURE_PRICE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_PRICE_VALUE_ADDED_TAX_INCLUDED">DISPLAY_HOME_FURNITURE_PRICE_VALUE_ADDED_TAX_INCLUDED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_TEXTURE">DISPLAY_HOME_FURNITURE_TEXTURE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX">DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX_PERCENTAGE">DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX_PERCENTAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_VISIBLE">DISPLAY_HOME_FURNITURE_VISIBLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_WIDTH">DISPLAY_HOME_FURNITURE_WIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_X">DISPLAY_HOME_FURNITURE_X</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_HOME_FURNITURE_Y">DISPLAY_HOME_FURNITURE_Y</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISPLAY_SELECTED_LEVEL">DISPLAY_SELECTED_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISTRIBUTE_FURNITURE_HORIZONTALLY">DISTRIBUTE_FURNITURE_HORIZONTALLY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#DISTRIBUTE_FURNITURE_VERTICALLY">DISTRIBUTE_FURNITURE_VERTICALLY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ENABLE_MAGNETISM">ENABLE_MAGNETISM</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#EXIT">EXIT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#EXPORT_TO_CSV">EXPORT_TO_CSV</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#EXPORT_TO_OBJ">EXPORT_TO_OBJ</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#EXPORT_TO_SVG">EXPORT_TO_SVG</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#FLIP_HORIZONTALLY">FLIP_HORIZONTALLY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#FLIP_VERTICALLY">FLIP_VERTICALLY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#GROUP_FURNITURE">GROUP_FURNITURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#HELP">HELP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#HIDE_BACKGROUND_IMAGE">HIDE_BACKGROUND_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#IMPORT_BACKGROUND_IMAGE">IMPORT_BACKGROUND_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#IMPORT_FURNITURE">IMPORT_FURNITURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#IMPORT_FURNITURE_LIBRARY">IMPORT_FURNITURE_LIBRARY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#IMPORT_TEXTURE">IMPORT_TEXTURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#IMPORT_TEXTURES_LIBRARY">IMPORT_TEXTURES_LIBRARY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#INCREASE_TEXT_SIZE">INCREASE_TEXT_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#JOIN_WALLS">JOIN_WALLS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#LOCK_BASE_PLAN">LOCK_BASE_PLAN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MAKE_ALL_LEVELS_VIEWABLE">MAKE_ALL_LEVELS_VIEWABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MAKE_LEVEL_ONLY_VIEWABLE_ONE">MAKE_LEVEL_ONLY_VIEWABLE_ONE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MAKE_LEVEL_UNVIEWABLE">MAKE_LEVEL_UNVIEWABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MAKE_LEVEL_VIEWABLE">MAKE_LEVEL_VIEWABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_3D_ATTRIBUTES">MODIFY_3D_ATTRIBUTES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_BACKGROUND_IMAGE">MODIFY_BACKGROUND_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_COMPASS">MODIFY_COMPASS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_DIMENSION_LINE">MODIFY_DIMENSION_LINE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_FURNITURE">MODIFY_FURNITURE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_LABEL">MODIFY_LABEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_LEVEL">MODIFY_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_OBSERVER">MODIFY_OBSERVER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_POLYLINE">MODIFY_POLYLINE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_ROOM">MODIFY_ROOM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#MODIFY_WALL">MODIFY_WALL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#NEW_HOME">NEW_HOME</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#NEW_HOME_FROM_EXAMPLE">NEW_HOME_FROM_EXAMPLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#OPEN">OPEN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PAGE_SETUP">PAGE_SETUP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PAN">PAN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PASTE">PASTE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PASTE_STYLE">PASTE_STYLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PASTE_TO_GROUP">PASTE_TO_GROUP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PREFERENCES">PREFERENCES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PRINT">PRINT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PRINT_PREVIEW">PRINT_PREVIEW</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#PRINT_TO_PDF">PRINT_TO_PDF</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#RECOMPUTE_ROOM_POINTS">RECOMPUTE_ROOM_POINTS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#REDO">REDO</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#RESET_FURNITURE_ELEVATION">RESET_FURNITURE_ELEVATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#REVERSE_WALL_DIRECTION">REVERSE_WALL_DIRECTION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SAVE">SAVE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SAVE_AND_COMPRESS">SAVE_AND_COMPRESS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SAVE_AS">SAVE_AS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SELECT">SELECT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SELECT_ALL">SELECT_ALL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SELECT_ALL_AT_ALL_LEVELS">SELECT_ALL_AT_ALL_LEVELS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SELECT_OBJECT">SELECT_OBJECT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SHOW_BACKGROUND_IMAGE">SHOW_BACKGROUND_IMAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_ANGLE">SORT_HOME_FURNITURE_BY_ANGLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_CATALOG_ID">SORT_HOME_FURNITURE_BY_CATALOG_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_COLOR">SORT_HOME_FURNITURE_BY_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_CREATOR">SORT_HOME_FURNITURE_BY_CREATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_DEPTH">SORT_HOME_FURNITURE_BY_DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_DESCENDING_ORDER">SORT_HOME_FURNITURE_BY_DESCENDING_ORDER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_DESCRIPTION">SORT_HOME_FURNITURE_BY_DESCRIPTION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_ELEVATION">SORT_HOME_FURNITURE_BY_ELEVATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_HEIGHT">SORT_HOME_FURNITURE_BY_HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_LEVEL">SORT_HOME_FURNITURE_BY_LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_LICENSE">SORT_HOME_FURNITURE_BY_LICENSE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_MODEL_SIZE">SORT_HOME_FURNITURE_BY_MODEL_SIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_MOVABILITY">SORT_HOME_FURNITURE_BY_MOVABILITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_NAME">SORT_HOME_FURNITURE_BY_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_PRICE">SORT_HOME_FURNITURE_BY_PRICE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_PRICE_VALUE_ADDED_TAX_INCLUDED">SORT_HOME_FURNITURE_BY_PRICE_VALUE_ADDED_TAX_INCLUDED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_TEXTURE">SORT_HOME_FURNITURE_BY_TEXTURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_TYPE">SORT_HOME_FURNITURE_BY_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX">SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX_PERCENTAGE">SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX_PERCENTAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_VISIBILITY">SORT_HOME_FURNITURE_BY_VISIBILITY</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_WIDTH">SORT_HOME_FURNITURE_BY_WIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_X">SORT_HOME_FURNITURE_BY_X</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SORT_HOME_FURNITURE_BY_Y">SORT_HOME_FURNITURE_BY_Y</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#SPLIT_WALL">SPLIT_WALL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#STORE_POINT_OF_VIEW">STORE_POINT_OF_VIEW</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#TOGGLE_BOLD_STYLE">TOGGLE_BOLD_STYLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#TOGGLE_ITALIC_STYLE">TOGGLE_ITALIC_STYLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#TOGGLE_SELECTION">TOGGLE_SELECTION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#UNDO">UNDO</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#UNGROUP_FURNITURE">UNGROUP_FURNITURE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#UNLOCK_BASE_PLAN">UNLOCK_BASE_PLAN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#VIEW_FROM_OBSERVER">VIEW_FROM_OBSERVER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#VIEW_FROM_TOP">VIEW_FROM_TOP</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ZOOM_IN">ZOOM_IN</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#ZOOM_OUT">ZOOM_OUT</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="NEW_HOME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_HOME</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> NEW_HOME</pre>
</li>
</ul>
<a name="NEW_HOME_FROM_EXAMPLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NEW_HOME_FROM_EXAMPLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> NEW_HOME_FROM_EXAMPLE</pre>
</li>
</ul>
<a name="CLOSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CLOSE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CLOSE</pre>
</li>
</ul>
<a name="OPEN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OPEN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> OPEN</pre>
</li>
</ul>
<a name="DELETE_RECENT_HOMES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELETE_RECENT_HOMES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DELETE_RECENT_HOMES</pre>
</li>
</ul>
<a name="SAVE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SAVE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SAVE</pre>
</li>
</ul>
<a name="SAVE_AS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SAVE_AS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SAVE_AS</pre>
</li>
</ul>
<a name="SAVE_AND_COMPRESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SAVE_AND_COMPRESS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SAVE_AND_COMPRESS</pre>
</li>
</ul>
<a name="PAGE_SETUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PAGE_SETUP</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PAGE_SETUP</pre>
</li>
</ul>
<a name="PRINT_PREVIEW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRINT_PREVIEW</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PRINT_PREVIEW</pre>
</li>
</ul>
<a name="PRINT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRINT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PRINT</pre>
</li>
</ul>
<a name="PRINT_TO_PDF">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRINT_TO_PDF</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PRINT_TO_PDF</pre>
</li>
</ul>
<a name="PREFERENCES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PREFERENCES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PREFERENCES</pre>
</li>
</ul>
<a name="EXIT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXIT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> EXIT</pre>
</li>
</ul>
<a name="UNDO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNDO</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> UNDO</pre>
</li>
</ul>
<a name="REDO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REDO</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> REDO</pre>
</li>
</ul>
<a name="CUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CUT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CUT</pre>
</li>
</ul>
<a name="COPY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COPY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> COPY</pre>
</li>
</ul>
<a name="PASTE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PASTE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PASTE</pre>
</li>
</ul>
<a name="PASTE_TO_GROUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PASTE_TO_GROUP</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PASTE_TO_GROUP</pre>
</li>
</ul>
<a name="PASTE_STYLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PASTE_STYLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PASTE_STYLE</pre>
</li>
</ul>
<a name="DELETE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELETE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DELETE</pre>
</li>
</ul>
<a name="SELECT_ALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SELECT_ALL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SELECT_ALL</pre>
</li>
</ul>
<a name="SELECT_ALL_AT_ALL_LEVELS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SELECT_ALL_AT_ALL_LEVELS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SELECT_ALL_AT_ALL_LEVELS</pre>
</li>
</ul>
<a name="ADD_HOME_FURNITURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ADD_HOME_FURNITURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ADD_HOME_FURNITURE</pre>
</li>
</ul>
<a name="ADD_FURNITURE_TO_GROUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ADD_FURNITURE_TO_GROUP</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ADD_FURNITURE_TO_GROUP</pre>
</li>
</ul>
<a name="DELETE_HOME_FURNITURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELETE_HOME_FURNITURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DELETE_HOME_FURNITURE</pre>
</li>
</ul>
<a name="MODIFY_FURNITURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_FURNITURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_FURNITURE</pre>
</li>
</ul>
<a name="IMPORT_FURNITURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMPORT_FURNITURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> IMPORT_FURNITURE</pre>
</li>
</ul>
<a name="IMPORT_FURNITURE_LIBRARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMPORT_FURNITURE_LIBRARY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> IMPORT_FURNITURE_LIBRARY</pre>
</li>
</ul>
<a name="IMPORT_TEXTURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMPORT_TEXTURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> IMPORT_TEXTURE</pre>
</li>
</ul>
<a name="IMPORT_TEXTURES_LIBRARY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMPORT_TEXTURES_LIBRARY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> IMPORT_TEXTURES_LIBRARY</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_CATALOG_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_CATALOG_ID</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_CATALOG_ID</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_NAME</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_NAME</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_DESCRIPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_DESCRIPTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_DESCRIPTION</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_CREATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_CREATOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_CREATOR</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_LICENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_LICENSE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_LICENSE</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_WIDTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_WIDTH</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_DEPTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_DEPTH</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_HEIGHT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_HEIGHT</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_X</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_X</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_Y</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_Y</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_ELEVATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_ELEVATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_ELEVATION</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_ANGLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_ANGLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_ANGLE</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_LEVEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_LEVEL</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_MODEL_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_MODEL_SIZE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_MODEL_SIZE</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_COLOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_COLOR</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_TEXTURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_TEXTURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_TEXTURE</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_MOVABILITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_MOVABILITY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_MOVABILITY</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_TYPE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_TYPE</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_VISIBILITY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_VISIBILITY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_VISIBILITY</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_PRICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_PRICE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_PRICE</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX_PERCENTAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX_PERCENTAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX_PERCENTAGE</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_VALUE_ADDED_TAX</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_PRICE_VALUE_ADDED_TAX_INCLUDED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_PRICE_VALUE_ADDED_TAX_INCLUDED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_PRICE_VALUE_ADDED_TAX_INCLUDED</pre>
</li>
</ul>
<a name="SORT_HOME_FURNITURE_BY_DESCENDING_ORDER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_BY_DESCENDING_ORDER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SORT_HOME_FURNITURE_BY_DESCENDING_ORDER</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_CATALOG_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_CATALOG_ID</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_CATALOG_ID</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_NAME</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_NAME</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_DESCRIPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_DESCRIPTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_DESCRIPTION</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_CREATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_CREATOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_CREATOR</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_LICENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_LICENSE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_LICENSE</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_WIDTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_WIDTH</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_DEPTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_DEPTH</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_HEIGHT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_HEIGHT</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_X</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_X</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_Y</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_Y</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_ELEVATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_ELEVATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_ELEVATION</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_ANGLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_ANGLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_ANGLE</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_LEVEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_LEVEL</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_MODEL_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_MODEL_SIZE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_MODEL_SIZE</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_COLOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_COLOR</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_TEXTURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_TEXTURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_TEXTURE</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_MOVABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_MOVABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_MOVABLE</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_DOOR_OR_WINDOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_DOOR_OR_WINDOW</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_DOOR_OR_WINDOW</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_VISIBLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_VISIBLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_VISIBLE</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_PRICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_PRICE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_PRICE</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX_PERCENTAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX_PERCENTAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX_PERCENTAGE</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_VALUE_ADDED_TAX</pre>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_PRICE_VALUE_ADDED_TAX_INCLUDED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_PRICE_VALUE_ADDED_TAX_INCLUDED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_HOME_FURNITURE_PRICE_VALUE_ADDED_TAX_INCLUDED</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_ON_TOP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_ON_TOP</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_ON_TOP</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_ON_BOTTOM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_ON_BOTTOM</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_ON_BOTTOM</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_ON_LEFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_ON_LEFT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_ON_LEFT</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_ON_RIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_ON_RIGHT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_ON_RIGHT</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_ON_FRONT_SIDE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_ON_FRONT_SIDE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_ON_FRONT_SIDE</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_ON_BACK_SIDE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_ON_BACK_SIDE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_ON_BACK_SIDE</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_ON_LEFT_SIDE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_ON_LEFT_SIDE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_ON_LEFT_SIDE</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_ON_RIGHT_SIDE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_ON_RIGHT_SIDE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_ON_RIGHT_SIDE</pre>
</li>
</ul>
<a name="ALIGN_FURNITURE_SIDE_BY_SIDE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_FURNITURE_SIDE_BY_SIDE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ALIGN_FURNITURE_SIDE_BY_SIDE</pre>
</li>
</ul>
<a name="DISTRIBUTE_FURNITURE_HORIZONTALLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISTRIBUTE_FURNITURE_HORIZONTALLY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISTRIBUTE_FURNITURE_HORIZONTALLY</pre>
</li>
</ul>
<a name="DISTRIBUTE_FURNITURE_VERTICALLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISTRIBUTE_FURNITURE_VERTICALLY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISTRIBUTE_FURNITURE_VERTICALLY</pre>
</li>
</ul>
<a name="RESET_FURNITURE_ELEVATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESET_FURNITURE_ELEVATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> RESET_FURNITURE_ELEVATION</pre>
</li>
</ul>
<a name="GROUP_FURNITURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GROUP_FURNITURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> GROUP_FURNITURE</pre>
</li>
</ul>
<a name="UNGROUP_FURNITURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNGROUP_FURNITURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> UNGROUP_FURNITURE</pre>
</li>
</ul>
<a name="EXPORT_TO_CSV">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXPORT_TO_CSV</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> EXPORT_TO_CSV</pre>
</li>
</ul>
<a name="SELECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SELECT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SELECT</pre>
</li>
</ul>
<a name="PAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PAN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> PAN</pre>
</li>
</ul>
<a name="CREATE_WALLS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATE_WALLS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CREATE_WALLS</pre>
</li>
</ul>
<a name="CREATE_ROOMS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATE_ROOMS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CREATE_ROOMS</pre>
</li>
</ul>
<a name="CREATE_DIMENSION_LINES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATE_DIMENSION_LINES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CREATE_DIMENSION_LINES</pre>
</li>
</ul>
<a name="CREATE_POLYLINES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATE_POLYLINES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CREATE_POLYLINES</pre>
</li>
</ul>
<a name="CREATE_LABELS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATE_LABELS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CREATE_LABELS</pre>
</li>
</ul>
<a name="DELETE_SELECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELETE_SELECTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DELETE_SELECTION</pre>
</li>
</ul>
<a name="LOCK_BASE_PLAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LOCK_BASE_PLAN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> LOCK_BASE_PLAN</pre>
</li>
</ul>
<a name="UNLOCK_BASE_PLAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UNLOCK_BASE_PLAN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> UNLOCK_BASE_PLAN</pre>
</li>
</ul>
<a name="ENABLE_MAGNETISM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENABLE_MAGNETISM</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ENABLE_MAGNETISM</pre>
</li>
</ul>
<a name="DISABLE_MAGNETISM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISABLE_MAGNETISM</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISABLE_MAGNETISM</pre>
</li>
</ul>
<a name="FLIP_HORIZONTALLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLIP_HORIZONTALLY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> FLIP_HORIZONTALLY</pre>
</li>
</ul>
<a name="FLIP_VERTICALLY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FLIP_VERTICALLY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> FLIP_VERTICALLY</pre>
</li>
</ul>
<a name="MODIFY_COMPASS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_COMPASS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_COMPASS</pre>
</li>
</ul>
<a name="MODIFY_WALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_WALL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_WALL</pre>
</li>
</ul>
<a name="JOIN_WALLS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>JOIN_WALLS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> JOIN_WALLS</pre>
</li>
</ul>
<a name="REVERSE_WALL_DIRECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>REVERSE_WALL_DIRECTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> REVERSE_WALL_DIRECTION</pre>
</li>
</ul>
<a name="SPLIT_WALL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SPLIT_WALL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SPLIT_WALL</pre>
</li>
</ul>
<a name="MODIFY_ROOM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_ROOM</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_ROOM</pre>
</li>
</ul>
<a name="RECOMPUTE_ROOM_POINTS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RECOMPUTE_ROOM_POINTS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> RECOMPUTE_ROOM_POINTS</pre>
</li>
</ul>
<a name="ADD_ROOM_POINT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ADD_ROOM_POINT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ADD_ROOM_POINT</pre>
</li>
</ul>
<a name="DELETE_ROOM_POINT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELETE_ROOM_POINT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DELETE_ROOM_POINT</pre>
</li>
</ul>
<a name="MODIFY_POLYLINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_POLYLINE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_POLYLINE</pre>
</li>
</ul>
<a name="MODIFY_DIMENSION_LINE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_DIMENSION_LINE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_DIMENSION_LINE</pre>
</li>
</ul>
<a name="MODIFY_LABEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_LABEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_LABEL</pre>
</li>
</ul>
<a name="INCREASE_TEXT_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INCREASE_TEXT_SIZE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> INCREASE_TEXT_SIZE</pre>
</li>
</ul>
<a name="DECREASE_TEXT_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECREASE_TEXT_SIZE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DECREASE_TEXT_SIZE</pre>
</li>
</ul>
<a name="TOGGLE_BOLD_STYLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TOGGLE_BOLD_STYLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> TOGGLE_BOLD_STYLE</pre>
</li>
</ul>
<a name="TOGGLE_ITALIC_STYLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TOGGLE_ITALIC_STYLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> TOGGLE_ITALIC_STYLE</pre>
</li>
</ul>
<a name="IMPORT_BACKGROUND_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IMPORT_BACKGROUND_IMAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> IMPORT_BACKGROUND_IMAGE</pre>
</li>
</ul>
<a name="MODIFY_BACKGROUND_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_BACKGROUND_IMAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_BACKGROUND_IMAGE</pre>
</li>
</ul>
<a name="HIDE_BACKGROUND_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HIDE_BACKGROUND_IMAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> HIDE_BACKGROUND_IMAGE</pre>
</li>
</ul>
<a name="SHOW_BACKGROUND_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHOW_BACKGROUND_IMAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SHOW_BACKGROUND_IMAGE</pre>
</li>
</ul>
<a name="DELETE_BACKGROUND_IMAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELETE_BACKGROUND_IMAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DELETE_BACKGROUND_IMAGE</pre>
</li>
</ul>
<a name="ADD_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ADD_LEVEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ADD_LEVEL</pre>
</li>
</ul>
<a name="ADD_LEVEL_AT_SAME_ELEVATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ADD_LEVEL_AT_SAME_ELEVATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ADD_LEVEL_AT_SAME_ELEVATION</pre>
</li>
</ul>
<a name="MAKE_LEVEL_VIEWABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAKE_LEVEL_VIEWABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MAKE_LEVEL_VIEWABLE</pre>
</li>
</ul>
<a name="MAKE_LEVEL_UNVIEWABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAKE_LEVEL_UNVIEWABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MAKE_LEVEL_UNVIEWABLE</pre>
</li>
</ul>
<a name="MAKE_LEVEL_ONLY_VIEWABLE_ONE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAKE_LEVEL_ONLY_VIEWABLE_ONE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MAKE_LEVEL_ONLY_VIEWABLE_ONE</pre>
</li>
</ul>
<a name="MAKE_ALL_LEVELS_VIEWABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MAKE_ALL_LEVELS_VIEWABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MAKE_ALL_LEVELS_VIEWABLE</pre>
</li>
</ul>
<a name="MODIFY_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_LEVEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_LEVEL</pre>
</li>
</ul>
<a name="DELETE_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELETE_LEVEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DELETE_LEVEL</pre>
</li>
</ul>
<a name="ZOOM_OUT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZOOM_OUT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ZOOM_OUT</pre>
</li>
</ul>
<a name="ZOOM_IN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ZOOM_IN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ZOOM_IN</pre>
</li>
</ul>
<a name="EXPORT_TO_SVG">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXPORT_TO_SVG</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> EXPORT_TO_SVG</pre>
</li>
</ul>
<a name="SELECT_OBJECT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SELECT_OBJECT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> SELECT_OBJECT</pre>
</li>
</ul>
<a name="TOGGLE_SELECTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TOGGLE_SELECTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> TOGGLE_SELECTION</pre>
</li>
</ul>
<a name="VIEW_FROM_TOP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIEW_FROM_TOP</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> VIEW_FROM_TOP</pre>
</li>
</ul>
<a name="VIEW_FROM_OBSERVER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VIEW_FROM_OBSERVER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> VIEW_FROM_OBSERVER</pre>
</li>
</ul>
<a name="MODIFY_OBSERVER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_OBSERVER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_OBSERVER</pre>
</li>
</ul>
<a name="STORE_POINT_OF_VIEW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STORE_POINT_OF_VIEW</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> STORE_POINT_OF_VIEW</pre>
</li>
</ul>
<a name="DELETE_POINTS_OF_VIEW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DELETE_POINTS_OF_VIEW</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DELETE_POINTS_OF_VIEW</pre>
</li>
</ul>
<a name="CREATE_PHOTOS_AT_POINTS_OF_VIEW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATE_PHOTOS_AT_POINTS_OF_VIEW</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CREATE_PHOTOS_AT_POINTS_OF_VIEW</pre>
</li>
</ul>
<a name="DETACH_3D_VIEW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DETACH_3D_VIEW</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DETACH_3D_VIEW</pre>
</li>
</ul>
<a name="ATTACH_3D_VIEW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ATTACH_3D_VIEW</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ATTACH_3D_VIEW</pre>
</li>
</ul>
<a name="DISPLAY_ALL_LEVELS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_ALL_LEVELS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_ALL_LEVELS</pre>
</li>
</ul>
<a name="DISPLAY_SELECTED_LEVEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISPLAY_SELECTED_LEVEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> DISPLAY_SELECTED_LEVEL</pre>
</li>
</ul>
<a name="MODIFY_3D_ATTRIBUTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODIFY_3D_ATTRIBUTES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> MODIFY_3D_ATTRIBUTES</pre>
</li>
</ul>
<a name="CREATE_PHOTO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATE_PHOTO</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CREATE_PHOTO</pre>
</li>
</ul>
<a name="CREATE_VIDEO">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATE_VIDEO</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> CREATE_VIDEO</pre>
</li>
</ul>
<a name="EXPORT_TO_OBJ">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>EXPORT_TO_OBJ</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> EXPORT_TO_OBJ</pre>
</li>
</ul>
<a name="HELP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HELP</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> HELP</pre>
</li>
</ul>
<a name="ABOUT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ABOUT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a> ABOUT</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (HomeView.ActionType c : HomeView.ActionType.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeView.ActionType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" target="_top">Frames</a></li>
<li><a href="HomeView.ActionType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
