<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>CatalogDoorOrWindow (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CatalogDoorOrWindow (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CatalogDoorOrWindow.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/Camera.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" target="_top">Frames</a></li>
<li><a href="CatalogDoorOrWindow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class CatalogDoorOrWindow" class="title">Class CatalogDoorOrWindow</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.CatalogPieceOfFurniture</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.CatalogDoorOrWindow</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a>, <a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, java.lang.Cloneable, java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">CatalogDoorOrWindow</span>
extends <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>
implements <a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></pre>
<div class="block">A door or a window of the catalog.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-boolean-float-boolean-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;name,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   java.lang.Integer&nbsp;color,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   float&nbsp;iconYaw,
                   boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-boolean-java.lang.Long-java.lang.String-float-boolean-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;name,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   java.lang.Integer&nbsp;color,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   float&nbsp;iconYaw,
                   boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-int-java.lang.Long-java.lang.String-float-float-float-boolean-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;name,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   java.lang.Integer&nbsp;color,
                   float[][]&nbsp;modelRotation,
                   int&nbsp;modelFlags,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   float&nbsp;iconYaw,
                   float&nbsp;iconPitch,
                   float&nbsp;iconScale,
                   boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency,
                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   int&nbsp;modelFlags,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency,
                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String&nbsp;license,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   int&nbsp;modelFlags,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency,
                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                   java.util.Map&lt;java.lang.String,<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#getCutOutShape--">getCutOutShape</a></span>()</code>
<div class="block">Returns the shape used to cut out walls that intersect this new door or window.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#getSashes--">getSashes</a></span>()</code>
<div class="block">Returns a copy of the sashes attached to this door or window.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#getWallDistance--">getWallDistance</a></span>()</code>
<div class="block">Returns the default distance that should lie at the back side of this door or window.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#getWallThickness--">getWallThickness</a></span>()</code>
<div class="block">Returns the default thickness of the wall in which this door or window should be placed.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#isDoorOrWindow--">isDoorOrWindow</a></span>()</code>
<div class="block">Returns always <code>true</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></span>()</code>
<div class="block">Returns always <code>false</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#isWallCutOutOnBothSides--">isWallCutOutOnBothSides</a></span>()</code>
<div class="block">Returns <code>true</code> if this door or window should cut out the both sides
 of the walls it intersects, even if its front or back side are within the wall thickness.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></span>()</code>
<div class="block">Returns <code>false</code> if the width and depth of the new door or window may
 not be changed independently from each other.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.CatalogPieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#clone--">clone</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#compareTo-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-">compareTo</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#equals-java.lang.Object-">equals</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getCategory--">getCategory</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getColor--">getColor</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getCreationDate--">getCreationDate</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getCreator--">getCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getDepth--">getDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getFilterCriteria--">getFilterCriteria</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getGrade--">getGrade</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getHeight--">getHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIcon--">getIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIconPitch--">getIconPitch</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIconScale--">getIconScale</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIconYaw--">getIconYaw</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getInformation--">getInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModel--">getModel</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModelFlags--">getModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModelRotation--">getModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModelSize--">getModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getPlanIcon--">getPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getPrice--">getPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getTags--">getTags</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getWidth--">getWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#hashCode--">hashCode</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isDeformable--">isDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isModifiable--">isModifiable</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isMovable--">isMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isProportional--">isProportional</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isTexturable--">isTexturable</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#matchesFilter-java.lang.String-">matchesFilter</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>finalize, getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           java.lang.String&nbsp;information,
                           java.lang.String[]&nbsp;tags,
                           java.lang.Long&nbsp;creationDate,
                           java.lang.Float&nbsp;grade,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>information</code> - additional information associated to the new door or window</dd>
<dd><code>tags</code> - tags associated to the new door or window</dd>
<dd><code>creationDate</code> - creation date of the new door or window in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the new door or window or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           java.lang.String&nbsp;information,
                           java.lang.String[]&nbsp;tags,
                           java.lang.Long&nbsp;creationDate,
                           java.lang.Float&nbsp;grade,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           java.lang.String&nbsp;cutOutShape,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>information</code> - additional information associated to the new door or window</dd>
<dd><code>tags</code> - tags associated to the new door or window</dd>
<dd><code>creationDate</code> - creation date of the new door or window in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the new door or window or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>cutOutShape</code> - the shape used to cut out walls that intersect the new door or window</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           java.lang.String&nbsp;information,
                           java.lang.String[]&nbsp;tags,
                           java.lang.Long&nbsp;creationDate,
                           java.lang.Float&nbsp;grade,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           float&nbsp;dropOnTopElevation,
                           boolean&nbsp;movable,
                           java.lang.String&nbsp;cutOutShape,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>information</code> - additional information associated to the new door or window</dd>
<dd><code>tags</code> - tags associated to the new door or window</dd>
<dd><code>creationDate</code> - creation date of the new door or window in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the new door or window or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>cutOutShape</code> - the shape used to cut out walls that intersect the new door or window</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           java.lang.String&nbsp;information,
                           java.lang.String[]&nbsp;tags,
                           java.lang.Long&nbsp;creationDate,
                           java.lang.Float&nbsp;grade,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           float&nbsp;dropOnTopElevation,
                           boolean&nbsp;movable,
                           java.lang.String&nbsp;cutOutShape,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           boolean&nbsp;backFaceShown,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>information</code> - additional information associated to the new door or window</dd>
<dd><code>tags</code> - tags associated to the new door or window</dd>
<dd><code>creationDate</code> - creation date of the new door or window in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the new door or window or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>cutOutShape</code> - the shape used to cut out walls that intersect the new door or window</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown instead of front faces</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           java.lang.String&nbsp;information,
                           java.lang.String[]&nbsp;tags,
                           java.lang.Long&nbsp;creationDate,
                           java.lang.Float&nbsp;grade,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           float&nbsp;dropOnTopElevation,
                           boolean&nbsp;movable,
                           java.lang.String&nbsp;cutOutShape,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           boolean&nbsp;wallCutOutOnBothSides,
                           boolean&nbsp;widthDepthDeformable,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           boolean&nbsp;backFaceShown,
                           java.lang.Long&nbsp;modelSize,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>information</code> - additional information associated to the new door or window</dd>
<dd><code>tags</code> - tags associated to the new door or window</dd>
<dd><code>creationDate</code> - creation date of the new door or window in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the new door or window or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>cutOutShape</code> - the shape used to cut out walls that intersect the new door or window</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>wallCutOutOnBothSides</code> - if <code>true</code> the new door or window should cut out
            the both sides of the walls it intersects</dd>
<dd><code>widthDepthDeformable</code> - if <code>false</code>, the width and depth of the new door or window may
            not be changed independently from each other</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown instead of front faces</dd>
<dd><code>modelSize</code> - size of the 3D model of the new light</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           java.lang.String&nbsp;information,
                           java.lang.String[]&nbsp;tags,
                           java.lang.Long&nbsp;creationDate,
                           java.lang.Float&nbsp;grade,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           float&nbsp;dropOnTopElevation,
                           boolean&nbsp;movable,
                           java.lang.String&nbsp;cutOutShape,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           boolean&nbsp;wallCutOutOnBothSides,
                           boolean&nbsp;widthDepthDeformable,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           boolean&nbsp;backFaceShown,
                           java.lang.Long&nbsp;modelSize,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency,
                           java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>information</code> - additional information associated to the new door or window</dd>
<dd><code>tags</code> - tags associated to the new door or window</dd>
<dd><code>creationDate</code> - creation date of the new door or window in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the new door or window or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>cutOutShape</code> - the shape used to cut out walls that intersect the new door or window</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>wallCutOutOnBothSides</code> - if <code>true</code> the new door or window should cut out
            the both sides of the walls it intersects</dd>
<dd><code>widthDepthDeformable</code> - if <code>false</code>, the width and depth of the new door or window may
            not be changed independently from each other</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown instead of front faces</dd>
<dd><code>modelSize</code> - size of the 3D model of the new light</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dd><code>properties</code> - additional properties associating a key to a value or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.7</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           java.lang.String&nbsp;information,
                           java.lang.String[]&nbsp;tags,
                           java.lang.Long&nbsp;creationDate,
                           java.lang.Float&nbsp;grade,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           float&nbsp;dropOnTopElevation,
                           boolean&nbsp;movable,
                           java.lang.String&nbsp;cutOutShape,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           boolean&nbsp;wallCutOutOnBothSides,
                           boolean&nbsp;widthDepthDeformable,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           int&nbsp;modelFlags,
                           java.lang.Long&nbsp;modelSize,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency,
                           java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>information</code> - additional information associated to the new door or window</dd>
<dd><code>tags</code> - tags associated to the new door or window</dd>
<dd><code>creationDate</code> - creation date of the new door or window in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the new door or window or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>cutOutShape</code> - the shape used to cut out walls that intersect the new door or window</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>wallCutOutOnBothSides</code> - if <code>true</code> the new door or window should cut out
            the both sides of the walls it intersects</dd>
<dd><code>widthDepthDeformable</code> - if <code>false</code>, the width and depth of the new door or window may
            not be changed independently from each other</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>modelFlags</code> - flags which should be applied to piece model</dd>
<dd><code>modelSize</code> - size of the 3D model of the new light</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dd><code>properties</code> - additional properties associating a key to a value or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;id,
                           java.lang.String&nbsp;name,
                           java.lang.String&nbsp;description,
                           java.lang.String&nbsp;information,
                           java.lang.String&nbsp;license,
                           java.lang.String[]&nbsp;tags,
                           java.lang.Long&nbsp;creationDate,
                           java.lang.Float&nbsp;grade,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           float&nbsp;dropOnTopElevation,
                           boolean&nbsp;movable,
                           java.lang.String&nbsp;cutOutShape,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           boolean&nbsp;wallCutOutOnBothSides,
                           boolean&nbsp;widthDepthDeformable,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           float[][]&nbsp;modelRotation,
                           int&nbsp;modelFlags,
                           java.lang.Long&nbsp;modelSize,
                           java.lang.String&nbsp;creator,
                           boolean&nbsp;resizable,
                           boolean&nbsp;deformable,
                           boolean&nbsp;texturable,
                           java.math.BigDecimal&nbsp;price,
                           java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                           java.lang.String&nbsp;currency,
                           java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                           java.util.Map&lt;java.lang.String,<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</pre>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new door or window, or <code>null</code></dd>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>description</code> - the description of the new door or window</dd>
<dd><code>information</code> - additional information associated to the new door or window</dd>
<dd><code>license</code> - license of the new door or window</dd>
<dd><code>tags</code> - tags associated to the new door or window</dd>
<dd><code>creationDate</code> - creation date of the new door or window in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the new door or window or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>cutOutShape</code> - the shape used to cut out walls that intersect the new door or window</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>wallCutOutOnBothSides</code> - if <code>true</code> the new door or window should cut out
            the both sides of the walls it intersects</dd>
<dd><code>widthDepthDeformable</code> - if <code>false</code>, the width and depth of the new door or window may
            not be changed independently from each other</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>modelFlags</code> - flags which should be applied to piece model</dd>
<dd><code>modelSize</code> - size of the 3D model of the new light</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new door or window may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new door or window, or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new door or window or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dd><code>properties</code> - additional properties associating a key to a value or <code>null</code></dd>
<dd><code>contents</code> - additional contents associating a key to a value or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-boolean-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;name,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           java.lang.Integer&nbsp;color,
                           float[][]&nbsp;modelRotation,
                           boolean&nbsp;backFaceShown,
                           float&nbsp;iconYaw,
                           boolean&nbsp;proportional)</pre>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>color</code> - the color of the door or window as RGB code or <code>null</code>
        if door or window color is unchanged</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown</dd>
<dd><code>iconYaw</code> - the yaw angle used to create the door or window icon</dd>
<dd><code>proportional</code> - if <code>true</code>, size proportions will be kept</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-boolean-java.lang.Long-java.lang.String-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;name,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           java.lang.Integer&nbsp;color,
                           float[][]&nbsp;modelRotation,
                           boolean&nbsp;backFaceShown,
                           java.lang.Long&nbsp;modelSize,
                           java.lang.String&nbsp;creator,
                           float&nbsp;iconYaw,
                           boolean&nbsp;proportional)</pre>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>color</code> - the color of the door or window as RGB code or <code>null</code>
        if door or window color is unchanged</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown</dd>
<dd><code>modelSize</code> - size of the 3D model of the new piece</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>iconYaw</code> - the yaw angle used to create the door or window icon</dd>
<dd><code>proportional</code> - if <code>true</code>, size proportions will be kept</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-int-java.lang.Long-java.lang.String-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CatalogDoorOrWindow</h4>
<pre>public&nbsp;CatalogDoorOrWindow(java.lang.String&nbsp;name,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                           <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                           float&nbsp;width,
                           float&nbsp;depth,
                           float&nbsp;height,
                           float&nbsp;elevation,
                           boolean&nbsp;movable,
                           float&nbsp;wallThickness,
                           float&nbsp;wallDistance,
                           <a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                           java.lang.Integer&nbsp;color,
                           float[][]&nbsp;modelRotation,
                           int&nbsp;modelFlags,
                           java.lang.Long&nbsp;modelSize,
                           java.lang.String&nbsp;creator,
                           float&nbsp;iconYaw,
                           float&nbsp;iconPitch,
                           float&nbsp;iconScale,
                           boolean&nbsp;proportional)</pre>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new door or window</dd>
<dd><code>icon</code> - content of the icon of the new door or window</dd>
<dd><code>model</code> - content of the 3D model of the new door or window</dd>
<dd><code>width</code> - the width in centimeters of the new door or window</dd>
<dd><code>depth</code> - the depth in centimeters of the new door or window</dd>
<dd><code>height</code> - the height in centimeters of the new door or window</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new door or window</dd>
<dd><code>movable</code> - if <code>true</code>, the new door or window is movable</dd>
<dd><code>wallThickness</code> - a value in percentage of the depth of the new door or window</dd>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the new door or window</dd>
<dd><code>sashes</code> - the sashes attached to the new door or window</dd>
<dd><code>color</code> - the color of the door or window as RGB code or <code>null</code>
        if door or window color is unchanged</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the door or window model</dd>
<dd><code>modelFlags</code> - flags which should be applied to the door or window model</dd>
<dd><code>modelSize</code> - size of the 3D model of the new piece</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>iconYaw</code> - the yaw angle used to create the door or window icon</dd>
<dd><code>iconPitch</code> - the pich angle used to create the door or window icon</dd>
<dd><code>iconScale</code> - the scale used to create the door or window icon</dd>
<dd><code>proportional</code> - if <code>true</code>, size proportions will be kept</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getWallThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallThickness</h4>
<pre>public&nbsp;float&nbsp;getWallThickness()</pre>
<div class="block">Returns the default thickness of the wall in which this door or window should be placed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getWallThickness--">getWallThickness</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value in percentage of the depth of the door or the window.</dd>
</dl>
</li>
</ul>
<a name="getWallDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallDistance</h4>
<pre>public&nbsp;float&nbsp;getWallDistance()</pre>
<div class="block">Returns the default distance that should lie at the back side of this door or window.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getWallDistance--">getWallDistance</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a distance in percentage of the depth of the door or the window.</dd>
</dl>
</li>
</ul>
<a name="isWallCutOutOnBothSides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWallCutOutOnBothSides</h4>
<pre>public&nbsp;boolean&nbsp;isWallCutOutOnBothSides()</pre>
<div class="block">Returns <code>true</code> if this door or window should cut out the both sides
 of the walls it intersects, even if its front or back side are within the wall thickness.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#isWallCutOutOnBothSides--">isWallCutOutOnBothSides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isWidthDepthDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWidthDepthDeformable</h4>
<pre>public&nbsp;boolean&nbsp;isWidthDepthDeformable()</pre>
<div class="block">Returns <code>false</code> if the width and depth of the new door or window may
 not be changed independently from each other.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getSashes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSashes</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;getSashes()</pre>
<div class="block">Returns a copy of the sashes attached to this door or window.
 If no sash is defined an empty array is returned.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getSashes--">getSashes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
</dl>
</li>
</ul>
<a name="getCutOutShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCutOutShape</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCutOutShape()</pre>
<div class="block">Returns the shape used to cut out walls that intersect this new door or window.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getCutOutShape--">getCutOutShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>null</code> or a shape defined with the syntax of the d attribute of a 
 <a href="http://www.w3.org/TR/SVG/paths.html">SVG path element</a>
 that fits in a square spreading from (0, 0) to (1, 1) which will be 
 scaled afterwards to the real size of this door or window.</dd>
</dl>
</li>
</ul>
<a name="isDoorOrWindow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDoorOrWindow</h4>
<pre>public&nbsp;boolean&nbsp;isDoorOrWindow()</pre>
<div class="block">Returns always <code>true</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="isHorizontallyRotatable--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isHorizontallyRotatable</h4>
<pre>public&nbsp;boolean&nbsp;isHorizontallyRotatable()</pre>
<div class="block">Returns always <code>false</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CatalogDoorOrWindow.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/Camera.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" target="_top">Frames</a></li>
<li><a href="CatalogDoorOrWindow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
