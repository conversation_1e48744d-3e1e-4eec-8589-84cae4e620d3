# AI Plugin Testing and Deployment Guide

## Overview

This guide provides comprehensive instructions for testing and deploying the Sweet Home 3D AI Plugin.

## Prerequisites

1. **Java Development Kit (JDK) 8 or higher**
2. **Sweet Home 3D 7.0 or higher**
3. **Internet connection** (for cloud AI providers)
4. **Local AI tools** (optional, for local testing)

## Testing Phases

### Phase 1: Unit Testing

#### Configuration System Testing
```java
// Run the validator
AIPluginValidator validator = new AIPluginValidator();
String report = validator.runAllValidations();
System.out.println(report);
```

#### Manual Configuration Tests
1. Test provider preset selection
2. Validate API key storage and retrieval
3. Test connection to various providers
4. Verify configuration persistence

### Phase 2: Integration Testing

#### AI Provider Testing

**OpenAI Testing:**
1. Configure OpenAI provider with valid API key
2. Test connection
3. Perform floor plan analysis
4. Verify response quality

**Local Provider Testing (Ollama):**
1. Install Ollama locally
2. Download a model (e.g., `ollama pull llama3.3`)
3. Configure plugin to use Ollama
4. Test local analysis

**Other Providers:**
- Test with Anthropic Claude
- Test with Google Gemini
- Test with local LM Studio
- Test with Together AI

#### Data Extraction Testing
1. Create various floor plan configurations
2. Test data extraction accuracy
3. Verify JSON structure
4. Test privacy sanitization

#### UI Testing
1. Test AI Settings Dialog
   - Provider selection
   - Connection testing
   - Configuration saving
2. Test AI Chat Dialog
   - Initial analysis
   - Follow-up questions
   - Error handling

### Phase 3: User Acceptance Testing

#### Test Scenarios

**Scenario 1: First-time User**
1. Install plugin
2. Open Sweet Home 3D
3. Click AI Analysis button
4. Configure AI provider
5. Analyze a floor plan

**Scenario 2: Experienced User**
1. Switch between different AI providers
2. Test various floor plan types
3. Ask follow-up questions
4. Verify privacy settings

**Scenario 3: Error Handling**
1. Test with invalid API keys
2. Test with network disconnection
3. Test with malformed responses
4. Verify error messages are user-friendly

## Deployment Instructions

### Building the Plugin

1. **Compile the Java classes:**
```bash
javac -cp "path/to/sweethome3d.jar" src/com/eteks/sweethome3d/plugin/ai/*.java
```

2. **Create the JAR file:**
```bash
jar cf ai-plugin.jar -C src com/eteks/sweethome3d/plugin/ai/
```

3. **Include resources:**
```bash
jar uf ai-plugin.jar -C src com/eteks/sweethome3d/plugin/ai/resources/
```

### Installation

1. **Copy the JAR file** to Sweet Home 3D plugins directory:
   - Windows: `%APPDATA%/eTeks/Sweet Home 3D/plugins/`
   - macOS: `~/Library/Application Support/eTeks/Sweet Home 3D/plugins/`
   - Linux: `~/.eteks/sweethome3d/plugins/`

2. **Restart Sweet Home 3D**

3. **Verify installation:**
   - Look for "AI Analysis" button in toolbar
   - Check Tools menu for AI Analysis option

### Configuration

1. **First-time setup:**
   - Click AI Analysis button
   - Configure your preferred AI provider
   - Test connection
   - Save configuration

2. **Provider-specific setup:**

   **For OpenAI:**
   - Get API key from OpenAI dashboard
   - Select "OpenAI" preset
   - Enter API key
   - Choose model (gpt-4o recommended)

   **For Ollama (Local):**
   - Install Ollama: `curl -fsSL https://ollama.ai/install.sh | sh`
   - Pull a model: `ollama pull llama3.3`
   - Select "Ollama (Local)" preset
   - No API key needed

   **For Anthropic:**
   - Get API key from Anthropic Console
   - Select "Anthropic" preset
   - Enter API key
   - Choose model (claude-3.5-sonnet recommended)

## Performance Testing

### Metrics to Monitor
- Response time for analysis
- Memory usage during operation
- Network bandwidth usage
- Error rates

### Load Testing
1. Test with large floor plans
2. Test multiple concurrent analyses
3. Monitor system resources

## Security Testing

### Privacy Validation
1. Verify personal information sanitization
2. Test API key encryption
3. Validate secure storage

### Network Security
1. Verify HTTPS connections
2. Test certificate validation
3. Check for data leaks

## Troubleshooting

### Common Issues

**"AI service not configured"**
- Solution: Configure AI provider in settings

**"Connection failed"**
- Check internet connection
- Verify API key
- Test with different provider

**"Rate limit exceeded"**
- Wait before retrying
- Consider upgrading API plan
- Switch to local provider

**Plugin not loading**
- Check Java version compatibility
- Verify JAR file integrity
- Check Sweet Home 3D version

### Debug Mode

Enable debug logging by adding system property:
```
-Dcom.eteks.sweethome3d.plugin.ai.debug=true
```

## Quality Assurance Checklist

- [ ] All provider presets work correctly
- [ ] Configuration persistence works
- [ ] Privacy settings are respected
- [ ] Error messages are user-friendly
- [ ] UI is responsive and intuitive
- [ ] Plugin loads without errors
- [ ] Analysis results are relevant
- [ ] Follow-up questions work
- [ ] Connection testing works
- [ ] Localization works (if applicable)

## Release Preparation

1. **Version Management**
   - Update version in ApplicationPlugin.properties
   - Tag release in version control

2. **Documentation**
   - Update README
   - Create release notes
   - Update user manual

3. **Distribution**
   - Create signed JAR file
   - Upload to distribution platform
   - Notify users of new release

## Support and Maintenance

### Monitoring
- Track usage statistics
- Monitor error rates
- Collect user feedback

### Updates
- Regular security updates
- New AI provider support
- Feature enhancements
- Bug fixes

### Community
- Maintain documentation
- Respond to user issues
- Provide examples and tutorials
