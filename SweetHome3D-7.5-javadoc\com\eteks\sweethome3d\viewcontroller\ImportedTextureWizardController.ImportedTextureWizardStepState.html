<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>ImportedTextureWizardController.ImportedTextureWizardStepState (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ImportedTextureWizardController.ImportedTextureWizardStepState (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":6,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ImportedTextureWizardController.ImportedTextureWizardStepState.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.ImportedTextureWizardStepState.html" target="_top">Frames</a></li>
<li><a href="ImportedTextureWizardController.ImportedTextureWizardStepState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class ImportedTextureWizardController.ImportedTextureWizardStepState" class="title">Class ImportedTextureWizardController.ImportedTextureWizardStepState</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller.WizardController.WizardControllerStepState</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController.ImportedTextureWizardStepState</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a></dd>
</dl>
<hr>
<br>
<pre>protected abstract class <span class="typeNameLabel">ImportedTextureWizardController.ImportedTextureWizardStepState</span>
extends <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></pre>
<div class="block">Step state superclass. All step state share the same step view,
 that will display a different component depending on their class name.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colLast" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.ImportedTextureWizardStepState.html#ImportedTextureWizardStepState--">ImportedTextureWizardStepState</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.ImportedTextureWizardStepState.html#enter--">enter</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.net.URL</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.ImportedTextureWizardStepState.html#getIcon--">getIcon</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>abstract <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController.Step</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.ImportedTextureWizardStepState.html#getStep--">getStep</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.ImportedTextureWizardStepState.html#getView--">getView</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.WizardController.WizardControllerStepState">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#exit--">exit</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#goBackToPreviousStep--">goBackToPreviousStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#goToNextStep--">goToNextStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#isFirstStep--">isFirstStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#isLastStep--">isLastStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#isNextStepEnabled--">isNextStepEnabled</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#setFirstStep-boolean-">setFirstStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#setLastStep-boolean-">setLastStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#setNextStepEnabled-boolean-">setNextStepEnabled</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ImportedTextureWizardStepState--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ImportedTextureWizardStepState</h4>
<pre>protected&nbsp;ImportedTextureWizardStepState()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getStep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStep</h4>
<pre>public abstract&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController.Step</a>&nbsp;getStep()</pre>
</li>
</ul>
<a name="enter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enter</h4>
<pre>public&nbsp;void&nbsp;enter()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#enter--">enter</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></code></dd>
</dl>
</li>
</ul>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getView()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#getView--">getView</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></code></dd>
</dl>
</li>
</ul>
<a name="getIcon--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;java.net.URL&nbsp;getIcon()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#getIcon--">getIcon</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ImportedTextureWizardController.ImportedTextureWizardStepState.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.ImportedTextureWizardStepState.html" target="_top">Frames</a></li>
<li><a href="ImportedTextureWizardController.ImportedTextureWizardStepState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
