<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Package com.eteks.sweethome3d.io (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.eteks.sweethome3d.io (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.eteks.sweethome3d.io" class="title">Uses of Package<br>com.eteks.sweethome3d.io</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> used by <a href="../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/ContentRecording.html#com.eteks.sweethome3d.applet">ContentRecording</a>
<div class="block">Describes how <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">content</a> associated to a home should be managed during recording.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/HomeXMLExporter.html#com.eteks.sweethome3d.applet">HomeXMLExporter</a>
<div class="block">Exporter for home instances.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/HomeXMLHandler.html#com.eteks.sweethome3d.applet">HomeXMLHandler</a>
<div class="block">SAX handler for Sweet Home 3D XML stream.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> used by <a href="../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/ContentDigestManager.html#com.eteks.sweethome3d.io">ContentDigestManager</a>
<div class="block">Manager able to store and compute content digest to compare content data faster.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/ContentRecording.html#com.eteks.sweethome3d.io">ContentRecording</a>
<div class="block">Describes how <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">content</a> associated to a home should be managed during recording.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/DefaultFurnitureCatalog.PropertyKey.html#com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a>
<div class="block">The keys of the properties values read in <code>.properties</code> files.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/DefaultTexturesCatalog.PropertyKey.html#com.eteks.sweethome3d.io">DefaultTexturesCatalog.PropertyKey</a>
<div class="block">The keys of the properties values read in <code>.properties</code> files.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/HomeXMLExporter.html#com.eteks.sweethome3d.io">HomeXMLExporter</a>
<div class="block">Exporter for home instances.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/HomeXMLHandler.html#com.eteks.sweethome3d.io">HomeXMLHandler</a>
<div class="block">SAX handler for Sweet Home 3D XML stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/ObjectXMLExporter.html#com.eteks.sweethome3d.io">ObjectXMLExporter</a>
<div class="block">Base class used to write objects to XML.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/io/class-use/XMLWriter.html#com.eteks.sweethome3d.io">XMLWriter</a>
<div class="block">A simple XML writer able to write XML elements, their attributes and texts, indenting child elements.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
