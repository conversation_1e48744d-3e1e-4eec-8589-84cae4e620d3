<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>ObserverCameraController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ObserverCameraController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObserverCameraController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" target="_top">Frames</a></li>
<li><a href="ObserverCameraController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class ObserverCameraController" class="title">Class ObserverCameraController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.ObserverCameraController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ObserverCameraController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for observer camera attributes view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ObserverCameraController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#ObserverCameraController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">ObserverCameraController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                        <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates the controller of 3D view with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.ObserverCameraController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ObserverCameraController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the edited camera elevation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getFieldOfView--">getFieldOfView</a></span>()</code>
<div class="block">Returns the edited observer field of view in radians.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getFieldOfViewInDegrees--">getFieldOfViewInDegrees</a></span>()</code>
<div class="block">Returns the edited observer field of view in degrees.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getMinimumElevation--">getMinimumElevation</a></span>()</code>
<div class="block">Returns the minimum elevation.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns the edited pitch in radians.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getPitchInDegrees--">getPitchInDegrees</a></span>()</code>
<div class="block">Returns the edited pitch in degrees.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getX--">getX</a></span>()</code>
<div class="block">Returns the edited abscissa.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getY--">getY</a></span>()</code>
<div class="block">Returns the edited ordinate.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getYaw--">getYaw</a></span>()</code>
<div class="block">Returns the edited yaw in radians.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getYawInDegrees--">getYawInDegrees</a></span>()</code>
<div class="block">Returns the edited yaw in degrees.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#isElevationAdjusted--">isElevationAdjusted</a></span>()</code>
<div class="block">Returns <code>true</code> if the observer elevation should be adjusted according 
 to the elevation of the selected level.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#isObserverCameraElevationAdjustedEditable--">isObserverCameraElevationAdjustedEditable</a></span>()</code>
<div class="block">Returns <code>true</code> if the adjustment of the observer camera according to the current level is modifiable.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#modifyObserverCamera--">modifyObserverCamera</a></span>()</code>
<div class="block">Controls the modification of the observer camera of the edited home.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.ObserverCameraController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ObserverCameraController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setElevation-float-">setElevation</a></span>(float&nbsp;elevation)</code>
<div class="block">Sets the edited camera elevation.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setElevationAdjusted-boolean-">setElevationAdjusted</a></span>(boolean&nbsp;observerCameraElevationAdjusted)</code>
<div class="block">Sets whether the observer elevation should be adjusted according 
 to the elevation of the selected level.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setFieldOfView-float-">setFieldOfView</a></span>(float&nbsp;fieldOfView)</code>
<div class="block">Sets the edited observer field of view in radians.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setFieldOfViewInDegrees-int-">setFieldOfViewInDegrees</a></span>(int&nbsp;fieldOfViewInDegrees)</code>
<div class="block">Sets the edited observer field of view in degrees.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setFieldOfViewInDegrees-int-boolean-">setFieldOfViewInDegrees</a></span>(int&nbsp;fieldOfViewInDegrees,
                       boolean&nbsp;updateFieldOfView)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setPitch-float-">setPitch</a></span>(float&nbsp;pitch)</code>
<div class="block">Sets the edited pitch in radians.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setPitchInDegrees-int-">setPitchInDegrees</a></span>(int&nbsp;pitchInDegrees)</code>
<div class="block">Sets the edited pitch in degrees.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setX-float-">setX</a></span>(float&nbsp;x)</code>
<div class="block">Sets the edited abscissa.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setY-float-">setY</a></span>(float&nbsp;y)</code>
<div class="block">Sets the edited ordinate.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setYaw-float-">setYaw</a></span>(float&nbsp;yaw)</code>
<div class="block">Sets the edited yaw in radians.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#setYawInDegrees-int-">setYawInDegrees</a></span>(int&nbsp;yawInDegrees)</code>
<div class="block">Sets the edited yaw in degrees.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates edited properties from the 3D attributes of the home edited by this controller.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ObserverCameraController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ObserverCameraController</h4>
<pre>public&nbsp;ObserverCameraController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</pre>
<div class="block">Creates the controller of 3D view with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.ObserverCameraController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ObserverCameraController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.ObserverCameraController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ObserverCameraController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates edited properties from the 3D attributes of the home edited by this controller.</div>
</li>
</ul>
<a name="setX-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public&nbsp;void&nbsp;setX(float&nbsp;x)</pre>
<div class="block">Sets the edited abscissa.</div>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public&nbsp;float&nbsp;getX()</pre>
<div class="block">Returns the edited abscissa.</div>
</li>
</ul>
<a name="setY-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public&nbsp;void&nbsp;setY(float&nbsp;y)</pre>
<div class="block">Sets the edited ordinate.</div>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public&nbsp;float&nbsp;getY()</pre>
<div class="block">Returns the edited ordinate.</div>
</li>
</ul>
<a name="setElevation-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevation</h4>
<pre>public&nbsp;void&nbsp;setElevation(float&nbsp;elevation)</pre>
<div class="block">Sets the edited camera elevation.</div>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>public&nbsp;float&nbsp;getElevation()</pre>
<div class="block">Returns the edited camera elevation.</div>
</li>
</ul>
<a name="getMinimumElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinimumElevation</h4>
<pre>public&nbsp;float&nbsp;getMinimumElevation()</pre>
<div class="block">Returns the minimum elevation.</div>
</li>
</ul>
<a name="isElevationAdjusted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isElevationAdjusted</h4>
<pre>public&nbsp;boolean&nbsp;isElevationAdjusted()</pre>
<div class="block">Returns <code>true</code> if the observer elevation should be adjusted according 
 to the elevation of the selected level.</div>
</li>
</ul>
<a name="setElevationAdjusted-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevationAdjusted</h4>
<pre>public&nbsp;void&nbsp;setElevationAdjusted(boolean&nbsp;observerCameraElevationAdjusted)</pre>
<div class="block">Sets whether the observer elevation should be adjusted according 
 to the elevation of the selected level.</div>
</li>
</ul>
<a name="isObserverCameraElevationAdjustedEditable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isObserverCameraElevationAdjustedEditable</h4>
<pre>public&nbsp;boolean&nbsp;isObserverCameraElevationAdjustedEditable()</pre>
<div class="block">Returns <code>true</code> if the adjustment of the observer camera according to the current level is modifiable.</div>
</li>
</ul>
<a name="setYawInDegrees-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYawInDegrees</h4>
<pre>public&nbsp;void&nbsp;setYawInDegrees(int&nbsp;yawInDegrees)</pre>
<div class="block">Sets the edited yaw in degrees.</div>
</li>
</ul>
<a name="getYawInDegrees--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYawInDegrees</h4>
<pre>public&nbsp;int&nbsp;getYawInDegrees()</pre>
<div class="block">Returns the edited yaw in degrees.</div>
</li>
</ul>
<a name="setYaw-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYaw</h4>
<pre>public&nbsp;void&nbsp;setYaw(float&nbsp;yaw)</pre>
<div class="block">Sets the edited yaw in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getYaw--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYaw</h4>
<pre>public&nbsp;float&nbsp;getYaw()</pre>
<div class="block">Returns the edited yaw in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setPitchInDegrees-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitchInDegrees</h4>
<pre>public&nbsp;void&nbsp;setPitchInDegrees(int&nbsp;pitchInDegrees)</pre>
<div class="block">Sets the edited pitch in degrees.</div>
</li>
</ul>
<a name="getPitchInDegrees--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitchInDegrees</h4>
<pre>public&nbsp;int&nbsp;getPitchInDegrees()</pre>
<div class="block">Returns the edited pitch in degrees.</div>
</li>
</ul>
<a name="setPitch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitch</h4>
<pre>public&nbsp;void&nbsp;setPitch(float&nbsp;pitch)</pre>
<div class="block">Sets the edited pitch in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;float&nbsp;getPitch()</pre>
<div class="block">Returns the edited pitch in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setFieldOfViewInDegrees-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldOfViewInDegrees</h4>
<pre>public&nbsp;void&nbsp;setFieldOfViewInDegrees(int&nbsp;fieldOfViewInDegrees)</pre>
<div class="block">Sets the edited observer field of view in degrees.</div>
</li>
</ul>
<a name="setFieldOfViewInDegrees-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldOfViewInDegrees</h4>
<pre>public&nbsp;void&nbsp;setFieldOfViewInDegrees(int&nbsp;fieldOfViewInDegrees,
                                    boolean&nbsp;updateFieldOfView)</pre>
</li>
</ul>
<a name="getFieldOfViewInDegrees--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldOfViewInDegrees</h4>
<pre>public&nbsp;int&nbsp;getFieldOfViewInDegrees()</pre>
<div class="block">Returns the edited observer field of view in degrees.</div>
</li>
</ul>
<a name="setFieldOfView-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldOfView</h4>
<pre>public&nbsp;void&nbsp;setFieldOfView(float&nbsp;fieldOfView)</pre>
<div class="block">Sets the edited observer field of view in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getFieldOfView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldOfView</h4>
<pre>public&nbsp;float&nbsp;getFieldOfView()</pre>
<div class="block">Returns the edited observer field of view in radians.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="modifyObserverCamera--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>modifyObserverCamera</h4>
<pre>public&nbsp;void&nbsp;modifyObserverCamera()</pre>
<div class="block">Controls the modification of the observer camera of the edited home.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObserverCameraController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" target="_top">Frames</a></li>
<li><a href="ObserverCameraController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
