<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Interface com.eteks.sweethome3d.model.HomeRecorder (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.eteks.sweethome3d.model.HomeRecorder (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/HomeRecorder.html" target="_top">Frames</a></li>
<li><a href="HomeRecorder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.eteks.sweethome3d.model.HomeRecorder" class="title">Uses of Interface<br>com.eteks.sweethome3d.model.HomeRecorder</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d">com.eteks.sweethome3d</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as a stand-alone application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a> in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a> that return <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></td>
<td class="colLast"><span class="typeNameLabel">SweetHome3D.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/SweetHome3D.html#getHomeRecorder--">getHomeRecorder</a></span>()</code>
<div class="block">Returns a recorder able to write and read homes in files.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></td>
<td class="colLast"><span class="typeNameLabel">SweetHome3D.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/SweetHome3D.html#getHomeRecorder-com.eteks.sweethome3d.model.HomeRecorder.Type-">getHomeRecorder</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a>&nbsp;type)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a> in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> that implement <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html" title="class in com.eteks.sweethome3d.applet">HomeAppletRecorder</a></span></code>
<div class="block">Recorder that stores homes on a HTTP server.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> that return <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></td>
<td class="colLast"><span class="typeNameLabel">AppletApplication.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletApplication.html#getHomeRecorder--">getHomeRecorder</a></span>()</code>
<div class="block">Returns a recorder able to write and read homes on server.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#AppletContentManager-com.eteks.sweethome3d.model.HomeRecorder-com.eteks.sweethome3d.model.UserPreferences-">AppletContentManager</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;recorder,
                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#AppletContentManager-com.eteks.sweethome3d.model.HomeRecorder-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">AppletContentManager</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;recorder,
                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> that implement <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html" title="class in com.eteks.sweethome3d.io">HomeFileRecorder</a></span></code>
<div class="block">Recorder that stores homes in files with <a href="../../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io"><code>DefaultHomeOutputStream</code></a> and
 <a href="../../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io"><code>DefaultHomeInputStream</code></a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>abstract <a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeApplication.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html#getHomeRecorder--">getHomeRecorder</a></span>()</code>
<div class="block">Returns the default recorder able to write and read homes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeApplication.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html#getHomeRecorder-com.eteks.sweethome3d.model.HomeRecorder.Type-">getHomeRecorder</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a>&nbsp;type)</code>
<div class="block">Returns a recorder of a given <code>type</code> able to write and read homes.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/HomeRecorder.html" target="_top">Frames</a></li>
<li><a href="HomeRecorder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
