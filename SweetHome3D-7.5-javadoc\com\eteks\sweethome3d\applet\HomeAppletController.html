<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>HomeAppletController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeAppletController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeAppletController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/applet/HomeAppletController.html" target="_top">Frames</a></li>
<li><a href="HomeAppletController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.applet</div>
<h2 title="Class HomeAppletController" class="title">Class HomeAppletController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller.HomeController</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html" title="class in com.eteks.sweethome3d.plugin">com.eteks.sweethome3d.plugin.HomePluginController</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.applet.HomeAppletController</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeAppletController</span>
extends <a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html" title="class in com.eteks.sweethome3d.plugin">HomePluginController</a></pre>
<div class="block">Home applet pane controller.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#HomeAppletController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-boolean-boolean-boolean-boolean-">HomeAppletController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                    <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                    <a href="../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager,
                    boolean&nbsp;newHomeEnabled,
                    boolean&nbsp;openEnabled,
                    boolean&nbsp;saveEnabled,
                    boolean&nbsp;saveAsEnabled)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#HomeAppletController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-boolean-boolean-boolean-boolean-long-">HomeAppletController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                    <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                    <a href="../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager,
                    boolean&nbsp;newHomeEnabled,
                    boolean&nbsp;openEnabled,
                    boolean&nbsp;saveEnabled,
                    boolean&nbsp;saveAsEnabled,
                    long&nbsp;homeMaximumLength)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#editPreferences--">editPreferences</a></span>()</code>
<div class="block">Edits preferences relevant to applet version.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#exportToSH3D--">exportToSH3D</a></span>()</code>
<div class="block">Controls the export of home to a SH3D file.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#help--">help</a></span>()</code>
<div class="block">Displays Sweet Home user guide in a navigator window.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#importFromSH3D--">importFromSH3D</a></span>()</code>
<div class="block">Controls the import of home from a SH3D file.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#newHome--">newHome</a></span>()</code>
<div class="block">Creates a new home after saving and deleting the current home.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#open--">open</a></span>()</code>
<div class="block">Opens a home after saving and deleting the current home.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#save--">save</a></span>()</code>
<div class="block">Forces a save as operation for imported homes.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html#saveAs-com.eteks.sweethome3d.model.HomeRecorder.Type-java.lang.Runnable-">saveAs</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a>&nbsp;recorderType,
      java.lang.Runnable&nbsp;postSaveTask)</code>
<div class="block">Prompts the user to choose a name for the edited home, 
 suggesting the imported file name after an import.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.plugin.HomePluginController">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.plugin.<a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html" title="class in com.eteks.sweethome3d.plugin">HomePluginController</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html#getPlugins--">getPlugins</a>, <a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html#importPlugin-java.lang.String-">importPlugin</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.HomeController">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#about--">about</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#addFurnitureToGroup--">addFurnitureToGroup</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#addHomeFurniture--">addHomeFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#checkUpdates-boolean-">checkUpdates</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#close--">close</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#close-java.lang.Runnable-">close</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createPhoto--">createPhoto</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createPhotos--">createPhotos</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.TransferObserver-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType...-">createTransferData</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createVideo--">createVideo</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#cut-java.util.List-">cut</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#delete--">delete</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteBackgroundImage--">deleteBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteCameras--">deleteCameras</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteRecentHomes--">deleteRecentHomes</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#disableMagnetism--">disableMagnetism</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-float-float-">drop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-float-float-">drop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Level-float-float-java.lang.Float-">drop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Selectable-">drop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#dropFiles-java.util.List-float-float-">dropFiles</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableActionsBoundToSelection--">enableActionsBoundToSelection</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableMagnetism--">enableMagnetism</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enablePasteAction--">enablePasteAction</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableSelectAllAction--">enableSelectAllAction</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exit--">exit</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToCSV--">exportToCSV</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToOBJ--">exportToOBJ</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToSVG--">exportToSVG</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#focusedViewChanged-com.eteks.sweethome3d.viewcontroller.View-">focusedViewChanged</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getContentManager--">getContentManager</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getFurnitureCatalogController--">getFurnitureCatalogController</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getFurnitureController--">getFurnitureController</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getHomeController3D--">getHomeController3D</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getPlanController--">getPlanController</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getRecentHomes--">getRecentHomes</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getUndoableEditSupport--">getUndoableEditSupport</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getVersion--">getVersion</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#hideBackgroundImage--">hideBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importBackgroundImage--">importBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurniture--">importFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurnitureLibrary--">importFurnitureLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurnitureLibrary-java.lang.String-">importFurnitureLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importLanguageLibrary--">importLanguageLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importLanguageLibrary-java.lang.String-">importLanguageLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexture--">importTexture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexturesLibrary--">importTexturesLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexturesLibrary-java.lang.String-">importTexturesLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#modifyBackgroundImage--">modifyBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#modifySelectedFurniture--">modifySelectedFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#newHomeFromExample--">newHomeFromExample</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#open-java.lang.String-">open</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#paste-java.util.List-">paste</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#pasteStyle--">pasteStyle</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#pasteToGroup--">pasteToGroup</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#previewPrint--">previewPrint</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#print--">print</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#printToPDF--">printToPDF</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#redo--">redo</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAndCompress--">saveAndCompress</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAs--">saveAs</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAsAndCompress--">saveAsAndCompress</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#selectAll--">selectAll</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setHomeProperty-java.lang.String-java.lang.String-">setHomeProperty</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setupPage--">setupPage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setVisualProperty-java.lang.String-java.lang.Object-">setVisualProperty</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#showBackgroundImage--">showBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#storeCamera--">storeCamera</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#undo--">undo</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#zoomIn--">zoomIn</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#zoomOut--">zoomOut</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeAppletController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeAppletController</h4>
<pre>public&nbsp;HomeAppletController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                            <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                            <a href="../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager,
                            boolean&nbsp;newHomeEnabled,
                            boolean&nbsp;openEnabled,
                            boolean&nbsp;saveEnabled,
                            boolean&nbsp;saveAsEnabled)</pre>
</li>
</ul>
<a name="HomeAppletController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-boolean-boolean-boolean-boolean-long-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeAppletController</h4>
<pre>public&nbsp;HomeAppletController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                            <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                            <a href="../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager,
                            boolean&nbsp;newHomeEnabled,
                            boolean&nbsp;openEnabled,
                            boolean&nbsp;saveEnabled,
                            boolean&nbsp;saveAsEnabled,
                            long&nbsp;homeMaximumLength)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getView--">getView</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></code></dd>
</dl>
</li>
</ul>
<a name="newHome--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHome</h4>
<pre>public&nbsp;void&nbsp;newHome()</pre>
<div class="block">Creates a new home after saving and deleting the current home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#newHome--">newHome</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></code></dd>
</dl>
</li>
</ul>
<a name="open--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;void&nbsp;open()</pre>
<div class="block">Opens a home after saving and deleting the current home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#open--">open</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></code></dd>
</dl>
</li>
</ul>
<a name="save--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public&nbsp;void&nbsp;save()</pre>
<div class="block">Forces a save as operation for imported homes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#save--">save</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></code></dd>
</dl>
</li>
</ul>
<a name="saveAs-com.eteks.sweethome3d.model.HomeRecorder.Type-java.lang.Runnable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveAs</h4>
<pre>protected&nbsp;void&nbsp;saveAs(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a>&nbsp;recorderType,
                      java.lang.Runnable&nbsp;postSaveTask)</pre>
<div class="block">Prompts the user to choose a name for the edited home, 
 suggesting the imported file name after an import.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAs-com.eteks.sweethome3d.model.HomeRecorder.Type-java.lang.Runnable-">saveAs</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></code></dd>
</dl>
</li>
</ul>
<a name="help--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>help</h4>
<pre>public&nbsp;void&nbsp;help()</pre>
<div class="block">Displays Sweet Home user guide in a navigator window.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#help--">help</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></code></dd>
</dl>
</li>
</ul>
<a name="exportToSH3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToSH3D</h4>
<pre>public&nbsp;void&nbsp;exportToSH3D()</pre>
<div class="block">Controls the export of home to a SH3D file.</div>
</li>
</ul>
<a name="importFromSH3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importFromSH3D</h4>
<pre>public&nbsp;void&nbsp;importFromSH3D()</pre>
<div class="block">Controls the import of home from a SH3D file.</div>
</li>
</ul>
<a name="editPreferences--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>editPreferences</h4>
<pre>public&nbsp;void&nbsp;editPreferences()</pre>
<div class="block">Edits preferences relevant to applet version.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#editPreferences--">editPreferences</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeAppletController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/applet/HomeAppletController.html" target="_top">Frames</a></li>
<li><a href="HomeAppletController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
