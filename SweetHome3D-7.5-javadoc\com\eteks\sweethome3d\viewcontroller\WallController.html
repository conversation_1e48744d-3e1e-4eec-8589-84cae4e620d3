<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>WallController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="WallController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/WallController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/WallController.html" target="_top">Frames</a></li>
<li><a href="WallController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class WallController" class="title">Class WallController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.WallController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">WallController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for wall view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getLeftSidePaint--">wall paint type</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallShape.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallShape</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getShape--">wall shape</a>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#WallController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">WallController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
              javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of wall view with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WallController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getArcExtentInDegrees--">getArcExtentInDegrees</a></span>()</code>
<div class="block">Returns the edited arc extent.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getArcLength--">getArcLength</a></span>()</code>
<div class="block">Returns the length of wall after applying the edited arc extent.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getDistanceToEndPoint--">getDistanceToEndPoint</a></span>()</code>
<div class="block">Returns the edited distance to end point.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getLeftSideBaseboardController--">getLeftSideBaseboardController</a></span>()</code>
<div class="block">Returns the controller of the wall left side baseboard.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getLeftSideColor--">getLeftSideColor</a></span>()</code>
<div class="block">Returns the edited color of the left side.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getLeftSidePaint--">getLeftSidePaint</a></span>()</code>
<div class="block">Returns whether the left side is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getLeftSideShininess--">getLeftSideShininess</a></span>()</code>
<div class="block">Returns the edited left side shininess.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getLeftSideTextureController--">getLeftSideTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the wall left side.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getLength--">getLength</a></span>()</code>
<div class="block">Returns the edited length.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getPattern--">getPattern</a></span>()</code>
<div class="block">Returns the pattern of edited wall in plan.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getRectangularWallHeight--">getRectangularWallHeight</a></span>()</code>
<div class="block">Returns the edited height of a rectangular wall.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getRightSideBaseboardController--">getRightSideBaseboardController</a></span>()</code>
<div class="block">Returns the controller of the wall right side baseboard.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getRightSideColor--">getRightSideColor</a></span>()</code>
<div class="block">Returns the edited color of the right side.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getRightSidePaint--">getRightSidePaint</a></span>()</code>
<div class="block">Returns whether the right side is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getRightSideShininess--">getRightSideShininess</a></span>()</code>
<div class="block">Returns the edited right side shininess.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getRightSideTextureController--">getRightSideTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the wall right side.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallShape.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallShape</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getShape--">getShape</a></span>()</code>
<div class="block">Returns whether the edited wall is a rectangular wall, a sloping wall or unknown.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getSlopingWallHeightAtEnd--">getSlopingWallHeightAtEnd</a></span>()</code>
<div class="block">Returns the edited height at end of a sloping wall.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getSlopingWallHeightAtStart--">getSlopingWallHeightAtStart</a></span>()</code>
<div class="block">Returns the edited height at start of a sloping wall.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getThickness--">getThickness</a></span>()</code>
<div class="block">Returns the edited thickness.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getTopColor--">getTopColor</a></span>()</code>
<div class="block">Returns the edited top color in the 3D view.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getTopPaint--">getTopPaint</a></span>()</code>
<div class="block">Returns whether the top of the wall in the 3D view uses default rendering, is colored, or unknown painted.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getXEnd--">getXEnd</a></span>()</code>
<div class="block">Returns the edited abscissa of the end point.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getXStart--">getXStart</a></span>()</code>
<div class="block">Returns the edited abscissa of the start point.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getYEnd--">getYEnd</a></span>()</code>
<div class="block">Returns the edited ordinate of the end point.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getYStart--">getYStart</a></span>()</code>
<div class="block">Returns the edited ordinate of the start point.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#isEditablePoints--">isEditablePoints</a></span>()</code>
<div class="block">Returns whether the point coordinates can be be edited or not.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#modifyWalls--">modifyWalls</a></span>()</code>
<div class="block">Controls the modification of selected walls in edited home.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WallController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setArcExtentInDegrees-java.lang.Float-">setArcExtentInDegrees</a></span>(java.lang.Float&nbsp;arcExtentInDegrees)</code>
<div class="block">Sets the edited arc extent.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setDistanceToEndPoint-java.lang.Float-">setDistanceToEndPoint</a></span>(java.lang.Float&nbsp;distanceToEndPoint)</code>
<div class="block">Sets the edited distance to end point.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setEditablePoints-boolean-">setEditablePoints</a></span>(boolean&nbsp;editablePoints)</code>
<div class="block">Sets whether the point coordinates can be be edited or not.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setLeftSideColor-java.lang.Integer-">setLeftSideColor</a></span>(java.lang.Integer&nbsp;leftSideColor)</code>
<div class="block">Sets the edited color of the left side.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setLeftSidePaint-com.eteks.sweethome3d.viewcontroller.WallController.WallPaint-">setLeftSidePaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;leftSidePaint)</code>
<div class="block">Sets whether the left side is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setLeftSideShininess-java.lang.Float-">setLeftSideShininess</a></span>(java.lang.Float&nbsp;leftSideShininess)</code>
<div class="block">Sets the edited left side shininess.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setLength-java.lang.Float-">setLength</a></span>(java.lang.Float&nbsp;length)</code>
<div class="block">Sets the edited length.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setPattern-com.eteks.sweethome3d.model.TextureImage-">setPattern</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</code>
<div class="block">Sets the pattern of edited wall in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setRectangularWallHeight-java.lang.Float-">setRectangularWallHeight</a></span>(java.lang.Float&nbsp;rectangularWallHeight)</code>
<div class="block">Sets the edited height of a rectangular wall.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setRightSideColor-java.lang.Integer-">setRightSideColor</a></span>(java.lang.Integer&nbsp;rightSideColor)</code>
<div class="block">Sets the edited color of the right side.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setRightSidePaint-com.eteks.sweethome3d.viewcontroller.WallController.WallPaint-">setRightSidePaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;rightSidePaint)</code>
<div class="block">Sets whether the right side is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setRightSideShininess-java.lang.Float-">setRightSideShininess</a></span>(java.lang.Float&nbsp;rightSideShininess)</code>
<div class="block">Sets the edited right side shininess.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setShape-com.eteks.sweethome3d.viewcontroller.WallController.WallShape-">setShape</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallShape.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallShape</a>&nbsp;shape)</code>
<div class="block">Sets whether the edited wall is a rectangular wall, a sloping wall or unknown.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setSlopingWallHeightAtEnd-java.lang.Float-">setSlopingWallHeightAtEnd</a></span>(java.lang.Float&nbsp;sloppingWallHeightAtEnd)</code>
<div class="block">Sets the edited height at end of a sloping wall.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setSlopingWallHeightAtStart-java.lang.Float-">setSlopingWallHeightAtStart</a></span>(java.lang.Float&nbsp;slopingWallHeightAtStart)</code>
<div class="block">Sets the edited height at start of a sloping wall.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setThickness-java.lang.Float-">setThickness</a></span>(java.lang.Float&nbsp;thickness)</code>
<div class="block">Sets the edited thickness.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setTopColor-java.lang.Integer-">setTopColor</a></span>(java.lang.Integer&nbsp;topColor)</code>
<div class="block">Sets the edited top color in the 3D view.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setTopPaint-com.eteks.sweethome3d.viewcontroller.WallController.WallPaint-">setTopPaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;topPaint)</code>
<div class="block">Sets whether the top of the wall in the 3D view uses default rendering, is colored, or unknown painted.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setXEnd-java.lang.Float-">setXEnd</a></span>(java.lang.Float&nbsp;xEnd)</code>
<div class="block">Sets the edited abscissa of the end point.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setXStart-java.lang.Float-">setXStart</a></span>(java.lang.Float&nbsp;xStart)</code>
<div class="block">Sets the edited abscissa of the start point.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setYEnd-java.lang.Float-">setYEnd</a></span>(java.lang.Float&nbsp;yEnd)</code>
<div class="block">Sets the edited ordinate of the end point.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#setYStart-java.lang.Float-">setYStart</a></span>(java.lang.Float&nbsp;yStart)</code>
<div class="block">Sets the edited ordinate of the start point.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates edited properties from selected walls in the home edited by this controller.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="WallController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>WallController</h4>
<pre>public&nbsp;WallController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                      javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of wall view with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getLeftSideTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSideTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getLeftSideTextureController()</pre>
<div class="block">Returns the texture controller of the wall left side.</div>
</li>
</ul>
<a name="getLeftSideBaseboardController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSideBaseboardController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;getLeftSideBaseboardController()</pre>
<div class="block">Returns the controller of the wall left side baseboard.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getRightSideTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSideTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getRightSideTextureController()</pre>
<div class="block">Returns the texture controller of the wall right side.</div>
</li>
</ul>
<a name="getRightSideBaseboardController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSideBaseboardController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;getRightSideBaseboardController()</pre>
<div class="block">Returns the controller of the wall right side baseboard.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WallController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WallController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates edited properties from selected walls in the home edited by this controller.</div>
</li>
</ul>
<a name="setXStart-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXStart</h4>
<pre>public&nbsp;void&nbsp;setXStart(java.lang.Float&nbsp;xStart)</pre>
<div class="block">Sets the edited abscissa of the start point.</div>
</li>
</ul>
<a name="getXStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXStart</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getXStart()</pre>
<div class="block">Returns the edited abscissa of the start point.</div>
</li>
</ul>
<a name="setYStart-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYStart</h4>
<pre>public&nbsp;void&nbsp;setYStart(java.lang.Float&nbsp;yStart)</pre>
<div class="block">Sets the edited ordinate of the start point.</div>
</li>
</ul>
<a name="getYStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYStart</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getYStart()</pre>
<div class="block">Returns the edited ordinate of the start point.</div>
</li>
</ul>
<a name="setXEnd-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXEnd</h4>
<pre>public&nbsp;void&nbsp;setXEnd(java.lang.Float&nbsp;xEnd)</pre>
<div class="block">Sets the edited abscissa of the end point.</div>
</li>
</ul>
<a name="getXEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXEnd</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getXEnd()</pre>
<div class="block">Returns the edited abscissa of the end point.</div>
</li>
</ul>
<a name="setYEnd-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYEnd</h4>
<pre>public&nbsp;void&nbsp;setYEnd(java.lang.Float&nbsp;yEnd)</pre>
<div class="block">Sets the edited ordinate of the end point.</div>
</li>
</ul>
<a name="getYEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYEnd</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getYEnd()</pre>
<div class="block">Returns the edited ordinate of the end point.</div>
</li>
</ul>
<a name="setLength-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLength</h4>
<pre>public&nbsp;void&nbsp;setLength(java.lang.Float&nbsp;length)</pre>
<div class="block">Sets the edited length.</div>
</li>
</ul>
<a name="getLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLength</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getLength()</pre>
<div class="block">Returns the edited length.</div>
</li>
</ul>
<a name="setDistanceToEndPoint-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistanceToEndPoint</h4>
<pre>public&nbsp;void&nbsp;setDistanceToEndPoint(java.lang.Float&nbsp;distanceToEndPoint)</pre>
<div class="block">Sets the edited distance to end point.</div>
</li>
</ul>
<a name="getDistanceToEndPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistanceToEndPoint</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getDistanceToEndPoint()</pre>
<div class="block">Returns the edited distance to end point.</div>
</li>
</ul>
<a name="setEditablePoints-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditablePoints</h4>
<pre>public&nbsp;void&nbsp;setEditablePoints(boolean&nbsp;editablePoints)</pre>
<div class="block">Sets whether the point coordinates can be be edited or not.</div>
</li>
</ul>
<a name="isEditablePoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditablePoints</h4>
<pre>public&nbsp;boolean&nbsp;isEditablePoints()</pre>
<div class="block">Returns whether the point coordinates can be be edited or not.</div>
</li>
</ul>
<a name="setLeftSideColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeftSideColor</h4>
<pre>public&nbsp;void&nbsp;setLeftSideColor(java.lang.Integer&nbsp;leftSideColor)</pre>
<div class="block">Sets the edited color of the left side.</div>
</li>
</ul>
<a name="getLeftSideColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSideColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getLeftSideColor()</pre>
<div class="block">Returns the edited color of the left side.</div>
</li>
</ul>
<a name="setLeftSidePaint-com.eteks.sweethome3d.viewcontroller.WallController.WallPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeftSidePaint</h4>
<pre>public&nbsp;void&nbsp;setLeftSidePaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;leftSidePaint)</pre>
<div class="block">Sets whether the left side is colored, textured or unknown painted.</div>
</li>
</ul>
<a name="getLeftSidePaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSidePaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;getLeftSidePaint()</pre>
<div class="block">Returns whether the left side is colored, textured or unknown painted.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html#COLORED"><code>WallController.WallPaint.COLORED</code></a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html#TEXTURED"><code>WallController.WallPaint.TEXTURED</code></a> or <code>null</code></dd>
</dl>
</li>
</ul>
<a name="setLeftSideShininess-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLeftSideShininess</h4>
<pre>public&nbsp;void&nbsp;setLeftSideShininess(java.lang.Float&nbsp;leftSideShininess)</pre>
<div class="block">Sets the edited left side shininess.</div>
</li>
</ul>
<a name="getLeftSideShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLeftSideShininess</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getLeftSideShininess()</pre>
<div class="block">Returns the edited left side shininess.</div>
</li>
</ul>
<a name="setRightSideColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightSideColor</h4>
<pre>public&nbsp;void&nbsp;setRightSideColor(java.lang.Integer&nbsp;rightSideColor)</pre>
<div class="block">Sets the edited color of the right side.</div>
</li>
</ul>
<a name="getRightSideColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSideColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getRightSideColor()</pre>
<div class="block">Returns the edited color of the right side.</div>
</li>
</ul>
<a name="setRightSidePaint-com.eteks.sweethome3d.viewcontroller.WallController.WallPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightSidePaint</h4>
<pre>public&nbsp;void&nbsp;setRightSidePaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;rightSidePaint)</pre>
<div class="block">Sets whether the right side is colored, textured or unknown painted.</div>
</li>
</ul>
<a name="getRightSidePaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSidePaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;getRightSidePaint()</pre>
<div class="block">Returns whether the right side is colored, textured or unknown painted.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html#COLORED"><code>WallController.WallPaint.COLORED</code></a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html#TEXTURED"><code>WallController.WallPaint.TEXTURED</code></a> or <code>null</code></dd>
</dl>
</li>
</ul>
<a name="setRightSideShininess-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRightSideShininess</h4>
<pre>public&nbsp;void&nbsp;setRightSideShininess(java.lang.Float&nbsp;rightSideShininess)</pre>
<div class="block">Sets the edited right side shininess.</div>
</li>
</ul>
<a name="getRightSideShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRightSideShininess</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getRightSideShininess()</pre>
<div class="block">Returns the edited right side shininess.</div>
</li>
</ul>
<a name="setPattern-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPattern</h4>
<pre>public&nbsp;void&nbsp;setPattern(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern)</pre>
<div class="block">Sets the pattern of edited wall in plan, and notifies
 listeners of this change.</div>
</li>
</ul>
<a name="getPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPattern</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;getPattern()</pre>
<div class="block">Returns the pattern of edited wall in plan.</div>
</li>
</ul>
<a name="setTopColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTopColor</h4>
<pre>public&nbsp;void&nbsp;setTopColor(java.lang.Integer&nbsp;topColor)</pre>
<div class="block">Sets the edited top color in the 3D view.</div>
</li>
</ul>
<a name="getTopColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTopColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getTopColor()</pre>
<div class="block">Returns the edited top color in the 3D view.</div>
</li>
</ul>
<a name="setTopPaint-com.eteks.sweethome3d.viewcontroller.WallController.WallPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTopPaint</h4>
<pre>public&nbsp;void&nbsp;setTopPaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;topPaint)</pre>
<div class="block">Sets whether the top of the wall in the 3D view uses default rendering, is colored, or unknown painted.</div>
</li>
</ul>
<a name="getTopPaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTopPaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallPaint</a>&nbsp;getTopPaint()</pre>
<div class="block">Returns whether the top of the wall in the 3D view uses default rendering, is colored, or unknown painted.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html#DEFAULT"><code>WallController.WallPaint.DEFAULT</code></a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallPaint.html#COLORED"><code>WallController.WallPaint.COLORED</code></a> or <code>null</code></dd>
</dl>
</li>
</ul>
<a name="setShape-com.eteks.sweethome3d.viewcontroller.WallController.WallShape-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShape</h4>
<pre>public&nbsp;void&nbsp;setShape(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallShape.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallShape</a>&nbsp;shape)</pre>
<div class="block">Sets whether the edited wall is a rectangular wall, a sloping wall or unknown.</div>
</li>
</ul>
<a name="getShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShape</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallShape.html" title="enum in com.eteks.sweethome3d.viewcontroller">WallController.WallShape</a>&nbsp;getShape()</pre>
<div class="block">Returns whether the edited wall is a rectangular wall, a sloping wall or unknown.</div>
</li>
</ul>
<a name="setRectangularWallHeight-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRectangularWallHeight</h4>
<pre>public&nbsp;void&nbsp;setRectangularWallHeight(java.lang.Float&nbsp;rectangularWallHeight)</pre>
<div class="block">Sets the edited height of a rectangular wall.</div>
</li>
</ul>
<a name="getRectangularWallHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRectangularWallHeight</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getRectangularWallHeight()</pre>
<div class="block">Returns the edited height of a rectangular wall.</div>
</li>
</ul>
<a name="setSlopingWallHeightAtStart-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSlopingWallHeightAtStart</h4>
<pre>public&nbsp;void&nbsp;setSlopingWallHeightAtStart(java.lang.Float&nbsp;slopingWallHeightAtStart)</pre>
<div class="block">Sets the edited height at start of a sloping wall.</div>
</li>
</ul>
<a name="getSlopingWallHeightAtStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlopingWallHeightAtStart</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getSlopingWallHeightAtStart()</pre>
<div class="block">Returns the edited height at start of a sloping wall.</div>
</li>
</ul>
<a name="setSlopingWallHeightAtEnd-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSlopingWallHeightAtEnd</h4>
<pre>public&nbsp;void&nbsp;setSlopingWallHeightAtEnd(java.lang.Float&nbsp;sloppingWallHeightAtEnd)</pre>
<div class="block">Sets the edited height at end of a sloping wall.</div>
</li>
</ul>
<a name="getSlopingWallHeightAtEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSlopingWallHeightAtEnd</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getSlopingWallHeightAtEnd()</pre>
<div class="block">Returns the edited height at end of a sloping wall.</div>
</li>
</ul>
<a name="setThickness-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThickness</h4>
<pre>public&nbsp;void&nbsp;setThickness(java.lang.Float&nbsp;thickness)</pre>
<div class="block">Sets the edited thickness.</div>
</li>
</ul>
<a name="getThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThickness</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getThickness()</pre>
<div class="block">Returns the edited thickness.</div>
</li>
</ul>
<a name="setArcExtentInDegrees-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setArcExtentInDegrees</h4>
<pre>public&nbsp;void&nbsp;setArcExtentInDegrees(java.lang.Float&nbsp;arcExtentInDegrees)</pre>
<div class="block">Sets the edited arc extent.</div>
</li>
</ul>
<a name="getArcExtentInDegrees--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArcExtentInDegrees</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getArcExtentInDegrees()</pre>
<div class="block">Returns the edited arc extent.</div>
</li>
</ul>
<a name="getArcLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArcLength</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getArcLength()</pre>
<div class="block">Returns the length of wall after applying the edited arc extent.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the arc length or null if data is missing to compute it</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="modifyWalls--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>modifyWalls</h4>
<pre>public&nbsp;void&nbsp;modifyWalls()</pre>
<div class="block">Controls the modification of selected walls in edited home.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/WallController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/WallController.html" target="_top">Frames</a></li>
<li><a href="WallController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
