<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>HomeController3D (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeController3D (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeController3D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeController3D.html" target="_top">Frames</a></li>
<li><a href="HomeController3D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class HomeController3D" class="title">Class HomeController3D</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.HomeController3D</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeController3D</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for the home 3D view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></span></code>
<div class="block">Controller state classes super class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.EditingCameraState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.EditingCameraState</a></span></code>
<div class="block">Controller state handling mouse events to edit home items.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#HomeController3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.PlanController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">HomeController3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;planController,
                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home 3D view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#HomeController3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">HomeController3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home 3D view.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#deleteCameras-java.util.List-">deleteCameras</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;cameras)</code>
<div class="block">Deletes the given list of cameras from the ones stored in home.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#displayAllLevels--">displayAllLevels</a></span>()</code>
<div class="block">Makes all levels visible.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#displaySelectedLevel--">displaySelectedLevel</a></span>()</code>
<div class="block">Makes the selected level and below visible.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#elevateCamera-float-">elevateCamera</a></span>(float&nbsp;delta)</code>
<div class="block">Elevates home camera of <code>delta</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#escape--">escape</a></span>()</code>
<div class="block">Escapes of current editing action.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#getObserverCameraState--">getObserverCameraState</a></span>()</code>
<div class="block">Returns the observer camera state.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#getTopCameraState--">getTopCameraState</a></span>()</code>
<div class="block">Returns the top camera state.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#goToCamera-com.eteks.sweethome3d.model.Camera-">goToCamera</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera)</code>
<div class="block">Switches to observer or top camera and move camera to the values as the current camera.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#isEditingState--">isEditingState</a></span>()</code>
<div class="block">Returns <code>true</code> if this controller is moving items.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#modifyAttributes--">modifyAttributes</a></span>()</code>
<div class="block">Controls the edition of 3D attributes.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#modifyFieldOfView-float-">modifyFieldOfView</a></span>(float&nbsp;delta)</code>
<div class="block">Modifies home camera field of view of <code>delta</code>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#moveCamera-float-">moveCamera</a></span>(float&nbsp;delta)</code>
<div class="block">Moves home camera of <code>delta</code>.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#moveCameraSideways-float-">moveCameraSideways</a></span>(float&nbsp;delta)</code>
<div class="block">Moves home camera sideways of <code>delta</code>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#moveMouse-float-float-">moveMouse</a></span>(float&nbsp;x,
         float&nbsp;y)</code>
<div class="block">Processes a mouse button moved event.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#pressMouse-float-float-int-boolean-boolean-boolean-boolean-com.eteks.sweethome3d.viewcontroller.View.PointerType-">pressMouse</a></span>(float&nbsp;x,
          float&nbsp;y,
          int&nbsp;clickCount,
          boolean&nbsp;shiftDown,
          boolean&nbsp;alignmentActivated,
          boolean&nbsp;duplicationActivated,
          boolean&nbsp;magnetismToggled,
          <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a>&nbsp;pointerType)</code>
<div class="block">Processes a mouse button pressed event.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#releaseMouse-float-float-">releaseMouse</a></span>(float&nbsp;x,
            float&nbsp;y)</code>
<div class="block">Processes a mouse button released event.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#rotateCameraPitch-float-">rotateCameraPitch</a></span>(float&nbsp;delta)</code>
<div class="block">Rotates home camera pitch angle of <code>delta</code> radians.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#rotateCameraYaw-float-">rotateCameraYaw</a></span>(float&nbsp;delta)</code>
<div class="block">Rotates home camera yaw angle of <code>delta</code> radians.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#setAlignmentActivated-boolean-">setAlignmentActivated</a></span>(boolean&nbsp;alignmentActivated)</code>
<div class="block">Activates or deactivates alignment feature during editing action.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#setCameraState-com.eteks.sweethome3d.viewcontroller.HomeController3D.CameraControllerState-">setCameraState</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a>&nbsp;state)</code>
<div class="block">Changes current state of controller.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#setDuplicationActivated-boolean-">setDuplicationActivated</a></span>(boolean&nbsp;duplicationActivated)</code>
<div class="block">Activates or deactivates duplication feature during editing action.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#storeCamera-java.lang.String-">storeCamera</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Stores a clone of the current camera in home under the given <code>name</code>.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#toggleMagnetism-boolean-">toggleMagnetism</a></span>(boolean&nbsp;magnetismToggled)</code>
<div class="block">Toggles temporary magnetism feature of user preferences during editing action.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#viewFromObserver--">viewFromObserver</a></span>()</code>
<div class="block">Changes home camera for <a href="../../../../com/eteks/sweethome3d/model/Home.html#getObserverCamera--"><code>observer camera</code></a>.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#viewFromTop--">viewFromTop</a></span>()</code>
<div class="block">Changes home camera for <a href="../../../../com/eteks/sweethome3d/model/Home.html#getTopCamera--"><code>top camera</code></a>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeController3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeController3D</h4>
<pre>public&nbsp;HomeController3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                        <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                        javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of home 3D view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home edited by this controller and its view</dd>
</dl>
</li>
</ul>
<a name="HomeController3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.PlanController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeController3D</h4>
<pre>public&nbsp;HomeController3D(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;planController,
                        <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                        javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of home 3D view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home edited by this controller and its view</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="viewFromTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>viewFromTop</h4>
<pre>public&nbsp;void&nbsp;viewFromTop()</pre>
<div class="block">Changes home camera for <a href="../../../../com/eteks/sweethome3d/model/Home.html#getTopCamera--"><code>top camera</code></a>.</div>
</li>
</ul>
<a name="viewFromObserver--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>viewFromObserver</h4>
<pre>public&nbsp;void&nbsp;viewFromObserver()</pre>
<div class="block">Changes home camera for <a href="../../../../com/eteks/sweethome3d/model/Home.html#getObserverCamera--"><code>observer camera</code></a>.</div>
</li>
</ul>
<a name="storeCamera-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>storeCamera</h4>
<pre>public&nbsp;void&nbsp;storeCamera(java.lang.String&nbsp;name)</pre>
<div class="block">Stores a clone of the current camera in home under the given <code>name</code>.</div>
</li>
</ul>
<a name="goToCamera-com.eteks.sweethome3d.model.Camera-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>goToCamera</h4>
<pre>public&nbsp;void&nbsp;goToCamera(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera)</pre>
<div class="block">Switches to observer or top camera and move camera to the values as the current camera.</div>
</li>
</ul>
<a name="deleteCameras-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteCameras</h4>
<pre>public&nbsp;void&nbsp;deleteCameras(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;cameras)</pre>
<div class="block">Deletes the given list of cameras from the ones stored in home.</div>
</li>
</ul>
<a name="displayAllLevels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayAllLevels</h4>
<pre>public&nbsp;void&nbsp;displayAllLevels()</pre>
<div class="block">Makes all levels visible.</div>
</li>
</ul>
<a name="displaySelectedLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displaySelectedLevel</h4>
<pre>public&nbsp;void&nbsp;displaySelectedLevel()</pre>
<div class="block">Makes the selected level and below visible.</div>
</li>
</ul>
<a name="modifyAttributes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifyAttributes</h4>
<pre>public&nbsp;void&nbsp;modifyAttributes()</pre>
<div class="block">Controls the edition of 3D attributes.</div>
</li>
</ul>
<a name="setCameraState-com.eteks.sweethome3d.viewcontroller.HomeController3D.CameraControllerState-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCameraState</h4>
<pre>protected&nbsp;void&nbsp;setCameraState(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a>&nbsp;state)</pre>
<div class="block">Changes current state of controller.</div>
</li>
</ul>
<a name="moveCamera-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveCamera</h4>
<pre>public&nbsp;void&nbsp;moveCamera(float&nbsp;delta)</pre>
<div class="block">Moves home camera of <code>delta</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - the value in cm that the camera should move forward
               (with a negative delta) or backward (with a positive delta)</dd>
</dl>
</li>
</ul>
<a name="moveCameraSideways-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveCameraSideways</h4>
<pre>public&nbsp;void&nbsp;moveCameraSideways(float&nbsp;delta)</pre>
<div class="block">Moves home camera sideways of <code>delta</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - the value in cm that the camera should move left
               (with a negative delta) or right (with a positive delta)</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="elevateCamera-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>elevateCamera</h4>
<pre>public&nbsp;void&nbsp;elevateCamera(float&nbsp;delta)</pre>
<div class="block">Elevates home camera of <code>delta</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - the value in cm that the camera should move down
              (with a negative delta) or up (with a positive delta)</dd>
</dl>
</li>
</ul>
<a name="rotateCameraYaw-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rotateCameraYaw</h4>
<pre>public&nbsp;void&nbsp;rotateCameraYaw(float&nbsp;delta)</pre>
<div class="block">Rotates home camera yaw angle of <code>delta</code> radians.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - the value in rad that the camera should turn around yaw axis</dd>
</dl>
</li>
</ul>
<a name="rotateCameraPitch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rotateCameraPitch</h4>
<pre>public&nbsp;void&nbsp;rotateCameraPitch(float&nbsp;delta)</pre>
<div class="block">Rotates home camera pitch angle of <code>delta</code> radians.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - the value in rad that the camera should turn around pitch axis</dd>
</dl>
</li>
</ul>
<a name="modifyFieldOfView-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifyFieldOfView</h4>
<pre>public&nbsp;void&nbsp;modifyFieldOfView(float&nbsp;delta)</pre>
<div class="block">Modifies home camera field of view of <code>delta</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>delta</code> - the value in rad that should be added the field of view
               to get a narrower view (with a negative delta) or a wider view (with a positive delta)</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="pressMouse-float-float-int-boolean-boolean-boolean-boolean-com.eteks.sweethome3d.viewcontroller.View.PointerType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pressMouse</h4>
<pre>public&nbsp;void&nbsp;pressMouse(float&nbsp;x,
                       float&nbsp;y,
                       int&nbsp;clickCount,
                       boolean&nbsp;shiftDown,
                       boolean&nbsp;alignmentActivated,
                       boolean&nbsp;duplicationActivated,
                       boolean&nbsp;magnetismToggled,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a>&nbsp;pointerType)</pre>
<div class="block">Processes a mouse button pressed event.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="releaseMouse-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>releaseMouse</h4>
<pre>public&nbsp;void&nbsp;releaseMouse(float&nbsp;x,
                         float&nbsp;y)</pre>
<div class="block">Processes a mouse button released event.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="moveMouse-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveMouse</h4>
<pre>public&nbsp;void&nbsp;moveMouse(float&nbsp;x,
                      float&nbsp;y)</pre>
<div class="block">Processes a mouse button moved event.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isEditingState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditingState</h4>
<pre>public&nbsp;boolean&nbsp;isEditingState()</pre>
<div class="block">Returns <code>true</code> if this controller is moving items.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="escape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>escape</h4>
<pre>public&nbsp;void&nbsp;escape()</pre>
<div class="block">Escapes of current editing action.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="toggleMagnetism-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleMagnetism</h4>
<pre>public&nbsp;void&nbsp;toggleMagnetism(boolean&nbsp;magnetismToggled)</pre>
<div class="block">Toggles temporary magnetism feature of user preferences during editing action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>magnetismToggled</code> - if <code>true</code> then magnetism feature is toggled.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setAlignmentActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignmentActivated</h4>
<pre>public&nbsp;void&nbsp;setAlignmentActivated(boolean&nbsp;alignmentActivated)</pre>
<div class="block">Activates or deactivates alignment feature during editing action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignmentActivated</code> - if <code>true</code> then alignment is active.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setDuplicationActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuplicationActivated</h4>
<pre>public&nbsp;void&nbsp;setDuplicationActivated(boolean&nbsp;duplicationActivated)</pre>
<div class="block">Activates or deactivates duplication feature during editing action.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duplicationActivated</code> - if <code>true</code> then duplication is active.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getObserverCameraState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObserverCameraState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a>&nbsp;getObserverCameraState()</pre>
<div class="block">Returns the observer camera state.</div>
</li>
</ul>
<a name="getTopCameraState--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getTopCameraState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D.CameraControllerState</a>&nbsp;getTopCameraState()</pre>
<div class="block">Returns the top camera state.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeController3D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.CameraControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeController3D.html" target="_top">Frames</a></li>
<li><a href="HomeController3D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
