<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>PlanController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PlanController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/PlanController.html" target="_top">Frames</a></li>
<li><a href="PlanController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class PlanController" class="title">Class PlanController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller.FurnitureController</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.PlanController</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PlanController</span>
extends <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for the plan view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></span></code>
<div class="block">Controller state classes super class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerStateDecorator</a></span></code>
<div class="block">A decorator on controller state, useful to change the behavior of an existing state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a></span></code>
<div class="block">Fields that can be edited in plan view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></span></code>
<div class="block">Selectable modes in controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.Property</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#PlanController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">PlanController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
              javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of plan view.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addDimensionLines-java.util.List-">addDimensionLines</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;dimensionLines)</code>
<div class="block">Add <code>dimensionLines</code> to home and post an undoable new dimension line operation.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addFurniture-java.util.List-">addFurniture</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</code>
<div class="block">Adds furniture to home and updates door and window flags if they intersect with walls and magnetism is enabled.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addItems-java.util.List-">addItems</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Adds <code>items</code> to home and post an undoable operation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addLabels-java.util.List-">addLabels</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;&nbsp;labels)</code>
<div class="block">Add <code>labels</code> to home and post an undoable new label operation.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addLevel--">addLevel</a></span>()</code>
<div class="block">Controls the creation of a new level.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addLevelAtSameElevation--">addLevelAtSameElevation</a></span>()</code>
<div class="block">Controls the creation of a new level at same elevation.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addPointToSelectedRoom-float-float-">addPointToSelectedRoom</a></span>(float&nbsp;x,
                      float&nbsp;y)</code>
<div class="block">Adds a point to the selected room at the given coordinates and posts an undoable operation.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addPolylines-java.util.List-">addPolylines</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;&nbsp;polylines)</code>
<div class="block">Adds <code>polylines</code> to home and posts an undoable new polyline line operation.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.PlanController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addRooms-java.util.List-">addRooms</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;&nbsp;rooms)</code>
<div class="block">Add <code>newRooms</code> to home and post an undoable new room line operation.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addWalls-java.util.List-">addWalls</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;walls)</code>
<div class="block">Adds <code>walls</code> to home and post an undoable new wall operation.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#adjustMagnetizedPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-float-float-">adjustMagnetizedPieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                                float&nbsp;x,
                                float&nbsp;y)</code>
<div class="block">Attempts to modify <code>piece</code> location depending of its context.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#createDimensionLine-float-float-">createDimensionLine</a></span>(float&nbsp;x,
                   float&nbsp;y)</code>
<div class="block">Creates a new dimension line using its controller.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#createDimensionLine-float-float-float-float-float-">createDimensionLine</a></span>(float&nbsp;xStart,
                   float&nbsp;yStart,
                   float&nbsp;xEnd,
                   float&nbsp;yEnd,
                   float&nbsp;offset)</code>
<div class="block">Returns a new dimension instance joining (<code>xStart</code>,
 <code>yStart</code>) and (<code>xEnd</code>, <code>yEnd</code>) points.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#createLabel-float-float-">createLabel</a></span>(float&nbsp;x,
           float&nbsp;y)</code>
<div class="block">Creates a new label using its controller.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#createLevel-java.lang.String-float-float-float-">createLevel</a></span>(java.lang.String&nbsp;name,
           float&nbsp;elevation,
           float&nbsp;floorThickness,
           float&nbsp;height)</code>
<div class="block">Returns a new level added to home.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#createPolyline-float:A:A-">createPolyline</a></span>(float[][]&nbsp;polylinePoints)</code>
<div class="block">Returns a new polyline instance with the given points.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#createRoom-float:A:A-">createRoom</a></span>(float[][]&nbsp;roomPoints)</code>
<div class="block">Returns a new room instance with the given points.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#createWall-float-float-float-float-com.eteks.sweethome3d.model.Wall-com.eteks.sweethome3d.model.Wall-">createWall</a></span>(float&nbsp;xStart,
          float&nbsp;yStart,
          float&nbsp;xEnd,
          float&nbsp;yEnd,
          <a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wallStartAtStart,
          <a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wallEndAtStart)</code>
<div class="block">Returns a new wall instance between (<code>xStart</code>,
 <code>yStart</code>) and (<code>xEnd</code>, <code>yEnd</code>)
 end points.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#decreaseTextSize--">decreaseTextSize</a></span>()</code>
<div class="block">Decrease the size of texts in selected items.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#deleteItems-java.util.List-">deleteItems</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Deletes <code>items</code> in plan and record it as an undoable operation.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#deletePointFromSelectedRoom-float-float-">deletePointFromSelectedRoom</a></span>(float&nbsp;x,
                           float&nbsp;y)</code>
<div class="block">Deletes the point of the selected room at the given coordinates and posts an undoable operation.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#deleteSelectedLevel--">deleteSelectedLevel</a></span>()</code>
<div class="block">Deletes the selected level and the items that belongs to it.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#deleteSelection--">deleteSelection</a></span>()</code>
<div class="block">Deletes the selection in home.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#escape--">escape</a></span>()</code>
<div class="block">Escapes of current action.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#flipHorizontally--">flipHorizontally</a></span>()</code>
<div class="block">Flips horizontally selected objects.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#flipItem-com.eteks.sweethome3d.model.Selectable-float:A-int-float-boolean-java.util.List-">flipItem</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
        float[]&nbsp;itemTextBaseOffsets,
        int&nbsp;offsetIndex,
        float&nbsp;axisCoordinate,
        boolean&nbsp;horizontalFlip,
        java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;flippedItems)</code>
<div class="block">Flips the given <code>item</code> with the given axis coordinate.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#flipVertically--">flipVertically</a></span>()</code>
<div class="block">Flips vertically selected objects.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCameraElevationState--">getCameraElevationState</a></span>()</code>
<div class="block">Returns the camera elevation state.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCameraPitchRotationState--">getCameraPitchRotationState</a></span>()</code>
<div class="block">Returns the camera pitch rotation state.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCameraYawRotationState--">getCameraYawRotationState</a></span>()</code>
<div class="block">Returns the camera yaw rotation state.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCompassResizeState--">getCompassResizeState</a></span>()</code>
<div class="block">Returns the compass resize state.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCompassRotationState--">getCompassRotationState</a></span>()</code>
<div class="block">Returns the compass rotation state.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineCreationState--">getDimensionLineCreationState</a></span>()</code>
<div class="block">Returns the dimension line creation state.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineDrawingState--">getDimensionLineDrawingState</a></span>()</code>
<div class="block">Returns the dimension line drawing state.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineElevationState--">getDimensionLineElevationState</a></span>()</code>
<div class="block">Returns the dimension line elevation state.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineOffsetState--">getDimensionLineOffsetState</a></span>()</code>
<div class="block">Returns the dimension line offset state.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLinePitchRotationState--">getDimensionLinePitchRotationState</a></span>()</code>
<div class="block">Returns the dimension line rotation state.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineResizeState--">getDimensionLineResizeState</a></span>()</code>
<div class="block">Returns the dimension line resize state.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDragAndDropState--">getDragAndDropState</a></span>()</code>
<div class="block">Returns the drag and drop state.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getHorizontalRulerView--">getHorizontalRulerView</a></span>()</code>
<div class="block">Returns the horizontal ruler of the plan view.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getLabelCreationState--">getLabelCreationState</a></span>()</code>
<div class="block">Returns the label creation state.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getLabelElevationState--">getLabelElevationState</a></span>()</code>
<div class="block">Returns the label elevation state.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getLabelRotationState--">getLabelRotationState</a></span>()</code>
<div class="block">Returns the label rotation state.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getLightPowerModificationState--">getLightPowerModificationState</a></span>()</code>
<div class="block">Returns the light power modification state.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getMaximumScale--">getMaximumScale</a></span>()</code>
<div class="block">Returns the maximum scale of the plan view.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getMinimumScale--">getMinimumScale</a></span>()</code>
<div class="block">Returns the minimum scale of the plan view.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getMode--">getMode</a></span>()</code>
<div class="block">Returns the active mode of this controller.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPanningState--">getPanningState</a></span>()</code>
<div class="block">Returns the panning state.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureElevationState--">getPieceOfFurnitureElevationState</a></span>()</code>
<div class="block">Returns the piece elevation state.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureHeightState--">getPieceOfFurnitureHeightState</a></span>()</code>
<div class="block">Returns the piece height state.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureNameOffsetState--">getPieceOfFurnitureNameOffsetState</a></span>()</code>
<div class="block">Returns the piece name offset state.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureNameRotationState--">getPieceOfFurnitureNameRotationState</a></span>()</code>
<div class="block">Returns the piece name rotation state.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurniturePitchRotationState--">getPieceOfFurniturePitchRotationState</a></span>()</code>
<div class="block">Returns the piece pitch rotation state.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureResizeState--">getPieceOfFurnitureResizeState</a></span>()</code>
<div class="block">Returns the piece resize state.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureRollRotationState--">getPieceOfFurnitureRollRotationState</a></span>()</code>
<div class="block">Returns the piece roll rotation state.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureRotationState--">getPieceOfFurnitureRotationState</a></span>()</code>
<div class="block">Returns the piece rotation state.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPointerTypeLastMousePress--">getPointerTypeLastMousePress</a></span>()</code>
<div class="block">Returns the pointer type used at the last mouse press.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPolylineCreationState--">getPolylineCreationState</a></span>()</code>
<div class="block">Returns the polyline creation state.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPolylineDrawingState--">getPolylineDrawingState</a></span>()</code>
<div class="block">Returns the polyline drawing state.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPolylineResizeState--">getPolylineResizeState</a></span>()</code>
<div class="block">Returns the polyline resize state.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRectangleSelectionState--">getRectangleSelectionState</a></span>()</code>
<div class="block">Returns the rectangle selection state.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomAreaOffsetState--">getRoomAreaOffsetState</a></span>()</code>
<div class="block">Returns the room area offset state.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomAreaRotationState--">getRoomAreaRotationState</a></span>()</code>
<div class="block">Returns the room area rotation state.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomCreationState--">getRoomCreationState</a></span>()</code>
<div class="block">Returns the room creation state.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomDrawingState--">getRoomDrawingState</a></span>()</code>
<div class="block">Returns the room drawing state.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomNameOffsetState--">getRoomNameOffsetState</a></span>()</code>
<div class="block">Returns the room name offset state.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomNameRotationState--">getRoomNameRotationState</a></span>()</code>
<div class="block">Returns the room name rotation state.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomResizeState--">getRoomResizeState</a></span>()</code>
<div class="block">Returns the room resize state.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getScale--">getScale</a></span>()</code>
<div class="block">Returns the scale in plan view.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectableItemAt-float-float-">getSelectableItemAt</a></span>(float&nbsp;x,
                   float&nbsp;y)</code>
<div class="block">Returns the selectable item at (<code>x</code>, <code>y</code>) point.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectableItemsAt-float-float-">getSelectableItemsAt</a></span>(float&nbsp;x,
                    float&nbsp;y)</code>
<div class="block">Returns the selectable items at (<code>x</code>, <code>y</code>) point.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectableItemsIntersectingRectangle-float-float-float-float-">getSelectableItemsIntersectingRectangle</a></span>(float&nbsp;x0,
                                       float&nbsp;y0,
                                       float&nbsp;x1,
                                       float&nbsp;y1)</code>
<div class="block">Returns the items that intersects with the rectangle of (<code>x0</code>,
 <code>y0</code>), (<code>x1</code>, <code>y1</code>) opposite corners.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectionMoveState--">getSelectionMoveState</a></span>()</code>
<div class="block">Returns the selection move state.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectionState--">getSelectionState</a></span>()</code>
<div class="block">Returns the selection state.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getVerticalRulerView--">getVerticalRulerView</a></span>()</code>
<div class="block">Returns the vertical ruler of the plan view.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getWallArcExtentState--">getWallArcExtentState</a></span>()</code>
<div class="block">Returns the wall arc extent state.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getWallCreationState--">getWallCreationState</a></span>()</code>
<div class="block">Returns the wall creation state.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getWallDrawingState--">getWallDrawingState</a></span>()</code>
<div class="block">Returns the wall drawing state.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getWallResizeState--">getWallResizeState</a></span>()</code>
<div class="block">Returns the wall resize state.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>protected float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getXLastMouseMove--">getXLastMouseMove</a></span>()</code>
<div class="block">Returns the abscissa of mouse position at last mouse move.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>protected float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getXLastMousePress--">getXLastMousePress</a></span>()</code>
<div class="block">Returns the abscissa of mouse position at last mouse press.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>protected float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getYLastMouseMove--">getYLastMouseMove</a></span>()</code>
<div class="block">Returns the ordinate of mouse position at last mouse move.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>protected float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getYLastMousePress--">getYLastMousePress</a></span>()</code>
<div class="block">Returns the ordinate of mouse position at last mouse press.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#increaseTextSize--">increaseTextSize</a></span>()</code>
<div class="block">Increase the size of texts in selected items.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isBasePlanModificationState--">isBasePlanModificationState</a></span>()</code>
<div class="block">Returns <code>true</code> if the interactions in the current mode may modify
 the base plan of a home.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isFeedbackDisplayed--">isFeedbackDisplayed</a></span>()</code>
<div class="block">Returns <code>true</code> if this view accepts to display requested feedback.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isItemDeletable-com.eteks.sweethome3d.model.Selectable-">isItemDeletable</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be deleted.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isItemMovable-com.eteks.sweethome3d.model.Selectable-">isItemMovable</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be moved
 in the plan.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isItemPartOfBasePlan-com.eteks.sweethome3d.model.Selectable-">isItemPartOfBasePlan</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> it the given <code>item</code> belongs
 to the base plan.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isItemResizable-com.eteks.sweethome3d.model.Selectable-">isItemResizable</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be resized.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isModificationState--">isModificationState</a></span>()</code>
<div class="block">Returns <code>true</code> if the interactions in the current mode may modify
 the state of a home.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isRoomPointDeletableAt-com.eteks.sweethome3d.model.Room-float-float-">isRoomPointDeletableAt</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
                      float&nbsp;x,
                      float&nbsp;y)</code>
<div class="block">Returns <code>true</code> if the given point can be removed from the <code>room</code>.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isRoomPointsComputableAt-com.eteks.sweethome3d.model.Room-float-float-">isRoomPointsComputableAt</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
                        float&nbsp;x,
                        float&nbsp;y)</code>
<div class="block">Returns <code>true</code> if the <code>room</code> can be recomputed at the given coordinates.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#joinSelectedWalls--">joinSelectedWalls</a></span>()</code>
<div class="block">Controls how selected walls are joined.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#lockBasePlan--">lockBasePlan</a></span>()</code>
<div class="block">Locks home base plan.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifyCompass--">modifyCompass</a></span>()</code>
<div class="block">Controls the modification of the compass.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifyObserverCamera--">modifyObserverCamera</a></span>()</code>
<div class="block">Controls the modification of the observer camera.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifySelectedDimensionLines--">modifySelectedDimensionLines</a></span>()</code>
<div class="block">Controls the modification of the selected labels.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifySelectedItem--">modifySelectedItem</a></span>()</code>
<div class="block">Controls the modification of the item selected in plan.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifySelectedLabels--">modifySelectedLabels</a></span>()</code>
<div class="block">Controls the modification of the selected labels.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifySelectedLevel--">modifySelectedLevel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifySelectedPolylines--">modifySelectedPolylines</a></span>()</code>
<div class="block">Controls the modification of the selected polylines.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifySelectedRooms--">modifySelectedRooms</a></span>()</code>
<div class="block">Controls the modification of the selected rooms.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#modifySelectedWalls--">modifySelectedWalls</a></span>()</code>
<div class="block">Controls the modification of selected walls.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#moveItems-java.util.List-float-float-">moveItems</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
         float&nbsp;dx,
         float&nbsp;dy)</code>
<div class="block">Moves <code>items</code> of (<code>dx</code>, <code>dy</code>) units.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#moveMouse-float-float-">moveMouse</a></span>(float&nbsp;x,
         float&nbsp;y)</code>
<div class="block">Processes a mouse button moved event.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#moveSelection-float-float-">moveSelection</a></span>(float&nbsp;dx,
             float&nbsp;dy)</code>
<div class="block">Moves the selection of (<code>dx</code>,<code>dy</code>) in home.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#pressMouse-float-float-int-boolean-boolean-">pressMouse</a></span>(float&nbsp;x,
          float&nbsp;y,
          int&nbsp;clickCount,
          boolean&nbsp;shiftDown,
          boolean&nbsp;duplicationActivated)</code>
<div class="block">Processes a mouse button pressed event.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#pressMouse-float-float-int-boolean-boolean-boolean-boolean-">pressMouse</a></span>(float&nbsp;x,
          float&nbsp;y,
          int&nbsp;clickCount,
          boolean&nbsp;shiftDown,
          boolean&nbsp;alignmentActivated,
          boolean&nbsp;duplicationActivated,
          boolean&nbsp;magnetismToggled)</code>
<div class="block">Processes a mouse button pressed event.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#pressMouse-float-float-int-boolean-boolean-boolean-boolean-com.eteks.sweethome3d.viewcontroller.View.PointerType-">pressMouse</a></span>(float&nbsp;x,
          float&nbsp;y,
          int&nbsp;clickCount,
          boolean&nbsp;shiftDown,
          boolean&nbsp;alignmentActivated,
          boolean&nbsp;duplicationActivated,
          boolean&nbsp;magnetismToggled,
          <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a>&nbsp;pointerType)</code>
<div class="block">Processes a mouse button pressed event.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#recomputeSelectedRoomPoints-float-float-">recomputeSelectedRoomPoints</a></span>(float&nbsp;x,
                           float&nbsp;y)</code>
<div class="block">Controls the recomputation of the points of the selected room at the given coordinates.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#releaseMouse-float-float-">releaseMouse</a></span>(float&nbsp;x,
            float&nbsp;y)</code>
<div class="block">Processes a mouse button released event.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.PlanController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#reverseSelectedWallsDirection--">reverseSelectedWallsDirection</a></span>()</code>
<div class="block">Controls the direction reverse of selected walls.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#selectAll--">selectAll</a></span>()</code>
<div class="block">Selects all visible items in the selected level of home.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#selectAllAtAllLevels--">selectAllAtAllLevels</a></span>()</code>
<div class="block">Selects all visible items in all levels of home.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#selectAndShowItems-java.util.List-">selectAndShowItems</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Selects <code>items</code> and make them visible at screen.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#selectItem-com.eteks.sweethome3d.model.Selectable-">selectItem</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Selects the given <code>item</code>.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#selectItems-java.util.List-">selectItems</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Selects <code>items</code>.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setAlignmentActivated-boolean-">setAlignmentActivated</a></span>(boolean&nbsp;alignmentActivated)</code>
<div class="block">Activates or deactivates alignment feature.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setAllLevelsViewable--">setAllLevelsViewable</a></span>()</code>
<div class="block">Makes all levels viewable.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setDuplicationActivated-boolean-">setDuplicationActivated</a></span>(boolean&nbsp;duplicationActivated)</code>
<div class="block">Activates or deactivates duplication feature.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setEditionActivated-boolean-">setEditionActivated</a></span>(boolean&nbsp;editionActivated)</code>
<div class="block">Activates or deactivates edition.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setFeedbackDisplayed-boolean-">setFeedbackDisplayed</a></span>(boolean&nbsp;displayed)</code>
<div class="block">Sets whether requested feedback should be displayed in the view or not.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</code>
<div class="block">Sets the active mode of this controller and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setScale-float-">setScale</a></span>(float&nbsp;scale)</code>
<div class="block">Controls the scale in plan view and and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setSelectedLevel-com.eteks.sweethome3d.model.Level-">setSelectedLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Sets the selected level in home.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setSelectedLevelOnlyViewable--">setSelectedLevelOnlyViewable</a></span>()</code>
<div class="block">Makes the selected level the only viewable one.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setState-com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState-">setState</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;state)</code>
<div class="block">Changes current state of controller.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#splitSelectedWall--">splitSelectedWall</a></span>()</code>
<div class="block">Controls the split of the selected wall in two joined walls of equal length.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#startDraggedItems-java.util.List-float-float-">startDraggedItems</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems,
                 float&nbsp;x,
                 float&nbsp;y)</code>
<div class="block">Displays in plan view the feedback of <code>draggedItems</code>,
 during a drag and drop operation initiated from outside of plan view.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#stopDraggedItems--">stopDraggedItems</a></span>()</code>
<div class="block">Deletes in plan view the feedback of the dragged items.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#toggleBoldStyle--">toggleBoldStyle</a></span>()</code>
<div class="block">Toggles bold style of texts in selected items.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#toggleItalicStyle--">toggleItalicStyle</a></span>()</code>
<div class="block">Toggles italic style of texts in selected items.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#toggleItemSelection-com.eteks.sweethome3d.model.Selectable-">toggleItemSelection</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Toggles the selection of the given <code>item</code>.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#toggleMagnetism-boolean-">toggleMagnetism</a></span>(boolean&nbsp;magnetismToggled)</code>
<div class="block">Toggles temporary magnetism feature of user preferences.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#toggleSelectedLevelViewability--">toggleSelectedLevelViewability</a></span>()</code>
<div class="block">Toggles the viewability of the selected level.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#unlockBasePlan--">unlockBasePlan</a></span>()</code>
<div class="block">Unlocks home base plan.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#updateEditableProperty-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">updateEditableProperty</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;editableProperty,
                      java.lang.Object&nbsp;value)</code>
<div class="block">Updates an editable property with the entered <code>value</code>.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#wasAlignmentActivatedLastMousePress--">wasAlignmentActivatedLastMousePress</a></span>()</code>
<div class="block">Returns <code>true</code> if alignment was activated at last mouse press.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#wasDuplicationActivatedLastMousePress--">wasDuplicationActivatedLastMousePress</a></span>()</code>
<div class="block">Returns <code>true</code> if duplication was activated at last mouse press.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#wasMagnetismToggledLastMousePress--">wasMagnetismToggledLastMousePress</a></span>()</code>
<div class="block">Returns <code>true</code> if magnetism was toggled at last mouse press.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#wasShiftDownLastMousePress--">wasShiftDownLastMousePress</a></span>()</code>
<div class="block">Returns <code>true</code> if shift key was down at last mouse press.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#zoom-float-">zoom</a></span>(float&nbsp;factor)</code>
<div class="block">Processes a zoom event.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.FurnitureController">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurniture-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">addFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurnitureToGroup-java.util.List-com.eteks.sweethome3d.model.HomeFurnitureGroup-">addFurnitureToGroup</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnBackSide--">alignSelectedFurnitureOnBackSide</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnBottom--">alignSelectedFurnitureOnBottom</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnFrontSide--">alignSelectedFurnitureOnFrontSide</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnLeft--">alignSelectedFurnitureOnLeft</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnLeftSide--">alignSelectedFurnitureOnLeftSide</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnRight--">alignSelectedFurnitureOnRight</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnRightSide--">alignSelectedFurnitureOnRightSide</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnTop--">alignSelectedFurnitureOnTop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureSideBySide--">alignSelectedFurnitureSideBySide</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomeFurnitureGroup-java.util.List-">createHomeFurnitureGroup</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">createHomeFurnitureGroup</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomePieceOfFurniture-com.eteks.sweethome3d.model.PieceOfFurniture-">createHomePieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#deleteFurniture-java.util.List-">deleteFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#distributeSelectedFurniture-boolean-">distributeSelectedFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#distributeSelectedFurnitureHorizontally--">distributeSelectedFurnitureHorizontally</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#distributeSelectedFurnitureVertically--">distributeSelectedFurnitureVertically</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getFurnitureInSameGroup-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getFurnitureInSameGroup</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getHighestSurroundingPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getHighestSurroundingPieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getSurroundingFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getSurroundingFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#groupSelectedFurniture--">groupSelectedFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#importFurniture--">importFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#importFurniture-java.lang.String-">importFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureDeletable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureDeletable</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureMovable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureMovable</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurniturePartOfBasePlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurniturePartOfBasePlan</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureVisibleAtSelectedLevel-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureVisibleAtSelectedLevel</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#modifySelectedFurniture--">modifySelectedFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#moveSelectedFurnitureBefore-com.eteks.sweethome3d.model.HomePieceOfFurniture-">moveSelectedFurnitureBefore</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#resetFurnitureElevation--">resetFurnitureElevation</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setFurnitureVisibleProperties-java.util.List-">setFurnitureVisibleProperties</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setFurnitureVisiblePropertyNames-java.util.List-">setFurnitureVisiblePropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setHomeProperty-java.lang.String-java.lang.String-">setHomeProperty</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setSelectedFurniture-java.util.List-">setSelectedFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setSelectedFurniture-java.util.List-boolean-">setSelectedFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setVisualProperty-java.lang.String-java.lang.Object-">setVisualProperty</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">sortFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-java.lang.String-">sortFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">toggleFurnitureSort</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-java.lang.String-">toggleFurnitureSort</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSortOrder--">toggleFurnitureSortOrder</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">toggleFurnitureVisibleProperty</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-java.lang.String-">toggleFurnitureVisibleProperty</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleSelectedFurnitureVisibility--">toggleSelectedFurnitureVisibility</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#ungroupSelectedFurniture--">ungroupSelectedFurniture</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PlanController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PlanController</h4>
<pre>public&nbsp;PlanController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                      javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of plan view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home plan edited by this controller and its view</dd>
<dd><code>preferences</code> - the preferences of the application</dd>
<dd><code>viewFactory</code> - a factory able to create the plan view managed by this controller</dd>
<dd><code>contentManager</code> - a content manager used to import furniture</dd>
<dd><code>undoSupport</code> - undo support to post changes on plan by this controller</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getView--">getView</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a></code></dd>
</dl>
</li>
</ul>
<a name="setState-com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setState</h4>
<pre>protected&nbsp;void&nbsp;setState(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;state)</pre>
<div class="block">Changes current state of controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.PlanController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.PlanController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;getMode()</pre>
<div class="block">Returns the active mode of this controller.</div>
</li>
</ul>
<a name="setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMode</h4>
<pre>public&nbsp;void&nbsp;setMode(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</pre>
<div class="block">Sets the active mode of this controller and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="isModificationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isModificationState</h4>
<pre>public&nbsp;boolean&nbsp;isModificationState()</pre>
<div class="block">Returns <code>true</code> if the interactions in the current mode may modify
 the state of a home.</div>
</li>
</ul>
<a name="isBasePlanModificationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBasePlanModificationState</h4>
<pre>public&nbsp;boolean&nbsp;isBasePlanModificationState()</pre>
<div class="block">Returns <code>true</code> if the interactions in the current mode may modify
 the base plan of a home.</div>
</li>
</ul>
<a name="deleteSelection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteSelection</h4>
<pre>public&nbsp;void&nbsp;deleteSelection()</pre>
<div class="block">Deletes the selection in home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#deleteSelection--">deleteSelection</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a></code></dd>
</dl>
</li>
</ul>
<a name="escape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>escape</h4>
<pre>public&nbsp;void&nbsp;escape()</pre>
<div class="block">Escapes of current action.</div>
</li>
</ul>
<a name="moveSelection-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveSelection</h4>
<pre>public&nbsp;void&nbsp;moveSelection(float&nbsp;dx,
                          float&nbsp;dy)</pre>
<div class="block">Moves the selection of (<code>dx</code>,<code>dy</code>) in home.</div>
</li>
</ul>
<a name="toggleMagnetism-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleMagnetism</h4>
<pre>public&nbsp;void&nbsp;toggleMagnetism(boolean&nbsp;magnetismToggled)</pre>
<div class="block">Toggles temporary magnetism feature of user preferences.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>magnetismToggled</code> - if <code>true</code> then magnetism feature is toggled.</dd>
</dl>
</li>
</ul>
<a name="setAlignmentActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignmentActivated</h4>
<pre>public&nbsp;void&nbsp;setAlignmentActivated(boolean&nbsp;alignmentActivated)</pre>
<div class="block">Activates or deactivates alignment feature.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignmentActivated</code> - if <code>true</code> then alignment is active.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setDuplicationActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDuplicationActivated</h4>
<pre>public&nbsp;void&nbsp;setDuplicationActivated(boolean&nbsp;duplicationActivated)</pre>
<div class="block">Activates or deactivates duplication feature.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>duplicationActivated</code> - if <code>true</code> then duplication is active.</dd>
</dl>
</li>
</ul>
<a name="setEditionActivated-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditionActivated</h4>
<pre>public&nbsp;void&nbsp;setEditionActivated(boolean&nbsp;editionActivated)</pre>
<div class="block">Activates or deactivates edition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>editionActivated</code> - if <code>true</code> then edition is active</dd>
</dl>
</li>
</ul>
<a name="updateEditableProperty-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateEditableProperty</h4>
<pre>public&nbsp;void&nbsp;updateEditableProperty(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;editableProperty,
                                   java.lang.Object&nbsp;value)</pre>
<div class="block">Updates an editable property with the entered <code>value</code>.</div>
</li>
</ul>
<a name="pressMouse-float-float-int-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pressMouse</h4>
<pre>public&nbsp;void&nbsp;pressMouse(float&nbsp;x,
                       float&nbsp;y,
                       int&nbsp;clickCount,
                       boolean&nbsp;shiftDown,
                       boolean&nbsp;duplicationActivated)</pre>
<div class="block">Processes a mouse button pressed event.</div>
</li>
</ul>
<a name="pressMouse-float-float-int-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pressMouse</h4>
<pre>public&nbsp;void&nbsp;pressMouse(float&nbsp;x,
                       float&nbsp;y,
                       int&nbsp;clickCount,
                       boolean&nbsp;shiftDown,
                       boolean&nbsp;alignmentActivated,
                       boolean&nbsp;duplicationActivated,
                       boolean&nbsp;magnetismToggled)</pre>
<div class="block">Processes a mouse button pressed event.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="pressMouse-float-float-int-boolean-boolean-boolean-boolean-com.eteks.sweethome3d.viewcontroller.View.PointerType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pressMouse</h4>
<pre>public&nbsp;void&nbsp;pressMouse(float&nbsp;x,
                       float&nbsp;y,
                       int&nbsp;clickCount,
                       boolean&nbsp;shiftDown,
                       boolean&nbsp;alignmentActivated,
                       boolean&nbsp;duplicationActivated,
                       boolean&nbsp;magnetismToggled,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a>&nbsp;pointerType)</pre>
<div class="block">Processes a mouse button pressed event.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="releaseMouse-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>releaseMouse</h4>
<pre>public&nbsp;void&nbsp;releaseMouse(float&nbsp;x,
                         float&nbsp;y)</pre>
<div class="block">Processes a mouse button released event.</div>
</li>
</ul>
<a name="moveMouse-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveMouse</h4>
<pre>public&nbsp;void&nbsp;moveMouse(float&nbsp;x,
                      float&nbsp;y)</pre>
<div class="block">Processes a mouse button moved event.</div>
</li>
</ul>
<a name="zoom-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoom</h4>
<pre>public&nbsp;void&nbsp;zoom(float&nbsp;factor)</pre>
<div class="block">Processes a zoom event.</div>
</li>
</ul>
<a name="setFeedbackDisplayed-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFeedbackDisplayed</h4>
<pre>public&nbsp;void&nbsp;setFeedbackDisplayed(boolean&nbsp;displayed)</pre>
<div class="block">Sets whether requested feedback should be displayed in the view or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isFeedbackDisplayed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFeedbackDisplayed</h4>
<pre>public&nbsp;boolean&nbsp;isFeedbackDisplayed()</pre>
<div class="block">Returns <code>true</code> if this view accepts to display requested feedback.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getSelectionState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectionState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getSelectionState()</pre>
<div class="block">Returns the selection state.</div>
</li>
</ul>
<a name="getSelectionMoveState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectionMoveState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getSelectionMoveState()</pre>
<div class="block">Returns the selection move state.</div>
</li>
</ul>
<a name="getRectangleSelectionState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRectangleSelectionState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getRectangleSelectionState()</pre>
<div class="block">Returns the rectangle selection state.</div>
</li>
</ul>
<a name="getPanningState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPanningState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPanningState()</pre>
<div class="block">Returns the panning state.</div>
</li>
</ul>
<a name="getDragAndDropState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDragAndDropState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getDragAndDropState()</pre>
<div class="block">Returns the drag and drop state.</div>
</li>
</ul>
<a name="getWallCreationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallCreationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getWallCreationState()</pre>
<div class="block">Returns the wall creation state.</div>
</li>
</ul>
<a name="getWallDrawingState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallDrawingState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getWallDrawingState()</pre>
<div class="block">Returns the wall drawing state.</div>
</li>
</ul>
<a name="getWallResizeState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallResizeState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getWallResizeState()</pre>
<div class="block">Returns the wall resize state.</div>
</li>
</ul>
<a name="getWallArcExtentState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallArcExtentState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getWallArcExtentState()</pre>
<div class="block">Returns the wall arc extent state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getPieceOfFurnitureRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPieceOfFurnitureRotationState()</pre>
<div class="block">Returns the piece rotation state.</div>
</li>
</ul>
<a name="getPieceOfFurniturePitchRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurniturePitchRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPieceOfFurniturePitchRotationState()</pre>
<div class="block">Returns the piece pitch rotation state.</div>
</li>
</ul>
<a name="getPieceOfFurnitureRollRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureRollRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPieceOfFurnitureRollRotationState()</pre>
<div class="block">Returns the piece roll rotation state.</div>
</li>
</ul>
<a name="getPieceOfFurnitureElevationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureElevationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPieceOfFurnitureElevationState()</pre>
<div class="block">Returns the piece elevation state.</div>
</li>
</ul>
<a name="getPieceOfFurnitureHeightState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureHeightState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPieceOfFurnitureHeightState()</pre>
<div class="block">Returns the piece height state.</div>
</li>
</ul>
<a name="getPieceOfFurnitureResizeState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureResizeState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPieceOfFurnitureResizeState()</pre>
<div class="block">Returns the piece resize state.</div>
</li>
</ul>
<a name="getLightPowerModificationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightPowerModificationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getLightPowerModificationState()</pre>
<div class="block">Returns the light power modification state.</div>
</li>
</ul>
<a name="getPieceOfFurnitureNameOffsetState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureNameOffsetState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPieceOfFurnitureNameOffsetState()</pre>
<div class="block">Returns the piece name offset state.</div>
</li>
</ul>
<a name="getPieceOfFurnitureNameRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureNameRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPieceOfFurnitureNameRotationState()</pre>
<div class="block">Returns the piece name rotation state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getCameraYawRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCameraYawRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getCameraYawRotationState()</pre>
<div class="block">Returns the camera yaw rotation state.</div>
</li>
</ul>
<a name="getCameraPitchRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCameraPitchRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getCameraPitchRotationState()</pre>
<div class="block">Returns the camera pitch rotation state.</div>
</li>
</ul>
<a name="getCameraElevationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCameraElevationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getCameraElevationState()</pre>
<div class="block">Returns the camera elevation state.</div>
</li>
</ul>
<a name="getDimensionLineCreationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDimensionLineCreationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getDimensionLineCreationState()</pre>
<div class="block">Returns the dimension line creation state.</div>
</li>
</ul>
<a name="getDimensionLineDrawingState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDimensionLineDrawingState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getDimensionLineDrawingState()</pre>
<div class="block">Returns the dimension line drawing state.</div>
</li>
</ul>
<a name="getDimensionLineResizeState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDimensionLineResizeState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getDimensionLineResizeState()</pre>
<div class="block">Returns the dimension line resize state.</div>
</li>
</ul>
<a name="getDimensionLineOffsetState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDimensionLineOffsetState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getDimensionLineOffsetState()</pre>
<div class="block">Returns the dimension line offset state.</div>
</li>
</ul>
<a name="getDimensionLinePitchRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDimensionLinePitchRotationState</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getDimensionLinePitchRotationState()</pre>
<div class="block">Returns the dimension line rotation state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getDimensionLineElevationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDimensionLineElevationState</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getDimensionLineElevationState()</pre>
<div class="block">Returns the dimension line elevation state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getRoomCreationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoomCreationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getRoomCreationState()</pre>
<div class="block">Returns the room creation state.</div>
</li>
</ul>
<a name="getRoomDrawingState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoomDrawingState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getRoomDrawingState()</pre>
<div class="block">Returns the room drawing state.</div>
</li>
</ul>
<a name="getRoomResizeState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoomResizeState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getRoomResizeState()</pre>
<div class="block">Returns the room resize state.</div>
</li>
</ul>
<a name="getRoomAreaOffsetState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoomAreaOffsetState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getRoomAreaOffsetState()</pre>
<div class="block">Returns the room area offset state.</div>
</li>
</ul>
<a name="getRoomAreaRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoomAreaRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getRoomAreaRotationState()</pre>
<div class="block">Returns the room area rotation state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getRoomNameOffsetState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoomNameOffsetState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getRoomNameOffsetState()</pre>
<div class="block">Returns the room name offset state.</div>
</li>
</ul>
<a name="getRoomNameRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoomNameRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getRoomNameRotationState()</pre>
<div class="block">Returns the room name rotation state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getPolylineCreationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolylineCreationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPolylineCreationState()</pre>
<div class="block">Returns the polyline creation state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getPolylineDrawingState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolylineDrawingState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPolylineDrawingState()</pre>
<div class="block">Returns the polyline drawing state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getPolylineResizeState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolylineResizeState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getPolylineResizeState()</pre>
<div class="block">Returns the polyline resize state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getLabelCreationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLabelCreationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getLabelCreationState()</pre>
<div class="block">Returns the label creation state.</div>
</li>
</ul>
<a name="getLabelRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLabelRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getLabelRotationState()</pre>
<div class="block">Returns the label rotation state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getLabelElevationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLabelElevationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getLabelElevationState()</pre>
<div class="block">Returns the label elevation state.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getCompassRotationState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompassRotationState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getCompassRotationState()</pre>
<div class="block">Returns the compass rotation state.</div>
</li>
</ul>
<a name="getCompassResizeState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompassResizeState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;getCompassResizeState()</pre>
<div class="block">Returns the compass resize state.</div>
</li>
</ul>
<a name="getXLastMousePress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXLastMousePress</h4>
<pre>protected&nbsp;float&nbsp;getXLastMousePress()</pre>
<div class="block">Returns the abscissa of mouse position at last mouse press.</div>
</li>
</ul>
<a name="getYLastMousePress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYLastMousePress</h4>
<pre>protected&nbsp;float&nbsp;getYLastMousePress()</pre>
<div class="block">Returns the ordinate of mouse position at last mouse press.</div>
</li>
</ul>
<a name="wasShiftDownLastMousePress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wasShiftDownLastMousePress</h4>
<pre>protected&nbsp;boolean&nbsp;wasShiftDownLastMousePress()</pre>
<div class="block">Returns <code>true</code> if shift key was down at last mouse press.</div>
</li>
</ul>
<a name="wasMagnetismToggledLastMousePress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wasMagnetismToggledLastMousePress</h4>
<pre>protected&nbsp;boolean&nbsp;wasMagnetismToggledLastMousePress()</pre>
<div class="block">Returns <code>true</code> if magnetism was toggled at last mouse press.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="wasAlignmentActivatedLastMousePress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wasAlignmentActivatedLastMousePress</h4>
<pre>protected&nbsp;boolean&nbsp;wasAlignmentActivatedLastMousePress()</pre>
<div class="block">Returns <code>true</code> if alignment was activated at last mouse press.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="wasDuplicationActivatedLastMousePress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wasDuplicationActivatedLastMousePress</h4>
<pre>protected&nbsp;boolean&nbsp;wasDuplicationActivatedLastMousePress()</pre>
<div class="block">Returns <code>true</code> if duplication was activated at last mouse press.</div>
</li>
</ul>
<a name="getPointerTypeLastMousePress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPointerTypeLastMousePress</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a>&nbsp;getPointerTypeLastMousePress()</pre>
<div class="block">Returns the pointer type used at the last mouse press.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="getXLastMouseMove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXLastMouseMove</h4>
<pre>protected&nbsp;float&nbsp;getXLastMouseMove()</pre>
<div class="block">Returns the abscissa of mouse position at last mouse move.</div>
</li>
</ul>
<a name="getYLastMouseMove--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYLastMouseMove</h4>
<pre>protected&nbsp;float&nbsp;getYLastMouseMove()</pre>
<div class="block">Returns the ordinate of mouse position at last mouse move.</div>
</li>
</ul>
<a name="modifySelectedItem--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedItem</h4>
<pre>public&nbsp;void&nbsp;modifySelectedItem()</pre>
<div class="block">Controls the modification of the item selected in plan.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="modifySelectedWalls--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedWalls</h4>
<pre>public&nbsp;void&nbsp;modifySelectedWalls()</pre>
<div class="block">Controls the modification of selected walls.</div>
</li>
</ul>
<a name="lockBasePlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lockBasePlan</h4>
<pre>public&nbsp;void&nbsp;lockBasePlan()</pre>
<div class="block">Locks home base plan.</div>
</li>
</ul>
<a name="isItemPartOfBasePlan-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isItemPartOfBasePlan</h4>
<pre>protected&nbsp;boolean&nbsp;isItemPartOfBasePlan(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Returns <code>true</code> it the given <code>item</code> belongs
 to the base plan.</div>
</li>
</ul>
<a name="unlockBasePlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unlockBasePlan</h4>
<pre>public&nbsp;void&nbsp;unlockBasePlan()</pre>
<div class="block">Unlocks home base plan.</div>
</li>
</ul>
<a name="isItemMovable-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isItemMovable</h4>
<pre>protected&nbsp;boolean&nbsp;isItemMovable(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be moved
 in the plan. Default implementation returns <code>true</code>.</div>
</li>
</ul>
<a name="isItemResizable-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isItemResizable</h4>
<pre>protected&nbsp;boolean&nbsp;isItemResizable(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be resized.
 Default implementation returns <code>false</code> if the given <code>item</code>
 is a non resizable piece of furniture.</div>
</li>
</ul>
<a name="isItemDeletable-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isItemDeletable</h4>
<pre>protected&nbsp;boolean&nbsp;isItemDeletable(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be deleted.
 Default implementation returns <code>true</code> except if the given <code>item</code>
 is a camera or a compass or if the given <code>item</code> isn't a
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureDeletable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">deletable piece of furniture</a>.</div>
</li>
</ul>
<a name="flipHorizontally--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flipHorizontally</h4>
<pre>public&nbsp;void&nbsp;flipHorizontally()</pre>
<div class="block">Flips horizontally selected objects.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="flipVertically--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flipVertically</h4>
<pre>public&nbsp;void&nbsp;flipVertically()</pre>
<div class="block">Flips vertically selected objects.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="flipItem-com.eteks.sweethome3d.model.Selectable-float:A-int-float-boolean-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>flipItem</h4>
<pre>protected&nbsp;void&nbsp;flipItem(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
                        float[]&nbsp;itemTextBaseOffsets,
                        int&nbsp;offsetIndex,
                        float&nbsp;axisCoordinate,
                        boolean&nbsp;horizontalFlip,
                        java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;flippedItems)</pre>
<div class="block">Flips the given <code>item</code> with the given axis coordinate.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>item</code> - the item to flip</dd>
<dd><code>itemTextBaseOffsets</code> - base offset for the texts of the item</dd>
<dd><code>offsetIndex</code> - index to get the first text base offset of item</dd>
<dd><code>axisCoordinate</code> - the coordinate of the symmetry axis</dd>
<dd><code>horizontalFlip</code> - if <code>true</code> the item should be flipped horizontally otherwise vertically</dd>
<dd><code>flippedItems</code> - list of all the items that must be flipped</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="joinSelectedWalls--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>joinSelectedWalls</h4>
<pre>public&nbsp;void&nbsp;joinSelectedWalls()</pre>
<div class="block">Controls how selected walls are joined.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="reverseSelectedWallsDirection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reverseSelectedWallsDirection</h4>
<pre>public&nbsp;void&nbsp;reverseSelectedWallsDirection()</pre>
<div class="block">Controls the direction reverse of selected walls.</div>
</li>
</ul>
<a name="splitSelectedWall--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>splitSelectedWall</h4>
<pre>public&nbsp;void&nbsp;splitSelectedWall()</pre>
<div class="block">Controls the split of the selected wall in two joined walls of equal length.</div>
</li>
</ul>
<a name="modifySelectedRooms--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedRooms</h4>
<pre>public&nbsp;void&nbsp;modifySelectedRooms()</pre>
<div class="block">Controls the modification of the selected rooms.</div>
</li>
</ul>
<a name="createDimensionLine-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDimensionLine</h4>
<pre>protected&nbsp;void&nbsp;createDimensionLine(float&nbsp;x,
                                   float&nbsp;y)</pre>
<div class="block">Creates a new dimension line using its controller.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="createLabel-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createLabel</h4>
<pre>protected&nbsp;void&nbsp;createLabel(float&nbsp;x,
                           float&nbsp;y)</pre>
<div class="block">Creates a new label using its controller.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="modifySelectedLabels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedLabels</h4>
<pre>public&nbsp;void&nbsp;modifySelectedLabels()</pre>
<div class="block">Controls the modification of the selected labels.</div>
</li>
</ul>
<a name="modifySelectedPolylines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedPolylines</h4>
<pre>public&nbsp;void&nbsp;modifySelectedPolylines()</pre>
<div class="block">Controls the modification of the selected polylines.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="modifySelectedDimensionLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedDimensionLines</h4>
<pre>public&nbsp;void&nbsp;modifySelectedDimensionLines()</pre>
<div class="block">Controls the modification of the selected labels.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="modifyCompass--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifyCompass</h4>
<pre>public&nbsp;void&nbsp;modifyCompass()</pre>
<div class="block">Controls the modification of the compass.</div>
</li>
</ul>
<a name="modifyObserverCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifyObserverCamera</h4>
<pre>public&nbsp;void&nbsp;modifyObserverCamera()</pre>
<div class="block">Controls the modification of the observer camera.</div>
</li>
</ul>
<a name="toggleBoldStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleBoldStyle</h4>
<pre>public&nbsp;void&nbsp;toggleBoldStyle()</pre>
<div class="block">Toggles bold style of texts in selected items.</div>
</li>
</ul>
<a name="toggleItalicStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleItalicStyle</h4>
<pre>public&nbsp;void&nbsp;toggleItalicStyle()</pre>
<div class="block">Toggles italic style of texts in selected items.</div>
</li>
</ul>
<a name="increaseTextSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>increaseTextSize</h4>
<pre>public&nbsp;void&nbsp;increaseTextSize()</pre>
<div class="block">Increase the size of texts in selected items.</div>
</li>
</ul>
<a name="decreaseTextSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decreaseTextSize</h4>
<pre>public&nbsp;void&nbsp;decreaseTextSize()</pre>
<div class="block">Decrease the size of texts in selected items.</div>
</li>
</ul>
<a name="getMinimumScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinimumScale</h4>
<pre>public&nbsp;float&nbsp;getMinimumScale()</pre>
<div class="block">Returns the minimum scale of the plan view.</div>
</li>
</ul>
<a name="getMaximumScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaximumScale</h4>
<pre>public&nbsp;float&nbsp;getMaximumScale()</pre>
<div class="block">Returns the maximum scale of the plan view.</div>
</li>
</ul>
<a name="getScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScale</h4>
<pre>public&nbsp;float&nbsp;getScale()</pre>
<div class="block">Returns the scale in plan view.</div>
</li>
</ul>
<a name="setScale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScale</h4>
<pre>public&nbsp;void&nbsp;setScale(float&nbsp;scale)</pre>
<div class="block">Controls the scale in plan view and and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="setSelectedLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectedLevel</h4>
<pre>public final&nbsp;void&nbsp;setSelectedLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Sets the selected level in home.</div>
</li>
</ul>
<a name="selectAll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectAll</h4>
<pre>public&nbsp;void&nbsp;selectAll()</pre>
<div class="block">Selects all visible items in the selected level of home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#selectAll--">selectAll</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a></code></dd>
</dl>
</li>
</ul>
<a name="selectAllAtAllLevels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectAllAtAllLevels</h4>
<pre>public&nbsp;void&nbsp;selectAllAtAllLevels()</pre>
<div class="block">Selects all visible items in all levels of home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getHorizontalRulerView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHorizontalRulerView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getHorizontalRulerView()</pre>
<div class="block">Returns the horizontal ruler of the plan view.</div>
</li>
</ul>
<a name="getVerticalRulerView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVerticalRulerView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getVerticalRulerView()</pre>
<div class="block">Returns the vertical ruler of the plan view.</div>
</li>
</ul>
<a name="startDraggedItems-java.util.List-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDraggedItems</h4>
<pre>public&nbsp;void&nbsp;startDraggedItems(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems,
                              float&nbsp;x,
                              float&nbsp;y)</pre>
<div class="block">Displays in plan view the feedback of <code>draggedItems</code>,
 during a drag and drop operation initiated from outside of plan view.</div>
</li>
</ul>
<a name="stopDraggedItems--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopDraggedItems</h4>
<pre>public&nbsp;void&nbsp;stopDraggedItems()</pre>
<div class="block">Deletes in plan view the feedback of the dragged items.</div>
</li>
</ul>
<a name="adjustMagnetizedPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>adjustMagnetizedPieceOfFurniture</h4>
<pre>protected&nbsp;void&nbsp;adjustMagnetizedPieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                                                float&nbsp;x,
                                                float&nbsp;y)</pre>
<div class="block">Attempts to modify <code>piece</code> location depending of its context.
 If the <code>piece</code> is a door or a window and the point (<code>x</code>, <code>y</code>)
 belongs to a wall, the piece will be resized, rotated and moved so
 its opening depth is equal to wall thickness and its angle matches wall direction.
 If the <code>piece</code> isn't a door or a window and the point (<code>x</code>, <code>y</code>)
 belongs to a wall, the piece will be rotated and moved so
 its back face lies along the closest wall side and its angle matches wall direction.
 If the <code>piece</code> isn't a door or a window, its bounding box is included in
 the one of an other object and its elevation is equal to zero, it will be elevated
 to appear on the top of the latter.</div>
</li>
</ul>
<a name="addLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLevel</h4>
<pre>public&nbsp;void&nbsp;addLevel()</pre>
<div class="block">Controls the creation of a new level.</div>
</li>
</ul>
<a name="addLevelAtSameElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLevelAtSameElevation</h4>
<pre>public&nbsp;void&nbsp;addLevelAtSameElevation()</pre>
<div class="block">Controls the creation of a new level at same elevation.</div>
</li>
</ul>
<a name="createLevel-java.lang.String-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createLevel</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;createLevel(java.lang.String&nbsp;name,
                            float&nbsp;elevation,
                            float&nbsp;floorThickness,
                            float&nbsp;height)</pre>
<div class="block">Returns a new level added to home.</div>
</li>
</ul>
<a name="toggleSelectedLevelViewability--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleSelectedLevelViewability</h4>
<pre>public&nbsp;void&nbsp;toggleSelectedLevelViewability()</pre>
<div class="block">Toggles the viewability of the selected level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setSelectedLevelOnlyViewable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectedLevelOnlyViewable</h4>
<pre>public&nbsp;void&nbsp;setSelectedLevelOnlyViewable()</pre>
<div class="block">Makes the selected level the only viewable one.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setAllLevelsViewable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllLevelsViewable</h4>
<pre>public&nbsp;void&nbsp;setAllLevelsViewable()</pre>
<div class="block">Makes all levels viewable.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="modifySelectedLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedLevel</h4>
<pre>public&nbsp;void&nbsp;modifySelectedLevel()</pre>
</li>
</ul>
<a name="deleteSelectedLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteSelectedLevel</h4>
<pre>public&nbsp;void&nbsp;deleteSelectedLevel()</pre>
<div class="block">Deletes the selected level and the items that belongs to it.</div>
</li>
</ul>
<a name="createWall-float-float-float-float-com.eteks.sweethome3d.model.Wall-com.eteks.sweethome3d.model.Wall-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createWall</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;createWall(float&nbsp;xStart,
                          float&nbsp;yStart,
                          float&nbsp;xEnd,
                          float&nbsp;yEnd,
                          <a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wallStartAtStart,
                          <a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wallEndAtStart)</pre>
<div class="block">Returns a new wall instance between (<code>xStart</code>,
 <code>yStart</code>) and (<code>xEnd</code>, <code>yEnd</code>)
 end points. The new wall is added to home and its start point is joined
 to the start of <code>wallStartAtStart</code> or
 the end of <code>wallEndAtStart</code>.</div>
</li>
</ul>
<a name="createRoom-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createRoom</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;createRoom(float[][]&nbsp;roomPoints)</pre>
<div class="block">Returns a new room instance with the given points.
 The new room is added to home.</div>
</li>
</ul>
<a name="isRoomPointDeletableAt-com.eteks.sweethome3d.model.Room-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRoomPointDeletableAt</h4>
<pre>public&nbsp;boolean&nbsp;isRoomPointDeletableAt(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
                                      float&nbsp;x,
                                      float&nbsp;y)</pre>
<div class="block">Returns <code>true</code> if the given point can be removed from the <code>room</code>.</div>
</li>
</ul>
<a name="deletePointFromSelectedRoom-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deletePointFromSelectedRoom</h4>
<pre>public&nbsp;void&nbsp;deletePointFromSelectedRoom(float&nbsp;x,
                                        float&nbsp;y)</pre>
<div class="block">Deletes the point of the selected room at the given coordinates and posts an undoable operation.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="isRoomPointsComputableAt-com.eteks.sweethome3d.model.Room-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRoomPointsComputableAt</h4>
<pre>public&nbsp;boolean&nbsp;isRoomPointsComputableAt(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
                                        float&nbsp;x,
                                        float&nbsp;y)</pre>
<div class="block">Returns <code>true</code> if the <code>room</code> can be recomputed at the given coordinates.</div>
</li>
</ul>
<a name="recomputeSelectedRoomPoints-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>recomputeSelectedRoomPoints</h4>
<pre>public&nbsp;void&nbsp;recomputeSelectedRoomPoints(float&nbsp;x,
                                        float&nbsp;y)</pre>
<div class="block">Controls the recomputation of the points of the selected room at the given coordinates.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="addPointToSelectedRoom-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPointToSelectedRoom</h4>
<pre>public&nbsp;void&nbsp;addPointToSelectedRoom(float&nbsp;x,
                                   float&nbsp;y)</pre>
<div class="block">Adds a point to the selected room at the given coordinates and posts an undoable operation.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="createDimensionLine-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDimensionLine</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;createDimensionLine(float&nbsp;xStart,
                                            float&nbsp;yStart,
                                            float&nbsp;xEnd,
                                            float&nbsp;yEnd,
                                            float&nbsp;offset)</pre>
<div class="block">Returns a new dimension instance joining (<code>xStart</code>,
 <code>yStart</code>) and (<code>xEnd</code>, <code>yEnd</code>) points.
 The new dimension line is added to home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="createPolyline-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPolyline</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;createPolyline(float[][]&nbsp;polylinePoints)</pre>
<div class="block">Returns a new polyline instance with the given points.
 The new polyline is added to home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getSelectableItemAt-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectableItemAt</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;getSelectableItemAt(float&nbsp;x,
                                      float&nbsp;y)</pre>
<div class="block">Returns the selectable item at (<code>x</code>, <code>y</code>) point.</div>
</li>
</ul>
<a name="getSelectableItemsAt-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectableItemsAt</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;getSelectableItemsAt(float&nbsp;x,
                                                       float&nbsp;y)</pre>
<div class="block">Returns the selectable items at (<code>x</code>, <code>y</code>) point.</div>
</li>
</ul>
<a name="getSelectableItemsIntersectingRectangle-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectableItemsIntersectingRectangle</h4>
<pre>protected&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;getSelectableItemsIntersectingRectangle(float&nbsp;x0,
                                                                             float&nbsp;y0,
                                                                             float&nbsp;x1,
                                                                             float&nbsp;y1)</pre>
<div class="block">Returns the items that intersects with the rectangle of (<code>x0</code>,
 <code>y0</code>), (<code>x1</code>, <code>y1</code>) opposite corners.</div>
</li>
</ul>
<a name="deleteItems-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteItems</h4>
<pre>public&nbsp;void&nbsp;deleteItems(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Deletes <code>items</code> in plan and record it as an undoable operation.</div>
</li>
</ul>
<a name="moveItems-java.util.List-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveItems</h4>
<pre>public&nbsp;void&nbsp;moveItems(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                      float&nbsp;dx,
                      float&nbsp;dy)</pre>
<div class="block">Moves <code>items</code> of (<code>dx</code>, <code>dy</code>) units.</div>
</li>
</ul>
<a name="selectAndShowItems-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectAndShowItems</h4>
<pre>protected&nbsp;void&nbsp;selectAndShowItems(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Selects <code>items</code> and make them visible at screen.</div>
</li>
</ul>
<a name="selectItems-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectItems</h4>
<pre>protected&nbsp;void&nbsp;selectItems(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Selects <code>items</code>.</div>
</li>
</ul>
<a name="selectItem-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectItem</h4>
<pre>public&nbsp;void&nbsp;selectItem(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Selects the given <code>item</code>.</div>
</li>
</ul>
<a name="toggleItemSelection-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleItemSelection</h4>
<pre>public&nbsp;void&nbsp;toggleItemSelection(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Toggles the selection of the given <code>item</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="addItems-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addItems</h4>
<pre>public&nbsp;void&nbsp;addItems(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Adds <code>items</code> to home and post an undoable operation.</div>
</li>
</ul>
<a name="addFurniture-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurniture</h4>
<pre>public&nbsp;void&nbsp;addFurniture(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</pre>
<div class="block">Adds furniture to home and updates door and window flags if they intersect with walls and magnetism is enabled.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurniture-java.util.List-">addFurniture</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furniture</code> - the furniture to add.</dd>
</dl>
</li>
</ul>
<a name="addWalls-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addWalls</h4>
<pre>public&nbsp;void&nbsp;addWalls(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;walls)</pre>
<div class="block">Adds <code>walls</code> to home and post an undoable new wall operation.</div>
</li>
</ul>
<a name="addRooms-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRooms</h4>
<pre>public&nbsp;void&nbsp;addRooms(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;&nbsp;rooms)</pre>
<div class="block">Add <code>newRooms</code> to home and post an undoable new room line operation.</div>
</li>
</ul>
<a name="addDimensionLines-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addDimensionLines</h4>
<pre>public&nbsp;void&nbsp;addDimensionLines(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;dimensionLines)</pre>
<div class="block">Add <code>dimensionLines</code> to home and post an undoable new dimension line operation.</div>
</li>
</ul>
<a name="addPolylines-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPolylines</h4>
<pre>public&nbsp;void&nbsp;addPolylines(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;&nbsp;polylines)</pre>
<div class="block">Adds <code>polylines</code> to home and posts an undoable new polyline line operation.</div>
</li>
</ul>
<a name="addLabels-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>addLabels</h4>
<pre>public&nbsp;void&nbsp;addLabels(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;&nbsp;labels)</pre>
<div class="block">Add <code>labels</code> to home and post an undoable new label operation.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/PlanController.html" target="_top">Frames</a></li>
<li><a href="PlanController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
