<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomeEnvironment (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeEnvironment (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeEnvironment.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeEnvironment.html" target="_top">Frames</a></li>
<li><a href="HomeEnvironment.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class HomeEnvironment" class="title">Class HomeEnvironment</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomeEnvironment</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeEnvironment</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>
implements java.io.Serializable, java.lang.Cloneable</pre>
<div class="block">The environment attributes of a home.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.HomeEnvironment">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.DrawingMode</a></span></code>
<div class="block">The various modes used to draw home in 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.Property</a></span></code>
<div class="block">The environment properties that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#HomeEnvironment--">HomeEnvironment</a></span>()</code>
<div class="block">Creates default environment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#HomeEnvironment-int-com.eteks.sweethome3d.model.HomeTexture-int-com.eteks.sweethome3d.model.HomeTexture-int-float-">HomeEnvironment</a></span>(int&nbsp;groundColor,
               <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
               int&nbsp;skyColor,
               <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture,
               int&nbsp;lightColor,
               float&nbsp;wallsAlpha)</code>
<div class="block">Creates home environment from parameters.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#HomeEnvironment-int-com.eteks.sweethome3d.model.HomeTexture-int-int-float-">HomeEnvironment</a></span>(int&nbsp;groundColor,
               <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
               int&nbsp;skyColor,
               int&nbsp;lightColor,
               float&nbsp;wallsAlpha)</code>
<div class="block">Creates home environment from parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#HomeEnvironment-java.lang.String-">HomeEnvironment</a></span>(java.lang.String&nbsp;id)</code>
<div class="block">Creates default environment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#HomeEnvironment-java.lang.String-int-com.eteks.sweethome3d.model.HomeTexture-int-com.eteks.sweethome3d.model.HomeTexture-int-float-">HomeEnvironment</a></span>(java.lang.String&nbsp;id,
               int&nbsp;groundColor,
               <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
               int&nbsp;skyColor,
               <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture,
               int&nbsp;lightColor,
               float&nbsp;wallsAlpha)</code>
<div class="block">Creates home environment from parameters.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#addPropertyChangeListener-com.eteks.sweethome3d.model.HomeEnvironment.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this environment.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model">HomeEnvironment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this environment.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getCeillingLightColor--">getCeillingLightColor</a></span>()</code>
<div class="block">Returns the color of ceiling lights.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.DrawingMode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getDrawingMode--">getDrawingMode</a></span>()</code>
<div class="block">Returns the drawing mode of this environment.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getGroundColor--">getGroundColor</a></span>()</code>
<div class="block">Returns the ground color of this environment.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getGroundTexture--">getGroundTexture</a></span>()</code>
<div class="block">Returns the ground texture of this environment.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getLightColor--">getLightColor</a></span>()</code>
<div class="block">Returns the light color of this environment.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getPhotoAspectRatio--">getPhotoAspectRatio</a></span>()</code>
<div class="block">Returns the preferred photo aspect ratio.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getPhotoHeight--">getPhotoHeight</a></span>()</code>
<div class="block">Returns the preferred photo height.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getPhotoQuality--">getPhotoQuality</a></span>()</code>
<div class="block">Returns the preferred photo quality.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getPhotoWidth--">getPhotoWidth</a></span>()</code>
<div class="block">Returns the preferred photo width.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getSkyColor--">getSkyColor</a></span>()</code>
<div class="block">Returns the sky color of this environment.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getSkyTexture--">getSkyTexture</a></span>()</code>
<div class="block">Returns the sky texture of this environment.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getSubpartSizeUnderLight--">getSubpartSizeUnderLight</a></span>()</code>
<div class="block">Returns the size of subparts under home lights in this environment.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getVideoAspectRatio--">getVideoAspectRatio</a></span>()</code>
<div class="block">Returns the preferred video aspect ratio.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getVideoCameraPath--">getVideoCameraPath</a></span>()</code>
<div class="block">Returns the preferred video camera path.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getVideoFrameRate--">getVideoFrameRate</a></span>()</code>
<div class="block">Returns the preferred video frame rate.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getVideoHeight--">getVideoHeight</a></span>()</code>
<div class="block">Returns the preferred video height.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getVideoQuality--">getVideoQuality</a></span>()</code>
<div class="block">Returns preferred video quality.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getVideoSpeed--">getVideoSpeed</a></span>()</code>
<div class="block">Returns the preferred speed of movements in videos in m/s.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getVideoWidth--">getVideoWidth</a></span>()</code>
<div class="block">Returns the preferred video width.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getWallsAlpha--">getWallsAlpha</a></span>()</code>
<div class="block">Returns the walls transparency alpha factor of this environment.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#isAllLevelsVisible--">isAllLevelsVisible</a></span>()</code>
<div class="block">Returns whether all levels should be visible or not.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#isBackgroundImageVisibleOnGround3D--">isBackgroundImageVisibleOnGround3D</a></span>()</code>
<div class="block">Returns <code>true</code> if the background image should be displayed on the ground in 3D.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#isObserverCameraElevationAdjusted--">isObserverCameraElevationAdjusted</a></span>()</code>
<div class="block">Returns <code>true</code> if the observer elevation should be adjusted according
 to the elevation of the selected level.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#removePropertyChangeListener-com.eteks.sweethome3d.model.HomeEnvironment.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this environment.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setAllLevelsVisible-boolean-">setAllLevelsVisible</a></span>(boolean&nbsp;allLevelsVisible)</code>
<div class="block">Sets whether all levels should be visible or not and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setBackgroundImageVisibleOnGround3D-boolean-">setBackgroundImageVisibleOnGround3D</a></span>(boolean&nbsp;backgroundImageVisibleOnGround3D)</code>
<div class="block">Sets whether the background image should be displayed on the ground in 3D and
 fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setCeillingLightColor-int-">setCeillingLightColor</a></span>(int&nbsp;ceilingLightColor)</code>
<div class="block">Sets the color of ceiling lights and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setDrawingMode-com.eteks.sweethome3d.model.HomeEnvironment.DrawingMode-">setDrawingMode</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.DrawingMode</a>&nbsp;drawingMode)</code>
<div class="block">Sets the drawing mode of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setGroundColor-int-">setGroundColor</a></span>(int&nbsp;groundColor)</code>
<div class="block">Sets the ground color of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setGroundTexture-com.eteks.sweethome3d.model.HomeTexture-">setGroundTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture)</code>
<div class="block">Sets the ground texture of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setLightColor-int-">setLightColor</a></span>(int&nbsp;lightColor)</code>
<div class="block">Sets the color that lights this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setObserverCameraElevationAdjusted-boolean-">setObserverCameraElevationAdjusted</a></span>(boolean&nbsp;observerCameraElevationAdjusted)</code>
<div class="block">Sets whether the observer elevation should be adjusted according
 to the elevation of the selected level and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setPhotoAspectRatio-com.eteks.sweethome3d.model.AspectRatio-">setPhotoAspectRatio</a></span>(<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;photoAspectRatio)</code>
<div class="block">Sets the preferred photo aspect ratio, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setPhotoHeight-int-">setPhotoHeight</a></span>(int&nbsp;photoHeight)</code>
<div class="block">Sets the preferred photo height, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setPhotoQuality-int-">setPhotoQuality</a></span>(int&nbsp;photoQuality)</code>
<div class="block">Sets preferred photo quality, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setPhotoWidth-int-">setPhotoWidth</a></span>(int&nbsp;photoWidth)</code>
<div class="block">Sets the preferred photo width, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setSkyColor-int-">setSkyColor</a></span>(int&nbsp;skyColor)</code>
<div class="block">Sets the sky color of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setSkyTexture-com.eteks.sweethome3d.model.HomeTexture-">setSkyTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture)</code>
<div class="block">Sets the sky texture of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setSubpartSizeUnderLight-float-">setSubpartSizeUnderLight</a></span>(float&nbsp;subpartSizeUnderLight)</code>
<div class="block">Sets the size of subparts under home lights of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setVideoAspectRatio-com.eteks.sweethome3d.model.AspectRatio-">setVideoAspectRatio</a></span>(<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;videoAspectRatio)</code>
<div class="block">Sets the preferred video aspect ratio, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setVideoCameraPath-java.util.List-">setVideoCameraPath</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;cameraPath)</code>
<div class="block">Sets the preferred video camera path, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setVideoFrameRate-int-">setVideoFrameRate</a></span>(int&nbsp;videoFrameRate)</code>
<div class="block">Sets the preferred video frame rate, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setVideoQuality-int-">setVideoQuality</a></span>(int&nbsp;videoQuality)</code>
<div class="block">Sets the preferred video quality, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setVideoSpeed-float-">setVideoSpeed</a></span>(float&nbsp;videoSpeed)</code>
<div class="block">Sets the preferred speed of movements in videos in m/s.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setVideoWidth-int-">setVideoWidth</a></span>(int&nbsp;videoWidth)</code>
<div class="block">Sets the preferred video width, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setWallsAlpha-float-">setWallsAlpha</a></span>(float&nbsp;wallsAlpha)</code>
<div class="block">Sets the walls transparency alpha of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeEnvironment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeEnvironment</h4>
<pre>public&nbsp;HomeEnvironment()</pre>
<div class="block">Creates default environment.</div>
</li>
</ul>
<a name="HomeEnvironment-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeEnvironment</h4>
<pre>public&nbsp;HomeEnvironment(java.lang.String&nbsp;id)</pre>
<div class="block">Creates default environment.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="HomeEnvironment-int-com.eteks.sweethome3d.model.HomeTexture-int-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeEnvironment</h4>
<pre>public&nbsp;HomeEnvironment(int&nbsp;groundColor,
                       <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
                       int&nbsp;skyColor,
                       int&nbsp;lightColor,
                       float&nbsp;wallsAlpha)</pre>
<div class="block">Creates home environment from parameters.</div>
</li>
</ul>
<a name="HomeEnvironment-int-com.eteks.sweethome3d.model.HomeTexture-int-com.eteks.sweethome3d.model.HomeTexture-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeEnvironment</h4>
<pre>public&nbsp;HomeEnvironment(int&nbsp;groundColor,
                       <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
                       int&nbsp;skyColor,
                       <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture,
                       int&nbsp;lightColor,
                       float&nbsp;wallsAlpha)</pre>
<div class="block">Creates home environment from parameters.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="HomeEnvironment-java.lang.String-int-com.eteks.sweethome3d.model.HomeTexture-int-com.eteks.sweethome3d.model.HomeTexture-int-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeEnvironment</h4>
<pre>public&nbsp;HomeEnvironment(java.lang.String&nbsp;id,
                       int&nbsp;groundColor,
                       <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
                       int&nbsp;skyColor,
                       <a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture,
                       int&nbsp;lightColor,
                       float&nbsp;wallsAlpha)</pre>
<div class="block">Creates home environment from parameters.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.model.HomeEnvironment.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this environment.
 Properties change will be notified with an event of <code>PropertyChangeEvent</code> class which property name
 will be equal to the value returned by <code>Enum.name()</code> call.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.model.HomeEnvironment.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this environment.</div>
</li>
</ul>
<a name="isObserverCameraElevationAdjusted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isObserverCameraElevationAdjusted</h4>
<pre>public&nbsp;boolean&nbsp;isObserverCameraElevationAdjusted()</pre>
<div class="block">Returns <code>true</code> if the observer elevation should be adjusted according
 to the elevation of the selected level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="setObserverCameraElevationAdjusted-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setObserverCameraElevationAdjusted</h4>
<pre>public&nbsp;void&nbsp;setObserverCameraElevationAdjusted(boolean&nbsp;observerCameraElevationAdjusted)</pre>
<div class="block">Sets whether the observer elevation should be adjusted according
 to the elevation of the selected level and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="getGroundColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroundColor</h4>
<pre>public&nbsp;int&nbsp;getGroundColor()</pre>
<div class="block">Returns the ground color of this environment.</div>
</li>
</ul>
<a name="setGroundColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroundColor</h4>
<pre>public&nbsp;void&nbsp;setGroundColor(int&nbsp;groundColor)</pre>
<div class="block">Sets the ground color of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="getGroundTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroundTexture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;getGroundTexture()</pre>
<div class="block">Returns the ground texture of this environment.</div>
</li>
</ul>
<a name="setGroundTexture-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGroundTexture</h4>
<pre>public&nbsp;void&nbsp;setGroundTexture(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture)</pre>
<div class="block">Sets the ground texture of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="isBackgroundImageVisibleOnGround3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackgroundImageVisibleOnGround3D</h4>
<pre>public&nbsp;boolean&nbsp;isBackgroundImageVisibleOnGround3D()</pre>
<div class="block">Returns <code>true</code> if the background image should be displayed on the ground in 3D.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setBackgroundImageVisibleOnGround3D-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackgroundImageVisibleOnGround3D</h4>
<pre>public&nbsp;void&nbsp;setBackgroundImageVisibleOnGround3D(boolean&nbsp;backgroundImageVisibleOnGround3D)</pre>
<div class="block">Sets whether the background image should be displayed on the ground in 3D and
 fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getSkyColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSkyColor</h4>
<pre>public&nbsp;int&nbsp;getSkyColor()</pre>
<div class="block">Returns the sky color of this environment.</div>
</li>
</ul>
<a name="setSkyColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSkyColor</h4>
<pre>public&nbsp;void&nbsp;setSkyColor(int&nbsp;skyColor)</pre>
<div class="block">Sets the sky color of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="getSkyTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSkyTexture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;getSkyTexture()</pre>
<div class="block">Returns the sky texture of this environment.</div>
</li>
</ul>
<a name="setSkyTexture-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSkyTexture</h4>
<pre>public&nbsp;void&nbsp;setSkyTexture(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture)</pre>
<div class="block">Sets the sky texture of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="getLightColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLightColor</h4>
<pre>public&nbsp;int&nbsp;getLightColor()</pre>
<div class="block">Returns the light color of this environment.</div>
</li>
</ul>
<a name="setLightColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLightColor</h4>
<pre>public&nbsp;void&nbsp;setLightColor(int&nbsp;lightColor)</pre>
<div class="block">Sets the color that lights this environment and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="getCeillingLightColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeillingLightColor</h4>
<pre>public&nbsp;int&nbsp;getCeillingLightColor()</pre>
<div class="block">Returns the color of ceiling lights.</div>
</li>
</ul>
<a name="setCeillingLightColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeillingLightColor</h4>
<pre>public&nbsp;void&nbsp;setCeillingLightColor(int&nbsp;ceilingLightColor)</pre>
<div class="block">Sets the color of ceiling lights and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="getWallsAlpha--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallsAlpha</h4>
<pre>public&nbsp;float&nbsp;getWallsAlpha()</pre>
<div class="block">Returns the walls transparency alpha factor of this environment.</div>
</li>
</ul>
<a name="setWallsAlpha-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallsAlpha</h4>
<pre>public&nbsp;void&nbsp;setWallsAlpha(float&nbsp;wallsAlpha)</pre>
<div class="block">Sets the walls transparency alpha of this environment and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallsAlpha</code> - a value between 0 and 1, 0 meaning opaque and 1 invisible.</dd>
</dl>
</li>
</ul>
<a name="getDrawingMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDrawingMode</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.DrawingMode</a>&nbsp;getDrawingMode()</pre>
<div class="block">Returns the drawing mode of this environment.</div>
</li>
</ul>
<a name="setDrawingMode-com.eteks.sweethome3d.model.HomeEnvironment.DrawingMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDrawingMode</h4>
<pre>public&nbsp;void&nbsp;setDrawingMode(<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model">HomeEnvironment.DrawingMode</a>&nbsp;drawingMode)</pre>
<div class="block">Sets the drawing mode of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="getSubpartSizeUnderLight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubpartSizeUnderLight</h4>
<pre>public&nbsp;float&nbsp;getSubpartSizeUnderLight()</pre>
<div class="block">Returns the size of subparts under home lights in this environment.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a size in centimeters or 0 if home lights don't illuminate home.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.7</dd>
</dl>
</li>
</ul>
<a name="setSubpartSizeUnderLight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubpartSizeUnderLight</h4>
<pre>public&nbsp;void&nbsp;setSubpartSizeUnderLight(float&nbsp;subpartSizeUnderLight)</pre>
<div class="block">Sets the size of subparts under home lights of this environment and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.7</dd>
</dl>
</li>
</ul>
<a name="isAllLevelsVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAllLevelsVisible</h4>
<pre>public&nbsp;boolean&nbsp;isAllLevelsVisible()</pre>
<div class="block">Returns whether all levels should be visible or not.</div>
</li>
</ul>
<a name="setAllLevelsVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllLevelsVisible</h4>
<pre>public&nbsp;void&nbsp;setAllLevelsVisible(boolean&nbsp;allLevelsVisible)</pre>
<div class="block">Sets whether all levels should be visible or not and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="getPhotoWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhotoWidth</h4>
<pre>public&nbsp;int&nbsp;getPhotoWidth()</pre>
<div class="block">Returns the preferred photo width.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setPhotoWidth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhotoWidth</h4>
<pre>public&nbsp;void&nbsp;setPhotoWidth(int&nbsp;photoWidth)</pre>
<div class="block">Sets the preferred photo width, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getPhotoHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhotoHeight</h4>
<pre>public&nbsp;int&nbsp;getPhotoHeight()</pre>
<div class="block">Returns the preferred photo height.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setPhotoHeight-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhotoHeight</h4>
<pre>public&nbsp;void&nbsp;setPhotoHeight(int&nbsp;photoHeight)</pre>
<div class="block">Sets the preferred photo height, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getPhotoAspectRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhotoAspectRatio</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;getPhotoAspectRatio()</pre>
<div class="block">Returns the preferred photo aspect ratio.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setPhotoAspectRatio-com.eteks.sweethome3d.model.AspectRatio-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhotoAspectRatio</h4>
<pre>public&nbsp;void&nbsp;setPhotoAspectRatio(<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;photoAspectRatio)</pre>
<div class="block">Sets the preferred photo aspect ratio, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getPhotoQuality--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhotoQuality</h4>
<pre>public&nbsp;int&nbsp;getPhotoQuality()</pre>
<div class="block">Returns the preferred photo quality.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setPhotoQuality-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhotoQuality</h4>
<pre>public&nbsp;void&nbsp;setPhotoQuality(int&nbsp;photoQuality)</pre>
<div class="block">Sets preferred photo quality, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getVideoWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVideoWidth</h4>
<pre>public&nbsp;int&nbsp;getVideoWidth()</pre>
<div class="block">Returns the preferred video width.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="setVideoWidth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVideoWidth</h4>
<pre>public&nbsp;void&nbsp;setVideoWidth(int&nbsp;videoWidth)</pre>
<div class="block">Sets the preferred video width, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getVideoHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVideoHeight</h4>
<pre>public&nbsp;int&nbsp;getVideoHeight()</pre>
<div class="block">Returns the preferred video height.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getVideoAspectRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVideoAspectRatio</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;getVideoAspectRatio()</pre>
<div class="block">Returns the preferred video aspect ratio.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="setVideoAspectRatio-com.eteks.sweethome3d.model.AspectRatio-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVideoAspectRatio</h4>
<pre>public&nbsp;void&nbsp;setVideoAspectRatio(<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model">AspectRatio</a>&nbsp;videoAspectRatio)</pre>
<div class="block">Sets the preferred video aspect ratio, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getVideoQuality--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVideoQuality</h4>
<pre>public&nbsp;int&nbsp;getVideoQuality()</pre>
<div class="block">Returns preferred video quality.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="setVideoQuality-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVideoQuality</h4>
<pre>public&nbsp;void&nbsp;setVideoQuality(int&nbsp;videoQuality)</pre>
<div class="block">Sets the preferred video quality, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getVideoSpeed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVideoSpeed</h4>
<pre>public&nbsp;float&nbsp;getVideoSpeed()</pre>
<div class="block">Returns the preferred speed of movements in videos in m/s.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setVideoSpeed-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVideoSpeed</h4>
<pre>public&nbsp;void&nbsp;setVideoSpeed(float&nbsp;videoSpeed)</pre>
<div class="block">Sets the preferred speed of movements in videos in m/s.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getVideoFrameRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVideoFrameRate</h4>
<pre>public&nbsp;int&nbsp;getVideoFrameRate()</pre>
<div class="block">Returns the preferred video frame rate.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="setVideoFrameRate-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVideoFrameRate</h4>
<pre>public&nbsp;void&nbsp;setVideoFrameRate(int&nbsp;videoFrameRate)</pre>
<div class="block">Sets the preferred video frame rate, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getVideoCameraPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVideoCameraPath</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;getVideoCameraPath()</pre>
<div class="block">Returns the preferred video camera path.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="setVideoCameraPath-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVideoCameraPath</h4>
<pre>public&nbsp;void&nbsp;setVideoCameraPath(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;cameraPath)</pre>
<div class="block">Sets the preferred video camera path, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model">HomeEnvironment</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this environment.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeEnvironment.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeEnvironment.html" target="_top">Frames</a></li>
<li><a href="HomeEnvironment.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
