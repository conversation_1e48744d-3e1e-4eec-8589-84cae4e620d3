<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>SweetHome3DApplet (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SweetHome3DApplet (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SweetHome3DApplet.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DViewer.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/applet/SweetHome3DApplet.html" target="_top">Frames</a></li>
<li><a href="SweetHome3DApplet.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JApplet">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JApplet">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.applet</div>
<h2 title="Class SweetHome3DApplet" class="title">Class SweetHome3DApplet</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>java.awt.Panel</li>
<li>
<ul class="inheritance">
<li>java.applet.Applet</li>
<li>
<ul class="inheritance">
<li>javax.swing.JApplet</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.applet.SweetHome3DApplet</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.awt.image.ImageObserver, java.awt.MenuContainer, java.io.Serializable, javax.accessibility.Accessible, javax.swing.RootPaneContainer</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">SweetHome3DApplet</span>
extends javax.swing.JApplet</pre>
<div class="block">This applet class loads Sweet Home 3D classes from jars in classpath or from extension
 jars stored as resources.
 <p>This applet accepts the following parameters:

 <ul><li><code>furnitureCatalogURLs</code> specifies the URLs of the furniture libraries available
     in Sweet Home 3D catalog. These URLs are comma or space separated, and if they are not
     absolute URLs, they will be considered as relative to applet codebase. Each URL is a ZIP file
     that must contain a file named <code>PluginFurnitureCatalog.properties</code> describing the
     properties of each piece of furniture proposed by the URL file.
     <br>By default, the value of this parameter is <code>catalog.zip</code>. If this file
     or one of the URLs specified by this parameter doesn't exist, it will be ignored.</li>

     <li><code>furnitureResourcesURLBase</code> specifies the URL used as a base to build the URLs of
     the 3D models and icons cited in the <code>PluginFurnitureCatalog.properties</code> file of a
     furniture catalog. If this URL isn't an absolute URL it will be considered relative to
     applet codebase. If this URL base should the applet code base itself, use a value equal to ".".
     <br>If this parameter isn't defined, the URLs of 3D model and icons will be relative to their
     furniture catalog file or absolute.</li>

     <li><code>texturesCatalogURLs</code> specifies the URLs of the textures libraries available
     in Sweet Home 3D catalog. These URLs are comma or space separated, and if they are not
     absolute URLs, they will be considered as relative to applet codebase. Each URL is a ZIP file
     that must contain a file named <code>PluginTexturesCatalog.properties</code> describing the
     properties of each texture proposed by the URL file.
     <br>By default, the value of this parameter is <code>catalog.zip</code>, meaning that the
     furniture and textures can be stored in the same file. If this file
     or one of the URLs specified by this parameter doesn't exist, it will be ignored.</li>

     <li><code>texturesResourcesURLBase</code> specifies the URL used as a base to build the URLs of
     the texture images cited in the <code>PluginTexturesCatalog.properties</code> file of a
     textures catalog. If this URL isn't an absolute URL it will be considered relative to
     applet codebase. If this URL base should the applet code base itself, use a value equal to ".".
     <br>If this parameter isn't defined, the URLs of texture images will be relative to their
     textures catalog file or absolute.</li>

     <li><code>pluginURLs</code> specifies the URLs of the actions available to users through
     <a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html" title="class in com.eteks.sweethome3d.plugin"><code>plugins</code></a>.These URLs are comma or space separated,
     and if they are not absolute URLs, they will be considered as relative to applet codebase.
     If some classes of a plugin needs to access to resources protected by applet sandbox,
     its JAR file should be signed, added to <code>archive</code> applet attribute and
     and in a <code>jar</code> element of applet JNLP file.
     <br>By default, the value of this parameter is empty. If one of the URLs specified by
     this parameter doesn't exist, it will be ignored.</li>

     <li><code>writeHomeURL</code> specifies the URL of the HTTP service able
     to write the data of a home. This data will be uploaded in the file parameter named
     <code>home</code> of a POST request encoded with multipart/form-data MIME type, with
     the name of the uploaded home being stored in its <code>filename</code> attribute.
     This service must return 1 if it wrote the uploaded data successfully.
     <br>By default, this URL is <code>writeHome.php</code> and if it's not an absolute URL
     it will be considered as relative to applet codebase. If its value is empty,
     <i>New</i>, <i>Save</i> and <i>Save as...</i> actions will be disabled and their buttons
     won't be displayed.</li>

     <li><code>homeMaximumLength</code> specifies the maximum length in bytes of a home that
     the HTTP service used to write its data will accept. If not specified, the length of a saved
     home won't be verified before the writing service is called, leading to a possible error
     when that service refuses to write a file larger that this maximum.</li>

     <li><code>readHomeURL</code> specifies the URL of the HTTP service able
     to return the data of a home written with the write home service. The home name
     is specified by the parameter named <code>home</code> of a GET request.
     <br>By default, this URL is <code>readHome.php?home=%s</code> (the %s sign will be
     replaced by the requested home name). If it's not an absolute URL it will be
     considered as relative to applet codebase.</li>

     <li><code>listHomesURL</code> specifies the URL of the HTTP service able
     to return the list of home names able to be read from server. It must return
     these names in a string, separated from each other by a carriage return (\n).
     <br>By default, this URL is <code>listHomes.php</code> and if it's not an absolute URL
     it will be considered as relative to applet codebase. If its value is empty,
     <i>New</i>, <i>Open</i> and <i>Save as...</i> actions will be disabled and their buttons
     won't be displayed. If <code>defaultHome</code> is empty, <i>Save</i> action
     will be also disabled</li>

     <li><code>deleteHomeURL</code> specifies the URL of the HTTP service able
     to delete the data of a home written with the write home service. The home name
     is specified by the parameter named <code>home</code> of a GET request and the
     the %s sign will be replaced by the home name that should be deleted.
     This service must return 1 if it deleted the given home successfully.
     <br>By default, this service doesn't exist and deletion is not possible from the applet.
     If it's not an absolute URL it will be considered as relative to applet codebase.
     This service is available to the user if <code>listHomesURL</code> is defined.</li>

     <li><code>defaultHome</code> specifies the home that will be opened at applet launch
     with <code>readHomeURL</code> service.
     <br>Omit this parameter or let its value empty, if no home should be opened.
     <br>If you want the applet open a home at launch without creating a <code>readHomeURL</code>
     service, set <code>%s</code> value for <code>readHomeURL</code> parameter and put the absolute
     URL of the home file or its URL relative to applet codebase in <code>defaultHome</code>
     parameter.</li>

     <li><code>writePreferencesURL</code> specifies the URL of the HTTP service able
     to write the XML content describing the user preferences. This data will be uploaded
     in the parameter named <code>preferences</code> of a POST request.
     This service must return 1 if it completed successfully.
     <br>By default, this URL is empty and if it's not an absolute URL
     it will be considered as relative to applet codebase.</li>

     <li><code>readPreferencesURL</code> specifies the URL of the HTTP service able
     to return an XML content describing the user preferences as a set of properties.
     The DTD of the XML content supported by the applet is specified at
     <a href="http://java.sun.com/dtd/properties.dtd">http://java.sun.com/dtd/properties.dtd</a>.
     <br>By default, this URL is empty and if it's not an absolute URL it will be
     considered as relative to applet codebase.</li>

     <li><code>enableExportToSH3D</code> specifies whether this applet should enable
     the action that lets the user export the edited home to a SH3D file.
     <br>By default, the value of this parameter is <code>false</code>.</li>

     <li><code>enableImportFromSH3D</code> specifies whether this applet should enable
     the action that lets the user import a SH3D file to replace the edited home.
     <br>By default, the value of this parameter is <code>false</code>.</li>

     <li><code>enableExportToCSV</code> specifies whether this applet should enable
     the action that lets the user export the furniture list of the edited home to a CSV file.
     <br>By default, the value of this parameter is <code>false</code>.</li>

     <li><code>enableExportToSVG</code> specifies whether this applet should enable
     the action that lets the user export the plan of the edited home to a SVG file.
     <br>By default, the value of this parameter is <code>false</code>.</li>

     <li><code>enableExportToOBJ</code> specifies whether this applet should enable
     the action that lets the user export the 3D view of the edited home to an OBJ file.
     <br>By default, the value of this parameter is <code>false</code>.</li>

     <li><code>enablePrintToPDF</code> specifies whether this applet should enable
     the action that lets the user print the edited home to a PDF file.
     <br>By default, the value of this parameter is <code>false</code>.</li>

     <li><code>enableCreatePhoto</code> specifies whether this applet should enable
     the action that lets the user create a photo from the 3D view of the edited home.
     <br>By default, the value of this parameter is <code>false</code>.</li>

     <li><code>enableCreateVideo</code> specifies whether this applet should enable
     the action that lets the user create a 3D video of the edited home.
     <br>By default, the value of this parameter is <code>false</code>.</li>

     <li><code>showMemoryStatus</code> specifies whether this applet should display
     each second the available memory in browser status bar when it has focus.
     <br>By default, the value of this parameter is <code>false</code> and
     the status message won't be modified by the applet.</li>

     <li><code>userLanguage</code> specifies the ISO 639 code (fr, en...) of the
     language used by the items displayed by this applet.
     <br>By default, the selected language depends on the user environment.</li></ul>

 <p>The bytecode of this class is Java 1.1 compatible to be able to notify users that
 it requires Java 5 when it's run under an old JVM.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.applet.SweetHome3DApplet">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JApplet">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JApplet</h3>
<code>javax.swing.JApplet.AccessibleJApplet</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.applet.Applet">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.applet.Applet</h3>
<code>java.applet.Applet.AccessibleApplet</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Panel">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Panel</h3>
<code>java.awt.Panel.AccessibleAWTPanel</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JApplet">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JApplet</h3>
<code>accessibleContext, rootPane, rootPaneCheckingEnabled</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html#SweetHome3DApplet--">SweetHome3DApplet</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html#destroy--">destroy</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html#getApplication--">getApplication</a></span>()</code>
<div class="block">Returns the application instance created by the applet.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html#getApplicationClassName--">getApplicationClassName</a></span>()</code>
<div class="block">Returns the name of the <a href="../../../../com/eteks/sweethome3d/applet/AppletApplication.html" title="class in com.eteks.sweethome3d.applet">application</a> class associated to this applet.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html#init--">init</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html#isModified--">isModified</a></span>()</code>
<div class="block">Returns <code>true</code> if one of the homes edited by this applet is modified.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JApplet">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JApplet</h3>
<code>addImpl, createRootPane, getAccessibleContext, getContentPane, getGlassPane, getGraphics, getJMenuBar, getLayeredPane, getRootPane, getTransferHandler, isRootPaneCheckingEnabled, paramString, remove, repaint, setContentPane, setGlassPane, setJMenuBar, setLayeredPane, setLayout, setRootPane, setRootPaneCheckingEnabled, setTransferHandler, update</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.applet.Applet">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.applet.Applet</h3>
<code>getAppletContext, getAppletInfo, getAudioClip, getAudioClip, getCodeBase, getDocumentBase, getImage, getImage, getLocale, getParameter, getParameterInfo, isActive, isValidateRoot, newAudioClip, play, play, resize, resize, setStub, showStatus, start, stop</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Panel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Panel</h3>
<code>addNotify</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, doLayout, findComponentAt, findComponentAt, getAlignmentX, getAlignmentY, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getInsets, getLayout, getListeners, getMaximumSize, getMinimumSize, getMousePosition, getPreferredSize, insets, invalidate, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paint, paintComponents, preferredSize, print, printComponents, processContainerEvent, processEvent, remove, removeAll, removeContainerListener, removeNotify, setComponentZOrder, setFocusCycleRoot, setFocusTraversalKeys, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setFont, transferFocusDownCycle, validate, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addFocusListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseListener, addMouseMotionListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, contains, createImage, createImage, createVolatileImage, createVolatileImage, disable, disableEvents, dispatchEvent, enable, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getBackground, getBaseline, getBaselineResizeBehavior, getBounds, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getCursor, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getFontMetrics, getForeground, getGraphicsConfiguration, getHeight, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocation, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getSize, getToolkit, getTreeLock, getWidth, getX, getY, gotFocus, handleEvent, hasFocus, hide, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isDoubleBuffered, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isOpaque, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, printAll, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processKeyEvent, processMouseEvent, processMouseMotionEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseListener, removeMouseMotionListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, reshape, revalidate, setBackground, setBounds, setBounds, setComponentOrientation, setCursor, setDropTarget, setEnabled, setFocusable, setFocusTraversalKeysEnabled, setForeground, setIgnoreRepaint, setLocale, setLocation, setLocation, setMaximumSize, setMinimumSize, setName, setPreferredSize, setSize, setSize, setVisible, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SweetHome3DApplet--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SweetHome3DApplet</h4>
<pre>public&nbsp;SweetHome3DApplet()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;void&nbsp;init()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>init</code>&nbsp;in class&nbsp;<code>java.applet.Applet</code></dd>
</dl>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>destroy</h4>
<pre>public&nbsp;void&nbsp;destroy()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>destroy</code>&nbsp;in class&nbsp;<code>java.applet.Applet</code></dd>
</dl>
</li>
</ul>
<a name="isModified--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isModified</h4>
<pre>public&nbsp;boolean&nbsp;isModified()</pre>
<div class="block">Returns <code>true</code> if one of the homes edited by this applet is modified.</div>
</li>
</ul>
<a name="getApplicationClassName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getApplicationClassName</h4>
<pre>protected&nbsp;java.lang.String&nbsp;getApplicationClassName()</pre>
<div class="block">Returns the name of the <a href="../../../../com/eteks/sweethome3d/applet/AppletApplication.html" title="class in com.eteks.sweethome3d.applet">application</a> class associated to this applet.
 This class must have a constructor taking in parameter a <code>JApplet</code>.</div>
</li>
</ul>
<a name="getApplication--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getApplication</h4>
<pre>protected&nbsp;java.lang.Object&nbsp;getApplication()</pre>
<div class="block">Returns the application instance created by the applet.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SweetHome3DApplet.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DViewer.html" title="class in com.eteks.sweethome3d.applet"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/applet/SweetHome3DApplet.html" target="_top">Frames</a></li>
<li><a href="SweetHome3DApplet.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JApplet">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JApplet">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
