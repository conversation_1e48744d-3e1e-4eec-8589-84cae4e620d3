<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>PluginAction.Property (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PluginAction.Property (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PluginAction.Property.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/plugin/PluginAction.Property.html" target="_top">Frames</a></li>
<li><a href="PluginAction.Property.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.plugin</div>
<h2 title="Enum PluginAction.Property" class="title">Enum PluginAction.Property</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.plugin.PluginAction.Property</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>&gt;</dd>
</dl>
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html" title="class in com.eteks.sweethome3d.plugin">PluginAction</a></dd>
</dl>
<hr>
<br>
<pre>public static enum <span class="typeNameLabel">PluginAction.Property</span>
extends java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>&gt;</pre>
<div class="block">Enumeration of the various properties this action may define.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#ENABLED">ENABLED</a></span></code>
<div class="block">The key of the property of <code>Boolean</code> type that specifies
 if an action is enabled or not.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#MENU">MENU</a></span></code>
<div class="block">The key of the property of <code>String</code> type that specifies
 in which menu of the main menu bar an action should appear.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#MNEMONIC">MNEMONIC</a></span></code>
<div class="block">The key of the property of <code>Character</code> type that specifies 
 the ASCII character used as the mnemonic of an action.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#NAME">NAME</a></span></code>
<div class="block">The key of the property of <code>String</code> type that specifies
 the name of an action, used for a menu or button.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#SHORT_DESCRIPTION">SHORT_DESCRIPTION</a></span></code>
<div class="block">The key of the property of <code>String</code> type that specifies
 a short description of an action, used for tool tip text.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#SMALL_ICON">SMALL_ICON</a></span></code>
<div class="block">The key of the property of <code>Content</code> type that specifies
 an image content of an action, used for tool bar buttons.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#TOOL_BAR">TOOL_BAR</a></span></code>
<div class="block">The key of the property of <code>Boolean</code> type that specifies
 if an action will appear in the main tool bar.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a> NAME</pre>
<div class="block">The key of the property of <code>String</code> type that specifies
 the name of an action, used for a menu or button.</div>
</li>
</ul>
<a name="SHORT_DESCRIPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHORT_DESCRIPTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a> SHORT_DESCRIPTION</pre>
<div class="block">The key of the property of <code>String</code> type that specifies
 a short description of an action, used for tool tip text.</div>
</li>
</ul>
<a name="SMALL_ICON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SMALL_ICON</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a> SMALL_ICON</pre>
<div class="block">The key of the property of <code>Content</code> type that specifies
 an image content of an action, used for tool bar buttons.</div>
</li>
</ul>
<a name="MNEMONIC">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MNEMONIC</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a> MNEMONIC</pre>
<div class="block">The key of the property of <code>Character</code> type that specifies 
 the ASCII character used as the mnemonic of an action.</div>
</li>
</ul>
<a name="TOOL_BAR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TOOL_BAR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a> TOOL_BAR</pre>
<div class="block">The key of the property of <code>Boolean</code> type that specifies
 if an action will appear in the main tool bar.</div>
</li>
</ul>
<a name="MENU">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MENU</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a> MENU</pre>
<div class="block">The key of the property of <code>String</code> type that specifies
 in which menu of the main menu bar an action should appear.</div>
</li>
</ul>
<a name="ENABLED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ENABLED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a> ENABLED</pre>
<div class="block">The key of the property of <code>Boolean</code> type that specifies
 if an action is enabled or not.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (PluginAction.Property c : PluginAction.Property.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PluginAction.Property.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/plugin/PluginAction.Property.html" target="_top">Frames</a></li>
<li><a href="PluginAction.Property.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
