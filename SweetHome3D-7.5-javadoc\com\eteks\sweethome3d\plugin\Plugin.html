<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>Plugin (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Plugin (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":6,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Plugin.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/plugin/Plugin.html" target="_top">Frames</a></li>
<li><a href="Plugin.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.plugin</div>
<h2 title="Class Plugin" class="title">Class Plugin</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.plugin.Plugin</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">Plugin</span>
extends java.lang.Object</pre>
<div class="block">The super class of a plug-in.
 Subclasses should implement <a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getActions--"><code>getActions</code></a> method
 to return the actions that will be available to user.
 This class should be packed in a JAR file with a family of properties file named
 <code>ApplicationPlugin.properties</code> at its root or in one of its subfolders.
 This file describes a plug-in with the following keys (all of them are mandatory
 except <code>id</code>):
 <ul><li>The <code>id</code> key gives an optional id to the plug-in,
     which can be useful to determine if an update for this plug-in is available
     during libraries updates checking.</li>
     <li>The <code>name</code> key specifies the name of the plug-in.</li>
     <li>The <code>class</code> key specifies the fully qualified class name
     of the plug-in.</li>
     <li>The <code>description</code> key specifies the description of
     the plug-in.</li>
     <li>The <code>version</code> key specifies the version of the plug-in.
     <li>The <code>license</code> key specifies the license under which
     the plug-in is distributed.</li>
     <li>The <code>provider</code> key specifies the provider, the developer
     and/or the editor of the plug-in.</li>
     <li>The <code>applicationMinimumVersion</code> key specifies the
     minimum application version under which this plug-in may work. Note that
     only the first two groups of digits will be used for the comparison
     with current JVM version, and that plug-ins were available from
     version 1.5.</li>
     <li>The <code>javaMinimumVersion</code> key specifies the
     minimum Java version under which this plug-in may work. Note that
     only the first two groups of digits will be used for the comparison
     with current JVM version.</li></ul>
 <br>For example, a plug-in class named <code>com.mycompany.mypackage.MyPlugin</code>
 will become a plug-in if it's packed in a JAR file with the following
 <code>ApplicationPlugin.properties</code> file:
 <pre> name=My plug-in
 class=com.mycompany.mypackage.MyPlugin
 description=This plug-in rocks!
 version=1.0
 license=GNU GPL
 provider=MyCompany
 applicationMinimumVersion=1.5
 javaMinimumVersion=1.5</pre></div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#Plugin--">Plugin</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#destroy--">destroy</a></span>()</code>
<div class="block">This method will be called when the home referenced by this plug-in will be deleted.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html" title="class in com.eteks.sweethome3d.plugin">PluginAction</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getActions--">getActions</a></span>()</code>
<div class="block">Returns the actions available on this plug-in.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Returns the description of this plug-in.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getHome--">getHome</a></span>()</code>
<div class="block">Returns the home associated to this plug-in instance.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getHomeController--">getHomeController</a></span>()</code>
<div class="block">Returns the controller of the home of this plug-in.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getLicense--">getLicense</a></span>()</code>
<div class="block">Returns the license of this plug-in.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this plug-in.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.ClassLoader</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getPluginClassLoader--">getPluginClassLoader</a></span>()</code>
<div class="block">Returns the class loader used to load this plug-in.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getProvider--">getProvider</a></span>()</code>
<div class="block">Returns the provider of this plug-in.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>javax.swing.undo.UndoableEditSupport</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getUndoableEditSupport--">getUndoableEditSupport</a></span>()</code>
<div class="block">Returns the undoable edit support that records undoable modifications made on a home.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getUserPreferences--">getUserPreferences</a></span>()</code>
<div class="block">Returns the user preferences of the current application.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#getVersion--">getVersion</a></span>()</code>
<div class="block">Returns the version of this plug-in.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html#init--">init</a></span>()</code>
<div class="block">This method will be called once the properties of this plug-in are initialized.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Plugin--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Plugin</h4>
<pre>public&nbsp;Plugin()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPluginClassLoader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPluginClassLoader</h4>
<pre>public final&nbsp;java.lang.ClassLoader&nbsp;getPluginClassLoader()</pre>
<div class="block">Returns the class loader used to load this plug-in.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this plug-in.</div>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getDescription()</pre>
<div class="block">Returns the description of this plug-in.</div>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">Returns the version of this plug-in.</div>
</li>
</ul>
<a name="getLicense--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLicense</h4>
<pre>public final&nbsp;java.lang.String&nbsp;getLicense()</pre>
<div class="block">Returns the license of this plug-in.</div>
</li>
</ul>
<a name="getProvider--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProvider</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProvider()</pre>
<div class="block">Returns the provider of this plug-in.</div>
</li>
</ul>
<a name="getUserPreferences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserPreferences</h4>
<pre>public final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;getUserPreferences()</pre>
<div class="block">Returns the user preferences of the current application.</div>
</li>
</ul>
<a name="getHome--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHome</h4>
<pre>public final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;getHome()</pre>
<div class="block">Returns the home associated to this plug-in instance.</div>
</li>
</ul>
<a name="getHomeController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;getHomeController()</pre>
<div class="block">Returns the controller of the home of this plug-in.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.5</dd>
</dl>
</li>
</ul>
<a name="getUndoableEditSupport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUndoableEditSupport</h4>
<pre>public final&nbsp;javax.swing.undo.UndoableEditSupport&nbsp;getUndoableEditSupport()</pre>
<div class="block">Returns the undoable edit support that records undoable modifications made on a home.</div>
</li>
</ul>
<a name="init--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>init</h4>
<pre>public&nbsp;void&nbsp;init()</pre>
<div class="block">This method will be called once the properties of this plug-in are initialized.
 Subclasses may override it to initialize data associated to this plug-in.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="destroy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>destroy</h4>
<pre>public&nbsp;void&nbsp;destroy()</pre>
<div class="block">This method will be called when the home referenced by this plug-in will be deleted.
 Subclasses may override it to free resources associated to this plug-in.</div>
</li>
</ul>
<a name="getActions--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getActions</h4>
<pre>public abstract&nbsp;<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html" title="class in com.eteks.sweethome3d.plugin">PluginAction</a>[]&nbsp;getActions()</pre>
<div class="block">Returns the actions available on this plug-in.
 These actions may define the properties defined by
 <a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin"><code>PluginAction.Property</code></a> enumeration.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Plugin.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/plugin/Plugin.html" target="_top">Frames</a></li>
<li><a href="Plugin.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
