<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>PlanView (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PlanView (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanView.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/PlanView.html" target="_top">Frames</a></li>
<li><a href="PlanView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Interface PlanView" class="title">Interface PlanView</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html" title="class in com.eteks.sweethome3d.swing">MultipleLevelsPlanPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html" title="class in com.eteks.sweethome3d.swing">PlanComponent</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">PlanView</span>
extends <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></pre>
<div class="block">The view that displays the plan of a home.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Interface and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a></span></code>
<div class="block">The cursor types available in plan view.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.TransferableView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView.TransferObserver</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.ExportableView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#canImportDraggedItems-java.util.List-int-int-">canImportDraggedItems</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                     int&nbsp;x,
                     int&nbsp;y)</code>
<div class="block">Returns <code>true</code> if this plan accepts to import dragged items at the given coordinates.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertXModelToScreen-float-">convertXModelToScreen</a></span>(float&nbsp;x)</code>
<div class="block">Returns <code>x</code> converted in screen coordinates space.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertXPixelToModel-int-">convertXPixelToModel</a></span>(int&nbsp;x)</code>
<div class="block">Returns <code>x</code> converted in model coordinates space.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertYModelToScreen-float-">convertYModelToScreen</a></span>(float&nbsp;y)</code>
<div class="block">Returns <code>y</code> converted in screen coordinates space.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertYPixelToModel-int-">convertYPixelToModel</a></span>(int&nbsp;y)</code>
<div class="block">Returns <code>y</code> converted in model coordinates space.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#deleteFeedback--">deleteFeedback</a></span>()</code>
<div class="block">Deletes all elements shown as feedback.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#deleteToolTipFeedback--">deleteToolTipFeedback</a></span>()</code>
<div class="block">Deletes tool tip text from screen.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getHorizontalRuler--">getHorizontalRuler</a></span>()</code>
<div class="block">Returns the component used as an horizontal ruler for this plan.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getPieceOfFurnitureSizeInPlan</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPixelLength--">getPixelLength</a></span>()</code>
<div class="block">Returns the length in centimeters of a pixel with the current scale.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPrintPreferredScale-float-float-">getPrintPreferredScale</a></span>(float&nbsp;preferredWidth,
                      float&nbsp;preferredHeight)</code>
<div class="block">Returns the preferred scale to ensure it can be fully printed on the given print zone.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getScale--">getScale</a></span>()</code>
<div class="block">Returns the scale used to display the plan.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">getTextBounds</a></span>(java.lang.String&nbsp;text,
             <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
             float&nbsp;x,
             float&nbsp;y,
             float&nbsp;angle)</code>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> displayed at
 the point (<code>x</code>,<code>y</code>).</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getVerticalRuler--">getVerticalRuler</a></span>()</code>
<div class="block">Returns the component used as a vertical ruler for this plan.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#isFurnitureSizeInPlanSupported--">isFurnitureSizeInPlanSupported</a></span>()</code>
<div class="block">Returns <code>true</code> if the view is able to compute the size of horizontally rotated furniture.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#makePointVisible-float-float-">makePointVisible</a></span>(float&nbsp;x,
                float&nbsp;y)</code>
<div class="block">Ensures the point at (<code>x</code>, <code>y</code>) is visible,
 moving scroll bars if needed.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#makeSelectionVisible--">makeSelectionVisible</a></span>()</code>
<div class="block">Ensures selected items are visible at screen and moves
 scroll bars if needed.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#moveView-float-float-">moveView</a></span>(float&nbsp;dx,
        float&nbsp;dy)</code>
<div class="block">Moves the view from (dx, dy) unit in the scrolling zone it belongs to.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPoint)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setAngleFeedback-float-float-float-float-float-float-">setAngleFeedback</a></span>(float&nbsp;xCenter,
                float&nbsp;yCenter,
                float&nbsp;x1,
                float&nbsp;y1,
                float&nbsp;x2,
                float&nbsp;y2)</code>
<div class="block">Sets the points used to draw an angle in plan view.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setCursor-com.eteks.sweethome3d.viewcontroller.PlanView.CursorType-">setCursor</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a>&nbsp;cursorType)</code>
<div class="block">Sets the cursor of this component as rotation cursor.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setDimensionLinesFeedback-java.util.List-">setDimensionLinesFeedback</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;dimensionLines)</code>
<div class="block">Sets the given dimension lines to be drawn as feedback.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setDraggedItemsFeedback-java.util.List-">setDraggedItemsFeedback</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</code>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of plan view.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setRectangleFeedback-float-float-float-float-">setRectangleFeedback</a></span>(float&nbsp;x0,
                    float&nbsp;y0,
                    float&nbsp;x1,
                    float&nbsp;y1)</code>
<div class="block">Sets rectangle selection feedback coordinates.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setResizeIndicatorVisible-boolean-">setResizeIndicatorVisible</a></span>(boolean&nbsp;resizeIndicatorVisible)</code>
<div class="block">Sets whether the resize indicator of selected wall or piece of furniture
 should be visible or not.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setScale-float-">setScale</a></span>(float&nbsp;scale)</code>
<div class="block">Sets the scale used to display the plan.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipEditedProperties-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty:A-java.lang.Object:A-float-float-">setToolTipEditedProperties</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>[]&nbsp;toolTipEditedProperties,
                          java.lang.Object[]&nbsp;toolTipPropertyValues,
                          float&nbsp;x,
                          float&nbsp;y)</code>
<div class="block">Sets properties edited in tool tip.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipEditedPropertyValue-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">setToolTipEditedPropertyValue</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;toolTipEditedProperty,
                             java.lang.Object&nbsp;toolTipPropertyValue)</code>
<div class="block">Sets the value of a property edited in tool tip.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipFeedback-java.lang.String-float-float-">setToolTipFeedback</a></span>(java.lang.String&nbsp;toolTipFeedback,
                  float&nbsp;x,
                  float&nbsp;y)</code>
<div class="block">Sets tool tip text displayed as feedback.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.TransferableView">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">createTransferData</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.ExportableView">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html#exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">exportData</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html#isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">isFormatTypeSupported</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setRectangleFeedback-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRectangleFeedback</h4>
<pre>void&nbsp;setRectangleFeedback(float&nbsp;x0,
                          float&nbsp;y0,
                          float&nbsp;x1,
                          float&nbsp;y1)</pre>
<div class="block">Sets rectangle selection feedback coordinates.</div>
</li>
</ul>
<a name="makeSelectionVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makeSelectionVisible</h4>
<pre>void&nbsp;makeSelectionVisible()</pre>
<div class="block">Ensures selected items are visible at screen and moves
 scroll bars if needed.</div>
</li>
</ul>
<a name="makePointVisible-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makePointVisible</h4>
<pre>void&nbsp;makePointVisible(float&nbsp;x,
                      float&nbsp;y)</pre>
<div class="block">Ensures the point at (<code>x</code>, <code>y</code>) is visible,
 moving scroll bars if needed.</div>
</li>
</ul>
<a name="getScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScale</h4>
<pre>float&nbsp;getScale()</pre>
<div class="block">Returns the scale used to display the plan.</div>
</li>
</ul>
<a name="setScale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScale</h4>
<pre>void&nbsp;setScale(float&nbsp;scale)</pre>
<div class="block">Sets the scale used to display the plan.</div>
</li>
</ul>
<a name="getPrintPreferredScale-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintPreferredScale</h4>
<pre>float&nbsp;getPrintPreferredScale(float&nbsp;preferredWidth,
                             float&nbsp;preferredHeight)</pre>
<div class="block">Returns the preferred scale to ensure it can be fully printed on the given print zone.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferredWidth</code> - width of the zone in cm</dd>
<dd><code>preferredHeight</code> - height of the zone in cm</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="moveView-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveView</h4>
<pre>void&nbsp;moveView(float&nbsp;dx,
              float&nbsp;dy)</pre>
<div class="block">Moves the view from (dx, dy) unit in the scrolling zone it belongs to.</div>
</li>
</ul>
<a name="convertXPixelToModel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertXPixelToModel</h4>
<pre>float&nbsp;convertXPixelToModel(int&nbsp;x)</pre>
<div class="block">Returns <code>x</code> converted in model coordinates space.</div>
</li>
</ul>
<a name="convertYPixelToModel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertYPixelToModel</h4>
<pre>float&nbsp;convertYPixelToModel(int&nbsp;y)</pre>
<div class="block">Returns <code>y</code> converted in model coordinates space.</div>
</li>
</ul>
<a name="convertXModelToScreen-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertXModelToScreen</h4>
<pre>int&nbsp;convertXModelToScreen(float&nbsp;x)</pre>
<div class="block">Returns <code>x</code> converted in screen coordinates space.</div>
</li>
</ul>
<a name="convertYModelToScreen-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertYModelToScreen</h4>
<pre>int&nbsp;convertYModelToScreen(float&nbsp;y)</pre>
<div class="block">Returns <code>y</code> converted in screen coordinates space.</div>
</li>
</ul>
<a name="getPixelLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPixelLength</h4>
<pre>float&nbsp;getPixelLength()</pre>
<div class="block">Returns the length in centimeters of a pixel with the current scale.</div>
</li>
</ul>
<a name="getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextBounds</h4>
<pre>float[][]&nbsp;getTextBounds(java.lang.String&nbsp;text,
                        <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
                        float&nbsp;x,
                        float&nbsp;y,
                        float&nbsp;angle)</pre>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> displayed at
 the point (<code>x</code>,<code>y</code>).</div>
</li>
</ul>
<a name="setCursor-com.eteks.sweethome3d.viewcontroller.PlanView.CursorType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCursor</h4>
<pre>void&nbsp;setCursor(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a>&nbsp;cursorType)</pre>
<div class="block">Sets the cursor of this component as rotation cursor.</div>
</li>
</ul>
<a name="setToolTipFeedback-java.lang.String-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipFeedback</h4>
<pre>void&nbsp;setToolTipFeedback(java.lang.String&nbsp;toolTipFeedback,
                        float&nbsp;x,
                        float&nbsp;y)</pre>
<div class="block">Sets tool tip text displayed as feedback.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>toolTipFeedback</code> - the text displayed in the tool tip
                    or <code>null</code> to make tool tip disappear.</dd>
</dl>
</li>
</ul>
<a name="setToolTipEditedProperties-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty:A-java.lang.Object:A-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipEditedProperties</h4>
<pre>void&nbsp;setToolTipEditedProperties(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>[]&nbsp;toolTipEditedProperties,
                                java.lang.Object[]&nbsp;toolTipPropertyValues,
                                float&nbsp;x,
                                float&nbsp;y)</pre>
<div class="block">Sets properties edited in tool tip.</div>
</li>
</ul>
<a name="setToolTipEditedPropertyValue-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipEditedPropertyValue</h4>
<pre>void&nbsp;setToolTipEditedPropertyValue(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;toolTipEditedProperty,
                                   java.lang.Object&nbsp;toolTipPropertyValue)</pre>
<div class="block">Sets the value of a property edited in tool tip.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="deleteToolTipFeedback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteToolTipFeedback</h4>
<pre>void&nbsp;deleteToolTipFeedback()</pre>
<div class="block">Deletes tool tip text from screen.</div>
</li>
</ul>
<a name="setResizeIndicatorVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResizeIndicatorVisible</h4>
<pre>void&nbsp;setResizeIndicatorVisible(boolean&nbsp;resizeIndicatorVisible)</pre>
<div class="block">Sets whether the resize indicator of selected wall or piece of furniture
 should be visible or not.</div>
</li>
</ul>
<a name="setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignmentFeedback</h4>
<pre>void&nbsp;setAlignmentFeedback(java.lang.Class&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                          <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                          float&nbsp;x,
                          float&nbsp;y,
                          boolean&nbsp;showPoint)</pre>
<div class="block">Sets the location point for alignment feedback.</div>
</li>
</ul>
<a name="setAngleFeedback-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngleFeedback</h4>
<pre>void&nbsp;setAngleFeedback(float&nbsp;xCenter,
                      float&nbsp;yCenter,
                      float&nbsp;x1,
                      float&nbsp;y1,
                      float&nbsp;x2,
                      float&nbsp;y2)</pre>
<div class="block">Sets the points used to draw an angle in plan view.</div>
</li>
</ul>
<a name="setDraggedItemsFeedback-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDraggedItemsFeedback</h4>
<pre>void&nbsp;setDraggedItemsFeedback(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</pre>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of plan view.</div>
</li>
</ul>
<a name="setDimensionLinesFeedback-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDimensionLinesFeedback</h4>
<pre>void&nbsp;setDimensionLinesFeedback(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;dimensionLines)</pre>
<div class="block">Sets the given dimension lines to be drawn as feedback.</div>
</li>
</ul>
<a name="deleteFeedback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteFeedback</h4>
<pre>void&nbsp;deleteFeedback()</pre>
<div class="block">Deletes all elements shown as feedback.</div>
</li>
</ul>
<a name="getHorizontalRuler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHorizontalRuler</h4>
<pre><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getHorizontalRuler()</pre>
<div class="block">Returns the component used as an horizontal ruler for this plan.</div>
</li>
</ul>
<a name="getVerticalRuler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVerticalRuler</h4>
<pre><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getVerticalRuler()</pre>
<div class="block">Returns the component used as a vertical ruler for this plan.</div>
</li>
</ul>
<a name="canImportDraggedItems-java.util.List-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>canImportDraggedItems</h4>
<pre>boolean&nbsp;canImportDraggedItems(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                              int&nbsp;x,
                              int&nbsp;y)</pre>
<div class="block">Returns <code>true</code> if this plan accepts to import dragged items at the given coordinates.</div>
</li>
</ul>
<a name="getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureSizeInPlan</h4>
<pre>float[]&nbsp;getPieceOfFurnitureSizeInPlan(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan.</div>
</li>
</ul>
<a name="isFurnitureSizeInPlanSupported--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isFurnitureSizeInPlanSupported</h4>
<pre>boolean&nbsp;isFurnitureSizeInPlanSupported()</pre>
<div class="block">Returns <code>true</code> if the view is able to compute the size of horizontally rotated furniture.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanView.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/PlanView.html" target="_top">Frames</a></li>
<li><a href="PlanView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
