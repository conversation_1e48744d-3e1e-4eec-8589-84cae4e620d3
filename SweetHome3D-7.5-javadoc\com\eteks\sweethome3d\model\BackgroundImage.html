<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>BackgroundImage (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BackgroundImage (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BackgroundImage.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/BackgroundImage.html" target="_top">Frames</a></li>
<li><a href="BackgroundImage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class BackgroundImage" class="title">Class BackgroundImage</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.BackgroundImage</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BackgroundImage</span>
extends java.lang.Object
implements java.io.Serializable</pre>
<div class="block">The image displayed in background of the plan.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.BackgroundImage">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#BackgroundImage-com.eteks.sweethome3d.model.Content-float-float-float-float-float-float-float-">BackgroundImage</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
               float&nbsp;scaleDistance,
               float&nbsp;scaleDistanceXStart,
               float&nbsp;scaleDistanceYStart,
               float&nbsp;scaleDistanceXEnd,
               float&nbsp;scaleDistanceYEnd,
               float&nbsp;xOrigin,
               float&nbsp;yOrigin)</code>
<div class="block">Creates a visible background image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#BackgroundImage-com.eteks.sweethome3d.model.Content-float-float-float-float-float-float-float-boolean-">BackgroundImage</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
               float&nbsp;scaleDistance,
               float&nbsp;scaleDistanceXStart,
               float&nbsp;scaleDistanceYStart,
               float&nbsp;scaleDistanceXEnd,
               float&nbsp;scaleDistanceYEnd,
               float&nbsp;xOrigin,
               float&nbsp;yOrigin,
               boolean&nbsp;visible)</code>
<div class="block">Creates a background image.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the image content of this background image.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getScale--">getScale</a></span>()</code>
<div class="block">Returns the scale of this image.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getScale-float-float-float-float-float-">getScale</a></span>(float&nbsp;scaleDistance,
        float&nbsp;scaleDistanceXStart,
        float&nbsp;scaleDistanceYStart,
        float&nbsp;scaleDistanceXEnd,
        float&nbsp;scaleDistanceYEnd)</code>
<div class="block">Returns the scale equal to <code>scaleDistance</code> divided
 by the distance between the points 
 (<code>scaleDistanceXStart</code>, <code>scaleDistanceYStart</code>)
 and (<code>scaleDistanceXEnd</code>, <code>scaleDistanceYEnd</code>).</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getScaleDistance--">getScaleDistance</a></span>()</code>
<div class="block">Returns the distance used to compute the scale of this image.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getScaleDistanceXEnd--">getScaleDistanceXEnd</a></span>()</code>
<div class="block">Returns the abscissa of the end point used to compute 
 the scale of this image.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getScaleDistanceXStart--">getScaleDistanceXStart</a></span>()</code>
<div class="block">Returns the abscissa of the start point used to compute 
 the scale of this image.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getScaleDistanceYEnd--">getScaleDistanceYEnd</a></span>()</code>
<div class="block">Returns the ordinate of the end point used to compute 
 the scale of this image.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getScaleDistanceYStart--">getScaleDistanceYStart</a></span>()</code>
<div class="block">Returns the ordinate of the start point used to compute 
 the scale of this image.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getXOrigin--">getXOrigin</a></span>()</code>
<div class="block">Returns the origin abscissa of this image.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getYOrigin--">getYOrigin</a></span>()</code>
<div class="block">Returns the origin ordinate of this image.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html#isVisible--">isVisible</a></span>()</code>
<div class="block">Returns <code>true</code> if this image is visible in plan.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BackgroundImage-com.eteks.sweethome3d.model.Content-float-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BackgroundImage</h4>
<pre>public&nbsp;BackgroundImage(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
                       float&nbsp;scaleDistance,
                       float&nbsp;scaleDistanceXStart,
                       float&nbsp;scaleDistanceYStart,
                       float&nbsp;scaleDistanceXEnd,
                       float&nbsp;scaleDistanceYEnd,
                       float&nbsp;xOrigin,
                       float&nbsp;yOrigin)</pre>
<div class="block">Creates a visible background image.</div>
</li>
</ul>
<a name="BackgroundImage-com.eteks.sweethome3d.model.Content-float-float-float-float-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BackgroundImage</h4>
<pre>public&nbsp;BackgroundImage(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
                       float&nbsp;scaleDistance,
                       float&nbsp;scaleDistanceXStart,
                       float&nbsp;scaleDistanceYStart,
                       float&nbsp;scaleDistanceXEnd,
                       float&nbsp;scaleDistanceYEnd,
                       float&nbsp;xOrigin,
                       float&nbsp;yOrigin,
                       boolean&nbsp;visible)</pre>
<div class="block">Creates a background image.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.8</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getImage()</pre>
<div class="block">Returns the image content of this background image.</div>
</li>
</ul>
<a name="getScaleDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleDistance</h4>
<pre>public&nbsp;float&nbsp;getScaleDistance()</pre>
<div class="block">Returns the distance used to compute the scale of this image.</div>
</li>
</ul>
<a name="getScaleDistanceXStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleDistanceXStart</h4>
<pre>public&nbsp;float&nbsp;getScaleDistanceXStart()</pre>
<div class="block">Returns the abscissa of the start point used to compute 
 the scale of this image.</div>
</li>
</ul>
<a name="getScaleDistanceYStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleDistanceYStart</h4>
<pre>public&nbsp;float&nbsp;getScaleDistanceYStart()</pre>
<div class="block">Returns the ordinate of the start point used to compute 
 the scale of this image.</div>
</li>
</ul>
<a name="getScaleDistanceXEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleDistanceXEnd</h4>
<pre>public&nbsp;float&nbsp;getScaleDistanceXEnd()</pre>
<div class="block">Returns the abscissa of the end point used to compute 
 the scale of this image.</div>
</li>
</ul>
<a name="getScaleDistanceYEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleDistanceYEnd</h4>
<pre>public&nbsp;float&nbsp;getScaleDistanceYEnd()</pre>
<div class="block">Returns the ordinate of the end point used to compute 
 the scale of this image.</div>
</li>
</ul>
<a name="getScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScale</h4>
<pre>public&nbsp;float&nbsp;getScale()</pre>
<div class="block">Returns the scale of this image.</div>
</li>
</ul>
<a name="getScale-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScale</h4>
<pre>public static&nbsp;float&nbsp;getScale(float&nbsp;scaleDistance,
                             float&nbsp;scaleDistanceXStart,
                             float&nbsp;scaleDistanceYStart,
                             float&nbsp;scaleDistanceXEnd,
                             float&nbsp;scaleDistanceYEnd)</pre>
<div class="block">Returns the scale equal to <code>scaleDistance</code> divided
 by the distance between the points 
 (<code>scaleDistanceXStart</code>, <code>scaleDistanceYStart</code>)
 and (<code>scaleDistanceXEnd</code>, <code>scaleDistanceYEnd</code>).</div>
</li>
</ul>
<a name="getXOrigin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXOrigin</h4>
<pre>public&nbsp;float&nbsp;getXOrigin()</pre>
<div class="block">Returns the origin abscissa of this image.</div>
</li>
</ul>
<a name="getYOrigin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYOrigin</h4>
<pre>public&nbsp;float&nbsp;getYOrigin()</pre>
<div class="block">Returns the origin ordinate of this image.</div>
</li>
</ul>
<a name="isVisible--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isVisible</h4>
<pre>public&nbsp;boolean&nbsp;isVisible()</pre>
<div class="block">Returns <code>true</code> if this image is visible in plan.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.8</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BackgroundImage.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/BackgroundImage.html" target="_top">Frames</a></li>
<li><a href="BackgroundImage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
