<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:47 CEST 2024 -->
<title>FurnitureCatalogTree (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FurnitureCatalogTree (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FurnitureCatalogTree.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTransferHandler.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTable.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/FurnitureCatalogTree.html" target="_top">Frames</a></li>
<li><a href="FurnitureCatalogTree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JTree">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JTree">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class FurnitureCatalogTree" class="title">Class FurnitureCatalogTree</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>javax.swing.JComponent</li>
<li>
<ul class="inheritance">
<li>javax.swing.JTree</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.FurnitureCatalogTree</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>, java.awt.image.ImageObserver, java.awt.MenuContainer, java.io.Serializable, javax.accessibility.Accessible, javax.swing.Scrollable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">FurnitureCatalogTree</span>
extends javax.swing.JTree
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></pre>
<div class="block">A tree displaying furniture catalog by category.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.FurnitureCatalogTree">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JTree">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JTree</h3>
<code>javax.swing.JTree.AccessibleJTree, javax.swing.JTree.DropLocation, javax.swing.JTree.DynamicUtilTreeNode, javax.swing.JTree.EmptySelectionModel, javax.swing.JTree.TreeModelHandler, javax.swing.JTree.TreeSelectionRedirector</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JComponent</h3>
<code>javax.swing.JComponent.AccessibleJComponent</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JTree">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JTree</h3>
<code>ANCHOR_SELECTION_PATH_PROPERTY, CELL_EDITOR_PROPERTY, CELL_RENDERER_PROPERTY, cellEditor, cellRenderer, editable, EDITABLE_PROPERTY, EXPANDS_SELECTED_PATHS_PROPERTY, INVOKES_STOP_CELL_EDITING_PROPERTY, invokesStopCellEditing, LARGE_MODEL_PROPERTY, largeModel, LEAD_SELECTION_PATH_PROPERTY, ROOT_VISIBLE_PROPERTY, rootVisible, ROW_HEIGHT_PROPERTY, rowHeight, SCROLLS_ON_EXPAND_PROPERTY, scrollsOnExpand, SELECTION_MODEL_PROPERTY, selectionModel, selectionRedirector, SHOWS_ROOT_HANDLES_PROPERTY, showsRootHandles, TOGGLE_CLICK_COUNT_PROPERTY, toggleClickCount, TREE_MODEL_PROPERTY, treeModel, treeModelListener, VISIBLE_ROW_COUNT_PROPERTY, visibleRowCount</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JComponent</h3>
<code>listenerList, TOOL_TIP_TEXT_KEY, ui, UNDEFINED_CONDITION, WHEN_ANCESTOR_OF_FOCUSED_COMPONENT, WHEN_FOCUSED, WHEN_IN_FOCUSED_WINDOW</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>accessibleContext, BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html#FurnitureCatalogTree-com.eteks.sweethome3d.model.FurnitureCatalog-">FurnitureCatalogTree</a></span>(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog)</code>
<div class="block">Creates a tree that displays <code>catalog</code> content.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html#FurnitureCatalogTree-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">FurnitureCatalogTree</a></span>(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;controller)</code>
<div class="block">Creates a tree controlled by <code>controller</code> that displays
 <code>catalog</code> content and its selection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html#FurnitureCatalogTree-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">FurnitureCatalogTree</a></span>(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                    <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;controller)</code>
<div class="block">Creates a tree controlled by <code>controller</code> that displays
 <code>catalog</code> content and its selection.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>javax.swing.JToolTip</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html#createToolTip--">createToolTip</a></span>()</code>
<div class="block">Returns the tool tip displayed by this tree.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html#getToolTipText-java.awt.event.MouseEvent-">getToolTipText</a></span>(java.awt.event.MouseEvent&nbsp;ev)</code>
<div class="block">Returns a tooltip for furniture pieces described in this tree.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JTree">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JTree</h3>
<code>addSelectionInterval, addSelectionPath, addSelectionPaths, addSelectionRow, addSelectionRows, addTreeExpansionListener, addTreeSelectionListener, addTreeWillExpandListener, cancelEditing, clearSelection, clearToggledPaths, collapsePath, collapseRow, convertValueToText, createTreeModel, createTreeModelListener, expandPath, expandRow, fireTreeCollapsed, fireTreeExpanded, fireTreeWillCollapse, fireTreeWillExpand, fireValueChanged, getAccessibleContext, getAnchorSelectionPath, getCellEditor, getCellRenderer, getClosestPathForLocation, getClosestRowForLocation, getDefaultTreeModel, getDescendantToggledPaths, getDragEnabled, getDropLocation, getDropMode, getEditingPath, getExpandedDescendants, getExpandsSelectedPaths, getInvokesStopCellEditing, getLastSelectedPathComponent, getLeadSelectionPath, getLeadSelectionRow, getMaxSelectionRow, getMinSelectionRow, getModel, getNextMatch, getPathBetweenRows, getPathBounds, getPathForLocation, getPathForRow, getPreferredScrollableViewportSize, getRowBounds, getRowCount, getRowForLocation, getRowForPath, getRowHeight, getScrollableBlockIncrement, getScrollableTracksViewportHeight, getScrollableTracksViewportWidth, getScrollableUnitIncrement, getScrollsOnExpand, getSelectionCount, getSelectionModel, getSelectionPath, getSelectionPaths, getSelectionRows, getShowsRootHandles, getToggleClickCount, getTreeExpansionListeners, getTreeSelectionListeners, getTreeWillExpandListeners, getUI, getUIClassID, getVisibleRowCount, hasBeenExpanded, isCollapsed, isCollapsed, isEditable, isEditing, isExpanded, isExpanded, isFixedRowHeight, isLargeModel, isPathEditable, isPathSelected, isRootVisible, isRowSelected, isSelectionEmpty, isVisible, makeVisible, paramString, removeDescendantSelectedPaths, removeDescendantToggledPaths, removeSelectionInterval, removeSelectionPath, removeSelectionPaths, removeSelectionRow, removeSelectionRows, removeTreeExpansionListener, removeTreeSelectionListener, removeTreeWillExpandListener, scrollPathToVisible, scrollRowToVisible, setAnchorSelectionPath, setCellEditor, setCellRenderer, setDragEnabled, setDropMode, setEditable, setExpandedState, setExpandsSelectedPaths, setInvokesStopCellEditing, setLargeModel, setLeadSelectionPath, setModel, setRootVisible, setRowHeight, setScrollsOnExpand, setSelectionInterval, setSelectionModel, setSelectionPath, setSelectionPaths, setSelectionRow, setSelectionRows, setShowsRootHandles, setToggleClickCount, setUI, setVisibleRowCount, startEditingAtPath, stopEditing, treeDidChange, updateUI</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JComponent</h3>
<code>addAncestorListener, addNotify, addVetoableChangeListener, computeVisibleRect, contains, disable, enable, firePropertyChange, firePropertyChange, firePropertyChange, fireVetoableChange, getActionForKeyStroke, getActionMap, getAlignmentX, getAlignmentY, getAncestorListeners, getAutoscrolls, getBaseline, getBaselineResizeBehavior, getBorder, getBounds, getClientProperty, getComponentGraphics, getComponentPopupMenu, getConditionForKeyStroke, getDebugGraphicsOptions, getDefaultLocale, getFontMetrics, getGraphics, getHeight, getInheritsPopupMenu, getInputMap, getInputMap, getInputVerifier, getInsets, getInsets, getListeners, getLocation, getMaximumSize, getMinimumSize, getNextFocusableComponent, getPopupLocation, getPreferredSize, getRegisteredKeyStrokes, getRootPane, getSize, getToolTipLocation, getToolTipText, getTopLevelAncestor, getTransferHandler, getVerifyInputWhenFocusTarget, getVetoableChangeListeners, getVisibleRect, getWidth, getX, getY, grabFocus, hide, isDoubleBuffered, isLightweightComponent, isManagingFocus, isOpaque, isOptimizedDrawingEnabled, isPaintingForPrint, isPaintingOrigin, isPaintingTile, isRequestFocusEnabled, isValidateRoot, paint, paintBorder, paintChildren, paintComponent, paintImmediately, paintImmediately, print, printAll, printBorder, printChildren, printComponent, processComponentKeyEvent, processKeyBinding, processKeyEvent, processMouseEvent, processMouseMotionEvent, putClientProperty, registerKeyboardAction, registerKeyboardAction, removeAncestorListener, removeNotify, removeVetoableChangeListener, repaint, repaint, requestDefaultFocus, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, resetKeyboardActions, reshape, revalidate, scrollRectToVisible, setActionMap, setAlignmentX, setAlignmentY, setAutoscrolls, setBackground, setBorder, setComponentPopupMenu, setDebugGraphicsOptions, setDefaultLocale, setDoubleBuffered, setEnabled, setFocusTraversalKeys, setFont, setForeground, setInheritsPopupMenu, setInputMap, setInputVerifier, setMaximumSize, setMinimumSize, setNextFocusableComponent, setOpaque, setPreferredSize, setRequestFocusEnabled, setToolTipText, setTransferHandler, setUI, setVerifyInputWhenFocusTarget, setVisible, unregisterKeyboardAction, update</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addImpl, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, doLayout, findComponentAt, findComponentAt, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getLayout, getMousePosition, insets, invalidate, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paintComponents, preferredSize, printComponents, processContainerEvent, processEvent, remove, remove, removeAll, removeContainerListener, setComponentZOrder, setFocusCycleRoot, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setLayout, transferFocusDownCycle, validate, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addFocusListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseListener, addMouseMotionListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, createImage, createImage, createVolatileImage, createVolatileImage, disableEvents, dispatchEvent, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getBackground, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getCursor, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getForeground, getGraphicsConfiguration, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocale, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getToolkit, getTreeLock, gotFocus, handleEvent, hasFocus, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseListener, removeMouseMotionListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, resize, resize, setBounds, setBounds, setComponentOrientation, setCursor, setDropTarget, setFocusable, setFocusTraversalKeysEnabled, setIgnoreRepaint, setLocale, setLocation, setLocation, setName, setSize, setSize, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="FurnitureCatalogTree-com.eteks.sweethome3d.model.FurnitureCatalog-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FurnitureCatalogTree</h4>
<pre>public&nbsp;FurnitureCatalogTree(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog)</pre>
<div class="block">Creates a tree that displays <code>catalog</code> content.</div>
</li>
</ul>
<a name="FurnitureCatalogTree-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FurnitureCatalogTree</h4>
<pre>public&nbsp;FurnitureCatalogTree(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;controller)</pre>
<div class="block">Creates a tree controlled by <code>controller</code> that displays
 <code>catalog</code> content and its selection.</div>
</li>
</ul>
<a name="FurnitureCatalogTree-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FurnitureCatalogTree</h4>
<pre>public&nbsp;FurnitureCatalogTree(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                            <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;controller)</pre>
<div class="block">Creates a tree controlled by <code>controller</code> that displays
 <code>catalog</code> content and its selection.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createToolTip--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createToolTip</h4>
<pre>public&nbsp;javax.swing.JToolTip&nbsp;createToolTip()</pre>
<div class="block">Returns the tool tip displayed by this tree.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>createToolTip</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="getToolTipText-java.awt.event.MouseEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getToolTipText</h4>
<pre>public&nbsp;java.lang.String&nbsp;getToolTipText(java.awt.event.MouseEvent&nbsp;ev)</pre>
<div class="block">Returns a tooltip for furniture pieces described in this tree.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getToolTipText</code>&nbsp;in class&nbsp;<code>javax.swing.JTree</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FurnitureCatalogTree.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTransferHandler.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTable.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/FurnitureCatalogTree.html" target="_top">Frames</a></li>
<li><a href="FurnitureCatalogTree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JTree">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JTree">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
