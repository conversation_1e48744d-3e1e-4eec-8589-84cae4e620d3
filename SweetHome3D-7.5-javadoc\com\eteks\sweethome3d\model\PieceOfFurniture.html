<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>PieceOfFurniture (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PieceOfFurniture (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PieceOfFurniture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/PatternsCatalog.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/PieceOfFurniture.html" target="_top">Frames</a></li>
<li><a href="PieceOfFurniture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Interface PieceOfFurniture" class="title">Interface PieceOfFurniture</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>, <a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html" title="interface in com.eteks.sweethome3d.model">ShelfUnit</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">CatalogDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogLight.html" title="class in com.eteks.sweethome3d.model">CatalogLight</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html" title="class in com.eteks.sweethome3d.model">CatalogShelfUnit</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">HomeDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model">HomeLight</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.html" title="class in com.eteks.sweethome3d.model">HomeShelfUnit</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">PieceOfFurniture</span></pre>
<div class="block">A piece of furniture.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a></span></code>
<div class="block">The default cut out shape that covers a 1 unit wide square.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a></span></code>
<div class="block">The flag used to specify that the shapes of a 3D model which uses a material prefixed by "edge_color" should be hidden.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a></span></code>
<div class="block">Identity model rotation matrix.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></span></code>
<div class="block">The flag used to specify that the back faces of a 3D model should be shown.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the color of this piece of furniture.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the value of an additional content <code>name</code> associated to this piece.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a></span>()</code>
<div class="block">Returns the creator of this piece or <code>null</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a></span>()</code>
<div class="block">Returns the price currency, noted with ISO 4217 code, or <code>null</code>
 if it has no price or default currency should be used.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a></span>()</code>
<div class="block">Returns the depth of this piece of furniture.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Returns the description of this piece of furniture.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a></span>()</code>
<div class="block">Returns the elevation at which should be placed an object dropped on this piece.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the elevation of this piece of furniture.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height of this piece of furniture.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a></span>()</code>
<div class="block">Returns the additional information associated to this piece, or <code>null</code>.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getLicense--">getLicense</a></span>()</code>
<div class="block">Returns the license of this piece of furniture.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a></span>()</code>
<div class="block">Returns flags that should apply on the model of this piece of furniture.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a></span>()</code>
<div class="block">Returns the rotation 3 by 3 matrix of this piece of furniture that ensures
 its model is correctly oriented.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a></span>()</code>
<div class="block">Returns the size of the 3D model of this piece of furniture or <code>null</code> if not known.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this piece of furniture.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a></span>()</code>
<div class="block">Returns the price of this piece of furniture or <code>null</code>.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the value of an additional property <code>name</code> of this piece.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.util.Collection&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a></span>()</code>
<div class="block">Returns the names of the additional properties of this piece.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></span>()</code>
<div class="block">Returns the shape used to cut out upper levels when they intersect with the piece
 like a staircase.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></span>()</code>
<div class="block">Returns the Value Added Tax percentage applied to the price of this piece of furniture.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the width of this piece of furniture.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a></span>()</code>
<div class="block">Returns <code>true</code> if the back face of the piece of furniture
 model should be displayed.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns <code>true</code> if the type of given additional property is a content.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece of furniture is a door or a window.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></span>()</code>
<div class="block">Returns <code>false</code> if this piece should not rotate around an horizontal axis.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece of furniture is movable.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is resizable.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a></span>()</code>
<div class="block">Returns <code>false</code> if this piece should always keep the same color or texture.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></span>()</code>
<div class="block">Returns <code>true</code> if the width and depth of this piece may
 be changed independently from each other.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DEFAULT_CUT_OUT_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_CUT_OUT_SHAPE</h4>
<pre>static final&nbsp;java.lang.String DEFAULT_CUT_OUT_SHAPE</pre>
<div class="block">The default cut out shape that covers a 1 unit wide square.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.model.PieceOfFurniture.DEFAULT_CUT_OUT_SHAPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="IDENTITY_ROTATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>IDENTITY_ROTATION</h4>
<pre>static final&nbsp;float[][] IDENTITY_ROTATION</pre>
<div class="block">Identity model rotation matrix.</div>
</li>
</ul>
<a name="SHOW_BACK_FACE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHOW_BACK_FACE</h4>
<pre>static final&nbsp;int SHOW_BACK_FACE</pre>
<div class="block">The flag used to specify that the back faces of a 3D model should be shown.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.model.PieceOfFurniture.SHOW_BACK_FACE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="HIDE_EDGE_COLOR_MATERIAL">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HIDE_EDGE_COLOR_MATERIAL</h4>
<pre>static final&nbsp;int HIDE_EDGE_COLOR_MATERIAL</pre>
<div class="block">The flag used to specify that the shapes of a 3D model which uses a material prefixed by "edge_color" should be hidden.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.model.PieceOfFurniture.HIDE_EDGE_COLOR_MATERIAL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this piece of furniture.</div>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>java.lang.String&nbsp;getDescription()</pre>
<div class="block">Returns the description of this piece of furniture.</div>
</li>
</ul>
<a name="getInformation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInformation</h4>
<pre>java.lang.String&nbsp;getInformation()</pre>
<div class="block">Returns the additional information associated to this piece, or <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="getLicense--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLicense</h4>
<pre>java.lang.String&nbsp;getLicense()</pre>
<div class="block">Returns the license of this piece of furniture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getDepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepth</h4>
<pre>float&nbsp;getDepth()</pre>
<div class="block">Returns the depth of this piece of furniture.</div>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>float&nbsp;getHeight()</pre>
<div class="block">Returns the height of this piece of furniture.</div>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>float&nbsp;getWidth()</pre>
<div class="block">Returns the width of this piece of furniture.</div>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>float&nbsp;getElevation()</pre>
<div class="block">Returns the elevation of this piece of furniture.</div>
</li>
</ul>
<a name="getDropOnTopElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDropOnTopElevation</h4>
<pre>float&nbsp;getDropOnTopElevation()</pre>
<div class="block">Returns the elevation at which should be placed an object dropped on this piece.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a percentage of the height of this piece. A negative value means that the piece
         should be ignored when an object is dropped on it.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="isMovable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMovable</h4>
<pre>boolean&nbsp;isMovable()</pre>
<div class="block">Returns <code>true</code> if this piece of furniture is movable.</div>
</li>
</ul>
<a name="isDoorOrWindow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDoorOrWindow</h4>
<pre>boolean&nbsp;isDoorOrWindow()</pre>
<div class="block">Returns <code>true</code> if this piece of furniture is a door or a window.
 As this method existed before <a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a> interface,
 you shouldn't rely on the value returned by this method to guess if a piece
 is an instance of <code>DoorOrWindow</code> class.</div>
</li>
</ul>
<a name="getIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIcon</h4>
<pre><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getIcon()</pre>
<div class="block">Returns the icon of this piece of furniture.</div>
</li>
</ul>
<a name="getPlanIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlanIcon</h4>
<pre><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getPlanIcon()</pre>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="getModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getModel()</pre>
<div class="block">Returns the 3D model of this piece of furniture.</div>
</li>
</ul>
<a name="getModelFlags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelFlags</h4>
<pre>int&nbsp;getModelFlags()</pre>
<div class="block">Returns flags that should apply on the model of this piece of furniture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getModelSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelSize</h4>
<pre>java.lang.Long&nbsp;getModelSize()</pre>
<div class="block">Returns the size of the 3D model of this piece of furniture or <code>null</code> if not known.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getModelRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelRotation</h4>
<pre>float[][]&nbsp;getModelRotation()</pre>
<div class="block">Returns the rotation 3 by 3 matrix of this piece of furniture that ensures
 its model is correctly oriented.</div>
</li>
</ul>
<a name="getStaircaseCutOutShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaircaseCutOutShape</h4>
<pre>java.lang.String&nbsp;getStaircaseCutOutShape()</pre>
<div class="block">Returns the shape used to cut out upper levels when they intersect with the piece
 like a staircase.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getCreator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreator</h4>
<pre>java.lang.String&nbsp;getCreator()</pre>
<div class="block">Returns the creator of this piece or <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="isBackFaceShown--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackFaceShown</h4>
<pre>boolean&nbsp;isBackFaceShown()</pre>
<div class="block">Returns <code>true</code> if the back face of the piece of furniture
 model should be displayed.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the color of this piece of furniture.</div>
</li>
</ul>
<a name="isResizable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isResizable</h4>
<pre>boolean&nbsp;isResizable()</pre>
<div class="block">Returns <code>true</code> if this piece is resizable.</div>
</li>
</ul>
<a name="isDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeformable</h4>
<pre>boolean&nbsp;isDeformable()</pre>
<div class="block">Returns <code>true</code> if this piece is deformable. The width, depth and height
 of a deformable piece may change independently from each other.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isWidthDepthDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWidthDepthDeformable</h4>
<pre>boolean&nbsp;isWidthDepthDeformable()</pre>
<div class="block">Returns <code>true</code> if the width and depth of this piece may
 be changed independently from each other.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isTexturable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTexturable</h4>
<pre>boolean&nbsp;isTexturable()</pre>
<div class="block">Returns <code>false</code> if this piece should always keep the same color or texture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isHorizontallyRotatable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHorizontallyRotatable</h4>
<pre>boolean&nbsp;isHorizontallyRotatable()</pre>
<div class="block">Returns <code>false</code> if this piece should not rotate around an horizontal axis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getPrice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrice</h4>
<pre>java.math.BigDecimal&nbsp;getPrice()</pre>
<div class="block">Returns the price of this piece of furniture or <code>null</code>.</div>
</li>
</ul>
<a name="getValueAddedTaxPercentage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueAddedTaxPercentage</h4>
<pre>java.math.BigDecimal&nbsp;getValueAddedTaxPercentage()</pre>
<div class="block">Returns the Value Added Tax percentage applied to the price of this piece of furniture.</div>
</li>
</ul>
<a name="getCurrency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrency</h4>
<pre>java.lang.String&nbsp;getCurrency()</pre>
<div class="block">Returns the price currency, noted with ISO 4217 code, or <code>null</code>
 if it has no price or default currency should be used.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProperty</h4>
<pre>java.lang.String&nbsp;getProperty(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the value of an additional property <code>name</code> of this piece.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the value of the property or <code>null</code> if it doesn't exist or if it's not a string.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getPropertyNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPropertyNames</h4>
<pre>java.util.Collection&lt;java.lang.String&gt;&nbsp;getPropertyNames()</pre>
<div class="block">Returns the names of the additional properties of this piece.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a collection of all the names of the properties</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getContentProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentProperty</h4>
<pre><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getContentProperty(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the value of an additional content <code>name</code> associated to this piece.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the value of the content or <code>null</code> if it doesn't exist or if it's not a content.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isContentProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isContentProperty</h4>
<pre>boolean&nbsp;isContentProperty(java.lang.String&nbsp;name)</pre>
<div class="block">Returns <code>true</code> if the type of given additional property is a content.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PieceOfFurniture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/PatternsCatalog.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/PieceOfFurniture.html" target="_top">Frames</a></li>
<li><a href="PieceOfFurniture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
