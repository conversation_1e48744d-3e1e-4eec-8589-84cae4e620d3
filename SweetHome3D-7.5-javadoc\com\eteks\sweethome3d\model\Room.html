<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>Room (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Room (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Room.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Room.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Room.html" target="_top">Frames</a></li>
<li><a href="Room.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class Room" class="title">Class Room</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.Room</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Room</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>
implements <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, <a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></pre>
<div class="block">A room or a polygon in a home plan.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.Room">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.Property.html" title="enum in com.eteks.sweethome3d.model">Room.Property</a></span></code>
<div class="block">The properties of a room that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#Room-float:A:A-">Room</a></span>(float[][]&nbsp;points)</code>
<div class="block">Creates a room from its name and the given coordinates.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#Room-java.lang.String-float:A:A-">Room</a></span>(java.lang.String&nbsp;id,
    float[][]&nbsp;points)</code>
<div class="block">Creates a room from its name and the given coordinates.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#addPoint-float-float-">addPoint</a></span>(float&nbsp;x,
        float&nbsp;y)</code>
<div class="block">Adds a point at the end of room points.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#addPoint-float-float-int-">addPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        int&nbsp;index)</code>
<div class="block">Adds a point at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this room.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#containsPoint-float-float-float-">containsPoint</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this room contains
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getArea--">getArea</a></span>()</code>
<div class="block">Returns the area of this room.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getAreaAngle--">getAreaAngle</a></span>()</code>
<div class="block">Returns the angle in radians used to display the room area.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getAreaStyle--">getAreaStyle</a></span>()</code>
<div class="block">Returns the text style used to display room area.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getAreaXOffset--">getAreaXOffset</a></span>()</code>
<div class="block">Returns the distance along x axis applied to room center abscissa
 to display room area.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getAreaYOffset--">getAreaYOffset</a></span>()</code>
<div class="block">Returns the distance along y axis applied to room center ordinate
 to display room area.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getBoundsMaximumCoordinates--">getBoundsMaximumCoordinates</a></span>()</code>
<div class="block">Returns the maximum coordinates of the rectangle bounding this room.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getBoundsMinimumCoordinates--">getBoundsMinimumCoordinates</a></span>()</code>
<div class="block">Returns the minimum coordinates of the rectangle bounding this room.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getCeilingColor--">getCeilingColor</a></span>()</code>
<div class="block">Returns the ceiling color color of this room.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getCeilingShininess--">getCeilingShininess</a></span>()</code>
<div class="block">Returns the ceiling shininess of this room.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getCeilingTexture--">getCeilingTexture</a></span>()</code>
<div class="block">Returns the ceiling texture of this room.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getFloorColor--">getFloorColor</a></span>()</code>
<div class="block">Returns the floor color of this room.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getFloorShininess--">getFloorShininess</a></span>()</code>
<div class="block">Returns the floor shininess of this room.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getFloorTexture--">getFloorTexture</a></span>()</code>
<div class="block">Returns the floor texture of this room.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getLevel--">getLevel</a></span>()</code>
<div class="block">Returns the level which this room belongs to.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this room.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getNameAngle--">getNameAngle</a></span>()</code>
<div class="block">Returns the angle in radians used to display the room name.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getNameStyle--">getNameStyle</a></span>()</code>
<div class="block">Returns the text style used to display room name.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getNameXOffset--">getNameXOffset</a></span>()</code>
<div class="block">Returns the distance along x axis applied to room center abscissa
 to display room name.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getNameYOffset--">getNameYOffset</a></span>()</code>
<div class="block">Returns the distance along y axis applied to room center ordinate
 to display room name.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getPointCount--">getPointCount</a></span>()</code>
<div class="block">Returns the number of points of the polygon matching this room.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getPointIndexAt-float-float-float-">getPointIndexAt</a></span>(float&nbsp;x,
               float&nbsp;y,
               float&nbsp;margin)</code>
<div class="block">Returns the index of the point of this room equal to
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getPoints--">getPoints</a></span>()</code>
<div class="block">Returns the points of the polygon matching this room.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getXCenter--">getXCenter</a></span>()</code>
<div class="block">Returns the abscissa of the center point of this room.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#getYCenter--">getYCenter</a></span>()</code>
<div class="block">Returns the ordinate of the center point of this room.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></span>(float&nbsp;x0,
                   float&nbsp;y0,
                   float&nbsp;x1,
                   float&nbsp;y1)</code>
<div class="block">Returns <code>true</code> if this room intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isAreaCenterPointAt-float-float-float-">isAreaCenterPointAt</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the center point at which is displayed the area
 of this room is equal to the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isAreaVisible--">isAreaVisible</a></span>()</code>
<div class="block">Returns whether the area of this room is visible or not.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Returns <code>true</code> if this room is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isCeilingFlat--">isCeilingFlat</a></span>()</code>
<div class="block">Returns <code>true</code> if the ceiling should remain flat whatever its environment.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isCeilingVisible--">isCeilingVisible</a></span>()</code>
<div class="block">Returns whether the ceiling of this room is visible or not.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isClockwise--">isClockwise</a></span>()</code>
<div class="block">Returns <code>true</code> if the points of this room are in clockwise order.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isFloorVisible--">isFloorVisible</a></span>()</code>
<div class="block">Returns whether the floor of this room is visible or not.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isNameCenterPointAt-float-float-float-">isNameCenterPointAt</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the center point at which is displayed the name
 of this room is equal to the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#isSingular--">isSingular</a></span>()</code>
<div class="block">Returns <code>true</code> if this room is comprised of only one polygon.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#move-float-float-">move</a></span>(float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Moves this room of (<code>dx</code>, <code>dy</code>) units.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#removePoint-int-">removePoint</a></span>(int&nbsp;index)</code>
<div class="block">Removes the point at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setAreaAngle-float-">setAreaAngle</a></span>(float&nbsp;areaAngle)</code>
<div class="block">Sets the angle in radians used to display the room area.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setAreaStyle-com.eteks.sweethome3d.model.TextStyle-">setAreaStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;areaStyle)</code>
<div class="block">Sets the text style used to display room area.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setAreaVisible-boolean-">setAreaVisible</a></span>(boolean&nbsp;areaVisible)</code>
<div class="block">Sets whether the area of this room is visible or not.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setAreaXOffset-float-">setAreaXOffset</a></span>(float&nbsp;areaXOffset)</code>
<div class="block">Sets the distance along x axis applied to room center abscissa to display room area.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setAreaYOffset-float-">setAreaYOffset</a></span>(float&nbsp;areaYOffset)</code>
<div class="block">Sets the distance along y axis applied to room center ordinate to display room area.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setCeilingColor-java.lang.Integer-">setCeilingColor</a></span>(java.lang.Integer&nbsp;ceilingColor)</code>
<div class="block">Sets the ceiling color of this room.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setCeilingFlat-boolean-">setCeilingFlat</a></span>(boolean&nbsp;ceilingFlat)</code>
<div class="block">Sets whether the floor texture should remain flat.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setCeilingShininess-float-">setCeilingShininess</a></span>(float&nbsp;ceilingShininess)</code>
<div class="block">Sets the ceiling shininess of this room.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setCeilingTexture-com.eteks.sweethome3d.model.HomeTexture-">setCeilingTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;ceilingTexture)</code>
<div class="block">Sets the ceiling texture of this room.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setCeilingVisible-boolean-">setCeilingVisible</a></span>(boolean&nbsp;ceilingVisible)</code>
<div class="block">Sets whether the ceiling of this room is visible or not.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setFloorColor-java.lang.Integer-">setFloorColor</a></span>(java.lang.Integer&nbsp;floorColor)</code>
<div class="block">Sets the floor color of this room.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setFloorShininess-float-">setFloorShininess</a></span>(float&nbsp;floorShininess)</code>
<div class="block">Sets the floor shininess of this room.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setFloorTexture-com.eteks.sweethome3d.model.HomeTexture-">setFloorTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;floorTexture)</code>
<div class="block">Sets the floor texture of this room.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setFloorVisible-boolean-">setFloorVisible</a></span>(boolean&nbsp;floorVisible)</code>
<div class="block">Sets whether the floor of this room is visible or not.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Sets the level of this room.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Sets the name of this room.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setNameAngle-float-">setNameAngle</a></span>(float&nbsp;nameAngle)</code>
<div class="block">Sets the angle in radians used to display the room name.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setNameStyle-com.eteks.sweethome3d.model.TextStyle-">setNameStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;nameStyle)</code>
<div class="block">Sets the text style used to display room name.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setNameXOffset-float-">setNameXOffset</a></span>(float&nbsp;nameXOffset)</code>
<div class="block">Sets the distance along x axis applied to room center abscissa to display room name.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setNameYOffset-float-">setNameYOffset</a></span>(float&nbsp;nameYOffset)</code>
<div class="block">Sets the distance along y axis applied to room center ordinate to display room name.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setPoint-float-float-int-">setPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        int&nbsp;index)</code>
<div class="block">Sets the point at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Room.html#setPoints-float:A:A-">setPoints</a></span>(float[][]&nbsp;points)</code>
<div class="block">Sets the points of the polygon matching this room.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Room-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Room</h4>
<pre>public&nbsp;Room(float[][]&nbsp;points)</pre>
<div class="block">Creates a room from its name and the given coordinates.</div>
</li>
</ul>
<a name="Room-java.lang.String-float:A:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Room</h4>
<pre>public&nbsp;Room(java.lang.String&nbsp;id,
            float[][]&nbsp;points)</pre>
<div class="block">Creates a room from its name and the given coordinates.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this room.</div>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the name of this room. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getNameXOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameXOffset</h4>
<pre>public&nbsp;float&nbsp;getNameXOffset()</pre>
<div class="block">Returns the distance along x axis applied to room center abscissa
 to display room name.</div>
</li>
</ul>
<a name="setNameXOffset-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameXOffset</h4>
<pre>public&nbsp;void&nbsp;setNameXOffset(float&nbsp;nameXOffset)</pre>
<div class="block">Sets the distance along x axis applied to room center abscissa to display room name.
 Once this room  is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getNameYOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameYOffset</h4>
<pre>public&nbsp;float&nbsp;getNameYOffset()</pre>
<div class="block">Returns the distance along y axis applied to room center ordinate
 to display room name.</div>
</li>
</ul>
<a name="setNameYOffset-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameYOffset</h4>
<pre>public&nbsp;void&nbsp;setNameYOffset(float&nbsp;nameYOffset)</pre>
<div class="block">Sets the distance along y axis applied to room center ordinate to display room name.
 Once this room is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getNameStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;getNameStyle()</pre>
<div class="block">Returns the text style used to display room name.</div>
</li>
</ul>
<a name="setNameStyle-com.eteks.sweethome3d.model.TextStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameStyle</h4>
<pre>public&nbsp;void&nbsp;setNameStyle(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;nameStyle)</pre>
<div class="block">Sets the text style used to display room name.
 Once this room is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getNameAngle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameAngle</h4>
<pre>public&nbsp;float&nbsp;getNameAngle()</pre>
<div class="block">Returns the angle in radians used to display the room name.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="setNameAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameAngle</h4>
<pre>public&nbsp;void&nbsp;setNameAngle(float&nbsp;nameAngle)</pre>
<div class="block">Sets the angle in radians used to display the room name. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public&nbsp;float[][]&nbsp;getPoints()</pre>
<div class="block">Returns the points of the polygon matching this room.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#getPoints--">getPoints</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of the (x,y) coordinates of the room points.</dd>
</dl>
</li>
</ul>
<a name="getPointCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPointCount</h4>
<pre>public&nbsp;int&nbsp;getPointCount()</pre>
<div class="block">Returns the number of points of the polygon matching this room.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setPoints-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoints</h4>
<pre>public&nbsp;void&nbsp;setPoints(float[][]&nbsp;points)</pre>
<div class="block">Sets the points of the polygon matching this room. Once this room
 is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="addPoint-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPoint</h4>
<pre>public&nbsp;void&nbsp;addPoint(float&nbsp;x,
                     float&nbsp;y)</pre>
<div class="block">Adds a point at the end of room points.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="addPoint-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPoint</h4>
<pre>public&nbsp;void&nbsp;addPoint(float&nbsp;x,
                     float&nbsp;y,
                     int&nbsp;index)</pre>
<div class="block">Adds a point at the given <code>index</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IndexOutOfBoundsException</code> - if <code>index</code> is negative or > <code>getPointCount()</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setPoint-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoint</h4>
<pre>public&nbsp;void&nbsp;setPoint(float&nbsp;x,
                     float&nbsp;y,
                     int&nbsp;index)</pre>
<div class="block">Sets the point at the given <code>index</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IndexOutOfBoundsException</code> - if <code>index</code> is negative or >= <code>getPointCount()</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="removePoint-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePoint</h4>
<pre>public&nbsp;void&nbsp;removePoint(int&nbsp;index)</pre>
<div class="block">Removes the point at the given <code>index</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IndexOutOfBoundsException</code> - if <code>index</code> is negative or >= <code>getPointCount()</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getBoundsMinimumCoordinates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBoundsMinimumCoordinates</h4>
<pre>public&nbsp;float[]&nbsp;getBoundsMinimumCoordinates()</pre>
<div class="block">Returns the minimum coordinates of the rectangle bounding this room.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getBoundsMaximumCoordinates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBoundsMaximumCoordinates</h4>
<pre>public&nbsp;float[]&nbsp;getBoundsMaximumCoordinates()</pre>
<div class="block">Returns the maximum coordinates of the rectangle bounding this room.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="isAreaVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAreaVisible</h4>
<pre>public&nbsp;boolean&nbsp;isAreaVisible()</pre>
<div class="block">Returns whether the area of this room is visible or not.</div>
</li>
</ul>
<a name="setAreaVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAreaVisible</h4>
<pre>public&nbsp;void&nbsp;setAreaVisible(boolean&nbsp;areaVisible)</pre>
<div class="block">Sets whether the area of this room is visible or not. Once this room
 is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getAreaXOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaXOffset</h4>
<pre>public&nbsp;float&nbsp;getAreaXOffset()</pre>
<div class="block">Returns the distance along x axis applied to room center abscissa
 to display room area.</div>
</li>
</ul>
<a name="setAreaXOffset-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAreaXOffset</h4>
<pre>public&nbsp;void&nbsp;setAreaXOffset(float&nbsp;areaXOffset)</pre>
<div class="block">Sets the distance along x axis applied to room center abscissa to display room area.
 Once this room  is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getAreaYOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaYOffset</h4>
<pre>public&nbsp;float&nbsp;getAreaYOffset()</pre>
<div class="block">Returns the distance along y axis applied to room center ordinate
 to display room area.</div>
</li>
</ul>
<a name="setAreaYOffset-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAreaYOffset</h4>
<pre>public&nbsp;void&nbsp;setAreaYOffset(float&nbsp;areaYOffset)</pre>
<div class="block">Sets the distance along y axis applied to room center ordinate to display room area.
 Once this room is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getAreaStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;getAreaStyle()</pre>
<div class="block">Returns the text style used to display room area.</div>
</li>
</ul>
<a name="setAreaStyle-com.eteks.sweethome3d.model.TextStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAreaStyle</h4>
<pre>public&nbsp;void&nbsp;setAreaStyle(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;areaStyle)</pre>
<div class="block">Sets the text style used to display room area.
 Once this room is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getAreaAngle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaAngle</h4>
<pre>public&nbsp;float&nbsp;getAreaAngle()</pre>
<div class="block">Returns the angle in radians used to display the room area.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="setAreaAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAreaAngle</h4>
<pre>public&nbsp;void&nbsp;setAreaAngle(float&nbsp;areaAngle)</pre>
<div class="block">Sets the angle in radians used to display the room area. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getXCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXCenter</h4>
<pre>public&nbsp;float&nbsp;getXCenter()</pre>
<div class="block">Returns the abscissa of the center point of this room.</div>
</li>
</ul>
<a name="getYCenter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYCenter</h4>
<pre>public&nbsp;float&nbsp;getYCenter()</pre>
<div class="block">Returns the ordinate of the center point of this room.</div>
</li>
</ul>
<a name="getFloorColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloorColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getFloorColor()</pre>
<div class="block">Returns the floor color of this room.</div>
</li>
</ul>
<a name="setFloorColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorColor</h4>
<pre>public&nbsp;void&nbsp;setFloorColor(java.lang.Integer&nbsp;floorColor)</pre>
<div class="block">Sets the floor color of this room. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getFloorTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloorTexture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;getFloorTexture()</pre>
<div class="block">Returns the floor texture of this room.</div>
</li>
</ul>
<a name="setFloorTexture-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorTexture</h4>
<pre>public&nbsp;void&nbsp;setFloorTexture(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;floorTexture)</pre>
<div class="block">Sets the floor texture of this room. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="isFloorVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFloorVisible</h4>
<pre>public&nbsp;boolean&nbsp;isFloorVisible()</pre>
<div class="block">Returns whether the floor of this room is visible or not.</div>
</li>
</ul>
<a name="setFloorVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorVisible</h4>
<pre>public&nbsp;void&nbsp;setFloorVisible(boolean&nbsp;floorVisible)</pre>
<div class="block">Sets whether the floor of this room is visible or not. Once this room
 is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getFloorShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloorShininess</h4>
<pre>public&nbsp;float&nbsp;getFloorShininess()</pre>
<div class="block">Returns the floor shininess of this room.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value between 0 (matt) and 1 (very shiny)</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setFloorShininess-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorShininess</h4>
<pre>public&nbsp;void&nbsp;setFloorShininess(float&nbsp;floorShininess)</pre>
<div class="block">Sets the floor shininess of this room. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getCeilingColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getCeilingColor()</pre>
<div class="block">Returns the ceiling color color of this room.</div>
</li>
</ul>
<a name="setCeilingColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingColor</h4>
<pre>public&nbsp;void&nbsp;setCeilingColor(java.lang.Integer&nbsp;ceilingColor)</pre>
<div class="block">Sets the ceiling color of this room. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getCeilingTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingTexture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;getCeilingTexture()</pre>
<div class="block">Returns the ceiling texture of this room.</div>
</li>
</ul>
<a name="setCeilingTexture-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingTexture</h4>
<pre>public&nbsp;void&nbsp;setCeilingTexture(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;ceilingTexture)</pre>
<div class="block">Sets the ceiling texture of this room. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="isCeilingVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCeilingVisible</h4>
<pre>public&nbsp;boolean&nbsp;isCeilingVisible()</pre>
<div class="block">Returns whether the ceiling of this room is visible or not.</div>
</li>
</ul>
<a name="setCeilingVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingVisible</h4>
<pre>public&nbsp;void&nbsp;setCeilingVisible(boolean&nbsp;ceilingVisible)</pre>
<div class="block">Sets whether the ceiling of this room is visible or not. Once this room
 is updated, listeners added to this room will receive a change notification.</div>
</li>
</ul>
<a name="getCeilingShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingShininess</h4>
<pre>public&nbsp;float&nbsp;getCeilingShininess()</pre>
<div class="block">Returns the ceiling shininess of this room.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value between 0 (matt) and 1 (very shiny)</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setCeilingShininess-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingShininess</h4>
<pre>public&nbsp;void&nbsp;setCeilingShininess(float&nbsp;ceilingShininess)</pre>
<div class="block">Sets the ceiling shininess of this room. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isCeilingFlat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCeilingFlat</h4>
<pre>public&nbsp;boolean&nbsp;isCeilingFlat()</pre>
<div class="block">Returns <code>true</code> if the ceiling should remain flat whatever its environment.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setCeilingFlat-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingFlat</h4>
<pre>public&nbsp;void&nbsp;setCeilingFlat(boolean&nbsp;ceilingFlat)</pre>
<div class="block">Sets whether the floor texture should remain flat. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;getLevel()</pre>
<div class="block">Returns the level which this room belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#getLevel--">getLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevel</h4>
<pre>public&nbsp;void&nbsp;setLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Sets the level of this room. Once this room is updated,
 listeners added to this room will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isAtLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAtLevel</h4>
<pre>public&nbsp;boolean&nbsp;isAtLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Returns <code>true</code> if this room is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getArea</h4>
<pre>public&nbsp;float&nbsp;getArea()</pre>
<div class="block">Returns the area of this room.</div>
</li>
</ul>
<a name="isClockwise--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isClockwise</h4>
<pre>public&nbsp;boolean&nbsp;isClockwise()</pre>
<div class="block">Returns <code>true</code> if the points of this room are in clockwise order.</div>
</li>
</ul>
<a name="isSingular--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSingular</h4>
<pre>public&nbsp;boolean&nbsp;isSingular()</pre>
<div class="block">Returns <code>true</code> if this room is comprised of only one polygon.</div>
</li>
</ul>
<a name="intersectsRectangle-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersectsRectangle</h4>
<pre>public&nbsp;boolean&nbsp;intersectsRectangle(float&nbsp;x0,
                                   float&nbsp;y0,
                                   float&nbsp;x1,
                                   float&nbsp;y1)</pre>
<div class="block">Returns <code>true</code> if this room intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="containsPoint-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsPoint</h4>
<pre>public&nbsp;boolean&nbsp;containsPoint(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this room contains
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#containsPoint-float-float-float-">containsPoint</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="getPointIndexAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPointIndexAt</h4>
<pre>public&nbsp;int&nbsp;getPointIndexAt(float&nbsp;x,
                           float&nbsp;y,
                           float&nbsp;margin)</pre>
<div class="block">Returns the index of the point of this room equal to
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the index of the first found point or -1.</dd>
</dl>
</li>
</ul>
<a name="isNameCenterPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNameCenterPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isNameCenterPointAt(float&nbsp;x,
                                   float&nbsp;y,
                                   float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the center point at which is displayed the name
 of this room is equal to the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</li>
</ul>
<a name="isAreaCenterPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAreaCenterPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isAreaCenterPointAt(float&nbsp;x,
                                   float&nbsp;y,
                                   float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the center point at which is displayed the area
 of this room is equal to the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</li>
</ul>
<a name="move-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>move</h4>
<pre>public&nbsp;void&nbsp;move(float&nbsp;dx,
                 float&nbsp;dy)</pre>
<div class="block">Moves this room of (<code>dx</code>, <code>dy</code>) units.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#move-float-float-">move</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this room.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Room.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Room.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Room.html" target="_top">Frames</a></li>
<li><a href="Room.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
