<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/viewcontroller/class-use/PlanController.ControllerState.html" target="_top">Frames</a></li>
<li><a href="PlanController.ControllerState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState" class="title">Uses of Class<br>com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerStateDecorator</a></span></code>
<div class="block">A decorator on controller state, useful to change the behavior of an existing state.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCameraElevationState--">getCameraElevationState</a></span>()</code>
<div class="block">Returns the camera elevation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCameraPitchRotationState--">getCameraPitchRotationState</a></span>()</code>
<div class="block">Returns the camera pitch rotation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCameraYawRotationState--">getCameraYawRotationState</a></span>()</code>
<div class="block">Returns the camera yaw rotation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCompassResizeState--">getCompassResizeState</a></span>()</code>
<div class="block">Returns the compass resize state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getCompassRotationState--">getCompassRotationState</a></span>()</code>
<div class="block">Returns the compass rotation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineCreationState--">getDimensionLineCreationState</a></span>()</code>
<div class="block">Returns the dimension line creation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineDrawingState--">getDimensionLineDrawingState</a></span>()</code>
<div class="block">Returns the dimension line drawing state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineElevationState--">getDimensionLineElevationState</a></span>()</code>
<div class="block">Returns the dimension line elevation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineOffsetState--">getDimensionLineOffsetState</a></span>()</code>
<div class="block">Returns the dimension line offset state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLinePitchRotationState--">getDimensionLinePitchRotationState</a></span>()</code>
<div class="block">Returns the dimension line rotation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDimensionLineResizeState--">getDimensionLineResizeState</a></span>()</code>
<div class="block">Returns the dimension line resize state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getDragAndDropState--">getDragAndDropState</a></span>()</code>
<div class="block">Returns the drag and drop state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getLabelCreationState--">getLabelCreationState</a></span>()</code>
<div class="block">Returns the label creation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getLabelElevationState--">getLabelElevationState</a></span>()</code>
<div class="block">Returns the label elevation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getLabelRotationState--">getLabelRotationState</a></span>()</code>
<div class="block">Returns the label rotation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getLightPowerModificationState--">getLightPowerModificationState</a></span>()</code>
<div class="block">Returns the light power modification state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPanningState--">getPanningState</a></span>()</code>
<div class="block">Returns the panning state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureElevationState--">getPieceOfFurnitureElevationState</a></span>()</code>
<div class="block">Returns the piece elevation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureHeightState--">getPieceOfFurnitureHeightState</a></span>()</code>
<div class="block">Returns the piece height state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureNameOffsetState--">getPieceOfFurnitureNameOffsetState</a></span>()</code>
<div class="block">Returns the piece name offset state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureNameRotationState--">getPieceOfFurnitureNameRotationState</a></span>()</code>
<div class="block">Returns the piece name rotation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurniturePitchRotationState--">getPieceOfFurniturePitchRotationState</a></span>()</code>
<div class="block">Returns the piece pitch rotation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureResizeState--">getPieceOfFurnitureResizeState</a></span>()</code>
<div class="block">Returns the piece resize state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureRollRotationState--">getPieceOfFurnitureRollRotationState</a></span>()</code>
<div class="block">Returns the piece roll rotation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPieceOfFurnitureRotationState--">getPieceOfFurnitureRotationState</a></span>()</code>
<div class="block">Returns the piece rotation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPolylineCreationState--">getPolylineCreationState</a></span>()</code>
<div class="block">Returns the polyline creation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPolylineDrawingState--">getPolylineDrawingState</a></span>()</code>
<div class="block">Returns the polyline drawing state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getPolylineResizeState--">getPolylineResizeState</a></span>()</code>
<div class="block">Returns the polyline resize state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRectangleSelectionState--">getRectangleSelectionState</a></span>()</code>
<div class="block">Returns the rectangle selection state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomAreaOffsetState--">getRoomAreaOffsetState</a></span>()</code>
<div class="block">Returns the room area offset state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomAreaRotationState--">getRoomAreaRotationState</a></span>()</code>
<div class="block">Returns the room area rotation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomCreationState--">getRoomCreationState</a></span>()</code>
<div class="block">Returns the room creation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomDrawingState--">getRoomDrawingState</a></span>()</code>
<div class="block">Returns the room drawing state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomNameOffsetState--">getRoomNameOffsetState</a></span>()</code>
<div class="block">Returns the room name offset state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomNameRotationState--">getRoomNameRotationState</a></span>()</code>
<div class="block">Returns the room name rotation state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getRoomResizeState--">getRoomResizeState</a></span>()</code>
<div class="block">Returns the room resize state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectionMoveState--">getSelectionMoveState</a></span>()</code>
<div class="block">Returns the selection move state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectionState--">getSelectionState</a></span>()</code>
<div class="block">Returns the selection state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getWallArcExtentState--">getWallArcExtentState</a></span>()</code>
<div class="block">Returns the wall arc extent state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getWallCreationState--">getWallCreationState</a></span>()</code>
<div class="block">Returns the wall creation state.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getWallDrawingState--">getWallDrawingState</a></span>()</code>
<div class="block">Returns the wall drawing state.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getWallResizeState--">getWallResizeState</a></span>()</code>
<div class="block">Returns the wall resize state.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setState-com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState-">setState</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;state)</code>
<div class="block">Changes current state of controller.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#ControllerStateDecorator-com.eteks.sweethome3d.viewcontroller.PlanController.ControllerState-">ControllerStateDecorator</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.ControllerState</a>&nbsp;state)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html" title="class in com.eteks.sweethome3d.viewcontroller">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/viewcontroller/class-use/PlanController.ControllerState.html" target="_top">Frames</a></li>
<li><a href="PlanController.ControllerState.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
