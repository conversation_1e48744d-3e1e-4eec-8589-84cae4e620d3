<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>HomeController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":42,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeController.html" target="_top">Frames</a></li>
<li><a href="HomeController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class HomeController" class="title">Class HomeController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.HomeController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html" title="class in com.eteks.sweethome3d.plugin">HomePluginController</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for the home view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-">HomeController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates the controller of home view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">HomeController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates the controller of home view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">HomeController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates the controller of home view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">HomeController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates the controller of home view.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#about--">about</a></span>()</code>
<div class="block">Displays about dialog.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#addFurnitureToGroup--">addFurnitureToGroup</a></span>()</code>
<div class="block">Adds the selected furniture in catalog to the selected group and selects it.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#addHomeFurniture--">addHomeFurniture</a></span>()</code>
<div class="block">Adds the selected furniture in catalog to home and selects it.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Attaches the given <code>view</code> to home view.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#checkUpdates-boolean-">checkUpdates</a></span>(boolean&nbsp;displayOnlyIfNewUpdates)</code>
<div class="block">Checks if some application or libraries updates are available.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#close--">close</a></span>()</code>
<div class="block">Manages home close operation.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#close-java.lang.Runnable-">close</a></span>(java.lang.Runnable&nbsp;postCloseTask)</code>
<div class="block">Manages home close operation.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createPhoto--">createPhoto</a></span>()</code>
<div class="block">Controls the creation of photo-realistic images.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createPhotos--">createPhotos</a></span>()</code>
<div class="block">Controls the creation of multiple photo-realistic images at the stored cameras locations.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.TransferObserver-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType...-">createTransferData</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView.TransferObserver</a>&nbsp;observer,
                  <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>...&nbsp;dataTypes)</code>
<div class="block">Returns the transfer data matching the requested types.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createVideo--">createVideo</a></span>()</code>
<div class="block">Controls the creation of 3D videos.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#cut-java.util.List-">cut</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Deletes items and post a cut operation to undo support.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#delete--">delete</a></span>()</code>
<div class="block">Deletes the selection in the focused component.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteBackgroundImage--">deleteBackgroundImage</a></span>()</code>
<div class="block">Deletes home background image and posts and posts an undoable operation.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteCameras--">deleteCameras</a></span>()</code>
<div class="block">Prompts stored cameras in home to be deleted and deletes the ones selected by the user.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteRecentHomes--">deleteRecentHomes</a></span>()</code>
<div class="block">Deletes the list of recent homes in user preferences.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Detaches the given <code>view</code> from home view.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#disableMagnetism--">disableMagnetism</a></span>()</code>
<div class="block">Disables magnetism in preferences.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-float-float-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Adds items to home, moves them of (dx, dy)
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-float-float-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Adds items to home, moves them of (dx, dy)
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Level-float-float-java.lang.Float-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level,
    float&nbsp;dx,
    float&nbsp;dy,
    java.lang.Float&nbsp;dz)</code>
<div class="block">Adds items to home, moves them of (dx, dy, dz) delta vector
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Selectable-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;beforeItem)</code>
<div class="block">Adds items to home before the given item
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#dropFiles-java.util.List-float-float-">dropFiles</a></span>(java.util.List&lt;java.lang.String&gt;&nbsp;importableModels,
         float&nbsp;dx,
         float&nbsp;dy)</code>
<div class="block">Adds imported models to home, moves them of (dx, dy)
 and post a drop operation to undo support.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#editPreferences--">editPreferences</a></span>()</code>
<div class="block">Edits preferences and changes them if user agrees.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableActionsBoundToSelection--">enableActionsBoundToSelection</a></span>()</code>
<div class="block">Enables or disables action bound to selection.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableMagnetism--">enableMagnetism</a></span>()</code>
<div class="block">Enables magnetism in preferences.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enablePasteAction--">enablePasteAction</a></span>()</code>
<div class="block">Enables clipboard paste action if clipboard isn't empty.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableSelectAllAction--">enableSelectAllAction</a></span>()</code>
<div class="block">Enables select all action if home isn't empty.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exit--">exit</a></span>()</code>
<div class="block">Controls application exit.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToCSV--">exportToCSV</a></span>()</code>
<div class="block">Controls the export of the furniture list of current home to a CSV file.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToOBJ--">exportToOBJ</a></span>()</code>
<div class="block">Controls the export of the 3D view of current home to an OBJ file.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToSVG--">exportToSVG</a></span>()</code>
<div class="block">Controls the export of the current home plan to a SVG file.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#focusedViewChanged-com.eteks.sweethome3d.viewcontroller.View-">focusedViewChanged</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;focusedView)</code>
<div class="block">Updates actions when focused view changed.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getContentManager--">getContentManager</a></span>()</code>
<div class="block">Returns the content manager of this controller.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getFurnitureCatalogController--">getFurnitureCatalogController</a></span>()</code>
<div class="block">Returns the furniture catalog controller managed by this controller.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getFurnitureController--">getFurnitureController</a></span>()</code>
<div class="block">Returns the furniture controller managed by this controller.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getHomeController3D--">getHomeController3D</a></span>()</code>
<div class="block">Returns the controller of home 3D view.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getPlanController--">getPlanController</a></span>()</code>
<div class="block">Returns the controller of home plan.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getRecentHomes--">getRecentHomes</a></span>()</code>
<div class="block">Returns a list of displayable recent homes.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>protected javax.swing.undo.UndoableEditSupport</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getUndoableEditSupport--">getUndoableEditSupport</a></span>()</code>
<div class="block">Returns the undoable edit support managed by this controller.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getVersion--">getVersion</a></span>()</code>
<div class="block">Returns the version of the application for display purpose.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#help--">help</a></span>()</code>
<div class="block">Displays help window.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#hideBackgroundImage--">hideBackgroundImage</a></span>()</code>
<div class="block">Hides the home background image.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importBackgroundImage--">importBackgroundImage</a></span>()</code>
<div class="block">Displays the wizard that helps to import home background image.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurniture--">importFurniture</a></span>()</code>
<div class="block">Imports furniture to the catalog or home depending on the focused view.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurnitureLibrary--">importFurnitureLibrary</a></span>()</code>
<div class="block">Imports a furniture library chosen by the user.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurnitureLibrary-java.lang.String-">importFurnitureLibrary</a></span>(java.lang.String&nbsp;furnitureLibraryName)</code>
<div class="block">Imports a given furniture library.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importLanguageLibrary--">importLanguageLibrary</a></span>()</code>
<div class="block">Imports a language library chosen by the user.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importLanguageLibrary-java.lang.String-">importLanguageLibrary</a></span>(java.lang.String&nbsp;languageLibraryName)</code>
<div class="block">Imports a given language library.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexture--">importTexture</a></span>()</code>
<div class="block">Imports a texture to the texture catalog.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexturesLibrary--">importTexturesLibrary</a></span>()</code>
<div class="block">Imports a textures library chosen by the user.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexturesLibrary-java.lang.String-">importTexturesLibrary</a></span>(java.lang.String&nbsp;texturesLibraryName)</code>
<div class="block">Imports a given textures library.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#modifyBackgroundImage--">modifyBackgroundImage</a></span>()</code>
<div class="block">Displays the wizard that helps to change home background image.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#modifySelectedFurniture--">modifySelectedFurniture</a></span>()</code>
<div class="block">Modifies the selected furniture of the focused view.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#newHome--">newHome</a></span>()</code>
<div class="block">Creates a new home and adds it to application home list.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#newHomeFromExample--">newHomeFromExample</a></span>()</code>
<div class="block">Creates a new home from an example chosen by the user.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#open--">open</a></span>()</code>
<div class="block">Opens a home.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#open-java.lang.String-">open</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Opens a given <code>homeName</code>home.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#paste-java.util.List-">paste</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Adds items to home and posts a paste operation to undo support.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#pasteStyle--">pasteStyle</a></span>()</code>
<div class="block">Paste the style of the item in clipboard on selected items compatible with it.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#pasteToGroup--">pasteToGroup</a></span>()</code>
<div class="block">Paste the furniture in clipboard to the selected group in home.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#previewPrint--">previewPrint</a></span>()</code>
<div class="block">Controls the print preview.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#print--">print</a></span>()</code>
<div class="block">Controls the print of this home.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#printToPDF--">printToPDF</a></span>()</code>
<div class="block">Controls the print of this home in a PDF file.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#redo--">redo</a></span>()</code>
<div class="block">Redoes last undone operation.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#save--">save</a></span>()</code>
<div class="block">Saves the home managed by this controller.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAndCompress--">saveAndCompress</a></span>()</code>
<div class="block">Saves the home managed by this controller and compresses it.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAs--">saveAs</a></span>()</code>
<div class="block">Saves the home managed by this controller with a different name.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAs-com.eteks.sweethome3d.model.HomeRecorder.Type-java.lang.Runnable-">saveAs</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a>&nbsp;recorderType,
      java.lang.Runnable&nbsp;postSaveTask)</code>
<div class="block">Saves the home managed by this controller with a different name.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAsAndCompress--">saveAsAndCompress</a></span>()</code>
<div class="block">Saves the home managed by this controller with a different name and compresses it.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#selectAll--">selectAll</a></span>()</code>
<div class="block">Selects everything in the focused component.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setHomeProperty-java.lang.String-java.lang.String-">setHomeProperty</a></span>(java.lang.String&nbsp;propertyName,
               java.lang.String&nbsp;propertyValue)</code>
<div class="block">Controls the change of value of a property in home.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</code>
<div class="block">Displays a tip message dialog depending on the given mode and
 sets the active mode of the plan controller.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setupPage--">setupPage</a></span>()</code>
<div class="block">Controls page setup.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setVisualProperty-java.lang.String-java.lang.Object-">setVisualProperty</a></span>(java.lang.String&nbsp;propertyName,
                 java.lang.Object&nbsp;propertyValue)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty</code></a> should be replaced by a call to
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setHomeProperty-java.lang.String-java.lang.String-"><code>setHomeProperty(String, String)</code></a> to ensure the property can be easily saved and read.</span></div>
</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#showBackgroundImage--">showBackgroundImage</a></span>()</code>
<div class="block">Shows the home background image.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#storeCamera--">storeCamera</a></span>()</code>
<div class="block">Prompts a name for the current camera and stores it in home.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#undo--">undo</a></span>()</code>
<div class="block">Undoes last operation.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#zoomIn--">zoomIn</a></span>()</code>
<div class="block">Zooms in in plan.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#zoomOut--">zoomOut</a></span>()</code>
<div class="block">Zooms out in plan.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeController</h4>
<pre>public&nbsp;HomeController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
<div class="block">Creates the controller of home view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home edited by this controller and its view.</dd>
<dd><code>application</code> - the instance of current application.</dd>
<dd><code>viewFactory</code> - a factory able to create views.</dd>
<dd><code>contentManager</code> - the content manager of the application.</dd>
</dl>
</li>
</ul>
<a name="HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeController</h4>
<pre>public&nbsp;HomeController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</pre>
<div class="block">Creates the controller of home view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home edited by this controller and its view.</dd>
<dd><code>application</code> - the instance of current application.</dd>
<dd><code>viewFactory</code> - a factory able to create views.</dd>
</dl>
</li>
</ul>
<a name="HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeController</h4>
<pre>public&nbsp;HomeController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</pre>
<div class="block">Creates the controller of home view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home edited by this controller and its view.</dd>
<dd><code>preferences</code> - the preferences of the application.</dd>
<dd><code>viewFactory</code> - a factory able to create views.</dd>
</dl>
</li>
</ul>
<a name="HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeController</h4>
<pre>public&nbsp;HomeController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
<div class="block">Creates the controller of home view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home edited by this controller and its view.</dd>
<dd><code>preferences</code> - the preferences of the application.</dd>
<dd><code>viewFactory</code> - a factory able to create views.</dd>
<dd><code>contentManager</code> - the content manager of the application.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="getContentManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentManager</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;getContentManager()</pre>
<div class="block">Returns the content manager of this controller.</div>
</li>
</ul>
<a name="getFurnitureCatalogController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureCatalogController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;getFurnitureCatalogController()</pre>
<div class="block">Returns the furniture catalog controller managed by this controller.</div>
</li>
</ul>
<a name="getFurnitureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;getFurnitureController()</pre>
<div class="block">Returns the furniture controller managed by this controller.</div>
</li>
</ul>
<a name="getPlanController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlanController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;getPlanController()</pre>
<div class="block">Returns the controller of home plan.</div>
</li>
</ul>
<a name="getHomeController3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeController3D</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;getHomeController3D()</pre>
<div class="block">Returns the controller of home 3D view.</div>
</li>
</ul>
<a name="getUndoableEditSupport--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUndoableEditSupport</h4>
<pre>protected final&nbsp;javax.swing.undo.UndoableEditSupport&nbsp;getUndoableEditSupport()</pre>
<div class="block">Returns the undoable edit support managed by this controller.</div>
</li>
</ul>
<a name="enableActionsBoundToSelection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableActionsBoundToSelection</h4>
<pre>protected&nbsp;void&nbsp;enableActionsBoundToSelection()</pre>
<div class="block">Enables or disables action bound to selection.
 This method will be called when selection in plan or in catalog changes and when
 focused component or modification state in plan changes.</div>
</li>
</ul>
<a name="enablePasteAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enablePasteAction</h4>
<pre>public&nbsp;void&nbsp;enablePasteAction()</pre>
<div class="block">Enables clipboard paste action if clipboard isn't empty.</div>
</li>
</ul>
<a name="enableSelectAllAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableSelectAllAction</h4>
<pre>protected&nbsp;void&nbsp;enableSelectAllAction()</pre>
<div class="block">Enables select all action if home isn't empty.</div>
</li>
</ul>
<a name="addHomeFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addHomeFurniture</h4>
<pre>public&nbsp;void&nbsp;addHomeFurniture()</pre>
<div class="block">Adds the selected furniture in catalog to home and selects it.</div>
</li>
</ul>
<a name="addFurnitureToGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurnitureToGroup</h4>
<pre>public&nbsp;void&nbsp;addFurnitureToGroup()</pre>
<div class="block">Adds the selected furniture in catalog to the selected group and selects it.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="modifySelectedFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedFurniture</h4>
<pre>public&nbsp;void&nbsp;modifySelectedFurniture()</pre>
<div class="block">Modifies the selected furniture of the focused view.</div>
</li>
</ul>
<a name="importLanguageLibrary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importLanguageLibrary</h4>
<pre>public&nbsp;void&nbsp;importLanguageLibrary()</pre>
<div class="block">Imports a language library chosen by the user.</div>
</li>
</ul>
<a name="importLanguageLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importLanguageLibrary</h4>
<pre>public&nbsp;void&nbsp;importLanguageLibrary(java.lang.String&nbsp;languageLibraryName)</pre>
<div class="block">Imports a given language library.</div>
</li>
</ul>
<a name="importFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importFurniture</h4>
<pre>public&nbsp;void&nbsp;importFurniture()</pre>
<div class="block">Imports furniture to the catalog or home depending on the focused view.</div>
</li>
</ul>
<a name="importFurnitureLibrary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importFurnitureLibrary</h4>
<pre>public&nbsp;void&nbsp;importFurnitureLibrary()</pre>
<div class="block">Imports a furniture library chosen by the user.</div>
</li>
</ul>
<a name="importFurnitureLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importFurnitureLibrary</h4>
<pre>public&nbsp;void&nbsp;importFurnitureLibrary(java.lang.String&nbsp;furnitureLibraryName)</pre>
<div class="block">Imports a given furniture library.</div>
</li>
</ul>
<a name="importTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importTexture</h4>
<pre>public&nbsp;void&nbsp;importTexture()</pre>
<div class="block">Imports a texture to the texture catalog.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="importTexturesLibrary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importTexturesLibrary</h4>
<pre>public&nbsp;void&nbsp;importTexturesLibrary()</pre>
<div class="block">Imports a textures library chosen by the user.</div>
</li>
</ul>
<a name="importTexturesLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importTexturesLibrary</h4>
<pre>public&nbsp;void&nbsp;importTexturesLibrary(java.lang.String&nbsp;texturesLibraryName)</pre>
<div class="block">Imports a given textures library.</div>
</li>
</ul>
<a name="undo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>undo</h4>
<pre>public&nbsp;void&nbsp;undo()</pre>
<div class="block">Undoes last operation.</div>
</li>
</ul>
<a name="redo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>redo</h4>
<pre>public&nbsp;void&nbsp;redo()</pre>
<div class="block">Redoes last undone operation.</div>
</li>
</ul>
<a name="cut-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cut</h4>
<pre>public&nbsp;void&nbsp;cut(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Deletes items and post a cut operation to undo support.</div>
</li>
</ul>
<a name="paste-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paste</h4>
<pre>public&nbsp;void&nbsp;paste(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Adds items to home and posts a paste operation to undo support.</div>
</li>
</ul>
<a name="drop-java.util.List-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drop</h4>
<pre>public&nbsp;void&nbsp;drop(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                 float&nbsp;dx,
                 float&nbsp;dy)</pre>
<div class="block">Adds items to home, moves them of (dx, dy)
 and posts a drop operation to undo support.</div>
</li>
</ul>
<a name="drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drop</h4>
<pre>public&nbsp;void&nbsp;drop(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
                 float&nbsp;dx,
                 float&nbsp;dy)</pre>
<div class="block">Adds items to home, moves them of (dx, dy)
 and posts a drop operation to undo support.</div>
</li>
</ul>
<a name="drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Level-float-float-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drop</h4>
<pre>public&nbsp;void&nbsp;drop(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
                 <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level,
                 float&nbsp;dx,
                 float&nbsp;dy,
                 java.lang.Float&nbsp;dz)</pre>
<div class="block">Adds items to home, moves them of (dx, dy, dz) delta vector
 and posts a drop operation to undo support.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drop</h4>
<pre>public&nbsp;void&nbsp;drop(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
                 <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;beforeItem)</pre>
<div class="block">Adds items to home before the given item
 and posts a drop operation to undo support.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.3</dd>
</dl>
</li>
</ul>
<a name="dropFiles-java.util.List-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dropFiles</h4>
<pre>public&nbsp;void&nbsp;dropFiles(java.util.List&lt;java.lang.String&gt;&nbsp;importableModels,
                      float&nbsp;dx,
                      float&nbsp;dy)</pre>
<div class="block">Adds imported models to home, moves them of (dx, dy)
 and post a drop operation to undo support.</div>
</li>
</ul>
<a name="pasteToGroup--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pasteToGroup</h4>
<pre>public&nbsp;void&nbsp;pasteToGroup()</pre>
<div class="block">Paste the furniture in clipboard to the selected group in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="pasteStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pasteStyle</h4>
<pre>public&nbsp;void&nbsp;pasteStyle()</pre>
<div class="block">Paste the style of the item in clipboard on selected items compatible with it.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.TransferObserver-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTransferData</h4>
<pre>public&nbsp;void&nbsp;createTransferData(<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView.TransferObserver</a>&nbsp;observer,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>...&nbsp;dataTypes)</pre>
<div class="block">Returns the transfer data matching the requested types.</div>
</li>
</ul>
<a name="delete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>delete</h4>
<pre>public&nbsp;void&nbsp;delete()</pre>
<div class="block">Deletes the selection in the focused component.</div>
</li>
</ul>
<a name="focusedViewChanged-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>focusedViewChanged</h4>
<pre>public&nbsp;void&nbsp;focusedViewChanged(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;focusedView)</pre>
<div class="block">Updates actions when focused view changed.</div>
</li>
</ul>
<a name="selectAll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectAll</h4>
<pre>public&nbsp;void&nbsp;selectAll()</pre>
<div class="block">Selects everything in the focused component.</div>
</li>
</ul>
<a name="newHome--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHome</h4>
<pre>public&nbsp;void&nbsp;newHome()</pre>
<div class="block">Creates a new home and adds it to application home list.</div>
</li>
</ul>
<a name="newHomeFromExample--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>newHomeFromExample</h4>
<pre>public&nbsp;void&nbsp;newHomeFromExample()</pre>
<div class="block">Creates a new home from an example chosen by the user.</div>
</li>
</ul>
<a name="open--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;void&nbsp;open()</pre>
<div class="block">Opens a home. This method displays an <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showOpenDialog--"><code>open dialog</code></a>
 in view, reads the home from the chosen name and adds it to application home list.</div>
</li>
</ul>
<a name="open-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>open</h4>
<pre>public&nbsp;void&nbsp;open(java.lang.String&nbsp;homeName)</pre>
<div class="block">Opens a given <code>homeName</code>home.</div>
</li>
</ul>
<a name="getRecentHomes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRecentHomes</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getRecentHomes()</pre>
<div class="block">Returns a list of displayable recent homes.</div>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;java.lang.String&nbsp;getVersion()</pre>
<div class="block">Returns the version of the application for display purpose.</div>
</li>
</ul>
<a name="deleteRecentHomes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteRecentHomes</h4>
<pre>public&nbsp;void&nbsp;deleteRecentHomes()</pre>
<div class="block">Deletes the list of recent homes in user preferences.</div>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close()</pre>
<div class="block">Manages home close operation. If the home managed by this controller is modified,
 this method will <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmSave-java.lang.String-"><code>confirm</code></a>
 in view whether home should be saved. Once home is actually saved,
 home is removed from application homes list.</div>
</li>
</ul>
<a name="close-java.lang.Runnable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close(java.lang.Runnable&nbsp;postCloseTask)</pre>
<div class="block">Manages home close operation. If the home managed by this controller is modified,
 this method will <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmSave-java.lang.String-"><code>confirm</code></a>
 in view whether home should be saved. Once home is actually saved,
 home is removed from application homes list and <code>postCloseTask</code>
 is called if it's not <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="save--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>save</h4>
<pre>public&nbsp;void&nbsp;save()</pre>
<div class="block">Saves the home managed by this controller. If home name doesn't exist,
 this method will act as <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAs--"><code>saveAs</code></a> method.</div>
</li>
</ul>
<a name="saveAs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveAs</h4>
<pre>public&nbsp;void&nbsp;saveAs()</pre>
<div class="block">Saves the home managed by this controller with a different name.
 This method displays a <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showSaveDialog-java.lang.String-"><code>save dialog</code></a> in  view,
 and saves home with the chosen name if any.</div>
</li>
</ul>
<a name="saveAs-com.eteks.sweethome3d.model.HomeRecorder.Type-java.lang.Runnable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveAs</h4>
<pre>protected&nbsp;void&nbsp;saveAs(<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a>&nbsp;recorderType,
                      java.lang.Runnable&nbsp;postSaveTask)</pre>
<div class="block">Saves the home managed by this controller with a different name.
 Once home is actually saved, home is removed from application homes list
 and <code>postCloseTask</code> is called if it's not <code>null</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="saveAndCompress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveAndCompress</h4>
<pre>public&nbsp;void&nbsp;saveAndCompress()</pre>
<div class="block">Saves the home managed by this controller and compresses it. If home name doesn't exist,
 this method will prompt user to choose a home name.</div>
</li>
</ul>
<a name="saveAsAndCompress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>saveAsAndCompress</h4>
<pre>public&nbsp;void&nbsp;saveAsAndCompress()</pre>
<div class="block">Saves the home managed by this controller with a different name and compresses it.
 This method displays a <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showSaveDialog-java.lang.String-"><code>save dialog</code></a> in  view,
 and saves home with the chosen name if any.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="exportToCSV--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToCSV</h4>
<pre>public&nbsp;void&nbsp;exportToCSV()</pre>
<div class="block">Controls the export of the furniture list of current home to a CSV file.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="exportToSVG--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToSVG</h4>
<pre>public&nbsp;void&nbsp;exportToSVG()</pre>
<div class="block">Controls the export of the current home plan to a SVG file.</div>
</li>
</ul>
<a name="exportToOBJ--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToOBJ</h4>
<pre>public&nbsp;void&nbsp;exportToOBJ()</pre>
<div class="block">Controls the export of the 3D view of current home to an OBJ file.</div>
</li>
</ul>
<a name="createPhotos--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPhotos</h4>
<pre>public&nbsp;void&nbsp;createPhotos()</pre>
<div class="block">Controls the creation of multiple photo-realistic images at the stored cameras locations.</div>
</li>
</ul>
<a name="createPhoto--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPhoto</h4>
<pre>public&nbsp;void&nbsp;createPhoto()</pre>
<div class="block">Controls the creation of photo-realistic images.</div>
</li>
</ul>
<a name="createVideo--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createVideo</h4>
<pre>public&nbsp;void&nbsp;createVideo()</pre>
<div class="block">Controls the creation of 3D videos.</div>
</li>
</ul>
<a name="setupPage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setupPage</h4>
<pre>public&nbsp;void&nbsp;setupPage()</pre>
<div class="block">Controls page setup.</div>
</li>
</ul>
<a name="previewPrint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>previewPrint</h4>
<pre>public&nbsp;void&nbsp;previewPrint()</pre>
<div class="block">Controls the print preview.</div>
</li>
</ul>
<a name="print--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;void&nbsp;print()</pre>
<div class="block">Controls the print of this home.</div>
</li>
</ul>
<a name="printToPDF--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>printToPDF</h4>
<pre>public&nbsp;void&nbsp;printToPDF()</pre>
<div class="block">Controls the print of this home in a PDF file.</div>
</li>
</ul>
<a name="exit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exit</h4>
<pre>public&nbsp;void&nbsp;exit()</pre>
<div class="block">Controls application exit. If any home in application homes list is modified,
 the user will be <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmExit--"><code>prompted</code></a> in view whether he wants
 to discard his modifications or not.</div>
</li>
</ul>
<a name="editPreferences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>editPreferences</h4>
<pre>public&nbsp;void&nbsp;editPreferences()</pre>
<div class="block">Edits preferences and changes them if user agrees.</div>
</li>
</ul>
<a name="enableMagnetism--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableMagnetism</h4>
<pre>public&nbsp;void&nbsp;enableMagnetism()</pre>
<div class="block">Enables magnetism in preferences.</div>
</li>
</ul>
<a name="disableMagnetism--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableMagnetism</h4>
<pre>public&nbsp;void&nbsp;disableMagnetism()</pre>
<div class="block">Disables magnetism in preferences.</div>
</li>
</ul>
<a name="setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMode</h4>
<pre>public&nbsp;void&nbsp;setMode(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</pre>
<div class="block">Displays a tip message dialog depending on the given mode and
 sets the active mode of the plan controller.</div>
</li>
</ul>
<a name="importBackgroundImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importBackgroundImage</h4>
<pre>public&nbsp;void&nbsp;importBackgroundImage()</pre>
<div class="block">Displays the wizard that helps to import home background image.</div>
</li>
</ul>
<a name="modifyBackgroundImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifyBackgroundImage</h4>
<pre>public&nbsp;void&nbsp;modifyBackgroundImage()</pre>
<div class="block">Displays the wizard that helps to change home background image.</div>
</li>
</ul>
<a name="hideBackgroundImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hideBackgroundImage</h4>
<pre>public&nbsp;void&nbsp;hideBackgroundImage()</pre>
<div class="block">Hides the home background image.</div>
</li>
</ul>
<a name="showBackgroundImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showBackgroundImage</h4>
<pre>public&nbsp;void&nbsp;showBackgroundImage()</pre>
<div class="block">Shows the home background image.</div>
</li>
</ul>
<a name="deleteBackgroundImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteBackgroundImage</h4>
<pre>public&nbsp;void&nbsp;deleteBackgroundImage()</pre>
<div class="block">Deletes home background image and posts and posts an undoable operation.</div>
</li>
</ul>
<a name="zoomOut--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomOut</h4>
<pre>public&nbsp;void&nbsp;zoomOut()</pre>
<div class="block">Zooms out in plan.</div>
</li>
</ul>
<a name="zoomIn--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>zoomIn</h4>
<pre>public&nbsp;void&nbsp;zoomIn()</pre>
<div class="block">Zooms in in plan.</div>
</li>
</ul>
<a name="storeCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>storeCamera</h4>
<pre>public&nbsp;void&nbsp;storeCamera()</pre>
<div class="block">Prompts a name for the current camera and stores it in home.</div>
</li>
</ul>
<a name="deleteCameras--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteCameras</h4>
<pre>public&nbsp;void&nbsp;deleteCameras()</pre>
<div class="block">Prompts stored cameras in home to be deleted and deletes the ones selected by the user.</div>
</li>
</ul>
<a name="detachView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detachView</h4>
<pre>public&nbsp;void&nbsp;detachView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</pre>
<div class="block">Detaches the given <code>view</code> from home view.</div>
</li>
</ul>
<a name="attachView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>attachView</h4>
<pre>public&nbsp;void&nbsp;attachView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</pre>
<div class="block">Attaches the given <code>view</code> to home view.</div>
</li>
</ul>
<a name="help--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>help</h4>
<pre>public&nbsp;void&nbsp;help()</pre>
<div class="block">Displays help window.</div>
</li>
</ul>
<a name="about--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>about</h4>
<pre>public&nbsp;void&nbsp;about()</pre>
<div class="block">Displays about dialog.</div>
</li>
</ul>
<a name="setVisualProperty-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisualProperty</h4>
<pre>public&nbsp;void&nbsp;setVisualProperty(java.lang.String&nbsp;propertyName,
                              java.lang.Object&nbsp;propertyValue)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty</code></a> should be replaced by a call to
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setHomeProperty-java.lang.String-java.lang.String-"><code>setHomeProperty(String, String)</code></a> to ensure the property can be easily saved and read.</span></div>
<div class="block">Controls the change of value of a visual property in home.</div>
</li>
</ul>
<a name="setHomeProperty-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHomeProperty</h4>
<pre>public&nbsp;void&nbsp;setHomeProperty(java.lang.String&nbsp;propertyName,
                            java.lang.String&nbsp;propertyValue)</pre>
<div class="block">Controls the change of value of a property in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="checkUpdates-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>checkUpdates</h4>
<pre>public&nbsp;void&nbsp;checkUpdates(boolean&nbsp;displayOnlyIfNewUpdates)</pre>
<div class="block">Checks if some application or libraries updates are available.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeController.html" target="_top">Frames</a></li>
<li><a href="HomeController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
