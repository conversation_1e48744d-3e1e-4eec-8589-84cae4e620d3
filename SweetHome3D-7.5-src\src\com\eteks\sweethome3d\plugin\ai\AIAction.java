/*
 * AIAction.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.UserPreferences;
import com.eteks.sweethome3d.plugin.PluginAction;
import com.eteks.sweethome3d.viewcontroller.HomeController;
import javax.swing.SwingUtilities;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.lang.ref.WeakReference;

/**
 * Action that opens the AI analysis dialog when triggered.
 * This action is added to the Sweet Home 3D toolbar and menu.
 * Uses SweetHome 3D's internationalization system for proper localization.
 *
 * <AUTHOR> Kpassegna
 */
public class AIAction extends PluginAction {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  private final UserPreferences preferences;

  /**
   * Creates a new AI action.
   *
   * @param homeController The home controller
   */
  public AIAction(HomeController homeController) {
    this.homeController = homeController;
    this.preferences = homeController.getUserPreferences();
    this.aiIntegration = new AIIntegration(this.preferences);

    // Initialize action properties using SweetHome 3D's approach
    updateActionProperties();

    // Listen for language changes to update action properties
    this.preferences.addPropertyChangeListener(UserPreferences.Property.LANGUAGE,
        new LanguageChangeListener(this));
  }
  
  /**
   * Preferences property listener bound to this action with a weak reference to avoid
   * strong link between preferences and this action.
   */
  private static class LanguageChangeListener implements PropertyChangeListener {
    private final WeakReference<AIAction> aiAction;

    public LanguageChangeListener(AIAction aiAction) {
      this.aiAction = new WeakReference<AIAction>(aiAction);
    }

    public void propertyChange(PropertyChangeEvent ev) {
      // If action was garbage collected, remove this listener from preferences
      AIAction aiAction = this.aiAction.get();
      if (aiAction == null) {
        ((UserPreferences)ev.getSource()).removePropertyChangeListener(
            UserPreferences.Property.LANGUAGE, this);
      } else {
        aiAction.updateActionProperties();
      }
    }
  }

  /**
   * Updates action properties using localized strings from resource bundle.
   */
  private void updateActionProperties() {
    try {
      // Use UserPreferences to get localized strings
      String name = preferences.getLocalizedString(AIAction.class, "AIAction.Name");
      String shortDescription = preferences.getLocalizedString(AIAction.class, "AIAction.ShortDescription");
      String menu = preferences.getLocalizedString(AIAction.class, "AIAction.Menu");

      putPropertyValue(Property.NAME, name);
      putPropertyValue(Property.SHORT_DESCRIPTION, shortDescription);
      putPropertyValue(Property.MENU, menu);
      putPropertyValue(Property.TOOL_BAR, true);
      putPropertyValue(Property.ENABLED, true);

      // Set icon (placeholder - actual icon file should be added to resources)
      // putPropertyValue(Property.SMALL_ICON, new URLContent(getClass().getResource("resources/ai-icon-16.png")));
    } catch (IllegalArgumentException ex) {
      // Fallback to English if localized strings are not found
      putPropertyValue(Property.NAME, "AI Analysis");
      putPropertyValue(Property.SHORT_DESCRIPTION, "Analyze floor plan with AI");
      putPropertyValue(Property.MENU, "Tools");
      putPropertyValue(Property.TOOL_BAR, true);
      putPropertyValue(Property.ENABLED, true);
    }
  }

  /**
   * Executes the AI action.
   */
  @Override
  public void execute() {
    // Check if AI is configured
    if (!aiIntegration.isConfigured()) {
      showConfigurationDialog();
      return;
    }

    // Open AI chat dialog
    openAIChatDialog();
  }
  
  /**
   * Shows the AI configuration dialog.
   */
  private void showConfigurationDialog() {
    SwingUtilities.invokeLater(() -> {
      AISettingsDialog dialog = new AISettingsDialog(homeController, aiIntegration);
      dialog.setVisible(true);
      
      // If configuration was saved, try to execute the action again
      if (dialog.isConfigurationSaved()) {
        aiIntegration.reconfigure();
        execute();
      }
    });
  }
  
  /**
   * Opens the AI chat dialog for analysis.
   */
  private void openAIChatDialog() {
    SwingUtilities.invokeLater(() -> {
      Home home = homeController.getHome();
      if (home == null) {
        return;
      }
      
      AIChatDialog chatDialog = new AIChatDialog(homeController, aiIntegration);
      chatDialog.setVisible(true);
    });
  }
  
  /**
   * Returns the home controller.
   */
  protected HomeController getHomeController() {
    return homeController;
  }
  
  /**
   * Returns the AI integration instance.
   */
  protected AIIntegration getAIIntegration() {
    return aiIntegration;
  }
}
