<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>com.eteks.sweethome3d.model Class Hierarchy (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.eteks.sweethome3d.model Class Hierarchy (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/package-tree.html">Prev</a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package com.eteks.sweethome3d.model</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">BackgroundImage</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Baseboard</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/BoxBounds.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">BoxBounds</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">CatalogPieceOfFurniture</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a>, java.lang.Cloneable, java.lang.Comparable&lt;T&gt;, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>)
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">CatalogDoorOrWindow</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogLight.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">CatalogLight</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">CatalogShelfUnit</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html" title="interface in com.eteks.sweethome3d.model">ShelfUnit</a>)</li>
</ul>
</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">CatalogTexture</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a>, java.lang.Comparable&lt;T&gt;, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">CollectionChangeSupport</span></a>&lt;T&gt;</li>
<li type="circle">java.util.EventObject (implements java.io.Serializable)
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">CollectionEvent</span></a>&lt;T&gt;</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/SelectionEvent.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">SelectionEvent</span></a></li>
</ul>
</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">FurnitureCatalog</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">FurnitureCategory</span></a> (implements java.lang.Comparable&lt;T&gt;)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Home</span></a> (implements java.lang.Cloneable, java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeApplication</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeDescriptor</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeMaterial</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeObject</span></a> (implements java.lang.Cloneable, java.io.Serializable)
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Camera</span></a>
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/ObserverCamera.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">ObserverCamera</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>)</li>
</ul>
</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Compass</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">DimensionLine</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeEnvironment</span></a> (implements java.lang.Cloneable, java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomePieceOfFurniture</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>)
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeDoorOrWindow</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeFurnitureGroup</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeLight</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">Light</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeShelfUnit</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html" title="interface in com.eteks.sweethome3d.model">ShelfUnit</a>)</li>
</ul>
</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Label</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Level</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Polyline</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Room</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Wall</span></a> (implements com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>)</li>
</ul>
</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomePrint</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeTexture</span></a> (implements java.io.Serializable, com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">LightSource</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">ObjectProperty</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PatternsCatalog.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">PatternsCatalog</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Sash</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">TextStyle</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">TexturesCatalog</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">TexturesCategory</span></a> (implements java.lang.Comparable&lt;T&gt;)</li>
<li type="circle">java.lang.Throwable (implements java.io.Serializable)
<ul>
<li type="circle">java.lang.Exception
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">RecorderException</span></a>
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/DamagedHomeRecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">DamagedHomeRecorderException</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/InterruptedRecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">InterruptedRecorderException</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/NotEnoughSpaceRecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">NotEnoughSpaceRecorderException</span></a></li>
</ul>
</li>
<li type="circle">java.lang.RuntimeException
<ul>
<li type="circle">java.lang.IllegalArgumentException
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/IllegalHomonymException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">IllegalHomonymException</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Transformation</span></a> (implements java.io.Serializable)</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">UserPreferences</span></a></li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">CatalogItem</span></a></li>
<li type="circle">java.lang.Cloneable
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Selectable</span></a></li>
</ul>
</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Elevatable</span></a></li>
<li type="circle">java.util.EventListener
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">CollectionListener</span></a>&lt;T&gt;</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/SelectionListener.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">SelectionListener</span></a></li>
</ul>
</li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeRecorder</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Library</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">PieceOfFurniture</span></a>
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">DoorOrWindow</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Light</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">ShelfUnit</span></a></li>
</ul>
</li>
<li type="circle">java.io.Serializable
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Content</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">TextureImage</span></a></li>
</ul>
</li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.Object
<ul>
<li type="circle">java.lang.Enum&lt;E&gt; (implements java.lang.Comparable&lt;T&gt;, java.io.Serializable)
<ul>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Polyline.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Polyline.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Polyline.CapStyle</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Polyline.JoinStyle</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Polyline.ArrowStyle</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Polyline.DashStyle</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Level.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Level.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Camera.Lens</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Camera.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Camera.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">DimensionLine.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">CollectionEvent.Type</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Wall.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Wall.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Compass.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Compass.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.Type.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">ObjectProperty.Type</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/ObserverCamera.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">ObserverCamera.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">UserPreferences.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeDoorOrWindow.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomePieceOfFurniture.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomePieceOfFurniture.SortableProperty</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeEnvironment.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.DrawingMode.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeEnvironment.DrawingMode</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">LengthUnit</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/AspectRatio.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">AspectRatio</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">TextStyle.Alignment</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Label.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Label.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Home.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeLight.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeLight.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeRecorder.Type</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/Room.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Room.Property</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePrint.PaperOrientation.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomePrint.PaperOrientation</span></a></li>
<li type="circle">com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">HomeShelfUnit.Property</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/package-tree.html">Prev</a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
