<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>DefaultHomeInputStream (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DefaultHomeInputStream (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultHomeInputStream.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultHomeInputStream.html" target="_top">Frames</a></li>
<li><a href="DefaultHomeInputStream.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.FilterInputStream">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class DefaultHomeInputStream" class="title">Class DefaultHomeInputStream</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.io.InputStream</li>
<li>
<ul class="inheritance">
<li>java.io.FilterInputStream</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.DefaultHomeInputStream</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Closeable, java.lang.AutoCloseable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DefaultHomeInputStream</span>
extends java.io.FilterInputStream</pre>
<div class="block">An <code>InputStream</code> filter that reads a home from a stream
 at .sh3d file format.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io"><code>DefaultHomeOutputStream</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.io.FilterInputStream">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.io.FilterInputStream</h3>
<code>in</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#DefaultHomeInputStream-java.io.File-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.io.HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-boolean-">DefaultHomeInputStream</a></span>(java.io.File&nbsp;zipFile,
                      <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                      <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a>&nbsp;xmlHandler,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      boolean&nbsp;preferPreferencesContent)</code>
<div class="block">Creates a home input stream able to read a home and its content from the given file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#DefaultHomeInputStream-java.io.InputStream-">DefaultHomeInputStream</a></span>(java.io.InputStream&nbsp;in)</code>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#DefaultHomeInputStream-java.io.InputStream-com.eteks.sweethome3d.io.ContentRecording-">DefaultHomeInputStream</a></span>(java.io.InputStream&nbsp;in,
                      <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording)</code>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#DefaultHomeInputStream-java.io.InputStream-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.io.HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-boolean-">DefaultHomeInputStream</a></span>(java.io.InputStream&nbsp;in,
                      <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                      <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a>&nbsp;xmlHandler,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      boolean&nbsp;preferPreferencesContent)</code>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#DefaultHomeInputStream-java.io.InputStream-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.model.UserPreferences-boolean-">DefaultHomeInputStream</a></span>(java.io.InputStream&nbsp;in,
                      <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      boolean&nbsp;preferPreferencesContent)</code>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#isPrefixCorrect--">isPrefixCorrect</a></span>()</code>
<div class="block">Returns <code>true</code> if the prefix of the read input stream is as expected.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#readHome--">readHome</a></span>()</code>
<div class="block">Reads home from a zipped stream containing a <code>Home.xml</code> or <code>Home</code> entry,
 or if the stream isn't zipped, reads the input stream as a XML input stream.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#resolveObjectInputStreamObject-java.lang.Object-">resolveObjectInputStreamObject</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">Returns <code>obj</code> or a substitute of it during Home entry deserialization.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.FilterInputStream">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.io.FilterInputStream</h3>
<code>available, close, mark, markSupported, read, read, read, reset, skip</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DefaultHomeInputStream-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultHomeInputStream</h4>
<pre>public&nbsp;DefaultHomeInputStream(java.io.InputStream&nbsp;in)
                       throws java.io.IOException</pre>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>. The dependencies of the read home included in the stream
 will be checked.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="DefaultHomeInputStream-java.io.InputStream-com.eteks.sweethome3d.io.ContentRecording-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultHomeInputStream</h4>
<pre>public&nbsp;DefaultHomeInputStream(java.io.InputStream&nbsp;in,
                              <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording)
                       throws java.io.IOException</pre>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="DefaultHomeInputStream-java.io.InputStream-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.model.UserPreferences-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultHomeInputStream</h4>
<pre>public&nbsp;DefaultHomeInputStream(java.io.InputStream&nbsp;in,
                              <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                              boolean&nbsp;preferPreferencesContent)</pre>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>. If <code>preferences</code> isn't <code>null</code>
 and <code>preferPreferencesContent</code> is <code>true</code>,
 the furniture and textures contents it references will replace the one of
 the read home when they are equal. If <code>preferPreferencesContent</code>
 is <code>false</code>, preferences content will be used only
 to replace damaged equal content that might be found in read home files.</div>
</li>
</ul>
<a name="DefaultHomeInputStream-java.io.InputStream-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.io.HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultHomeInputStream</h4>
<pre>public&nbsp;DefaultHomeInputStream(java.io.InputStream&nbsp;in,
                              <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                              <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a>&nbsp;xmlHandler,
                              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                              boolean&nbsp;preferPreferencesContent)</pre>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>in</code> - the zipped stream from which the home will be read</dd>
<dd><code>contentRecording</code> - specifies whether content referenced by the read home is included
            or not in the stream.</dd>
<dd><code>xmlHandler</code> - SAX handler used to parse <code>Home.xml</code> entry when present, or
            <code>null</code> if only <code>Home</code> entry should taken into account.</dd>
<dd><code>preferences</code> - if not <code>null</code> and <code>preferPreferencesContent</code>
            is <code>true</code>, the furniture and textures contents it references will
            replace the one of the read home when they are equal.
            If <code>preferPreferencesContent</code> is <code>false</code>, preferences
            content will be used only to replace damaged equal content that might be found
            in read home files.</dd>
<dd><code>preferPreferencesContent</code> - if <code>true</code>, the returned home will reference
            contents in preferences when equal.</dd>
</dl>
</li>
</ul>
<a name="DefaultHomeInputStream-java.io.File-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.io.HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DefaultHomeInputStream</h4>
<pre>public&nbsp;DefaultHomeInputStream(java.io.File&nbsp;zipFile,
                              <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                              <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a>&nbsp;xmlHandler,
                              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                              boolean&nbsp;preferPreferencesContent)
                       throws java.io.FileNotFoundException</pre>
<div class="block">Creates a home input stream able to read a home and its content from the given file.
 The file will be read directly without using a temporary copy except if it contains some invalid entries.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>zipFile</code> - the zipped file from which the home will be read</dd>
<dd><code>contentRecording</code> - specifies whether content referenced by the read home is included
            or not in the stream.</dd>
<dd><code>xmlHandler</code> - SAX handler used to parse <code>Home.xml</code> entry when present, or
            <code>null</code> if only <code>Home</code> entry should taken into account.</dd>
<dd><code>preferences</code> - if not <code>null</code> and <code>preferPreferencesContent</code>
            is <code>true</code>, the furniture and textures contents it references will
            replace the one of the read home when they are equal.
            If <code>preferPreferencesContent</code> is <code>false</code>, preferences
            content will be used only to replace damaged equal content that might be found
            in read home files.</dd>
<dd><code>preferPreferencesContent</code> - if <code>true</code>, the returned home will reference
            contents in preferences when equal.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code> - if the given file can't be opened</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isPrefixCorrect--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPrefixCorrect</h4>
<pre>public&nbsp;boolean&nbsp;isPrefixCorrect()
                        throws java.io.IOException</pre>
<div class="block">Returns <code>true</code> if the prefix of the read input stream is as expected.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="readHome--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readHome</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;readHome()
              throws java.io.IOException,
                     java.lang.ClassNotFoundException</pre>
<div class="block">Reads home from a zipped stream containing a <code>Home.xml</code> or <code>Home</code> entry,
 or if the stream isn't zipped, reads the input stream as a XML input stream.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
<dd><code>java.lang.ClassNotFoundException</code></dd>
</dl>
</li>
</ul>
<a name="resolveObjectInputStreamObject-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>resolveObjectInputStreamObject</h4>
<pre>protected&nbsp;java.lang.Object&nbsp;resolveObjectInputStreamObject(java.lang.Object&nbsp;obj)
                                                   throws java.io.IOException</pre>
<div class="block">Returns <code>obj</code> or a substitute of it during Home entry deserialization.
 This method does nothing and returns the <code>obj</code> in parameter.
 It can be overridden by sub classes if needed.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultHomeInputStream.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultHomeInputStream.html" target="_top">Frames</a></li>
<li><a href="DefaultHomeInputStream.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.FilterInputStream">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
