<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>HomePieceOfFurniture3D (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomePieceOfFurniture3D (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePieceOfFurniture3D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Ground3D.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/Label3D.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture3D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.j3d.Object3DBranch">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class HomePieceOfFurniture3D" class="title">Class HomePieceOfFurniture3D</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.SceneGraphObject</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.Node</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.Group</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.BranchGroup</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d.Object3DBranch</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.HomePieceOfFurniture3D</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomePieceOfFurniture3D</span>
extends <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d">Object3DBranch</a></pre>
<div class="block">Root of piece of furniture branch.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.j3d.Object3DBranch">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.eteks.sweethome3d.j3d.<a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d">Object3DBranch</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#DEFAULT_AMBIENT_COLOR">DEFAULT_AMBIENT_COLOR</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#DEFAULT_COLOR">DEFAULT_COLOR</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#DEFAULT_MATERIAL">DEFAULT_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#LINE_WIDTH_SCALE_FACTOR">LINE_WIDTH_SCALE_FACTOR</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#OUTLINE_COLORING_ATTRIBUTES">OUTLINE_COLORING_ATTRIBUTES</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#OUTLINE_LINE_ATTRIBUTES">OUTLINE_LINE_ATTRIBUTES</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#OUTLINE_POLYGON_ATTRIBUTES">OUTLINE_POLYGON_ATTRIBUTES</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#SELECTION_COLORING_ATTRIBUTES">SELECTION_COLORING_ATTRIBUTES</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#SELECTION_LINE_ATTRIBUTES">SELECTION_LINE_ATTRIBUTES</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#SELECTION_POLYGON_ATTRIBUTES">SELECTION_POLYGON_ATTRIBUTES</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#SELECTION_TRANSPARENCY_ATTRIBUTES">SELECTION_TRANSPARENCY_ATTRIBUTES</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.media.j3d.BranchGroup">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.media.j3d.BranchGroup</h3>
<code>ALLOW_DETACH</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.media.j3d.Group">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.media.j3d.Group</h3>
<code>ALLOW_CHILDREN_EXTEND, ALLOW_CHILDREN_READ, ALLOW_CHILDREN_WRITE, ALLOW_COLLISION_BOUNDS_READ, ALLOW_COLLISION_BOUNDS_WRITE</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.media.j3d.Node">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.media.j3d.Node</h3>
<code>ALLOW_AUTO_COMPUTE_BOUNDS_READ, ALLOW_AUTO_COMPUTE_BOUNDS_WRITE, ALLOW_BOUNDS_READ, ALLOW_BOUNDS_WRITE, ALLOW_COLLIDABLE_READ, ALLOW_COLLIDABLE_WRITE, ALLOW_LOCAL_TO_VWORLD_READ, ALLOW_LOCALE_READ, ALLOW_PARENT_READ, ALLOW_PICKABLE_READ, ALLOW_PICKABLE_WRITE, ENABLE_COLLISION_REPORTING, ENABLE_PICK_REPORTING</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html#HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-">HomePieceOfFurniture3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                      <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html#HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-boolean-boolean-">HomePieceOfFurniture3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                      <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      boolean&nbsp;ignoreDrawingMode,
                      boolean&nbsp;waitModelAndTextureLoadingEnd)</code>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html#HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-boolean-">HomePieceOfFurniture3D</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                      <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.lang.Object&nbsp;context,
                      boolean&nbsp;ignoreDrawingMode,
                      boolean&nbsp;waitModelAndTextureLoadingEnd)</code>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html#update--">update</a></span>()</code>
<div class="block">Updates this branch from the home piece it manages.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.j3d.Object3DBranch">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.j3d.<a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d">Object3DBranch</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getAreaPoints-java.awt.geom.Area-float-boolean-">getAreaPoints</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getAreaPoints-java.awt.geom.Area-java.util.List-java.util.List-float-boolean-">getAreaPoints</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getContext--">getContext</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getContextTexture-javax.media.j3d.Texture-java.lang.Object-">getContextTexture</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getHome--">getHome</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getHomeTextureClone-javax.media.j3d.Texture-com.eteks.sweethome3d.model.Home-">getHomeTextureClone</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getMaterial-java.lang.Integer-java.lang.Integer-float-">getMaterial</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getSelectionAppearance--">getSelectionAppearance</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getShape-float:A:A-">getShape</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributes-com.eteks.sweethome3d.model.HomeTexture-">getTextureAttributes</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributes-com.eteks.sweethome3d.model.HomeTexture-boolean-">getTextureAttributes</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributesFittingArea-com.eteks.sweethome3d.model.HomeTexture-float:A:A-boolean-">getTextureAttributesFittingArea</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getUserPreferences--">getUserPreferences</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.BranchGroup">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.BranchGroup</h3>
<code>cloneNode, compile, detach, pickAll, pickAll, pickAllSorted, pickAllSorted, pickAny, pickAny, pickClosest, pickClosest</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.Group">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.Group</h3>
<code>addChild, getAllChildren, getAlternateCollisionTarget, getChild, getCollisionBounds, indexOfChild, insertChild, moveTo, numChildren, removeAllChildren, removeChild, removeChild, setAlternateCollisionTarget, setChild, setCollisionBounds</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.Node">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.Node</h3>
<code>cloneTree, cloneTree, cloneTree, cloneTree, cloneTree, cloneTree, duplicateNode, getBounds, getBoundsAutoCompute, getCollidable, getLocale, getLocalToVworld, getLocalToVworld, getParent, getPickable, setBounds, setBoundsAutoCompute, setCollidable, setPickable</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.SceneGraphObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.SceneGraphObject</h3>
<code>clearCapability, clearCapabilityIsFrequent, duplicateSceneGraphObject, getCapability, getCapabilityIsFrequent, getName, getUserData, isCompiled, isLive, setCapability, setCapabilityIsFrequent, setName, setUserData, toString, updateNodeReferences</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomePieceOfFurniture3D</h4>
<pre>public&nbsp;HomePieceOfFurniture3D(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                              <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</pre>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</li>
</ul>
<a name="HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomePieceOfFurniture3D</h4>
<pre>public&nbsp;HomePieceOfFurniture3D(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                              <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                              boolean&nbsp;ignoreDrawingMode,
                              boolean&nbsp;waitModelAndTextureLoadingEnd)</pre>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</li>
</ul>
<a name="HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomePieceOfFurniture3D</h4>
<pre>public&nbsp;HomePieceOfFurniture3D(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                              <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                              java.lang.Object&nbsp;context,
                              boolean&nbsp;ignoreDrawingMode,
                              boolean&nbsp;waitModelAndTextureLoadingEnd)</pre>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="update--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>update</h4>
<pre>public&nbsp;void&nbsp;update()</pre>
<div class="block">Updates this branch from the home piece it manages.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#update--">update</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d">Object3DBranch</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePieceOfFurniture3D.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Ground3D.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/Label3D.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture3D.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.j3d.Object3DBranch">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
