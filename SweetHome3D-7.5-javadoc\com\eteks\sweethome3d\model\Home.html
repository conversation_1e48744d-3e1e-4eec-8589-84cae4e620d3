<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>Home (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Home (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":9,"i31":10,"i32":10,"i33":10,"i34":10,"i35":9,"i36":10,"i37":10,"i38":10,"i39":42,"i40":10,"i41":9,"i42":42,"i43":10,"i44":10,"i45":10,"i46":9,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":9,"i53":10,"i54":10,"i55":10,"i56":10,"i57":9,"i58":10,"i59":10,"i60":10,"i61":10,"i62":9,"i63":10,"i64":10,"i65":42,"i66":10,"i67":10,"i68":9,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":42,"i94":10,"i95":42,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":42};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Home.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Home.html" target="_top">Frames</a></li>
<li><a href="Home.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class Home" class="title">Class Home</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.Home</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Home</span>
extends java.lang.Object
implements java.io.Serializable, java.lang.Cloneable</pre>
<div class="block">The home managed by the application with its furniture and walls.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.Home">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model">Home.Property</a></span></code>
<div class="block">The properties of a home that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#CURRENT_VERSION">CURRENT_VERSION</a></span></code>
<div class="block">The current version of this home.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colLast" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#Home--">Home</a></span>()</code>
<div class="block">Creates a home with no furniture, no walls,
 and a height equal to 250 cm.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#Home-float-">Home</a></span>(float&nbsp;wallHeight)</code>
<div class="block">Creates a home with no furniture and no walls.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#Home-com.eteks.sweethome3d.model.Home-">Home</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Creates a home from an other one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#Home-java.util.List-">Home</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</code>
<div class="block">Creates a home with the given <code>furniture</code>,
 no walls and a height equal to 250 cm.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addDimensionLine-com.eteks.sweethome3d.model.DimensionLine-">addDimensionLine</a></span>(<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine)</code>
<div class="block">Adds the given dimension line to the set of dimension lines of this home.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addDimensionLinesListener-com.eteks.sweethome3d.model.CollectionListener-">addDimensionLinesListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the dimension line <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addFurnitureListener-com.eteks.sweethome3d.model.CollectionListener-">addFurnitureListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the furniture <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addLabel-com.eteks.sweethome3d.model.Label-">addLabel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)</code>
<div class="block">Adds the given label to the set of labels of this home.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addLabelsListener-com.eteks.sweethome3d.model.CollectionListener-">addLabelsListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the label <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addLevel-com.eteks.sweethome3d.model.Level-">addLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Adds the given <code>level</code> to the list of levels of this home.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addLevelsListener-com.eteks.sweethome3d.model.CollectionListener-">addLevelsListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the level <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">addPieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Adds the <code>piece</code> in parameter to this home at the end of the furniture list.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-int-">addPieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                   int&nbsp;index)</code>
<div class="block">Adds the <code>piece</code> in parameter at a given <code>index</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addPieceOfFurnitureToGroup-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.HomeFurnitureGroup-int-">addPieceOfFurnitureToGroup</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                          <a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;group,
                          int&nbsp;index)</code>
<div class="block">Adds the <code>piece</code> in parameter at the <code>index</code> in the given <code>group</code>.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addPolyline-com.eteks.sweethome3d.model.Polyline-">addPolyline</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline)</code>
<div class="block">Adds a given <code>polyline</code> at the end of the polylines list of this home.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addPolyline-com.eteks.sweethome3d.model.Polyline-int-">addPolyline</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline,
           int&nbsp;index)</code>
<div class="block">Adds a <code>polyline</code> at a given <code>index</code> of the set of polylines of this home.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addPolylinesListener-com.eteks.sweethome3d.model.CollectionListener-">addPolylinesListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the polyline <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addPropertyChangeListener-com.eteks.sweethome3d.model.Home.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model">Home.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(java.lang.String&nbsp;propertyName,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this home for a specific property name.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addRoom-com.eteks.sweethome3d.model.Room-">addRoom</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room)</code>
<div class="block">Adds the given <code>room</code> at the end of the rooms list of this home.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addRoom-com.eteks.sweethome3d.model.Room-int-">addRoom</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
       int&nbsp;index)</code>
<div class="block">Adds the <code>room</code> in parameter at a given <code>index</code>.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addRoomsListener-com.eteks.sweethome3d.model.CollectionListener-">addRoomsListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the room <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addSelectionListener-com.eteks.sweethome3d.model.SelectionListener-">addSelectionListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/SelectionListener.html" title="interface in com.eteks.sweethome3d.model">SelectionListener</a>&nbsp;listener)</code>
<div class="block">Adds the selection <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addWall-com.eteks.sweethome3d.model.Wall-">addWall</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)</code>
<div class="block">Adds the given <code>wall</code> to the set of walls of this home.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#addWallsListener-com.eteks.sweethome3d.model.CollectionListener-">addWallsListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the wall <code>listener</code> in parameter to this home.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this home and the objects it contains.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#deleteDimensionLine-com.eteks.sweethome3d.model.DimensionLine-">deleteDimensionLine</a></span>(<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine)</code>
<div class="block">Removes the given dimension line from the set of dimension lines of this home.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#deleteLabel-com.eteks.sweethome3d.model.Label-">deleteLabel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)</code>
<div class="block">Removes the given label from the set of labels of this home.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#deleteLevel-com.eteks.sweethome3d.model.Level-">deleteLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Removes the given <code>level</code> from the set of levels of this home
 and all the furniture, walls, rooms, dimension lines and labels that belong to this level.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#deletePieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">deletePieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Deletes the <code>piece</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#deletePolyline-com.eteks.sweethome3d.model.Polyline-">deletePolyline</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline)</code>
<div class="block">Removes a given <code>polyline</code> from the set of polylines of this home.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#deleteRoom-com.eteks.sweethome3d.model.Room-">deleteRoom</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room)</code>
<div class="block">Removes the given <code>room</code> from the set of rooms of this home.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#deleteWall-com.eteks.sweethome3d.model.Wall-">deleteWall</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)</code>
<div class="block">Removes the given <code>wall</code> from the set of walls of this home.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#deselectItem-com.eteks.sweethome3d.model.Selectable-">deselectItem</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Deselects <code>item</code> if it's selected and notifies listeners selection change.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#duplicate-java.util.List-">duplicate</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a deep copy of home selectable <code>items</code>.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getBackgroundImage--">getBackgroundImage</a></span>()</code>
<div class="block">Returns the plan background image of this home.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getCamera--">getCamera</a></span>()</code>
<div class="block">Returns the camera used to display this home.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getCompass--">getCompass</a></span>()</code>
<div class="block">Returns the compass associated to this home.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>java.util.Collection&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getDimensionLines--">getDimensionLines</a></span>()</code>
<div class="block">Returns an unmodifiable collection of the dimension lines of this home.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getDimensionLinesSubList-java.util.List-">getDimensionLinesSubList</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only dimension lines.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model">HomeEnvironment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getEnvironment--">getEnvironment</a></span>()</code>
<div class="block">Returns the environment attributes of this home.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurniture--">getFurniture</a></span>()</code>
<div class="block">Returns an unmodifiable list of the furniture managed by this home.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureAdditionalProperties--">getFurnitureAdditionalProperties</a></span>()</code>
<div class="block">Returns the list of furniture additional properties which should be handled in the user interface.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedProperty--">getFurnitureSortedProperty</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedProperty--"><code>getFurnitureSortedProperty()</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>setFurnitureSortedProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedPropertyName--"><code>getFurnitureSortedPropertyName()</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedPropertyName-java.lang.String-"><code>setFurnitureSortedPropertyName(String)</code></a>
     to allow sorting on additional properties.</span></div>
</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedPropertyName--">getFurnitureSortedPropertyName</a></span>()</code>
<div class="block">Returns the furniture property name on which home is sorted or <code>null</code> if
 home furniture isn't sorted.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSubList-java.util.List-">getFurnitureSubList</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only home furniture.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisibleProperties--">getFurnitureVisibleProperties</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisibleProperties--"><code>getFurnitureVisibleProperties()</code></a> and <code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisiblePropertyNames--"><code>getFurnitureVisiblePropertyNames()</code></a> and <code>#setFurnitureSortedPropertyName(List<String>)</code>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisiblePropertyNames--">getFurnitureVisiblePropertyNames</a></span>()</code>
<div class="block">Returns an unmodifiable list of the furniture property names that are visible.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getHomeObjects--">getHomeObjects</a></span>()</code>
<div class="block">Returns all the mutable objects handled by this home.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>java.util.Collection&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getLabels--">getLabels</a></span>()</code>
<div class="block">Returns an unmodifiable collection of the labels of this home.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getLabelsSubList-java.util.List-">getLabelsSubList</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only labels.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getLevels--">getLevels</a></span>()</code>
<div class="block">Returns an unmodifiable collection of the levels of this home.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this home.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>java.lang.Number</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getNumericProperty-java.lang.String-">getNumericProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the numeric value of the property <code>name</code> associated with this home.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/ObserverCamera.html" title="class in com.eteks.sweethome3d.model">ObserverCamera</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getObserverCamera--">getObserverCamera</a></span>()</code>
<div class="block">Returns the camera used to display this home from an observer point of view.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getPolylines--">getPolylines</a></span>()</code>
<div class="block">Returns an unmodifiable collection of the polylines of this home.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getPolylinesSubList-java.util.List-">getPolylinesSubList</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only labels.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getPrint--">getPrint</a></span>()</code>
<div class="block">Returns the print attributes of this home.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getProperty-java.lang.String-">getProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the value of the property <code>name</code> associated with this home.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>java.util.Collection&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getPropertyNames--">getPropertyNames</a></span>()</code>
<div class="block">Returns the property names.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getRooms--">getRooms</a></span>()</code>
<div class="block">Returns an unmodifiable collection of the rooms of this home.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getRoomsSubList-java.util.List-">getRoomsSubList</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only rooms.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getSelectableViewableItems--">getSelectableViewableItems</a></span>()</code>
<div class="block">Returns all the selectable and viewable items in this home, except the observer camera.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getSelectedItems--">getSelectedItems</a></span>()</code>
<div class="block">Returns an unmodifiable list of the selected items in home.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getSelectedLevel--">getSelectedLevel</a></span>()</code>
<div class="block">Returns the selected level in home or <code>null</code> if home has no level.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getStoredCameras--">getStoredCameras</a></span>()</code>
<div class="block">Returns an unmodifiable list of the cameras stored by this home.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getSubList-java.util.List-java.lang.Class-">getSubList</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
          java.lang.Class&lt;T&gt;&nbsp;subListClass)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only instances of <code>subListClass</code>.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getTopCamera--">getTopCamera</a></span>()</code>
<div class="block">Returns the camera used to display this home from a top point of view.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getVersion--">getVersion</a></span>()</code>
<div class="block">Returns the version of this home, the last time it was serialized or
 or <a href="../../../../com/eteks/sweethome3d/model/Home.html#CURRENT_VERSION"><code>CURRENT_VERSION</code></a> if it is not serialized yet or
 was serialized with Sweet Home 3D 0.x.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getVisualProperty-java.lang.String-">getVisualProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getVisualProperty-java.lang.String-"><code>getVisualProperty(String)</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty(String, Object)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getProperty-java.lang.String-"><code>getProperty(String)</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setProperty-java.lang.String-java.lang.String-"><code>setProperty(String, String)</code></a>
     to ensure they can be easily saved and read. Future file format might not save visual properties anymore.</span></div>
</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getWallHeight--">getWallHeight</a></span>()</code>
<div class="block">Returns the wall height of this home.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>java.util.Collection&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getWalls--">getWalls</a></span>()</code>
<div class="block">Returns an unmodifiable collection of the walls of this home.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getWallsSubList-java.util.List-">getWallsSubList</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only walls.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#isAllLevelsSelection--">isAllLevelsSelection</a></span>()</code>
<div class="block">Returns <code>true</code> if the selected items in this home are from all levels.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#isBasePlanLocked--">isBasePlanLocked</a></span>()</code>
<div class="block">Returns <code>true</code> if the home objects belonging to the base plan
 (generally walls, rooms, dimension lines and texts) are locked.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#isEmpty--">isEmpty</a></span>()</code>
<div class="block">Returns <code>true</code> if this home doesn't contain any item i.e.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#isFurnitureDescendingSorted--">isFurnitureDescendingSorted</a></span>()</code>
<div class="block">Returns whether furniture is sorted in ascending or descending order.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#isItemSelected-com.eteks.sweethome3d.model.Selectable-">isItemSelected</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> if the given <code>item</code> is selected in this home</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#isModified--">isModified</a></span>()</code>
<div class="block">Returns whether the state of this home is modified or not.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#isRecovered--">isRecovered</a></span>()</code>
<div class="block">Returns whether this home was recovered or not.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#isRepaired--">isRepaired</a></span>()</code>
<div class="block">Returns whether this home was repaired or not.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removeDimensionLinesListener-com.eteks.sweethome3d.model.CollectionListener-">removeDimensionLinesListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the dimension line <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removeFurnitureListener-com.eteks.sweethome3d.model.CollectionListener-">removeFurnitureListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the furniture <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removeLabelsListener-com.eteks.sweethome3d.model.CollectionListener-">removeLabelsListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the label <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removeLevelsListener-com.eteks.sweethome3d.model.CollectionListener-">removeLevelsListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the level <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removePolylinesListener-com.eteks.sweethome3d.model.CollectionListener-">removePolylinesListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the polyline <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removePropertyChangeListener-com.eteks.sweethome3d.model.Home.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model">Home.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(java.lang.String&nbsp;propertyName,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this object.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removeRoomsListener-com.eteks.sweethome3d.model.CollectionListener-">removeRoomsListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the room <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removeSelectionListener-com.eteks.sweethome3d.model.SelectionListener-">removeSelectionListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/SelectionListener.html" title="interface in com.eteks.sweethome3d.model">SelectionListener</a>&nbsp;listener)</code>
<div class="block">Removes the selection <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#removeWallsListener-com.eteks.sweethome3d.model.CollectionListener-">removeWallsListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the wall <code>listener</code> in parameter from this home.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setAllLevelsSelection-boolean-">setAllLevelsSelection</a></span>(boolean&nbsp;selectionAtAllLevels)</code>
<div class="block">Sets whether the selected items in this home are from all levels, and notifies listeners of the change.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setBackgroundImage-com.eteks.sweethome3d.model.BackgroundImage-">setBackgroundImage</a></span>(<a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage)</code>
<div class="block">Sets the plan background image of this home and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setBasePlanLocked-boolean-">setBasePlanLocked</a></span>(boolean&nbsp;basePlanLocked)</code>
<div class="block">Sets whether home objects belonging to the base plan (generally walls, rooms,
 dimension lines and texts) are locked and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setCamera-com.eteks.sweethome3d.model.Camera-">setCamera</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera)</code>
<div class="block">Sets the camera used to display this home and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureAdditionalProperties-java.util.List-">setFurnitureAdditionalProperties</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a>&gt;&nbsp;furnitureAdditionalProperties)</code>
<div class="block">Sets the list of furniture additional properties which should be handled in the user interface.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureDescendingSorted-boolean-">setFurnitureDescendingSorted</a></span>(boolean&nbsp;furnitureDescendingSorted)</code>
<div class="block">Sets the furniture sort order on which home should be sorted
 and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">setFurnitureSortedProperty</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureSortedProperty)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedProperty--"><code>getFurnitureSortedProperty()</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>setFurnitureSortedProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedPropertyName--"><code>getFurnitureSortedPropertyName()</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedPropertyName-java.lang.String-"><code>setFurnitureSortedPropertyName(String)</code></a>
     to allow sorting on additional properties.</span></div>
</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedPropertyName-java.lang.String-">setFurnitureSortedPropertyName</a></span>(java.lang.String&nbsp;furnitureSortedPropertyName)</code>
<div class="block">Sets the furniture property name on which this home should be sorted
 and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureVisibleProperties-java.util.List-">setFurnitureVisibleProperties</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;&nbsp;furnitureVisibleProperties)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisibleProperties--"><code>getFurnitureVisibleProperties()</code></a> and <code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisiblePropertyNames--"><code>getFurnitureVisiblePropertyNames()</code></a> and <code>#setFurnitureSortedPropertyName(List<String>)</code>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureVisiblePropertyNames-java.util.List-">setFurnitureVisiblePropertyNames</a></span>(java.util.List&lt;java.lang.String&gt;&nbsp;furnitureVisiblePropertyNames)</code>
<div class="block">Sets the furniture property names that are visible and the order in which they are visible,
 then fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setModified-boolean-">setModified</a></span>(boolean&nbsp;modified)</code>
<div class="block">Sets the modified state of this home and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Sets the name of this home and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setPrint-com.eteks.sweethome3d.model.HomePrint-">setPrint</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a>&nbsp;print)</code>
<div class="block">Sets the print attributes of this home and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></span>(java.lang.String&nbsp;name,
           java.lang.String&nbsp;value)</code>
<div class="block">Sets a property associated with this home.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setRecovered-boolean-">setRecovered</a></span>(boolean&nbsp;recovered)</code>
<div class="block">Sets whether this home was recovered or not and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setRepaired-boolean-">setRepaired</a></span>(boolean&nbsp;repaired)</code>
<div class="block">Sets whether this home is repaired or not and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setSelectedItems-java.util.List-">setSelectedItems</a></span>(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;selectedItems)</code>
<div class="block">Sets the selected items in home and notifies listeners selection change.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setSelectedLevel-com.eteks.sweethome3d.model.Level-">setSelectedLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;selectedLevel)</code>
<div class="block">Sets the selected level in home and notifies listeners of the change.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setStoredCameras-java.util.List-">setStoredCameras</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;storedCameras)</code>
<div class="block">Sets the cameras stored by this home and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setVersion-long-">setVersion</a></span>(long&nbsp;version)</code>
<div class="block">Sets the version of this home.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Home.html#setVisualProperty-java.lang.String-java.lang.Object-">setVisualProperty</a></span>(java.lang.String&nbsp;name,
                 java.lang.Object&nbsp;value)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getVisualProperty-java.lang.String-"><code>getVisualProperty(String)</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty(String, Object)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getProperty-java.lang.String-"><code>getProperty(String)</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setProperty-java.lang.String-java.lang.String-"><code>setProperty(String, String)</code></a>
     to ensure they can be easily saved and read. Future file format might not save visual properties anymore.</span></div>
</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="CURRENT_VERSION">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CURRENT_VERSION</h4>
<pre>public static final&nbsp;long CURRENT_VERSION</pre>
<div class="block">The current version of this home. Each time the field list is changed
 in <code>Home</code> class or in one of the classes that it uses,
 this number is increased.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.model.Home.CURRENT_VERSION">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Home--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Home</h4>
<pre>public&nbsp;Home()</pre>
<div class="block">Creates a home with no furniture, no walls,
 and a height equal to 250 cm.</div>
</li>
</ul>
<a name="Home-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Home</h4>
<pre>public&nbsp;Home(float&nbsp;wallHeight)</pre>
<div class="block">Creates a home with no furniture and no walls.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallHeight</code> - default height for home walls</dd>
</dl>
</li>
</ul>
<a name="Home-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Home</h4>
<pre>public&nbsp;Home(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</pre>
<div class="block">Creates a home with the given <code>furniture</code>,
 no walls and a height equal to 250 cm.</div>
</li>
</ul>
<a name="Home-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Home</h4>
<pre>protected&nbsp;Home(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</pre>
<div class="block">Creates a home from an other one. All mutable data of the source <code>home</code>
 is cloned to this home and listeners support is reset.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addLevelsListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLevelsListener</h4>
<pre>public&nbsp;void&nbsp;addLevelsListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the level <code>listener</code> in parameter to this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="removeLevelsListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeLevelsListener</h4>
<pre>public&nbsp;void&nbsp;removeLevelsListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the level <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getLevels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevels</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&gt;&nbsp;getLevels()</pre>
<div class="block">Returns an unmodifiable collection of the levels of this home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="addLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLevel</h4>
<pre>public&nbsp;void&nbsp;addLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Adds the given <code>level</code> to the list of levels of this home.
 Once the <code>level</code> is added, level listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>level</code> - the level to add</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="deleteLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteLevel</h4>
<pre>public&nbsp;void&nbsp;deleteLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Removes the given <code>level</code> from the set of levels of this home
 and all the furniture, walls, rooms, dimension lines and labels that belong to this level.
 Once the <code>level</code> is removed, level listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#DELETE"><code>DELETE</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>level</code> - the level to remove</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getSelectedLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectedLevel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;getSelectedLevel()</pre>
<div class="block">Returns the selected level in home or <code>null</code> if home has no level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setSelectedLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectedLevel</h4>
<pre>public&nbsp;void&nbsp;setSelectedLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;selectedLevel)</pre>
<div class="block">Sets the selected level in home and notifies listeners of the change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>selectedLevel</code> - the level to select</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isAllLevelsSelection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAllLevelsSelection</h4>
<pre>public&nbsp;boolean&nbsp;isAllLevelsSelection()</pre>
<div class="block">Returns <code>true</code> if the selected items in this home are from all levels.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="setAllLevelsSelection-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllLevelsSelection</h4>
<pre>public&nbsp;void&nbsp;setAllLevelsSelection(boolean&nbsp;selectionAtAllLevels)</pre>
<div class="block">Sets whether the selected items in this home are from all levels, and notifies listeners of the change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="addFurnitureListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurnitureListener</h4>
<pre>public&nbsp;void&nbsp;addFurnitureListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the furniture <code>listener</code> in parameter to this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="removeFurnitureListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeFurnitureListener</h4>
<pre>public&nbsp;void&nbsp;removeFurnitureListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the furniture <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove</dd>
</dl>
</li>
</ul>
<a name="getFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurniture</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;getFurniture()</pre>
<div class="block">Returns an unmodifiable list of the furniture managed by this home.
 This furniture in this list is always sorted in the index order they were added to home.</div>
</li>
</ul>
<a name="addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPieceOfFurniture</h4>
<pre>public&nbsp;void&nbsp;addPieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Adds the <code>piece</code> in parameter to this home at the end of the furniture list.
 Once the <code>piece</code> is added, furniture listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>piece</code> - the piece to add</dd>
</dl>
</li>
</ul>
<a name="addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPieceOfFurniture</h4>
<pre>public&nbsp;void&nbsp;addPieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                                int&nbsp;index)</pre>
<div class="block">Adds the <code>piece</code> in parameter at a given <code>index</code>.
 Once the <code>piece</code> is added, furniture listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>piece</code> - the piece to add</dd>
<dd><code>index</code> - the index at which the piece will be added</dd>
</dl>
</li>
</ul>
<a name="addPieceOfFurnitureToGroup-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.HomeFurnitureGroup-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPieceOfFurnitureToGroup</h4>
<pre>public&nbsp;void&nbsp;addPieceOfFurnitureToGroup(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                                       <a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;group,
                                       int&nbsp;index)</pre>
<div class="block">Adds the <code>piece</code> in parameter at the <code>index</code> in the given <code>group</code>.
 Once the <code>piece</code> is added, furniture listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification with an event <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getIndex--"><code>index</code></a> equal to -1.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>piece</code> - the piece to add</dd>
<dd><code>group</code> - the group to which the piece will be added</dd>
<dd><code>index</code> - the index at which the piece will be added</dd>
</dl>
</li>
</ul>
<a name="deletePieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deletePieceOfFurniture</h4>
<pre>public&nbsp;void&nbsp;deletePieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Deletes the <code>piece</code> in parameter from this home.
 Once the <code>piece</code> is deleted, furniture listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification. If the removed <code>piece</code> belongs to a group, the
 <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getIndex--"><code>index</code></a> of the event will be -1.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>piece</code> - the piece to remove</dd>
</dl>
</li>
</ul>
<a name="addSelectionListener-com.eteks.sweethome3d.model.SelectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addSelectionListener</h4>
<pre>public&nbsp;void&nbsp;addSelectionListener(<a href="../../../../com/eteks/sweethome3d/model/SelectionListener.html" title="interface in com.eteks.sweethome3d.model">SelectionListener</a>&nbsp;listener)</pre>
<div class="block">Adds the selection <code>listener</code> in parameter to this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="removeSelectionListener-com.eteks.sweethome3d.model.SelectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeSelectionListener</h4>
<pre>public&nbsp;void&nbsp;removeSelectionListener(<a href="../../../../com/eteks/sweethome3d/model/SelectionListener.html" title="interface in com.eteks.sweethome3d.model">SelectionListener</a>&nbsp;listener)</pre>
<div class="block">Removes the selection <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove</dd>
</dl>
</li>
</ul>
<a name="getSelectedItems--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectedItems</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;getSelectedItems()</pre>
<div class="block">Returns an unmodifiable list of the selected items in home.</div>
</li>
</ul>
<a name="isItemSelected-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isItemSelected</h4>
<pre>public&nbsp;boolean&nbsp;isItemSelected(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Returns <code>true</code> if the given <code>item</code> is selected in this home</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>item</code> - a selectable item.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setSelectedItems-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectedItems</h4>
<pre>public&nbsp;void&nbsp;setSelectedItems(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;selectedItems)</pre>
<div class="block">Sets the selected items in home and notifies listeners selection change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>selectedItems</code> - the list of selected items</dd>
</dl>
</li>
</ul>
<a name="deselectItem-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deselectItem</h4>
<pre>public&nbsp;void&nbsp;deselectItem(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Deselects <code>item</code> if it's selected and notifies listeners selection change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>item</code> - the item to remove from selected items</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="addRoomsListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRoomsListener</h4>
<pre>public&nbsp;void&nbsp;addRoomsListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the room <code>listener</code> in parameter to this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="removeRoomsListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeRoomsListener</h4>
<pre>public&nbsp;void&nbsp;removeRoomsListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the room <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove</dd>
</dl>
</li>
</ul>
<a name="getRooms--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRooms</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;&nbsp;getRooms()</pre>
<div class="block">Returns an unmodifiable collection of the rooms of this home.</div>
</li>
</ul>
<a name="addRoom-com.eteks.sweethome3d.model.Room-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRoom</h4>
<pre>public&nbsp;void&nbsp;addRoom(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room)</pre>
<div class="block">Adds the given <code>room</code> at the end of the rooms list of this home.
 Once the <code>room</code> is added, room listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>room</code> - the room to add</dd>
</dl>
</li>
</ul>
<a name="addRoom-com.eteks.sweethome3d.model.Room-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addRoom</h4>
<pre>public&nbsp;void&nbsp;addRoom(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
                    int&nbsp;index)</pre>
<div class="block">Adds the <code>room</code> in parameter at a given <code>index</code>.
 Once the <code>room</code> is added, room listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>room</code> - the room to add</dd>
<dd><code>index</code> - the index at which the room will be added</dd>
</dl>
</li>
</ul>
<a name="deleteRoom-com.eteks.sweethome3d.model.Room-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteRoom</h4>
<pre>public&nbsp;void&nbsp;deleteRoom(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room)</pre>
<div class="block">Removes the given <code>room</code> from the set of rooms of this home.
 Once the <code>room</code> is removed, room listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#DELETE"><code>DELETE</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>room</code> - the room to remove</dd>
</dl>
</li>
</ul>
<a name="addWallsListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addWallsListener</h4>
<pre>public&nbsp;void&nbsp;addWallsListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the wall <code>listener</code> in parameter to this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="removeWallsListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeWallsListener</h4>
<pre>public&nbsp;void&nbsp;removeWallsListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the wall <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove</dd>
</dl>
</li>
</ul>
<a name="getWalls--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWalls</h4>
<pre>public&nbsp;java.util.Collection&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;getWalls()</pre>
<div class="block">Returns an unmodifiable collection of the walls of this home.</div>
</li>
</ul>
<a name="addWall-com.eteks.sweethome3d.model.Wall-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addWall</h4>
<pre>public&nbsp;void&nbsp;addWall(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)</pre>
<div class="block">Adds the given <code>wall</code> to the set of walls of this home.
 Once the <code>wall</code> is added, wall listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wall</code> - the wall to add</dd>
</dl>
</li>
</ul>
<a name="deleteWall-com.eteks.sweethome3d.model.Wall-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteWall</h4>
<pre>public&nbsp;void&nbsp;deleteWall(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)</pre>
<div class="block">Removes the given <code>wall</code> from the set of walls of this home.
 Once the <code>wall</code> is removed, wall listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#DELETE"><code>DELETE</code></a>.
 If any wall is attached to <code>wall</code> they will be detached from it.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wall</code> - the wall to remove</dd>
</dl>
</li>
</ul>
<a name="addPolylinesListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPolylinesListener</h4>
<pre>public&nbsp;void&nbsp;addPolylinesListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the polyline <code>listener</code> in parameter to this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="removePolylinesListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePolylinesListener</h4>
<pre>public&nbsp;void&nbsp;removePolylinesListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the polyline <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getPolylines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolylines</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;&nbsp;getPolylines()</pre>
<div class="block">Returns an unmodifiable collection of the polylines of this home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="addPolyline-com.eteks.sweethome3d.model.Polyline-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPolyline</h4>
<pre>public&nbsp;void&nbsp;addPolyline(<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline)</pre>
<div class="block">Adds a given <code>polyline</code> at the end of the polylines list of this home.
 Once the <code>polyline</code> is added, polyline listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>polyline</code> - the polyline to add</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="addPolyline-com.eteks.sweethome3d.model.Polyline-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPolyline</h4>
<pre>public&nbsp;void&nbsp;addPolyline(<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline,
                        int&nbsp;index)</pre>
<div class="block">Adds a <code>polyline</code> at a given <code>index</code> of the set of polylines of this home.
 Once the <code>polyline</code> is added, polyline listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>polyline</code> - the polyline to add</dd>
<dd><code>index</code> - the index at which the polyline will be added</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="deletePolyline-com.eteks.sweethome3d.model.Polyline-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deletePolyline</h4>
<pre>public&nbsp;void&nbsp;deletePolyline(<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline)</pre>
<div class="block">Removes a given <code>polyline</code> from the set of polylines of this home.
 Once the <code>polyline</code> is removed, polyline listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#DELETE"><code>DELETE</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>polyline</code> - the polyline to remove</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="addDimensionLinesListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addDimensionLinesListener</h4>
<pre>public&nbsp;void&nbsp;addDimensionLinesListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the dimension line <code>listener</code> in parameter to this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="removeDimensionLinesListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeDimensionLinesListener</h4>
<pre>public&nbsp;void&nbsp;removeDimensionLinesListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the dimension line <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove</dd>
</dl>
</li>
</ul>
<a name="getDimensionLines--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDimensionLines</h4>
<pre>public&nbsp;java.util.Collection&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;getDimensionLines()</pre>
<div class="block">Returns an unmodifiable collection of the dimension lines of this home.</div>
</li>
</ul>
<a name="addDimensionLine-com.eteks.sweethome3d.model.DimensionLine-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addDimensionLine</h4>
<pre>public&nbsp;void&nbsp;addDimensionLine(<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine)</pre>
<div class="block">Adds the given dimension line to the set of dimension lines of this home.
 Once <code>dimensionLine</code> is added, dimension line listeners added
 to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dimensionLine</code> - the dimension line to add</dd>
</dl>
</li>
</ul>
<a name="deleteDimensionLine-com.eteks.sweethome3d.model.DimensionLine-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteDimensionLine</h4>
<pre>public&nbsp;void&nbsp;deleteDimensionLine(<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine)</pre>
<div class="block">Removes the given dimension line from the set of dimension lines of this home.
 Once <code>dimensionLine</code> is removed, dimension line listeners added
 to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#DELETE"><code>DELETE</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dimensionLine</code> - the dimension line to remove</dd>
</dl>
</li>
</ul>
<a name="addLabelsListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLabelsListener</h4>
<pre>public&nbsp;void&nbsp;addLabelsListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the label <code>listener</code> in parameter to this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="removeLabelsListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeLabelsListener</h4>
<pre>public&nbsp;void&nbsp;removeLabelsListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the label <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - the listener to remove</dd>
</dl>
</li>
</ul>
<a name="getLabels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLabels</h4>
<pre>public&nbsp;java.util.Collection&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;&nbsp;getLabels()</pre>
<div class="block">Returns an unmodifiable collection of the labels of this home.</div>
</li>
</ul>
<a name="addLabel-com.eteks.sweethome3d.model.Label-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLabel</h4>
<pre>public&nbsp;void&nbsp;addLabel(<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)</pre>
<div class="block">Adds the given label to the set of labels of this home.
 Once <code>label</code> is added, label listeners added
 to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#ADD"><code>ADD</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>label</code> - the label to add</dd>
</dl>
</li>
</ul>
<a name="deleteLabel-com.eteks.sweethome3d.model.Label-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteLabel</h4>
<pre>public&nbsp;void&nbsp;deleteLabel(<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)</pre>
<div class="block">Removes the given label from the set of labels of this home.
 Once <code>label</code> is removed, label listeners added to this home will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a>
 notification, with an <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--"><code>event type</code></a>
 equal to <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html#DELETE"><code>DELETE</code></a>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>label</code> - the label to remove</dd>
</dl>
</li>
</ul>
<a name="getSelectableViewableItems--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectableViewableItems</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;getSelectableViewableItems()</pre>
<div class="block">Returns all the selectable and viewable items in this home, except the observer camera.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a list containing viewable walls, rooms, furniture, dimension lines, polylines, labels and compass.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getHomeObjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeObjects</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>&gt;&nbsp;getHomeObjects()</pre>
<div class="block">Returns all the mutable objects handled by this home.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a list containing environment, compass, levels, walls, rooms, furniture and their possible children,
 polylines, dimension lines, labels and cameras.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="isEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public&nbsp;boolean&nbsp;isEmpty()</pre>
<div class="block">Returns <code>true</code> if this home doesn't contain any item i.e.
 no piece of furniture, no wall, no room, no dimension line and no label.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.model.Home.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model">Home.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this home.
 Properties change will be notified with an event of <code>PropertyChangeEvent</code> class which property name
 will be equal to the value returned by <code>Enum.name()</code> call.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>property</code> - the property to follow</dd>
<dd><code>listener</code> - the listener to add</dd>
</dl>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.model.Home.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model">Home.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>property</code> - the followed property</dd>
<dd><code>listener</code> - the listener to remove</dd>
</dl>
</li>
</ul>
<a name="getWallHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallHeight</h4>
<pre>public&nbsp;float&nbsp;getWallHeight()</pre>
<div class="block">Returns the wall height of this home.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this home.</div>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the name of this home and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the new name of this home</dd>
</dl>
</li>
</ul>
<a name="isModified--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isModified</h4>
<pre>public&nbsp;boolean&nbsp;isModified()</pre>
<div class="block">Returns whether the state of this home is modified or not.</div>
</li>
</ul>
<a name="setModified-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModified</h4>
<pre>public&nbsp;void&nbsp;setModified(boolean&nbsp;modified)</pre>
<div class="block">Sets the modified state of this home and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="isRecovered--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRecovered</h4>
<pre>public&nbsp;boolean&nbsp;isRecovered()</pre>
<div class="block">Returns whether this home was recovered or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setRecovered-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRecovered</h4>
<pre>public&nbsp;void&nbsp;setRecovered(boolean&nbsp;recovered)</pre>
<div class="block">Sets whether this home was recovered or not and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isRepaired--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRepaired</h4>
<pre>public&nbsp;boolean&nbsp;isRepaired()</pre>
<div class="block">Returns whether this home was repaired or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="setRepaired-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRepaired</h4>
<pre>public&nbsp;void&nbsp;setRepaired(boolean&nbsp;repaired)</pre>
<div class="block">Sets whether this home is repaired or not and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getFurnitureSortedPropertyName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureSortedPropertyName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFurnitureSortedPropertyName()</pre>
<div class="block">Returns the furniture property name on which home is sorted or <code>null</code> if
 home furniture isn't sorted.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setFurnitureSortedPropertyName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureSortedPropertyName</h4>
<pre>public&nbsp;void&nbsp;setFurnitureSortedPropertyName(java.lang.String&nbsp;furnitureSortedPropertyName)</pre>
<div class="block">Sets the furniture property name on which this home should be sorted
 and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureSortedPropertyName</code> - the name of the property</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getFurnitureSortedProperty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureSortedProperty</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;getFurnitureSortedProperty()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedProperty--"><code>getFurnitureSortedProperty()</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>setFurnitureSortedProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedPropertyName--"><code>getFurnitureSortedPropertyName()</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedPropertyName-java.lang.String-"><code>setFurnitureSortedPropertyName(String)</code></a>
     to allow sorting on additional properties.</span></div>
<div class="block">Returns the furniture property on which home is sorted or <code>null</code> if
 home furniture isn't sorted.</div>
</li>
</ul>
<a name="setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureSortedProperty</h4>
<pre>public&nbsp;void&nbsp;setFurnitureSortedProperty(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureSortedProperty)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedProperty--"><code>getFurnitureSortedProperty()</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>setFurnitureSortedProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedPropertyName--"><code>getFurnitureSortedPropertyName()</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedPropertyName-java.lang.String-"><code>setFurnitureSortedPropertyName(String)</code></a>
     to allow sorting on additional properties.</span></div>
<div class="block">Sets the furniture property on which this home should be sorted
 and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureSortedProperty</code> - the new property</dd>
</dl>
</li>
</ul>
<a name="isFurnitureDescendingSorted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFurnitureDescendingSorted</h4>
<pre>public&nbsp;boolean&nbsp;isFurnitureDescendingSorted()</pre>
<div class="block">Returns whether furniture is sorted in ascending or descending order.</div>
</li>
</ul>
<a name="setFurnitureDescendingSorted-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureDescendingSorted</h4>
<pre>public&nbsp;void&nbsp;setFurnitureDescendingSorted(boolean&nbsp;furnitureDescendingSorted)</pre>
<div class="block">Sets the furniture sort order on which home should be sorted
 and fires a <code>PropertyChangeEvent</code>.</div>
</li>
</ul>
<a name="getFurnitureVisiblePropertyNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureVisiblePropertyNames</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getFurnitureVisiblePropertyNames()</pre>
<div class="block">Returns an unmodifiable list of the furniture property names that are visible.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setFurnitureVisiblePropertyNames-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureVisiblePropertyNames</h4>
<pre>public&nbsp;void&nbsp;setFurnitureVisiblePropertyNames(java.util.List&lt;java.lang.String&gt;&nbsp;furnitureVisiblePropertyNames)</pre>
<div class="block">Sets the furniture property names that are visible and the order in which they are visible,
 then fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureVisiblePropertyNames</code> - the property names to display</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getFurnitureVisibleProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureVisibleProperties</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;&nbsp;getFurnitureVisibleProperties()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisibleProperties--"><code>getFurnitureVisibleProperties()</code></a> and <code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisiblePropertyNames--"><code>getFurnitureVisiblePropertyNames()</code></a> and <code>#setFurnitureSortedPropertyName(List<String>)</code>
     to allow displaying additional properties.</span></div>
<div class="block">Returns an unmodifiable list of the furniture properties that are visible.</div>
</li>
</ul>
<a name="setFurnitureVisibleProperties-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureVisibleProperties</h4>
<pre>public&nbsp;void&nbsp;setFurnitureVisibleProperties(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;&nbsp;furnitureVisibleProperties)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisibleProperties--"><code>getFurnitureVisibleProperties()</code></a> and <code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisiblePropertyNames--"><code>getFurnitureVisiblePropertyNames()</code></a> and <code>#setFurnitureSortedPropertyName(List<String>)</code>
     to allow displaying additional properties.</span></div>
<div class="block">Sets the furniture properties that are visible and the order in which they are visible,
 then fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureVisibleProperties</code> - the properties to display</dd>
</dl>
</li>
</ul>
<a name="getFurnitureAdditionalProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureAdditionalProperties</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a>&gt;&nbsp;getFurnitureAdditionalProperties()</pre>
<div class="block">Returns the list of furniture additional properties which should be handled in the user interface.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setFurnitureAdditionalProperties-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureAdditionalProperties</h4>
<pre>public&nbsp;void&nbsp;setFurnitureAdditionalProperties(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/ObjectProperty.html" title="class in com.eteks.sweethome3d.model">ObjectProperty</a>&gt;&nbsp;furnitureAdditionalProperties)</pre>
<div class="block">Sets the list of furniture additional properties which should be handled in the user interface.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getBackgroundImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackgroundImage</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;getBackgroundImage()</pre>
<div class="block">Returns the plan background image of this home.</div>
</li>
</ul>
<a name="setBackgroundImage-com.eteks.sweethome3d.model.BackgroundImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackgroundImage</h4>
<pre>public&nbsp;void&nbsp;setBackgroundImage(<a href="../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage)</pre>
<div class="block">Sets the plan background image of this home and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>backgroundImage</code> - the new background image</dd>
</dl>
</li>
</ul>
<a name="getTopCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTopCamera</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;getTopCamera()</pre>
<div class="block">Returns the camera used to display this home from a top point of view.</div>
</li>
</ul>
<a name="getObserverCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObserverCamera</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/ObserverCamera.html" title="class in com.eteks.sweethome3d.model">ObserverCamera</a>&nbsp;getObserverCamera()</pre>
<div class="block">Returns the camera used to display this home from an observer point of view.</div>
</li>
</ul>
<a name="setCamera-com.eteks.sweethome3d.model.Camera-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCamera</h4>
<pre>public&nbsp;void&nbsp;setCamera(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera)</pre>
<div class="block">Sets the camera used to display this home and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>camera</code> - the camera to use</dd>
</dl>
</li>
</ul>
<a name="getCamera--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCamera</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;getCamera()</pre>
<div class="block">Returns the camera used to display this home.</div>
</li>
</ul>
<a name="setStoredCameras-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStoredCameras</h4>
<pre>public&nbsp;void&nbsp;setStoredCameras(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;storedCameras)</pre>
<div class="block">Sets the cameras stored by this home and fires a <code>PropertyChangeEvent</code>.
 The list given as parameter is cloned but not the camera instances it contains.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>storedCameras</code> - the new list of cameras</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getStoredCameras--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStoredCameras</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;getStoredCameras()</pre>
<div class="block">Returns an unmodifiable list of the cameras stored by this home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getEnvironment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEnvironment</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model">HomeEnvironment</a>&nbsp;getEnvironment()</pre>
<div class="block">Returns the environment attributes of this home.</div>
</li>
</ul>
<a name="getCompass--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompass</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a>&nbsp;getCompass()</pre>
<div class="block">Returns the compass associated to this home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getPrint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a>&nbsp;getPrint()</pre>
<div class="block">Returns the print attributes of this home.</div>
</li>
</ul>
<a name="setPrint-com.eteks.sweethome3d.model.HomePrint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrint</h4>
<pre>public&nbsp;void&nbsp;setPrint(<a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a>&nbsp;print)</pre>
<div class="block">Sets the print attributes of this home and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>print</code> - the new print attributes</dd>
</dl>
</li>
</ul>
<a name="getVisualProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVisualProperty</h4>
<pre>public&nbsp;java.lang.Object&nbsp;getVisualProperty(java.lang.String&nbsp;name)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getVisualProperty-java.lang.String-"><code>getVisualProperty(String)</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty(String, Object)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getProperty-java.lang.String-"><code>getProperty(String)</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setProperty-java.lang.String-java.lang.String-"><code>setProperty(String, String)</code></a>
     to ensure they can be easily saved and read. Future file format might not save visual properties anymore.</span></div>
<div class="block">Returns the value of the visual property <code>name</code> associated with this home.</div>
</li>
</ul>
<a name="setVisualProperty-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisualProperty</h4>
<pre>public&nbsp;void&nbsp;setVisualProperty(java.lang.String&nbsp;name,
                              java.lang.Object&nbsp;value)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/model/Home.html#getVisualProperty-java.lang.String-"><code>getVisualProperty(String)</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty(String, Object)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/model/Home.html#getProperty-java.lang.String-"><code>getProperty(String)</code></a> and <a href="../../../../com/eteks/sweethome3d/model/Home.html#setProperty-java.lang.String-java.lang.String-"><code>setProperty(String, String)</code></a>
     to ensure they can be easily saved and read. Future file format might not save visual properties anymore.</span></div>
<div class="block">Sets a visual property associated with this home.</div>
</li>
</ul>
<a name="getProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProperty</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProperty(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the value of the property <code>name</code> associated with this home.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the value of the property or <code>null</code> if it doesn't exist.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="getNumericProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumericProperty</h4>
<pre>public&nbsp;java.lang.Number&nbsp;getNumericProperty(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the numeric value of the property <code>name</code> associated with this home.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an instance of <code>Long</code>, <code>Double</code> or <code>null</code> if the property
 doesn't exist or can't be parsed.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="setProperty-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProperty</h4>
<pre>public&nbsp;void&nbsp;setProperty(java.lang.String&nbsp;name,
                        java.lang.String&nbsp;value)</pre>
<div class="block">Sets a property associated with this home. Once the property is updated,
 listeners added to this home will receive a change event of
 <code>PropertyChangeEvent</code> class.<br>
 To avoid any issue with existing or future properties of Sweet Home 3D classes,
 do not use property names written with only upper case letters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the property to set</dd>
<dd><code>value</code> - the new value of the property</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="getPropertyNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPropertyNames</h4>
<pre>public&nbsp;java.util.Collection&lt;java.lang.String&gt;&nbsp;getPropertyNames()</pre>
<div class="block">Returns the property names.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a collection of all the names of the properties set with <a href="../../../../com/eteks/sweethome3d/model/Home.html#setProperty-java.lang.String-java.lang.String-"><code>setProperty</code></a></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(java.lang.String&nbsp;propertyName,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this home for a specific property name.
 Properties set with <a href="../../../../com/eteks/sweethome3d/model/Home.html#setProperty-java.lang.String-java.lang.String-"><code>setProperty</code></a> will be notified with
 an event of <code>PropertyChangeEvent</code> class which property name will be equal to the property,
 whereas changes on properties of <a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model"><code>Home.Property</code></a> enum will be notified with an event where
 the property name will be equal to the value returned by <code>Enum.name()</code> call.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(java.lang.String&nbsp;propertyName,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this object.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="isBasePlanLocked--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBasePlanLocked</h4>
<pre>public&nbsp;boolean&nbsp;isBasePlanLocked()</pre>
<div class="block">Returns <code>true</code> if the home objects belonging to the base plan
 (generally walls, rooms, dimension lines and texts) are locked.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.8</dd>
</dl>
</li>
</ul>
<a name="setBasePlanLocked-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBasePlanLocked</h4>
<pre>public&nbsp;void&nbsp;setBasePlanLocked(boolean&nbsp;basePlanLocked)</pre>
<div class="block">Sets whether home objects belonging to the base plan (generally walls, rooms,
 dimension lines and texts) are locked and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.8</dd>
</dl>
</li>
</ul>
<a name="getVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVersion</h4>
<pre>public&nbsp;long&nbsp;getVersion()</pre>
<div class="block">Returns the version of this home, the last time it was serialized or
 or <a href="../../../../com/eteks/sweethome3d/model/Home.html#CURRENT_VERSION"><code>CURRENT_VERSION</code></a> if it is not serialized yet or
 was serialized with Sweet Home 3D 0.x.
 Version is useful to know with which Sweet Home 3D version this home was saved
 and warn user that he may lose information if he saves with
 current application a home created by a more recent version.</div>
</li>
</ul>
<a name="setVersion-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVersion</h4>
<pre>public&nbsp;void&nbsp;setVersion(long&nbsp;version)</pre>
<div class="block">Sets the version of this home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this home and the objects it contains.
 Listeners bound to this home aren't added to the returned home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>clone</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="duplicate-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>duplicate</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;duplicate(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Returns a deep copy of home selectable <code>items</code>.
 Duplicated items are at the same index as their original and use different ids.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - the items to duplicate</dd>
</dl>
</li>
</ul>
<a name="getFurnitureSubList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureSubList</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;getFurnitureSubList(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Returns a sub list of <code>items</code> that contains only home furniture.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - the items among which the search is done</dd>
</dl>
</li>
</ul>
<a name="getWallsSubList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallsSubList</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;&nbsp;getWallsSubList(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Returns a sub list of <code>items</code> that contains only walls.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - the items among which the search is done</dd>
</dl>
</li>
</ul>
<a name="getRoomsSubList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoomsSubList</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;&nbsp;getRoomsSubList(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Returns a sub list of <code>items</code> that contains only rooms.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - the items among which the search is done</dd>
</dl>
</li>
</ul>
<a name="getPolylinesSubList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPolylinesSubList</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;&nbsp;getPolylinesSubList(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Returns a sub list of <code>items</code> that contains only labels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - the items among which the search is done</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getDimensionLinesSubList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDimensionLinesSubList</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;getDimensionLinesSubList(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Returns a sub list of <code>items</code> that contains only dimension lines.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - the items among which the search is done</dd>
</dl>
</li>
</ul>
<a name="getLabelsSubList-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLabelsSubList</h4>
<pre>public static&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;&nbsp;getLabelsSubList(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</pre>
<div class="block">Returns a sub list of <code>items</code> that contains only labels.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - the items among which the search is done</dd>
</dl>
</li>
</ul>
<a name="getSubList-java.util.List-java.lang.Class-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSubList</h4>
<pre>public static&nbsp;&lt;T&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;getSubList(java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                                               java.lang.Class&lt;T&gt;&nbsp;subListClass)</pre>
<div class="block">Returns a sub list of <code>items</code> that contains only instances of <code>subListClass</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>items</code> - the items among which the search is done</dd>
<dd><code>subListClass</code> - the class of the searched items</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Home.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Home.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Home.html" target="_top">Frames</a></li>
<li><a href="Home.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
