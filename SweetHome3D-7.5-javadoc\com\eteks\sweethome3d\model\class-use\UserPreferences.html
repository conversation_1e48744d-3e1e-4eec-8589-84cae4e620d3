<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.model.UserPreferences (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.model.UserPreferences (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/UserPreferences.html" target="_top">Frames</a></li>
<li><a href="UserPreferences.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.model.UserPreferences" class="title">Uses of Class<br>com.eteks.sweethome3d.model.UserPreferences</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d">com.eteks.sweethome3d</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as a stand-alone application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d</a></td>
<td class="colLast">
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.plugin">com.eteks.sweethome3d.plugin</a></td>
<td class="colLast">
<div class="block">Describes the super classes required to create Sweet Home 3D plug-ins.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a> that return <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></td>
<td class="colLast"><span class="typeNameLabel">SweetHome3D.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/SweetHome3D.html#getUserPreferences--">getUserPreferences</a></span>()</code>
<div class="block">Returns user preferences stored in resources and local file system.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html" title="class in com.eteks.sweethome3d.applet">AppletUserPreferences</a></span></code>
<div class="block">Applet user preferences.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> that return <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></td>
<td class="colLast"><span class="typeNameLabel">AppletApplication.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletApplication.html#getUserPreferences--">getUserPreferences</a></span>()</code>
<div class="block">Returns user preferences.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#AppletContentManager-com.eteks.sweethome3d.model.HomeRecorder-com.eteks.sweethome3d.model.UserPreferences-">AppletContentManager</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;recorder,
                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#AppletContentManager-com.eteks.sweethome3d.model.HomeRecorder-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">AppletContentManager</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a>&nbsp;recorder,
                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io">DefaultUserPreferences</a></span></code>
<div class="block">Default user preferences.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html" title="class in com.eteks.sweethome3d.io">FileUserPreferences</a></span></code>
<div class="block">User preferences initialized from
 <a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io"><code>default user preferences</code></a>
 and stored in user preferences on local file system.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#DefaultFurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File-">DefaultFurnitureCatalog</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       java.io.File&nbsp;furniturePluginFolder)</code>
<div class="block">Creates a default furniture catalog read from resources and
 furniture plugin folder if <code>furniturePluginFolder</code> isn't <code>null</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#DefaultFurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File:A-">DefaultFurnitureCatalog</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       java.io.File[]&nbsp;furniturePluginFolders)</code>
<div class="block">Creates a default furniture catalog read from resources and
 furniture plugin folders if <code>furniturePluginFolders</code> isn't <code>null</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#DefaultHomeInputStream-java.io.File-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.io.HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-boolean-">DefaultHomeInputStream</a></span>(java.io.File&nbsp;zipFile,
                      <a href="../../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                      <a href="../../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a>&nbsp;xmlHandler,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      boolean&nbsp;preferPreferencesContent)</code>
<div class="block">Creates a home input stream able to read a home and its content from the given file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#DefaultHomeInputStream-java.io.InputStream-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.io.HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-boolean-">DefaultHomeInputStream</a></span>(java.io.InputStream&nbsp;in,
                      <a href="../../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                      <a href="../../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a>&nbsp;xmlHandler,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      boolean&nbsp;preferPreferencesContent)</code>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html#DefaultHomeInputStream-java.io.InputStream-com.eteks.sweethome3d.io.ContentRecording-com.eteks.sweethome3d.model.UserPreferences-boolean-">DefaultHomeInputStream</a></span>(java.io.InputStream&nbsp;in,
                      <a href="../../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      boolean&nbsp;preferPreferencesContent)</code>
<div class="block">Creates a home input stream filter able to read a home and its content
 from <code>in</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#DefaultTexturesCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File-">DefaultTexturesCatalog</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.io.File&nbsp;texturesPluginFolder)</code>
<div class="block">Creates a default textures catalog read from resources and
 textures plugin folder if <code>texturesPluginFolder</code> isn't <code>null</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#DefaultTexturesCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File:A-">DefaultTexturesCatalog</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.io.File[]&nbsp;texturesPluginFolders)</code>
<div class="block">Creates a default textures catalog read from resources and
 textures plugin folders if <code>texturesPluginFolders</code> isn't <code>null</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-">HomeFileRecorder</a></span>(int&nbsp;compressionLevel,
                boolean&nbsp;includeOnlyTemporaryContent,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                boolean&nbsp;preferPreferencesContent)</code>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-boolean-">HomeFileRecorder</a></span>(int&nbsp;compressionLevel,
                boolean&nbsp;includeOnlyTemporaryContent,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                boolean&nbsp;preferPreferencesContent,
                boolean&nbsp;preferXmlEntry)</code>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-boolean-boolean-">HomeFileRecorder</a></span>(int&nbsp;compressionLevel,
                boolean&nbsp;includeOnlyTemporaryContent,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                boolean&nbsp;preferPreferencesContent,
                boolean&nbsp;preferXmlEntry,
                boolean&nbsp;acceptUrl)</code>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-">HomeXMLHandler</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.j3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> that return <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></td>
<td class="colLast"><span class="typeNameLabel">Object3DBranch.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getUserPreferences--">getUserPreferences</a></span>()</code>
<div class="block">Returns user preferences.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DBranchFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranchFactory.html#createObject3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.Selectable-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-">createObject3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Object&nbsp;context,
              boolean&nbsp;waitForLoading)</code>
<div class="block">Returns the 3D object matching a given <code>item</code>.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/DimensionLine3D.html#DimensionLine3D-com.eteks.sweethome3d.model.DimensionLine-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-">DimensionLine3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine,
               <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               java.lang.Object&nbsp;context,
               boolean&nbsp;waitForLoading)</code>
<div class="block">Creates the 3D object matching the given dimension line.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Ground3D.html#Ground3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-float-float-float-float-boolean-">Ground3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
        <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
        java.lang.Object&nbsp;context,
        float&nbsp;originX,
        float&nbsp;originY,
        float&nbsp;width,
        float&nbsp;depth,
        boolean&nbsp;waitTextureLoadingEnd)</code>
<div class="block">Creates a 3D ground for the given <code>home</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html#HomePieceOfFurniture3D-com.eteks.sweethome3d.model.HomePieceOfFurniture-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-boolean-">HomePieceOfFurniture3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                      <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.lang.Object&nbsp;context,
                      boolean&nbsp;ignoreDrawingMode,
                      boolean&nbsp;waitModelAndTextureLoadingEnd)</code>
<div class="block">Creates the 3D piece matching the given home <code>piece</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Label3D.html#Label3D-com.eteks.sweethome3d.model.Label-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-">Label3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label,
       <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
       java.lang.Object&nbsp;context,
       boolean&nbsp;waitForLoading)</code>
<div class="block">Creates the 3D object matching the given <code>label</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#Object3DBranch-java.lang.Object-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-">Object3DBranch</a></span>(java.lang.Object&nbsp;item,
              <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Object&nbsp;context)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranchFactory.html#Object3DBranchFactory-com.eteks.sweethome3d.model.UserPreferences-">Object3DBranchFactory</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranchFactory.html#Object3DBranchFactory-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-">Object3DBranchFactory</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                     java.lang.Object&nbsp;context)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Polyline3D.html#Polyline3D-com.eteks.sweethome3d.model.Polyline-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-">Polyline3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline,
          <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          java.lang.Object&nbsp;context)</code>
<div class="block">Creates the 3D object matching the given <code>polyline</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Room3D.html#Room3D-com.eteks.sweethome3d.model.Room-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-boolean-boolean-">Room3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
      <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
      java.lang.Object&nbsp;context,
      boolean&nbsp;ignoreCeilingPart,
      boolean&nbsp;ignoreDrawingMode,
      boolean&nbsp;waitTextureLoadingEnd)</code>
<div class="block">Creates the 3D room matching the given home <code>room</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Wall3D.html#Wall3D-com.eteks.sweethome3d.model.Wall-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-boolean-">Wall3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall,
      <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
      java.lang.Object&nbsp;context,
      boolean&nbsp;ignoreDrawingMode,
      boolean&nbsp;waitModelAndTextureLoadingEnd)</code>
<div class="block">Creates the 3D wall matching the given home <code>wall</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>abstract <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeApplication.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html#getUserPreferences--">getUserPreferences</a></span>()</code>
<div class="block">Returns user preferences.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.plugin">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a> that return <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></td>
<td class="colLast"><span class="typeNameLabel">Plugin.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/plugin/Plugin.html#getUserPreferences--">getUserPreferences</a></span>()</code>
<div class="block">Returns the user preferences of the current application.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/plugin/Plugin.html" title="class in com.eteks.sweethome3d.plugin">Plugin</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PluginManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html#getPlugins-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-javax.swing.undo.UndoableEditSupport-">getPlugins</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
          <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Returns an unmodifiable list of plug-in instances initialized with the
 given parameters.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">createBackgroundImageWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;backgroundImageWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps user to choose a background image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">createBaseboardChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;baseboardChoiceController)</code>
<div class="block">Returns a new view that edits the baseboard of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">createCompassView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;compassController)</code>
<div class="block">Returns a new view that edits compass values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">createDimensionLineView</a></span>(boolean&nbsp;modification,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;dimensionLineController)</code>
<div class="block">Returns a new view that edits dimension line values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">createFurnitureCatalogView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;furnitureCatalogController)</code>
<div class="block">Returns a new view that displays furniture <code>catalog</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureTablePanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#createFurnitureTable-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureTable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;controller)</code>
<div class="block">Creates and returns the main furniture table displayed by this component.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController)</code>
<div class="block">Returns a new table that displays <code>home</code> furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createHelpView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HelpController-">createHelpView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html" title="class in com.eteks.sweethome3d.viewcontroller">HelpController</a>&nbsp;helpController)</code>
<div class="block">Returns a new view that displays Sweet Home 3D help.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">createHome3DAttributesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;home3DAttributesController)</code>
<div class="block">Returns a new view that edits 3D attributes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">createHomeFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;homeFurnitureController)</code>
<div class="block">Returns a new view that edits furniture values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createHomeView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">createHomeView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</code>
<div class="block">Returns a new view that displays <code>home</code> and its sub views.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createImportedFurnitureWizardStepsView-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController-">createImportedFurnitureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                      java.lang.String&nbsp;modelName,
                                      boolean&nbsp;importHomePiece,
                                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController</a>&nbsp;importedFurnitureWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps user to import furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">createImportedTextureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                                    java.lang.String&nbsp;textureName,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;importedTextureWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to import a texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">createLabelView</a></span>(boolean&nbsp;modification,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;labelController)</code>
<div class="block">Returns a new view that edits label values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">createLevelView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;levelController)</code>
<div class="block">Returns a new view that edits level values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">createModelMaterialsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;controller)</code>
<div class="block">Returns a new view that edits the materials of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">createObserverCameraView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;observerCameraController)</code>
<div class="block">Returns a new view that edits observer camera values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">createPageSetupView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;pageSetupController)</code>
<div class="block">Creates a new view that edits page setup.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">createPhotosView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;photosController)</code>
<div class="block">Returns a new view able to compute a photos of a home from its stored points of view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">createPhotoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;photoController)</code>
<div class="block">Returns a new view able to create photo realistic images of the given home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#createPlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">createPlanComponent</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</code>
<div class="block">Creates and returns the main plan component displayed and layout by this component.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPlanView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">createPlanView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;planController)</code>
<div class="block">Returns a new view that displays <code>home</code> plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">createPolylineView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;polylineController)</code>
<div class="block">Returns a new view that edits polyline values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">createPrintPreviewView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</code>
<div class="block">Returns a new view that displays <code>home</code> print preview.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">createRoomView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;roomController)</code>
<div class="block">Returns a new view that edits room values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TextureChoiceView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createTextureChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.TextureChoiceController-">createTextureChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;textureChoiceController)</code>
<div class="block">Returns a new view that edits the texture of the given controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createThreadedTaskView-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController-">createThreadedTaskView</a></span>(java.lang.String&nbsp;taskMessage,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html" title="class in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController</a>&nbsp;threadedTaskController)</code>
<div class="block">Returns a new view that displays message for a threaded task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">createUserPreferencesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;userPreferencesController)</code>
<div class="block">Returns a new view that edits user preferences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">createVideoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;videoController)</code>
<div class="block">Returns a new view able to create 3D videos of the given home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">createView3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
            <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
            <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;homeController3D)</code>
<div class="block">Returns a new view that displays <code>home</code> in 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">createWallView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;wallController)</code>
<div class="block">Returns a new view that edits wall values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">createWizardView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;wizardController)</code>
<div class="block">Returns a new view that displays a wizard.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">SwingTools.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingTools.html#getLocalizedLabelText-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-java.lang.Object...-">getLocalizedLabelText</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                     java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                     java.lang.String&nbsp;resourceKey,
                     java.lang.Object...&nbsp;resourceParameters)</code>
<div class="block">Returns a localized text for menus items and labels depending on the system.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="typeNameLabel">SwingTools.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingTools.html#updateSwingResourceLanguage-com.eteks.sweethome3d.model.UserPreferences-">updateSwingResourceLanguage</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>
<div class="block">Updates the Swing resource bundles in use from the preferences Locale and the class loaders of preferences.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/BackgroundImageWizardStepsPanel.html#BackgroundImageWizardStepsPanel-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">BackgroundImageWizardStepsPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;controller)</code>
<div class="block">Creates a view for background image choice, scale and origin.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/BaseboardChoiceComponent.html#BaseboardChoiceComponent-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">BaseboardChoiceComponent</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays baseboard data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/CatalogItemToolTip.html#CatalogItemToolTip-boolean-com.eteks.sweethome3d.model.UserPreferences-">CatalogItemToolTip</a></span>(boolean&nbsp;ignoreCategory,
                  <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>
<div class="block">Creates a tool tip that displays the icon of a piece of furniture, its name
 and its category if <code>ignoreCategory</code> is <code>true</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/CatalogItemToolTip.html#CatalogItemToolTip-com.eteks.sweethome3d.swing.CatalogItemToolTip.DisplayedInformation-com.eteks.sweethome3d.model.UserPreferences-">CatalogItemToolTip</a></span>(<a href="../../../../../com/eteks/sweethome3d/swing/CatalogItemToolTip.DisplayedInformation.html" title="enum in com.eteks.sweethome3d.swing">CatalogItemToolTip.DisplayedInformation</a>&nbsp;displayedInformation,
                  <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>
<div class="block">Creates a tool tip that displays furniture information.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ColorButton.html#ColorButton-com.eteks.sweethome3d.model.UserPreferences-">ColorButton</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>
<div class="block">Creates a color button.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/CompassPanel.html#CompassPanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">CompassPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
            <a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays compass data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ControllerAction.html#ControllerAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-boolean-java.lang.Object-java.lang.String-java.lang.Object...-">ControllerAction</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                java.lang.String&nbsp;actionPrefix,
                boolean&nbsp;enabled,
                java.lang.Object&nbsp;controller,
                java.lang.String&nbsp;method,
                java.lang.Object...&nbsp;parameters)</code>
<div class="block">Creates an action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ControllerAction.html#ControllerAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-java.lang.Object-java.lang.String-java.lang.Object...-">ControllerAction</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                java.lang.String&nbsp;actionPrefix,
                java.lang.Object&nbsp;controller,
                java.lang.String&nbsp;method,
                java.lang.Object...&nbsp;parameters)</code>
<div class="block">Creates a disabled action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html#DimensionLinePanel-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">DimensionLinePanel</a></span>(boolean&nbsp;modification,
                  <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays wall data according to the units set in
 <code>preferences</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FileContentManager.html#FileContentManager-com.eteks.sweethome3d.model.UserPreferences-">FileContentManager</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FontNameComboBox.html#FontNameComboBox-com.eteks.sweethome3d.model.UserPreferences-">FontNameComboBox</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureCatalogListPanel.html#FurnitureCatalogListPanel-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">FurnitureCatalogListPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                         <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays <code>catalog</code> furniture in a list with a filter combo box
 and a search field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html#FurnitureCatalogTree-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">FurnitureCatalogTree</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;controller)</code>
<div class="block">Creates a tree controlled by <code>controller</code> that displays
 <code>catalog</code> content and its selection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureTable.html#FurnitureTable-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-">FurnitureTable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>
<div class="block">Creates a table that displays furniture of <code>home</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureTable.html#FurnitureTable-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">FurnitureTable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;controller)</code>
<div class="block">Creates a table controlled by <code>controller</code>
 that displays furniture of <code>home</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#FurnitureTablePanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">FurnitureTablePanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HelpPane.html#HelpPane-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HelpController-">HelpPane</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html" title="class in com.eteks.sweethome3d.viewcontroller">HelpController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/Home3DAttributesPanel.html#Home3DAttributesPanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">Home3DAttributesPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                     <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays home 3D attributes data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-boolean-">HomeComponent3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               boolean&nbsp;displayShadowOnFloor)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture,
 with shadows on the floor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-boolean-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
               boolean&nbsp;displayShadowOnFloor,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.swing.HomeComponent3D.Projection-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
               <a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.Projection.html" title="enum in com.eteks.sweethome3d.swing">HomeComponent3D.Projection</a>&nbsp;projection,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#HomeComponent3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.HomeController3D-">HomeComponent3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>
<div class="block">Creates a 3D component that displays <code>home</code> walls, rooms and furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeFurniturePanel.html#HomeFurniturePanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">HomeFurniturePanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays home furniture data according to the units
 set in <code>preferences</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#HomePane-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">HomePane</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
        <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;controller)</code>
<div class="block">Creates home view associated with its controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePDFPrinter.html#HomePDFPrinter-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-java.awt.Font-">HomePDFPrinter</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;controller,
              java.awt.Font&nbsp;defaultFont)</code>
<div class="block">Creates a PDF printer able to write to an output stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ImportedFurnitureWizardStepsPanel.html#ImportedFurnitureWizardStepsPanel-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController-">ImportedFurnitureWizardStepsPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                 java.lang.String&nbsp;modelName,
                                 boolean&nbsp;importHomePiece,
                                 <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController</a>&nbsp;controller)</code>
<div class="block">Creates a view for furniture import.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ImportedTextureWizardStepsPanel.html#ImportedTextureWizardStepsPanel-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">ImportedTextureWizardStepsPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;catalogTexture,
                               java.lang.String&nbsp;textureName,
                               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;controller)</code>
<div class="block">Creates a view for texture image choice and attributes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/LabelPanel.html#LabelPanel-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">LabelPanel</a></span>(boolean&nbsp;modification,
          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays label data.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/LevelPanel.html#LevelPanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">LevelPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays home levels data according to the units
 set in <code>preferences</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ModelMaterialsComponent.html#ModelMaterialsComponent-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">ModelMaterialsComponent</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;controller)</code>
<div class="block">Creates a texture button.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#MultipleLevelsPlanPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">MultipleLevelsPlanPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerLengthModel.html#NullableSpinnerLengthModel-com.eteks.sweethome3d.model.UserPreferences-float-float-">NullableSpinnerLengthModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          float&nbsp;minimum,
                          float&nbsp;maximum)</code>
<div class="block">Creates a model managing lengths between the given <code>minimum</code> and <code>maximum</code> values in centimeter.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerLengthModel.html#NullableSpinnerLengthModel-com.eteks.sweethome3d.model.UserPreferences-float-float-float-">NullableSpinnerLengthModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          float&nbsp;value,
                          float&nbsp;minimum,
                          float&nbsp;maximum)</code>
<div class="block">Creates a model managing lengths between the given <code>minimum</code> and <code>maximum</code> values in centimeter.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ObserverCameraPanel.html#ObserverCameraPanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">ObserverCameraPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays observer camera attributes data according to the units
 set in <code>preferences</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PageSetupPanel.html#PageSetupPanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">PageSetupPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays page setup.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotoPanel.html#PhotoPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.PhotoController-">PhotoPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotoPanel.html#PhotoPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">PhotoPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotoSizeAndQualityPanel.html#PhotoSizeAndQualityPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.AbstractPhotoController-">PhotoSizeAndQualityPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                        <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/AbstractPhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">AbstractPhotoController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotosPanel.html#PhotosPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.PhotosController-">PhotosPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
           <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
           <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
           <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotosPanel.html#PhotosPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">PhotosPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
           <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
           <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#PlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.PlanController-">PlanComponent</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
             <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
             <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
             <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</code>
<div class="block">Creates a new plan that displays <code>home</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#PlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">PlanComponent</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
             <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
             <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</code>
<div class="block">Creates a new plan that displays <code>home</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PolylinePanel.html#PolylinePanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">PolylinePanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
             <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;controller)</code>
<div class="block">Creates a preferences panel that layouts the editable properties
 of its <code>controller</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PrintPreviewPanel.html#PrintPreviewPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">PrintPreviewPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                 <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</code>
<div class="block">Creates a panel that displays print preview.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ResourceAction.html#ResourceAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-">ResourceAction</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Class&lt;?&gt;&nbsp;resourceClass,
              java.lang.String&nbsp;actionPrefix)</code>
<div class="block">Creates a disabled action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ResourceAction.html#ResourceAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-boolean-">ResourceAction</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Class&lt;?&gt;&nbsp;resourceClass,
              java.lang.String&nbsp;actionPrefix,
              boolean&nbsp;enabled)</code>
<div class="block">Creates an action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/RoomPanel.html#RoomPanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">RoomPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays room data according to the units set in
 <code>preferences</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/TextureChoiceComponent.html#TextureChoiceComponent-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.TextureChoiceController-">TextureChoiceComponent</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;controller)</code>
<div class="block">Creates a texture button.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ThreadedTaskPanel.html#ThreadedTaskPanel-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController-">ThreadedTaskPanel</a></span>(java.lang.String&nbsp;taskMessage,
                 <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html" title="class in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/UserPreferencesPanel.html#UserPreferencesPanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">UserPreferencesPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;controller)</code>
<div class="block">Creates a preferences panel that layouts the editable properties
 of its <code>controller</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/VideoPanel.html#VideoPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.VideoController-">VideoPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/VideoPanel.html#VideoPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">VideoPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;controller)</code>
<div class="block">Creates a video panel with default object 3D factory.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/WallPanel.html#WallPanel-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">WallPanel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;controller)</code>
<div class="block">Creates a panel that displays wall data according to the units set in
 <code>preferences</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/WizardPane.html#WizardPane-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">WizardPane</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;controller)</code>
<div class="block">Creates a wizard view controlled by <code>controller</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">createBackgroundImageWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;backgroundImageWizardController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">createBackgroundImageWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;backgroundImageWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to choose a background image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">createBaseboardChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;baseboardChoiceController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">createBaseboardChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;baseboardChoiceController)</code>
<div class="block">Returns a new view that edits the baseboard of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">createCompassView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;compassController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">createCompassView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;compassController)</code>
<div class="block">Returns a new view that edits compass values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">createDimensionLineView</a></span>(boolean&nbsp;modification,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;dimensionLineController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">createDimensionLineView</a></span>(boolean&nbsp;modification,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;dimensionLineController)</code>
<div class="block">Returns a new view that edits dimension line values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">createFurnitureCatalogView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;furnitureCatalogController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">createFurnitureCatalogView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;furnitureCatalogController)</code>
<div class="block">Returns a new view that displays furniture <code>catalog</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController)</code>
<div class="block">Returns a new view that displays <code>home</code> furniture list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHelpView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HelpController-">createHelpView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html" title="class in com.eteks.sweethome3d.viewcontroller">HelpController</a>&nbsp;helpController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHelpView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HelpController-">createHelpView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html" title="class in com.eteks.sweethome3d.viewcontroller">HelpController</a>&nbsp;helpController)</code>
<div class="block">Returns a new view that displays Sweet Home 3D help.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">createHome3DAttributesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;home3DAttributesController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">createHome3DAttributesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;home3DAttributesController)</code>
<div class="block">Returns a new view that edits 3D attributes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">createHomeFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;homeFurnitureController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">createHomeFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;homeFurnitureController)</code>
<div class="block">Returns a new view that edits furniture values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHomeView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">createHomeView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHomeView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">createHomeView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</code>
<div class="block">Returns a new view that displays <code>home</code> and its sub views.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createImportedFurnitureWizardStepsView-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController-">createImportedFurnitureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                      java.lang.String&nbsp;modelName,
                                      boolean&nbsp;importHomePiece,
                                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController</a>&nbsp;importedFurnitureWizardController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createImportedFurnitureWizardStepsView-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController-">createImportedFurnitureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                      java.lang.String&nbsp;modelName,
                                      boolean&nbsp;importHomePiece,
                                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController</a>&nbsp;importedFurnitureWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to import furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">createImportedTextureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                                    java.lang.String&nbsp;textureName,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;importedTextureWizardController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">createImportedTextureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                                    java.lang.String&nbsp;textureName,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;importedTextureWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to import a texture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">createLabelView</a></span>(boolean&nbsp;modification,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;labelController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">createLabelView</a></span>(boolean&nbsp;modification,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;labelController)</code>
<div class="block">Returns a new view that edits label values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">createLevelView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;levelController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">createLevelView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;levelController)</code>
<div class="block">Returns a new view that edits level values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">createModelMaterialsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;modelMaterialsController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">createModelMaterialsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;modelMaterialsController)</code>
<div class="block">Returns a new view that edits the materials of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html#createObject3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.Selectable-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-">createObject3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Object&nbsp;context,
              boolean&nbsp;waitForLoading)</code>
<div class="block">Returns the 3D object matching a given <code>item</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">createObserverCameraView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;home3dAttributesController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">createObserverCameraView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;home3DAttributesController)</code>
<div class="block">Returns a new view that edits observer camera values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">createPageSetupView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;pageSetupController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">createPageSetupView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;pageSetupController)</code>
<div class="block">Creates a new view that edits page setup.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">createPhotosView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;photosController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">createPhotosView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;photosController)</code>
<div class="block">Returns a new view able to compute a photos of a home from its stored points of view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">createPhotoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;photoController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">createPhotoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;photoController)</code>
<div class="block">Returns a new view able to compute a photo realistic image of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPlanView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">createPlanView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;planController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPlanView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">createPlanView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;planController)</code>
<div class="block">Returns a new view that displays <code>home</code> on a plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">createPolylineView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;polylineController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">createPolylineView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;polylineController)</code>
<div class="block">Returns a new view that edits polyline values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">createPrintPreviewView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">createPrintPreviewView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</code>
<div class="block">Returns a new view that displays home print preview.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">createRoomView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;roomController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">createRoomView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;roomController)</code>
<div class="block">Returns a new view that edits room values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TextureChoiceView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createTextureChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.TextureChoiceController-">createTextureChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;textureChoiceController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TextureChoiceView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createTextureChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.TextureChoiceController-">createTextureChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;textureChoiceController)</code>
<div class="block">Returns a new view that edits the texture of its controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createThreadedTaskView-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController-">createThreadedTaskView</a></span>(java.lang.String&nbsp;taskMessage,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html" title="class in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createThreadedTaskView-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController-">createThreadedTaskView</a></span>(java.lang.String&nbsp;taskMessage,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;userPreferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html" title="class in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController</a>&nbsp;threadedTaskController)</code>
<div class="block">Returns a new view that displays message for a threaded task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">createUserPreferencesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;userPreferencesController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">createUserPreferencesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;userPreferencesController)</code>
<div class="block">Returns a new view that edits user preferences.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">createVideoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;videoController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">createVideoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;videoController)</code>
<div class="block">Returns a new view able to compute a 3D video of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">createView3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
            <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
            <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">createView3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
            <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
            <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;homeController3D)</code>
<div class="block">Returns a new view that displays <code>home</code> in 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">createWallView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;wallController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">createWallView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;wallController)</code>
<div class="block">Returns a new view that edits wall values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">createWizardView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;wizardController)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">createWizardView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;wizardController)</code>
<div class="block">Returns a new view that displays a wizard.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/AbstractPhotoController.html#AbstractPhotoController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.viewcontroller.ContentManager-">AbstractPhotoController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view3D,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html#BackgroundImageWizardController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">BackgroundImageWizardController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#BaseboardChoiceController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">BaseboardChoiceController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates the controller of room view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html#CompassController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">CompassController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                 <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                 javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#DimensionLineController-com.eteks.sweethome3d.model.Home-float-float-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">DimensionLineController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       float&nbsp;x,
                       float&nbsp;y,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of dimension line view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#DimensionLineController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">DimensionLineController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of dimension line view with undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html#FurnitureCatalogController-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">FurnitureCatalogController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller of the furniture catalog view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#FurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">FurnitureController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates the controller of home furniture view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#FurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">FurnitureController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                   javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpController.html#HelpController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">HelpController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#Home3DAttributesController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">Home3DAttributesController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                          javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of 3D view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">HomeController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates the controller of home view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#HomeController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">HomeController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates the controller of home view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#HomeController3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.PlanController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">HomeController3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;planController,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home 3D view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#HomeController3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">HomeController3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home 3D view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#HomeFurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">HomeFurnitureController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#HomeFurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">HomeFurnitureController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedFurnitureWizardController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                 <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits <code>piece</code> values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-com.eteks.sweethome3d.model.Home-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">ImportedFurnitureWizardController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                 java.lang.String&nbsp;modelName,
                                 <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                                 javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates a controller that edits a new imported home piece of furniture
 with a given <code>modelName</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">ImportedFurnitureWizardController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                 <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                                 javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates a controller that edits a new imported home piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedFurnitureWizardController</a></span>(java.lang.String&nbsp;modelName,
                                 <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits a new catalog piece of furniture with a given
 <code>modelName</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedFurnitureWizardController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits a new catalog piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html#ImportedTextureWizardController-com.eteks.sweethome3d.model.CatalogTexture-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedTextureWizardController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits <code>texture</code> values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html#ImportedTextureWizardController-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedTextureWizardController</a></span>(java.lang.String&nbsp;textureName,
                               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits a new catalog texture with a given
 <code>textureName</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html#ImportedTextureWizardController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedTextureWizardController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits a new catalog texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#LabelController-com.eteks.sweethome3d.model.Home-float-float-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">LabelController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               float&nbsp;x,
               float&nbsp;y,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of label creation with undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#LabelController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">LabelController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of label modifications with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html#LevelController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">LevelController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home levels view with undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html#ModelMaterialsController-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ModelMaterialsController</a></span>(java.lang.String&nbsp;title,
                        <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#ObserverCameraController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">ObserverCameraController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                        <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates the controller of 3D view with undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html#PageSetupController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">PageSetupController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                   javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of page setup with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html#PhotoController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">PhotoController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view3D,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html#PhotosController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">PhotosController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view3D,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#PlanController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">PlanController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
              javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of plan view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html#PolylineController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">PolylineController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                  <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                  javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of polyline view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html#PrintPreviewController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.ViewFactory-">PrintPreviewController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates the controller of print preview with undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#RoomController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">RoomController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
              javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of room view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html#TextureChoiceController-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">TextureChoiceController</a></span>(java.lang.String&nbsp;title,
                       boolean&nbsp;rotationSupported,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html#TextureChoiceController-java.lang.String-java.lang.String-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">TextureChoiceController</a></span>(java.lang.String&nbsp;title,
                       java.lang.String&nbsp;fitAreaText,
                       boolean&nbsp;rotationSupported,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html#TextureChoiceController-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">TextureChoiceController</a></span>(java.lang.String&nbsp;title,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html#ThreadedTaskController-java.util.concurrent.Callable-java.lang.String-com.eteks.sweethome3d.viewcontroller.ThreadedTaskController.ExceptionHandler-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">ThreadedTaskController</a></span>(java.util.concurrent.Callable&lt;java.lang.Void&gt;&nbsp;threadedTask,
                      java.lang.String&nbsp;taskMessage,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.ExceptionHandler.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskController.ExceptionHandler</a>&nbsp;exceptionHandler,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates a controller that will execute in a separate thread the given task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#UserPreferencesController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">UserPreferencesController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates the controller of user preferences view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#UserPreferencesController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.viewcontroller.HomeController-">UserPreferencesController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</code>
<div class="block">Creates the controller of user preferences view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#VideoController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">VideoController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#WallController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">WallController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
              javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of wall view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#WizardController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">WizardController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/UserPreferences.html" target="_top">Frames</a></li>
<li><a href="UserPreferences.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
