<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Interface com.eteks.sweethome3d.model.Library (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.eteks.sweethome3d.model.Library (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/Library.html" target="_top">Frames</a></li>
<li><a href="Library.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.eteks.sweethome3d.model.Library" class="title">Uses of Interface<br>com.eteks.sweethome3d.model.Library</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.plugin">com.eteks.sweethome3d.plugin</a></td>
<td class="colLast">
<div class="block">Describes the super classes required to create Sweet Home 3D plug-ins.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a> in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">AppletUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Throws an exception because applet user preferences don't manage additional libraries.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Returns the libraries available in user preferences.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultFurnitureCatalog.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Returns the furniture libraries at initialization.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Throws an exception because default user preferences don't support libraries.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultTexturesCatalog.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Returns the furniture libraries at initialization.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#isLibraryDeletable-com.eteks.sweethome3d.model.Library-">isLibraryDeletable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&nbsp;library)</code>
<div class="block">Returns <code>true</code> if the given file <code>library</code> can be deleted.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#deleteLibraries-java.util.List-">deleteLibraries</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;libraries)</code>
<div class="block">Deletes the given <code>libraries</code> and updates user preferences.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>abstract java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Returns the libraries available in user preferences.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.plugin">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a> in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PluginManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html#getPluginLibraries--">getPluginLibraries</a></span>()</code>
<div class="block">Returns the available plug-in libraries.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PluginManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html#deletePlugins-java.util.List-">deletePlugins</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;libraries)</code>
<div class="block">Deletes the given plug-in <code>libraries</code> from managed plug-ins.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/Library.html" target="_top">Frames</a></li>
<li><a href="Library.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
