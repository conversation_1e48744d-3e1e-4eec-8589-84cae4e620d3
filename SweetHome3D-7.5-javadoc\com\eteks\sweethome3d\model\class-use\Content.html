<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Interface com.eteks.sweethome3d.model.Content (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.eteks.sweethome3d.model.Content (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/Content.html" target="_top">Frames</a></li>
<li><a href="Content.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.eteks.sweethome3d.model.Content" class="title">Uses of Interface<br>com.eteks.sweethome3d.model.Content</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d</a></td>
<td class="colLast">
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.tools">com.eteks.sweethome3d.tools</a></td>
<td class="colLast">
<div class="block">Contains various tool classes useful in 
<a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">View/Controller packages</a> and 
<a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">IO packages</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> that return <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLHandler.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#parseContent-java.lang.String-java.util.Map-java.lang.String-">parseContent</a></span>(java.lang.String&nbsp;elementName,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes,
            java.lang.String&nbsp;attributeName)</code>
<div class="block">Returns the content object matching the attribute named <code>attributeName</code> in the given element.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultFurnitureCatalog.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#getAdditionalContents-java.util.ResourceBundle-int-java.net.URL-java.net.URL-">getAdditionalContents</a></span>(java.util.ResourceBundle&nbsp;resource,
                     int&nbsp;index,
                     java.net.URL&nbsp;furnitureCatalogUrl,
                     java.net.URL&nbsp;furnitureResourcesUrlBase)</code>
<div class="block">Returns the contents of the piece at the given <code>index</code>
 different from default properties.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DamagedHomeIOException.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DamagedHomeIOException.html#getInvalidContent--">getInvalidContent</a></span>()</code>
<div class="block">Returns the invalid content in the damaged home.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">ContentDigestManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/ContentDigestManager.html#equals-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-">equals</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content1,
      <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content2)</code>
<div class="block">Returns <code>true</code> if the contents in parameter contains the same data,
 comparing their digest.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><span class="typeNameLabel">ContentDigestManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/ContentDigestManager.html#getContentDigest-com.eteks.sweethome3d.model.Content-">getContentDigest</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns the SHA-1 digest of the given <code>content</code>, computing it
 if it wasn't set.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.Long</code></td>
<td class="colLast"><span class="typeNameLabel">ContentDigestManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/ContentDigestManager.html#getContentSize-com.eteks.sweethome3d.model.Content-">getContentSize</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns the size of the given <code>content</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#getExportedContentName-java.lang.Object-com.eteks.sweethome3d.model.Content-">getExportedContentName</a></span>(java.lang.Object&nbsp;owner,
                      <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns the saved name of the given <code>content</code> owned by an object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">ContentDigestManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/ContentDigestManager.html#isContentDigestEqual-com.eteks.sweethome3d.model.Content-byte:A-">isContentDigestEqual</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                    byte[]&nbsp;digest)</code>
<div class="block">Returns <code>true</code> if the digest of the given <code>content</code>
 is equal to <code>digest</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ContentDigestManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/ContentDigestManager.html#setContentDigest-com.eteks.sweethome3d.model.Content-byte:A-">setContentDigest</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                byte[]&nbsp;digest)</code>
<div class="block">Sets the SHA-1 digest of the given <code>content</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeMaterial-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeMaterial-com.eteks.sweethome3d.model.Content-">writeMaterial</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>&nbsp;material,
             <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Writes in XML the <code>material</code> object with the given <code>writer</code>.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DamagedHomeIOException.html#DamagedHomeIOException-com.eteks.sweethome3d.model.Home-java.util.List-">DamagedHomeIOException</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                      java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent)</code>
<div class="block">Creates an exception for the given damaged home with the invalid content it may contains.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.j3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a> in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>javax.media.j3d.BranchGroup</code></td>
<td class="colLast"><span class="typeNameLabel">ModelManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/ModelManager.html#loadModel-com.eteks.sweethome3d.model.Content-">loadModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns the node loaded synchronously from <code>content</code> with supported loaders.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ModelManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/ModelManager.html#loadModel-com.eteks.sweethome3d.model.Content-boolean-com.eteks.sweethome3d.j3d.ModelManager.ModelObserver-">loadModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
         boolean&nbsp;synchronous,
         <a href="../../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a>&nbsp;modelObserver)</code>
<div class="block">Reads a 3D node from <code>content</code> with supported loaders
 and notifies the loaded model to the given <code>modelObserver</code> once available.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ModelManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/ModelManager.html#loadModel-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.j3d.ModelManager.ModelObserver-">loadModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
         <a href="../../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a>&nbsp;modelObserver)</code>
<div class="block">Reads asynchronously a 3D node from <code>content</code> with supported loaders
 and notifies the loaded model to the given <code>modelObserver</code> once available.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>javax.media.j3d.Texture</code></td>
<td class="colLast"><span class="typeNameLabel">TextureManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.html#loadTexture-com.eteks.sweethome3d.model.Content-">loadTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns a texture created from the image from <code>content</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">TextureManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.html#loadTexture-com.eteks.sweethome3d.model.Content-boolean-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">loadTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
           boolean&nbsp;synchronous,
           <a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</code>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">TextureManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.html#loadTexture-com.eteks.sweethome3d.model.Content-float-boolean-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">loadTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
           float&nbsp;angle,
           boolean&nbsp;synchronous,
           <a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</code>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">TextureManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.html#loadTexture-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">loadTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
           <a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</code>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>
 If the texture isn't loaded in cache yet, a one pixel white image texture will be notified 
 immediately to the given <code>textureObserver</code>, then a second notification will 
 be given in Event Dispatch Thread once the image texture is loaded.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeDescriptor.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeDescriptor.html#getContent--">getContent</a></span>()</code>
<div class="block">Returns the content to read this home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">PieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the value of an additional content <code>name</code> associated to this piece.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">CatalogPieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the value of an additional content <code>name</code> associated to this piece.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeObject.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the value of the content <code>name</code> associated to this object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">CatalogItem.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogItem.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this catalog item.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">PieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">CatalogPieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeDescriptor.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeDescriptor.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">CatalogTexture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextureImage.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/TextureImage.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the content of the image used for this texture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">BackgroundImage.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the image content of this background image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeTexture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the content of the image used for this texture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">CatalogTexture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the content of the image used for this texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">PieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">CatalogPieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">PieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getPlanIcon--">getPlanIcon</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">CatalogPieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">DamagedHomeRecorderException.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/DamagedHomeRecorderException.html#getInvalidContent--">getInvalidContent</a></span>()</code>
<div class="block">Returns the invalid content in the damaged home.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setIcon-com.eteks.sweethome3d.model.Content-">setIcon</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setIcon-com.eteks.sweethome3d.model.Content-">setIcon</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon)</code>
<div class="block">Sets the icon of this piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Sets the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setPlanIcon-com.eteks.sweethome3d.model.Content-">setPlanIcon</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPlanIcon-com.eteks.sweethome3d.model.Content-">setPlanIcon</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon)</code>
<div class="block">Sets the plan icon of this piece of furniture.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html#BackgroundImage-com.eteks.sweethome3d.model.Content-float-float-float-float-float-float-float-">BackgroundImage</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
               float&nbsp;scaleDistance,
               float&nbsp;scaleDistanceXStart,
               float&nbsp;scaleDistanceYStart,
               float&nbsp;scaleDistanceXEnd,
               float&nbsp;scaleDistanceYEnd,
               float&nbsp;xOrigin,
               float&nbsp;yOrigin)</code>
<div class="block">Creates a visible background image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html#BackgroundImage-com.eteks.sweethome3d.model.Content-float-float-float-float-float-float-float-boolean-">BackgroundImage</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
               float&nbsp;scaleDistance,
               float&nbsp;scaleDistanceXStart,
               float&nbsp;scaleDistanceYStart,
               float&nbsp;scaleDistanceXEnd,
               float&nbsp;scaleDistanceYEnd,
               float&nbsp;xOrigin,
               float&nbsp;yOrigin,
               boolean&nbsp;visible)</code>
<div class="block">Creates a background image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-boolean-float-boolean-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;name,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   java.lang.Integer&nbsp;color,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   float&nbsp;iconYaw,
                   boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-boolean-java.lang.Long-java.lang.String-float-boolean-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;name,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   java.lang.Integer&nbsp;color,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   float&nbsp;iconYaw,
                   boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-java.lang.Integer-float:A:A-int-java.lang.Long-java.lang.String-float-float-float-boolean-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;name,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   java.lang.Integer&nbsp;color,
                   float[][]&nbsp;modelRotation,
                   int&nbsp;modelFlags,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   float&nbsp;iconYaw,
                   float&nbsp;iconPitch,
                   float&nbsp;iconScale,
                   boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog door or window with all its values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency,
                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   int&nbsp;modelFlags,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency,
                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-boolean-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   boolean&nbsp;backFaceShown,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-com.eteks.sweethome3d.model.Sash:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String&nbsp;license,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   int&nbsp;modelFlags,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency,
                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                   java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String:A-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String[]&nbsp;lightSourceMaterialNames,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            int&nbsp;modelFlags,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            boolean&nbsp;backFaceShown,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            boolean&nbsp;backFaceShown,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-boolean-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            boolean&nbsp;backFaceShown,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String:A-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String&nbsp;license,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String[]&nbsp;lightSourceMaterialNames,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            int&nbsp;modelFlags,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
            java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-boolean-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       boolean&nbsp;movable,
                       boolean&nbsp;doorOrWindow)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-boolean-java.lang.Integer-float:A:A-boolean-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       boolean&nbsp;doorOrWindow,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       float&nbsp;iconYaw,
                       boolean&nbsp;proportional)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.Integer-float:A:A-boolean-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       float&nbsp;iconYaw,
                       boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-boolean-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       float&nbsp;iconYaw,
                       boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-boolean-java.lang.Long-java.lang.String-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       float&nbsp;iconYaw,
                       boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-int-java.lang.Long-java.lang.String-float-float-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       int&nbsp;modelFlags,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       float&nbsp;iconYaw,
                       float&nbsp;iconPitch,
                       float&nbsp;iconScale,
                       boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       boolean&nbsp;doorOrWindow,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency,
                       java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       int&nbsp;modelFlags,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency,
                       java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String&nbsp;license,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       int&nbsp;modelFlags,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency,
                       java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                       java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html#CatalogShelfUnit-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-float:A-com.eteks.sweethome3d.model.BoxBounds:A-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogShelfUnit</a></span>(java.lang.String&nbsp;id,
                java.lang.String&nbsp;name,
                java.lang.String&nbsp;description,
                java.lang.String&nbsp;information,
                java.lang.String&nbsp;license,
                java.lang.String[]&nbsp;tags,
                java.lang.Long&nbsp;creationDate,
                java.lang.Float&nbsp;grade,
                <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                float&nbsp;width,
                float&nbsp;depth,
                float&nbsp;height,
                float&nbsp;elevation,
                float&nbsp;dropOnTopElevation,
                float[]&nbsp;shelfElevations,
                <a href="../../../../../com/eteks/sweethome3d/model/BoxBounds.html" title="class in com.eteks.sweethome3d.model">BoxBounds</a>[]&nbsp;shelfBoxes,
                boolean&nbsp;movable,
                java.lang.String&nbsp;staircaseCutOutShape,
                float[][]&nbsp;modelRotation,
                int&nbsp;modelFlags,
                java.lang.Long&nbsp;modelSize,
                java.lang.String&nbsp;creator,
                boolean&nbsp;resizable,
                boolean&nbsp;deformable,
                boolean&nbsp;texturable,
                boolean&nbsp;horizontallyRotatable,
                java.math.BigDecimal&nbsp;price,
                java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                java.lang.String&nbsp;currency,
                java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog shelf unit of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html#CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.Content-float-float-">CatalogTexture</a></span>(java.lang.String&nbsp;name,
              <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
              float&nbsp;width,
              float&nbsp;height)</code>
<div class="block">Creates an unmodifiable catalog texture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html#CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.Content-float-float-boolean-">CatalogTexture</a></span>(java.lang.String&nbsp;name,
              <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
              float&nbsp;width,
              float&nbsp;height,
              boolean&nbsp;modifiable)</code>
<div class="block">Creates a catalog texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html#CatalogTexture-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-float-float-java.lang.String-">CatalogTexture</a></span>(java.lang.String&nbsp;id,
              java.lang.String&nbsp;name,
              <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
              float&nbsp;width,
              float&nbsp;height,
              java.lang.String&nbsp;creator)</code>
<div class="block">Creates a catalog texture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html#CatalogTexture-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-float-float-java.lang.String-boolean-">CatalogTexture</a></span>(java.lang.String&nbsp;id,
              java.lang.String&nbsp;name,
              <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image,
              float&nbsp;width,
              float&nbsp;height,
              java.lang.String&nbsp;creator,
              boolean&nbsp;modifiable)</code>
<div class="block">Creates a catalog texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeDescriptor.html#HomeDescriptor-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-">HomeDescriptor</a></span>(java.lang.String&nbsp;name,
              <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
              <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon)</code>
<div class="block">Creates a home descriptor.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html#CatalogDoorOrWindow-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float-float-boolean-boolean-com.eteks.sweethome3d.model.Sash:A-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                   java.lang.String&nbsp;name,
                   java.lang.String&nbsp;description,
                   java.lang.String&nbsp;information,
                   java.lang.String&nbsp;license,
                   java.lang.String[]&nbsp;tags,
                   java.lang.Long&nbsp;creationDate,
                   java.lang.Float&nbsp;grade,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                   <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                   float&nbsp;width,
                   float&nbsp;depth,
                   float&nbsp;height,
                   float&nbsp;elevation,
                   float&nbsp;dropOnTopElevation,
                   boolean&nbsp;movable,
                   java.lang.String&nbsp;cutOutShape,
                   float&nbsp;wallThickness,
                   float&nbsp;wallDistance,
                   boolean&nbsp;wallCutOutOnBothSides,
                   boolean&nbsp;widthDepthDeformable,
                   <a href="../../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes,
                   float[][]&nbsp;modelRotation,
                   int&nbsp;modelFlags,
                   java.lang.Long&nbsp;modelSize,
                   java.lang.String&nbsp;creator,
                   boolean&nbsp;resizable,
                   boolean&nbsp;deformable,
                   boolean&nbsp;texturable,
                   java.math.BigDecimal&nbsp;price,
                   java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                   java.lang.String&nbsp;currency,
                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                   java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog door or window of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String:A-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String&nbsp;license,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String[]&nbsp;lightSourceMaterialNames,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            int&nbsp;modelFlags,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
            java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String&nbsp;license,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       int&nbsp;modelFlags,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency,
                       java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                       java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html#CatalogShelfUnit-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-float:A-com.eteks.sweethome3d.model.BoxBounds:A-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogShelfUnit</a></span>(java.lang.String&nbsp;id,
                java.lang.String&nbsp;name,
                java.lang.String&nbsp;description,
                java.lang.String&nbsp;information,
                java.lang.String&nbsp;license,
                java.lang.String[]&nbsp;tags,
                java.lang.Long&nbsp;creationDate,
                java.lang.Float&nbsp;grade,
                <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                float&nbsp;width,
                float&nbsp;depth,
                float&nbsp;height,
                float&nbsp;elevation,
                float&nbsp;dropOnTopElevation,
                float[]&nbsp;shelfElevations,
                <a href="../../../../../com/eteks/sweethome3d/model/BoxBounds.html" title="class in com.eteks.sweethome3d.model">BoxBounds</a>[]&nbsp;shelfBoxes,
                boolean&nbsp;movable,
                java.lang.String&nbsp;staircaseCutOutShape,
                float[][]&nbsp;modelRotation,
                int&nbsp;modelFlags,
                java.lang.Long&nbsp;modelSize,
                java.lang.String&nbsp;creator,
                boolean&nbsp;resizable,
                boolean&nbsp;deformable,
                boolean&nbsp;texturable,
                boolean&nbsp;horizontallyRotatable,
                java.math.BigDecimal&nbsp;price,
                java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                java.lang.String&nbsp;currency,
                java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog shelf unit of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/DamagedHomeRecorderException.html#DamagedHomeRecorderException-com.eteks.sweethome3d.model.Home-java.util.List-">DamagedHomeRecorderException</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                            java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent)</code>
<div class="block">Creates an exception for the given damaged home with the invalid content it may contains.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/DamagedHomeRecorderException.html#DamagedHomeRecorderException-com.eteks.sweethome3d.model.Home-java.util.List-java.lang.String-">DamagedHomeRecorderException</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                            java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent,
                            java.lang.String&nbsp;message)</code>
<div class="block">Creates an exception for the given damaged home with the invalid content it may contains.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> that return <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">FileContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getContent-java.lang.String-">getContent</a></span>(java.lang.String&nbsp;contentPath)</code>
<div class="block">Returns a <a href="../../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools"><code>URL content</code></a> object that references
 the given file path.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ImportedFurnitureWizardStepsPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ImportedFurnitureWizardStepsPanel.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon content of the chosen piece.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ModelPreviewComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getIcon-int-">getIcon</a></span>(int&nbsp;maxWaitingDelay)</code>
<div class="block">Returns a temporary content of the icon matching the displayed view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ModelPreviewComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model content displayed by this component.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>javax.swing.Icon</code></td>
<td class="colLast"><span class="typeNameLabel">IconManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/IconManager.html#getIcon-com.eteks.sweethome3d.model.Content-java.awt.Component-">getIcon</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
       java.awt.Component&nbsp;waitingComponent)</code>
<div class="block">Returns an icon read from <code>content</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>javax.swing.Icon</code></td>
<td class="colLast"><span class="typeNameLabel">IconManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/IconManager.html#getIcon-com.eteks.sweethome3d.model.Content-int-java.awt.Component-">getIcon</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
       int&nbsp;height,
       java.awt.Component&nbsp;waitingComponent)</code>
<div class="block">Returns an icon read from <code>content</code> and rescaled at a given <code>height</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.awt.Dimension</code></td>
<td class="colLast"><span class="typeNameLabel">SwingTools.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingTools.html#getImageSizeInPixels-com.eteks.sweethome3d.model.Content-">getImageSizeInPixels</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image)</code>
<div class="block">Returns <code>image</code> size in pixels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ModelPreviewComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Sets the 3D model content displayed by this component.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmOpenDamagedHome-java.lang.String-com.eteks.sweethome3d.model.Home-java.util.List-">confirmOpenDamagedHome</a></span>(java.lang.String&nbsp;homeName,
                      <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                      java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent)</code>
<div class="block">Displays a dialog that lets user choose what he wants to do with a damaged home he tries to open it.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.tools">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a> in <a href="../../../../../com/eteks/sweethome3d/tools/package-summary.html">com.eteks.sweethome3d.tools</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/eteks/sweethome3d/tools/package-summary.html">com.eteks.sweethome3d.tools</a> that implement <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/tools/ResourceURLContent.html" title="class in com.eteks.sweethome3d.tools">ResourceURLContent</a></span></code>
<div class="block">URL content read from a class resource or a SH3F file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/tools/SimpleURLContent.html" title="class in com.eteks.sweethome3d.tools">SimpleURLContent</a></span></code>
<div class="block">Content read from a URL with no dependency on other content when this URL is a JAR entry.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/tools/TemporaryURLContent.html" title="class in com.eteks.sweethome3d.tools">TemporaryURLContent</a></span></code>
<div class="block">URL content for files, images stored in temporary files.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools">URLContent</a></span></code>
<div class="block">URL content for files, images...</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/tools/package-summary.html">com.eteks.sweethome3d.tools</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/tools/TemporaryURLContent.html" title="class in com.eteks.sweethome3d.tools">TemporaryURLContent</a></code></td>
<td class="colLast"><span class="typeNameLabel">TemporaryURLContent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/tools/TemporaryURLContent.html#copyToTemporaryURLContent-com.eteks.sweethome3d.model.Content-">copyToTemporaryURLContent</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns a <a href="../../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools"><code>URL content</code></a> object that references a temporary copy of
 a given <code>content</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#getContent-java.lang.String-">getContent</a></span>(java.lang.String&nbsp;contentLocation)</code>
<div class="block">Returns a <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model"><code>content</code></a> object that references a given content location.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the edited icon.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ImportedFurnitureWizardStepsView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon content of the chosen piece.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">BackgroundImageWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the image content of the background image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ImportedTextureWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the image content of the imported texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ImportedFurnitureWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the model content of the imported piece.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ModelMaterialsController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model which materials are displayed by the view.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">BackgroundImageWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html#setImage-com.eteks.sweethome3d.model.Content-">setImage</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image)</code>
<div class="block">Sets the image content of the background image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ImportedTextureWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html#setImage-com.eteks.sweethome3d.model.Content-">setImage</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image)</code>
<div class="block">Sets the image content of the imported texture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ImportedFurnitureWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Sets the model content of the imported piece.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ModelMaterialsController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Sets the 3D model which materials are displayed by the view
 and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmOpenDamagedHome-java.lang.String-com.eteks.sweethome3d.model.Home-java.util.List-">confirmOpenDamagedHome</a></span>(java.lang.String&nbsp;homeName,
                      <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                      java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent)</code>
<div class="block">Displays a dialog that lets user choose what he wants
 to do with a damaged home he tries to open it.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/Content.html" target="_top">Frames</a></li>
<li><a href="Content.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
