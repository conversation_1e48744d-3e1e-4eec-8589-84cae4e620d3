<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Interface com.eteks.sweethome3d.viewcontroller.DialogView (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.eteks.sweethome3d.viewcontroller.DialogView (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/viewcontroller/class-use/DialogView.html" target="_top">Frames</a></li>
<li><a href="DialogView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.eteks.sweethome3d.viewcontroller.DialogView" class="title">Uses of Interface<br>com.eteks.sweethome3d.viewcontroller.DialogView</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> that implement <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/CompassPanel.html" title="class in com.eteks.sweethome3d.swing">CompassPanel</a></span></code>
<div class="block">Compass editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html" title="class in com.eteks.sweethome3d.swing">DimensionLinePanel</a></span></code>
<div class="block">Dimension line editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/Home3DAttributesPanel.html" title="class in com.eteks.sweethome3d.swing">Home3DAttributesPanel</a></span></code>
<div class="block">Home 3D attributes editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeFurniturePanel.html" title="class in com.eteks.sweethome3d.swing">HomeFurniturePanel</a></span></code>
<div class="block">Home furniture editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/LabelPanel.html" title="class in com.eteks.sweethome3d.swing">LabelPanel</a></span></code>
<div class="block">Label editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/LevelPanel.html" title="class in com.eteks.sweethome3d.swing">LevelPanel</a></span></code>
<div class="block">Level editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ObserverCameraPanel.html" title="class in com.eteks.sweethome3d.swing">ObserverCameraPanel</a></span></code>
<div class="block">Observer camera editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PageSetupPanel.html" title="class in com.eteks.sweethome3d.swing">PageSetupPanel</a></span></code>
<div class="block">Home page setup editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotoPanel.html" title="class in com.eteks.sweethome3d.swing">PhotoPanel</a></span></code>
<div class="block">A panel to edit photo creation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotosPanel.html" title="class in com.eteks.sweethome3d.swing">PhotosPanel</a></span></code>
<div class="block">A panel to edit photos created at home points of view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PolylinePanel.html" title="class in com.eteks.sweethome3d.swing">PolylinePanel</a></span></code>
<div class="block">User preferences panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PrintPreviewPanel.html" title="class in com.eteks.sweethome3d.swing">PrintPreviewPanel</a></span></code>
<div class="block">Home print preview editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/RoomPanel.html" title="class in com.eteks.sweethome3d.swing">RoomPanel</a></span></code>
<div class="block">Room editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/UserPreferencesPanel.html" title="class in com.eteks.sweethome3d.swing">UserPreferencesPanel</a></span></code>
<div class="block">User preferences panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/VideoPanel.html" title="class in com.eteks.sweethome3d.swing">VideoPanel</a></span></code>
<div class="block">A panel used for video creation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/WallPanel.html" title="class in com.eteks.sweethome3d.swing">WallPanel</a></span></code>
<div class="block">Wall editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/WizardPane.html" title="class in com.eteks.sweethome3d.swing">WizardPane</a></span></code>
<div class="block">Wizard pane.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> that return <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">createCompassView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;compassController)</code>
<div class="block">Returns a new view that edits compass values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">createDimensionLineView</a></span>(boolean&nbsp;modification,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;dimensionLineController)</code>
<div class="block">Returns a new view that edits dimension line values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">createHome3DAttributesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;home3DAttributesController)</code>
<div class="block">Returns a new view that edits 3D attributes.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">createHomeFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;homeFurnitureController)</code>
<div class="block">Returns a new view that edits furniture values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">createLabelView</a></span>(boolean&nbsp;modification,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;labelController)</code>
<div class="block">Returns a new view that edits label values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">createLevelView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;levelController)</code>
<div class="block">Returns a new view that edits level values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">createObserverCameraView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;observerCameraController)</code>
<div class="block">Returns a new view that edits observer camera values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">createPageSetupView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;pageSetupController)</code>
<div class="block">Creates a new view that edits page setup.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">createPhotosView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;photosController)</code>
<div class="block">Returns a new view able to compute a photos of a home from its stored points of view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">createPhotoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;photoController)</code>
<div class="block">Returns a new view able to create photo realistic images of the given home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">createPolylineView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;polylineController)</code>
<div class="block">Returns a new view that edits polyline values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">createPrintPreviewView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</code>
<div class="block">Returns a new view that displays <code>home</code> print preview.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">createRoomView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;roomController)</code>
<div class="block">Returns a new view that edits room values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">createUserPreferencesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;userPreferencesController)</code>
<div class="block">Returns a new view that edits user preferences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">createVideoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;videoController)</code>
<div class="block">Returns a new view able to create 3D videos of the given home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">createWallView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;wallController)</code>
<div class="block">Returns a new view that edits wall values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">createWizardView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;wizardController)</code>
<div class="block">Returns a new view that displays a wizard.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">createCompassView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;compassController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createCompassView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.CompassController-">createCompassView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                 <a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html" title="class in com.eteks.sweethome3d.viewcontroller">CompassController</a>&nbsp;compassController)</code>
<div class="block">Returns a new view that edits compass values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">createDimensionLineView</a></span>(boolean&nbsp;modification,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;dimensionLineController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createDimensionLineView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.DimensionLineController-">createDimensionLineView</a></span>(boolean&nbsp;modification,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" title="class in com.eteks.sweethome3d.viewcontroller">DimensionLineController</a>&nbsp;dimensionLineController)</code>
<div class="block">Returns a new view that edits dimension line values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">createHome3DAttributesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;home3DAttributesController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHome3DAttributesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Home3DAttributesController-">createHome3DAttributesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html" title="class in com.eteks.sweethome3d.viewcontroller">Home3DAttributesController</a>&nbsp;home3DAttributesController)</code>
<div class="block">Returns a new view that edits 3D attributes.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">createHomeFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;homeFurnitureController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createHomeFurnitureView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeFurnitureController-">createHomeFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeFurnitureController</a>&nbsp;homeFurnitureController)</code>
<div class="block">Returns a new view that edits furniture values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">createLabelView</a></span>(boolean&nbsp;modification,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;labelController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createLabelView-boolean-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LabelController-">createLabelView</a></span>(boolean&nbsp;modification,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LabelController</a>&nbsp;labelController)</code>
<div class="block">Returns a new view that edits label values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">createLevelView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;levelController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createLevelView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.LevelController-">createLevelView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html" title="class in com.eteks.sweethome3d.viewcontroller">LevelController</a>&nbsp;levelController)</code>
<div class="block">Returns a new view that edits level values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">createObserverCameraView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;home3dAttributesController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createObserverCameraView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ObserverCameraController-">createObserverCameraView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html" title="class in com.eteks.sweethome3d.viewcontroller">ObserverCameraController</a>&nbsp;home3DAttributesController)</code>
<div class="block">Returns a new view that edits observer camera values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">createPageSetupView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;pageSetupController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPageSetupView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PageSetupController-">createPageSetupView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html" title="class in com.eteks.sweethome3d.viewcontroller">PageSetupController</a>&nbsp;pageSetupController)</code>
<div class="block">Creates a new view that edits page setup.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">createPhotosView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;photosController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPhotosView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotosController-">createPhotosView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotosController</a>&nbsp;photosController)</code>
<div class="block">Returns a new view able to compute a photos of a home from its stored points of view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">createPhotoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;photoController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPhotoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PhotoController-">createPhotoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html" title="class in com.eteks.sweethome3d.viewcontroller">PhotoController</a>&nbsp;photoController)</code>
<div class="block">Returns a new view able to compute a photo realistic image of a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">createPolylineView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;polylineController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPolylineView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PolylineController-">createPolylineView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                  <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html" title="class in com.eteks.sweethome3d.viewcontroller">PolylineController</a>&nbsp;polylineController)</code>
<div class="block">Returns a new view that edits polyline values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">createPrintPreviewView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createPrintPreviewView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-com.eteks.sweethome3d.viewcontroller.PrintPreviewController-">createPrintPreviewView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController,
                      <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller">PrintPreviewController</a>&nbsp;printPreviewController)</code>
<div class="block">Returns a new view that displays home print preview.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">createRoomView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;roomController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createRoomView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.RoomController-">createRoomView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html" title="class in com.eteks.sweethome3d.viewcontroller">RoomController</a>&nbsp;roomController)</code>
<div class="block">Returns a new view that edits room values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">createUserPreferencesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;userPreferencesController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createUserPreferencesView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.UserPreferencesController-">createUserPreferencesView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" title="class in com.eteks.sweethome3d.viewcontroller">UserPreferencesController</a>&nbsp;userPreferencesController)</code>
<div class="block">Returns a new view that edits user preferences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">createVideoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;videoController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createVideoView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.VideoController-">createVideoView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html" title="class in com.eteks.sweethome3d.viewcontroller">VideoController</a>&nbsp;videoController)</code>
<div class="block">Returns a new view able to compute a 3D video of a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">createWallView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;wallController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createWallView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WallController-">createWallView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html" title="class in com.eteks.sweethome3d.viewcontroller">WallController</a>&nbsp;wallController)</code>
<div class="block">Returns a new view that edits wall values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">createWizardView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;wizardController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createWizardView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.WizardController-">createWizardView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>&nbsp;wizardController)</code>
<div class="block">Returns a new view that displays a wizard.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">LabelController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">WallController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">PrintPreviewController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">LevelController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">PageSetupController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">PhotosController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">Home3DAttributesController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">VideoController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferencesController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">ObserverCameraController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">WizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">PolylineController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">CompassController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">RoomController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">PhotoController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><span class="typeNameLabel">DimensionLineController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/viewcontroller/class-use/DialogView.html" target="_top">Frames</a></li>
<li><a href="DialogView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
