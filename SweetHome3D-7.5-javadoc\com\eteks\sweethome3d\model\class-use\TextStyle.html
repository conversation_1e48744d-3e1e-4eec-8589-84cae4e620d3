<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.model.TextStyle (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.model.TextStyle (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/TextStyle.html" target="_top">Frames</a></li>
<li><a href="TextStyle.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.model.TextStyle" class="title">Uses of Class<br>com.eteks.sweethome3d.model.TextStyle</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeTextStyle-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.TextStyle-java.lang.String-">writeTextStyle</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
              <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle,
              java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>textStyle</code> object with the given <code>writer</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextStyle.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveBoldStyle-boolean-">deriveBoldStyle</a></span>(boolean&nbsp;bold)</code>
<div class="block">Returns a derived style of this text style with a given bold style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextStyle.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveItalicStyle-boolean-">deriveItalicStyle</a></span>(boolean&nbsp;italic)</code>
<div class="block">Returns a derived style of this text style with a given italic style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextStyle.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveStyle-float-">deriveStyle</a></span>(float&nbsp;fontSize)</code>
<div class="block">Returns a derived style of this text style with a given font size.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextStyle.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveStyle-java.lang.String-">deriveStyle</a></span>(java.lang.String&nbsp;fontName)</code>
<div class="block">Returns a derived style of this text style with a given font name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextStyle.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveStyle-com.eteks.sweethome3d.model.TextStyle.Alignment-">deriveStyle</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;alignment)</code>
<div class="block">Returns a derived style of this text style with a given alignment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">Room.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html#getAreaStyle--">getAreaStyle</a></span>()</code>
<div class="block">Returns the text style used to display room area.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultTextStyle-java.lang.Class-">getDefaultTextStyle</a></span>(java.lang.Class&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;selectableClass)</code>
<div class="block">Returns the default text style of a class of selectable item.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">DimensionLine.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/DimensionLine.html#getLengthStyle--">getLengthStyle</a></span>()</code>
<div class="block">Returns the text style used to display dimension line length.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameStyle--">getNameStyle</a></span>()</code>
<div class="block">Returns the text style used to display piece name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">Room.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html#getNameStyle--">getNameStyle</a></span>()</code>
<div class="block">Returns the text style used to display room name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">Label.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Label.html#getStyle--">getStyle</a></span>()</code>
<div class="block">Returns the style used to display the text of this label.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Room.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html#setAreaStyle-com.eteks.sweethome3d.model.TextStyle-">setAreaStyle</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;areaStyle)</code>
<div class="block">Sets the text style used to display room area.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DimensionLine.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/DimensionLine.html#setLengthStyle-com.eteks.sweethome3d.model.TextStyle-">setLengthStyle</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;lengthStyle)</code>
<div class="block">Sets the text style used to display dimension line length.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameStyle-com.eteks.sweethome3d.model.TextStyle-">setNameStyle</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;nameStyle)</code>
<div class="block">Sets the text style used to display piece name.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Room.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html#setNameStyle-com.eteks.sweethome3d.model.TextStyle-">setNameStyle</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;nameStyle)</code>
<div class="block">Sets the text style used to display room name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Label.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Label.html#setStyle-com.eteks.sweethome3d.model.TextStyle-">setStyle</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style)</code>
<div class="block">Sets the style used to display the text of this label.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected java.awt.Font</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getFont-java.awt.Font-com.eteks.sweethome3d.model.TextStyle-">getFont</a></span>(java.awt.Font&nbsp;defaultFont,
       <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle)</code>
<div class="block">Returns the AWT font matching a given text style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.awt.FontMetrics</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getFontMetrics-java.awt.Font-com.eteks.sweethome3d.model.TextStyle-">getFontMetrics</a></span>(java.awt.Font&nbsp;defaultFont,
              <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle)</code>
<div class="block">Returns the font metrics matching a given text style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">getTextBounds</a></span>(java.lang.String&nbsp;text,
             <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
             float&nbsp;x,
             float&nbsp;y,
             float&nbsp;angle)</code>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> centered at
 the point (<code>x</code>,<code>y</code>).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">getTextBounds</a></span>(java.lang.String&nbsp;text,
             <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
             float&nbsp;x,
             float&nbsp;y,
             float&nbsp;angle)</code>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> displayed at
 the point (<code>x</code>,<code>y</code>).</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><span class="typeNameLabel">PlanView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">getTextBounds</a></span>(java.lang.String&nbsp;text,
             <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
             float&nbsp;x,
             float&nbsp;y,
             float&nbsp;angle)</code>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> displayed at
 the point (<code>x</code>,<code>y</code>).</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/TextStyle.html" target="_top">Frames</a></li>
<li><a href="TextStyle.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
