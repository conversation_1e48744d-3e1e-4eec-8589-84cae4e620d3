/*
 * AIResourceBundle.java 18 Sep 2025
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 */
package com.eteks.sweethome3d.plugin.ai;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

/**
 * Utility class for accessing internationalized strings for the AI plugin.
 * This class provides a centralized way to access localized strings from
 * the ApplicationPlugin resource bundles.
 *
 * <AUTHOR>
 * @deprecated Use UserPreferences.getLocalizedString() directly for better integration with SweetHome 3D's internationalization system
 */
@Deprecated
public class AIResourceBundle {
  private static final String BUNDLE_NAME = "com.eteks.sweethome3d.plugin.ai.ApplicationPlugin";
  private static ResourceBundle resourceBundle;
  
  static {
    try {
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.getDefault());
    } catch (MissingResourceException ex) {
      // Fallback to default locale if current locale is not available
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.ENGLISH);
    }
  }
  
  /**
   * Returns the localized string for the given key.
   * 
   * @param key the resource key
   * @return the localized string, or the key itself if not found
   */
  public static String getString(String key) {
    try {
      return resourceBundle.getString(key);
    } catch (MissingResourceException ex) {
      // Return the key itself if the resource is not found
      return key;
    }
  }
  
  /**
   * Returns the localized string for the given key with formatted arguments.
   * 
   * @param key the resource key
   * @param arguments the arguments to format into the string
   * @return the formatted localized string
   */
  public static String getString(String key, Object... arguments) {
    try {
      String pattern = resourceBundle.getString(key);
      return MessageFormat.format(pattern, arguments);
    } catch (MissingResourceException ex) {
      // Return the key itself if the resource is not found
      return key;
    }
  }
  
  /**
   * Returns the localized string for the given key, or the default value if not found.
   * 
   * @param key the resource key
   * @param defaultValue the default value to return if key is not found
   * @return the localized string or default value
   */
  public static String getString(String key, String defaultValue) {
    try {
      return resourceBundle.getString(key);
    } catch (MissingResourceException ex) {
      return defaultValue;
    }
  }
  
  /**
   * Reloads the resource bundle for the current locale.
   * This can be useful if the locale has changed during runtime.
   */
  public static void reload() {
    try {
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.getDefault());
    } catch (MissingResourceException ex) {
      // Fallback to default locale if current locale is not available
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.ENGLISH);
    }
  }
  
  /**
   * Reloads the resource bundle for the specified locale.
   * 
   * @param locale the locale to load
   */
  public static void reload(Locale locale) {
    try {
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, locale);
    } catch (MissingResourceException ex) {
      // Fallback to default locale if specified locale is not available
      resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, Locale.ENGLISH);
    }
  }
  
  /**
   * Returns the current locale of the resource bundle.
   * 
   * @return the current locale
   */
  public static Locale getLocale() {
    return resourceBundle.getLocale();
  }
  
  // Convenience methods for commonly used strings
  
  /**
   * Returns the localized action name.
   */
  public static String getActionName() {
    return getString("AIAction.Name", "AI Analysis");
  }
  
  /**
   * Returns the localized action description.
   */
  public static String getActionDescription() {
    return getString("AIAction.ShortDescription", "Analyze floor plan with AI");
  }
  
  /**
   * Returns the localized chat dialog title.
   */
  public static String getChatDialogTitle() {
    return getString("AIChatDialog.title", "AI Floor Plan Analysis");
  }
  
  /**
   * Returns the localized settings dialog title.
   */
  public static String getSettingsDialogTitle() {
    return getString("AISettingsDialog.title", "AI Settings");
  }
  
  /**
   * Returns the localized button text for the given button key.
   * 
   * @param buttonKey the button key (e.g., "send", "save", "cancel")
   * @return the localized button text
   */
  public static String getButtonText(String buttonKey) {
    return getString("button." + buttonKey, buttonKey);
  }
  
  /**
   * Returns the localized label text for the given label key.
   * 
   * @param labelKey the label key (e.g., "provider", "apiKey", "model")
   * @return the localized label text
   */
  public static String getLabelText(String labelKey) {
    return getString("label." + labelKey, labelKey + ":");
  }
  
  /**
   * Returns the localized message text for the given message key.
   * 
   * @param messageKey the message key (e.g., "analyzing", "connectionSuccessful")
   * @return the localized message text
   */
  public static String getMessageText(String messageKey) {
    return getString("message." + messageKey, messageKey);
  }
  
  /**
   * Returns the localized error message for the given error key.
   * 
   * @param errorKey the error key (e.g., "analysisError", "connectionError")
   * @param arguments optional arguments to format into the error message
   * @return the localized error message
   */
  public static String getErrorMessage(String errorKey, Object... arguments) {
    return getString("error." + errorKey, arguments);
  }
  
  /**
   * Returns the localized provider name for the given provider key.
   * 
   * @param providerKey the provider key (e.g., "openai", "anthropic", "custom")
   * @return the localized provider name
   */
  public static String getProviderName(String providerKey) {
    return getString("provider." + providerKey, providerKey);
  }
  
  /**
   * Returns the localized analysis prompt.
   */
  public static String getAnalysisPrompt() {
    return getString("analysis.prompt", 
        "Please analyze this floor plan and provide comprehensive insights including:\n" +
        "1. Layout efficiency and space utilization\n" +
        "2. Traffic flow and circulation patterns\n" +
        "3. Natural lighting and ventilation opportunities\n" +
        "4. Accessibility considerations\n" +
        "5. Functional relationships between spaces\n" +
        "6. Suggestions for improvement\n" +
        "7. Compliance with common building standards\n" +
        "8. Energy efficiency considerations\n\n" +
        "Please provide specific, actionable recommendations that would enhance " +
        "the functionality, comfort, and aesthetic appeal of this space.");
  }
}
