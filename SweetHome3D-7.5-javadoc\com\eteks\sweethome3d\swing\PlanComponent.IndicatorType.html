<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:50 CEST 2024 -->
<title>PlanComponent.IndicatorType (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PlanComponent.IndicatorType (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanComponent.IndicatorType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" target="_top">Frames</a></li>
<li><a href="PlanComponent.IndicatorType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class PlanComponent.IndicatorType" class="title">Class PlanComponent.IndicatorType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.PlanComponent.IndicatorType</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html" title="class in com.eteks.sweethome3d.swing">PlanComponent</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">PlanComponent.IndicatorType</span>
extends java.lang.Object</pre>
<div class="block">Indicator types that may be displayed on selected items.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#ARC_EXTENT">ARC_EXTENT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#CHANGE_POWER">CHANGE_POWER</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#ELEVATE">ELEVATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#MOVE_TEXT">MOVE_TEXT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#RESIZE">RESIZE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#RESIZE_HEIGHT">RESIZE_HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#ROTATE">ROTATE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#ROTATE_PITCH">ROTATE_PITCH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#ROTATE_ROLL">ROTATE_ROLL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#ROTATE_TEXT">ROTATE_TEXT</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier</th>
<th class="colLast" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected </code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#IndicatorType-java.lang.String-">IndicatorType</a></span>(java.lang.String&nbsp;name)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#name--">name</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ROTATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROTATE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> ROTATE</pre>
</li>
</ul>
<a name="RESIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESIZE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> RESIZE</pre>
</li>
</ul>
<a name="ELEVATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ELEVATE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> ELEVATE</pre>
</li>
</ul>
<a name="RESIZE_HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESIZE_HEIGHT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> RESIZE_HEIGHT</pre>
</li>
</ul>
<a name="CHANGE_POWER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CHANGE_POWER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> CHANGE_POWER</pre>
</li>
</ul>
<a name="MOVE_TEXT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOVE_TEXT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> MOVE_TEXT</pre>
</li>
</ul>
<a name="ROTATE_TEXT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROTATE_TEXT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> ROTATE_TEXT</pre>
</li>
</ul>
<a name="ROTATE_PITCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROTATE_PITCH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> ROTATE_PITCH</pre>
</li>
</ul>
<a name="ROTATE_ROLL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROTATE_ROLL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> ROTATE_ROLL</pre>
</li>
</ul>
<a name="ARC_EXTENT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ARC_EXTENT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a> ARC_EXTENT</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="IndicatorType-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>IndicatorType</h4>
<pre>protected&nbsp;IndicatorType(java.lang.String&nbsp;name)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="name--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public final&nbsp;java.lang.String&nbsp;name()</pre>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;java.lang.String&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>toString</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanComponent.IndicatorType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" target="_top">Frames</a></li>
<li><a href="PlanComponent.IndicatorType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
