<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Interface com.eteks.sweethome3d.model.Selectable (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.eteks.sweethome3d.model.Selectable (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/Selectable.html" target="_top">Frames</a></li>
<li><a href="Selectable.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.eteks.sweethome3d.model.Selectable" class="title">Uses of Interface<br>com.eteks.sweethome3d.model.Selectable</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d</a></td>
<td class="colLast">
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.j3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a> in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DBranchFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranchFactory.html#createObject3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.Selectable-boolean-">createObject3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
              boolean&nbsp;waitForLoading)</code>
<div class="block">Returns the 3D object matching a given <code>item</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DBranchFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranchFactory.html#createObject3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.Selectable-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-">createObject3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Object&nbsp;context,
              boolean&nbsp;waitForLoading)</code>
<div class="block">Returns the 3D object matching a given <code>item</code>.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><span class="typeNameLabel">AbstractPhotoRenderer.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#render-java.awt.image.BufferedImage-com.eteks.sweethome3d.model.Camera-java.util.List-java.awt.image.ImageObserver-">render</a></span>(java.awt.image.BufferedImage&nbsp;image,
      <a href="../../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
      java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;updatedItems,
      java.awt.image.ImageObserver&nbsp;observer)</code>
<div class="block">Renders home in <code>image</code> at the given <code>camera</code> location and image size.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">YafarayRenderer.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html#render-java.awt.image.BufferedImage-com.eteks.sweethome3d.model.Camera-java.util.List-java.awt.image.ImageObserver-">render</a></span>(java.awt.image.BufferedImage&nbsp;image,
      <a href="../../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
      java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;updatedItems,
      java.awt.image.ImageObserver&nbsp;observer)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PhotoRenderer.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/PhotoRenderer.html#render-java.awt.image.BufferedImage-com.eteks.sweethome3d.model.Camera-java.util.List-java.awt.image.ImageObserver-">render</a></span>(java.awt.image.BufferedImage&nbsp;image,
      <a href="../../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
      java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;updatedItems,
      java.awt.image.ImageObserver&nbsp;observer)</code>
<div class="block">Renders home in <code>image</code> at the given <code>camera</code> location and image size.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that implement <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a></span></code>
<div class="block">A compass used to locate where a home is located and how it's oriented towards North.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a></span></code>
<div class="block">A dimension line in plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">HomeDoorOrWindow</a></span></code>
<div class="block">A door or a window in <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></span></code>
<div class="block">A group of furniture of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model">HomeLight</a></span></code>
<div class="block">A light in <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></span></code>
<div class="block">A piece of furniture in <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeShelfUnit.html" title="class in com.eteks.sweethome3d.model">HomeShelfUnit</a></span></code>
<div class="block">A shelf unit in <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a></span></code>
<div class="block">A free label.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/ObserverCamera.html" title="class in com.eteks.sweethome3d.model">ObserverCamera</a></span></code>
<div class="block">Observer camera characteristics in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a></span></code>
<div class="block">A polyline or a polygon in a home plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a></span></code>
<div class="block">A room or a polygon in a home plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a></span></code>
<div class="block">A wall of a home plan.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></td>
<td class="colLast"><span class="typeNameLabel">Selectable.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this object.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#duplicate-java.util.List-">duplicate</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a deep copy of home selectable <code>items</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getSelectableViewableItems--">getSelectableViewableItems</a></span>()</code>
<div class="block">Returns all the selectable and viewable items in this home, except the observer camera.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getSelectedItems--">getSelectedItems</a></span>()</code>
<div class="block">Returns an unmodifiable list of the selected items in home.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#deselectItem-com.eteks.sweethome3d.model.Selectable-">deselectItem</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Deselects <code>item</code> if it's selected and notifies listeners selection change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#isItemSelected-com.eteks.sweethome3d.model.Selectable-">isItemSelected</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> if the given <code>item</code> is selected in this home</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#duplicate-java.util.List-">duplicate</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a deep copy of home selectable <code>items</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultTextStyle-java.lang.Class-">getDefaultTextStyle</a></span>(java.lang.Class&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;selectableClass)</code>
<div class="block">Returns the default text style of a class of selectable item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getDimensionLinesSubList-java.util.List-">getDimensionLinesSubList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only dimension lines.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSubList-java.util.List-">getFurnitureSubList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only home furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getLabelsSubList-java.util.List-">getLabelsSubList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only labels.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getPolylinesSubList-java.util.List-">getPolylinesSubList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only labels.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getRoomsSubList-java.util.List-">getRoomsSubList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only rooms.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static &lt;T&gt;&nbsp;java.util.List&lt;T&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getSubList-java.util.List-java.lang.Class-">getSubList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
          java.lang.Class&lt;T&gt;&nbsp;subListClass)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only instances of <code>subListClass</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getWallsSubList-java.util.List-">getWallsSubList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Returns a sub list of <code>items</code> that contains only walls.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#setSelectedItems-java.util.List-">setSelectedItems</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;selectedItems)</code>
<div class="block">Sets the selected items in home and notifies listeners selection change.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> that return <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeComponent3D.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#getClosestItemAt-int-int-">getClosestItemAt</a></span>(int&nbsp;x,
                int&nbsp;y)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use rather getClosestSelectableItemAt.</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeComponent3D.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html#getClosestSelectableItemAt-int-int-">getClosestSelectableItemAt</a></span>(int&nbsp;x,
                          int&nbsp;y)</code>
<div class="block">Returns the closest <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model"><code>Selectable</code></a> object at component coordinates (x, y),
 or <code>null</code> if not found.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#getClipboardItems--">getClipboardItems</a></span>()</code>
<div class="block">Returns the list of selectable items that are currently in clipboard
 or <code>null</code> if clipboard doesn't contain any selectable item.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPaintedItems--">getPaintedItems</a></span>()</code>
<div class="block">Returns the collection of walls, furniture, rooms and dimension lines of the home
 painted by this component wherever the level they belong to is selected or not.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected java.awt.Shape</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getIndicator-com.eteks.sweethome3d.model.Selectable-com.eteks.sweethome3d.swing.PlanComponent.IndicatorType-">getIndicator</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
            <a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a>&nbsp;indicatorType)</code>
<div class="block">Returns the shape of the given indicator type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected java.awt.geom.Rectangle2D</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getItemBounds-java.awt.Graphics-com.eteks.sweethome3d.model.Selectable-">getItemBounds</a></span>(java.awt.Graphics&nbsp;g,
             <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns the bounds of the given <code>item</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPointFeedback)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPoint)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#canImportDraggedItems-java.util.List-int-int-">canImportDraggedItems</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                     int&nbsp;x,
                     int&nbsp;y)</code>
<div class="block">Returns <code>true</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#canImportDraggedItems-java.util.List-int-int-">canImportDraggedItems</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                     int&nbsp;x,
                     int&nbsp;y)</code>
<div class="block">Returns <code>true</code> if the given coordinates belong to the plan displayed by this component.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPointFeedback)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPoint)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setDraggedItemsFeedback-java.util.List-">setDraggedItemsFeedback</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</code>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of plan view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setDraggedItemsFeedback-java.util.List-">setDraggedItemsFeedback</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</code>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of the plan displayed by this component.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructor parameters in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeTransferableList.html#HomeTransferableList-java.util.List-">HomeTransferableList</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Creates a transferable list of a copy of <code>items</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></td>
<td class="colLast"><span class="typeNameLabel">View3D.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View3D.html#getClosestSelectableItemAt-int-int-">getClosestSelectableItemAt</a></span>(int&nbsp;x,
                          int&nbsp;y)</code>
<div class="block">Returns the closest <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model"><code>Selectable</code></a> object at component coordinates (x, y),
 or <code>null</code> if not found.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectableItemAt-float-float-">getSelectableItemAt</a></span>(float&nbsp;x,
                   float&nbsp;y)</code>
<div class="block">Returns the selectable item at (<code>x</code>, <code>y</code>) point.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">HomeView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#getClipboardItems--">getClipboardItems</a></span>()</code>
<div class="block">Returns the list of selectable items that are currently in clipboard
 or <code>null</code> if clipboard doesn't contain any selectable item.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectableItemsAt-float-float-">getSelectableItemsAt</a></span>(float&nbsp;x,
                    float&nbsp;y)</code>
<div class="block">Returns the selectable items at (<code>x</code>, <code>y</code>) point.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getSelectableItemsIntersectingRectangle-float-float-float-float-">getSelectableItemsIntersectingRectangle</a></span>(float&nbsp;x0,
                                       float&nbsp;y0,
                                       float&nbsp;x1,
                                       float&nbsp;y1)</code>
<div class="block">Returns the items that intersects with the rectangle of (<code>x0</code>,
 <code>y0</code>), (<code>x1</code>, <code>y1</code>) opposite corners.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html#createObject3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.Selectable-boolean-">createObject3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
              boolean&nbsp;waitForLoading)</code>
<div class="block">Returns the 3D object matching a given <code>item</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html#createObject3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.Selectable-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-boolean-">createObject3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
              <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Object&nbsp;context,
              boolean&nbsp;waitForLoading)</code>
<div class="block">Returns the 3D object matching a given <code>item</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Selectable-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;beforeItem)</code>
<div class="block">Adds items to home before the given item
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#flipItem-com.eteks.sweethome3d.model.Selectable-float:A-int-float-boolean-java.util.List-">flipItem</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
        float[]&nbsp;itemTextBaseOffsets,
        int&nbsp;offsetIndex,
        float&nbsp;axisCoordinate,
        boolean&nbsp;horizontalFlip,
        java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;flippedItems)</code>
<div class="block">Flips the given <code>item</code> with the given axis coordinate.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isItemDeletable-com.eteks.sweethome3d.model.Selectable-">isItemDeletable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be deleted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isItemMovable-com.eteks.sweethome3d.model.Selectable-">isItemMovable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be moved
 in the plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isItemPartOfBasePlan-com.eteks.sweethome3d.model.Selectable-">isItemPartOfBasePlan</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> it the given <code>item</code> belongs
 to the base plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#isItemResizable-com.eteks.sweethome3d.model.Selectable-">isItemResizable</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns <code>true</code> if the given <code>item</code> may be resized.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#selectItem-com.eteks.sweethome3d.model.Selectable-">selectItem</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Selects the given <code>item</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPoint)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#toggleItemSelection-com.eteks.sweethome3d.model.Selectable-">toggleItemSelection</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Toggles the selection of the given <code>item</code>.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#addItems-java.util.List-">addItems</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Adds <code>items</code> to home and post an undoable operation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">PlanView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#canImportDraggedItems-java.util.List-int-int-">canImportDraggedItems</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                     int&nbsp;x,
                     int&nbsp;y)</code>
<div class="block">Returns <code>true</code> if this plan accepts to import dragged items at the given coordinates.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#cut-java.util.List-">cut</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Deletes items and post a cut operation to undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#deleteItems-java.util.List-">deleteItems</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Deletes <code>items</code> in plan and record it as an undoable operation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-float-float-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Adds items to home, moves them of (dx, dy)
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-float-float-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Adds items to home, moves them of (dx, dy)
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Level-float-float-java.lang.Float-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    <a href="../../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level,
    float&nbsp;dx,
    float&nbsp;dy,
    java.lang.Float&nbsp;dz)</code>
<div class="block">Adds items to home, moves them of (dx, dy, dz) delta vector
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Selectable-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;beforeItem)</code>
<div class="block">Adds items to home before the given item
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#flipItem-com.eteks.sweethome3d.model.Selectable-float:A-int-float-boolean-java.util.List-">flipItem</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
        float[]&nbsp;itemTextBaseOffsets,
        int&nbsp;offsetIndex,
        float&nbsp;axisCoordinate,
        boolean&nbsp;horizontalFlip,
        java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;flippedItems)</code>
<div class="block">Flips the given <code>item</code> with the given axis coordinate.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#moveItems-java.util.List-float-float-">moveItems</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
         float&nbsp;dx,
         float&nbsp;dy)</code>
<div class="block">Moves <code>items</code> of (<code>dx</code>, <code>dy</code>) units.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#paste-java.util.List-">paste</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Adds items to home and posts a paste operation to undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#selectAndShowItems-java.util.List-">selectAndShowItems</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Selects <code>items</code> and make them visible at screen.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#selectItems-java.util.List-">selectItems</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items)</code>
<div class="block">Selects <code>items</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPoint)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setDraggedItemsFeedback-java.util.List-">setDraggedItemsFeedback</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</code>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of plan view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#startDraggedItems-java.util.List-float-float-">startDraggedItems</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems,
                 float&nbsp;x,
                 float&nbsp;y)</code>
<div class="block">Displays in plan view the feedback of <code>draggedItems</code>,
 during a drag and drop operation initiated from outside of plan view.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/Selectable.html" target="_top">Frames</a></li>
<li><a href="Selectable.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
