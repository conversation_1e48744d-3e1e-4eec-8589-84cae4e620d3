/*
 * AIPluginValidator.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.Home;
import com.eteks.sweethome3d.model.Wall;
import com.eteks.sweethome3d.model.Room;
import com.eteks.sweethome3d.model.HomePieceOfFurniture;
import java.util.ArrayList;
import java.util.List;

/**
 * Validator for testing AI plugin functionality.
 * Provides methods to validate plugin components and integration.
 * 
 * <AUTHOR>
 */
public class AIPluginValidator {
  
  /**
   * Validates the basic plugin configuration system.
   * 
   * @return List of validation results
   */
  public List<String> validateConfigurationSystem() {
    List<String> results = new ArrayList<>();
    
    try {
      // Test configuration creation
      AIProviderConfig config = AIProviderConfig.builder()
          .providerName("Test Provider")
          .baseUrl("https://api.example.com/v1")
          .apiKey("test-key")
          .model("test-model")
          .build();
      
      results.add("✓ Configuration creation successful");
      
      // Test configuration validation
      AIConfigValidator validator = new AIConfigValidator();
      ValidationResult validationResult = validator.validate(config);
      
      if (validationResult.isValid()) {
        results.add("✓ Configuration validation successful");
      } else {
        results.add("✗ Configuration validation failed: " + validationResult.getFirstError());
      }
      
      // Test configuration manager
      AIConfigurationManager configManager = new AIConfigurationManager();
      configManager.saveConfiguration(config);
      AIProviderConfig loadedConfig = configManager.loadConfiguration();
      
      if (loadedConfig != null && loadedConfig.getProviderName().equals("Test Provider")) {
        results.add("✓ Configuration save/load successful");
      } else {
        results.add("✗ Configuration save/load failed");
      }
      
    } catch (Exception e) {
      results.add("✗ Configuration system error: " + e.getMessage());
    }
    
    return results;
  }
  
  /**
   * Validates the data extraction system.
   * 
   * @return List of validation results
   */
  public List<String> validateDataExtraction() {
    List<String> results = new ArrayList<>();
    
    try {
      // Create a test home
      Home testHome = createTestHome();
      
      // Test data extraction
      FloorPlanDataExtractor extractor = new FloorPlanDataExtractor();
      String jsonData = extractor.extractToJson(testHome);
      
      if (jsonData != null && !jsonData.isEmpty()) {
        results.add("✓ Data extraction successful");
        
        // Validate JSON structure
        if (jsonData.contains("\"home\"") && jsonData.contains("\"walls\"") && 
            jsonData.contains("\"rooms\"") && jsonData.contains("\"furniture\"")) {
          results.add("✓ JSON structure validation successful");
        } else {
          results.add("✗ JSON structure validation failed");
        }
        
        // Test privacy sanitization
        PrivacyManager privacyManager = new PrivacyManager();
        String sanitizedData = privacyManager.sanitizeFloorPlanData(jsonData, false);
        
        if (!sanitizedData.equals(jsonData)) {
          results.add("✓ Privacy sanitization working");
        } else {
          results.add("? Privacy sanitization may not be working (no changes detected)");
        }
        
      } else {
        results.add("✗ Data extraction failed");
      }
      
    } catch (Exception e) {
      results.add("✗ Data extraction error: " + e.getMessage());
    }
    
    return results;
  }
  
  /**
   * Validates the AI client factory.
   * 
   * @return List of validation results
   */
  public List<String> validateAIClientFactory() {
    List<String> results = new ArrayList<>();
    
    try {
      // Test valid configuration
      AIProviderConfig validConfig = AIProviderConfig.builder()
          .providerName("Test Provider")
          .baseUrl("http://localhost:11434/v1")
          .apiKey("test-key")
          .model("test-model")
          .build();
      
      AIClient client = AIClientFactory.createClient(validConfig);
      if (client != null) {
        results.add("✓ AI client creation successful");
        client.close();
      } else {
        results.add("✗ AI client creation failed");
      }
      
      // Test invalid configuration
      try {
        AIProviderConfig invalidConfig = AIProviderConfig.builder()
            .providerName("Invalid")
            .baseUrl("")
            .model("")
            .build();
        
        AIClientFactory.createClient(invalidConfig);
        results.add("✗ Invalid configuration should have been rejected");
      } catch (IllegalArgumentException e) {
        results.add("✓ Invalid configuration properly rejected");
      }
      
    } catch (Exception e) {
      results.add("✗ AI client factory error: " + e.getMessage());
    }
    
    return results;
  }
  
  /**
   * Validates the provider presets.
   * 
   * @return List of validation results
   */
  public List<String> validateProviderPresets() {
    List<String> results = new ArrayList<>();
    
    try {
      int presetCount = 0;
      for (AIProviderPreset preset : AIProviderPreset.values()) {
        if (preset != AIProviderPreset.CUSTOM) {
          presetCount++;
          
          // Validate preset has required fields
          if (preset.getDisplayName() == null || preset.getDisplayName().isEmpty()) {
            results.add("✗ Preset " + preset + " missing display name");
          }
          
          if (preset.getBaseUrl() == null || preset.getBaseUrl().isEmpty()) {
            results.add("✗ Preset " + preset + " missing base URL");
          }
        }
      }
      
      results.add("✓ Found " + presetCount + " provider presets");
      
      // Test preset configuration creation
      AIProviderConfig config = AIProviderPreset.OLLAMA.createConfig("test-key");
      if (config.getProviderName().equals("Ollama (Local)")) {
        results.add("✓ Preset configuration creation successful");
      } else {
        results.add("✗ Preset configuration creation failed");
      }
      
    } catch (Exception e) {
      results.add("✗ Provider preset validation error: " + e.getMessage());
    }
    
    return results;
  }
  
  /**
   * Creates a test home for validation purposes.
   * 
   * @return A test home with basic elements
   */
  private Home createTestHome() {
    Home home = new Home();
    home.setName("Test Home");
    
    // Add a test wall
    Wall wall = new Wall(0, 0, 100, 0, 10);
    wall.setHeight(250f);
    home.addWall(wall);
    
    // Add a test room
    Room room = new Room(new float[][] {{0, 0}, {100, 0}, {100, 100}, {0, 100}});
    room.setName("Test Room");
    home.addRoom(room);
    
    return home;
  }
  
  /**
   * Runs all validation tests and returns a comprehensive report.
   * 
   * @return Complete validation report
   */
  public String runAllValidations() {
    StringBuilder report = new StringBuilder();
    report.append("AI Plugin Validation Report\n");
    report.append("===========================\n\n");
    
    report.append("Configuration System:\n");
    for (String result : validateConfigurationSystem()) {
      report.append("  ").append(result).append("\n");
    }
    report.append("\n");
    
    report.append("Data Extraction:\n");
    for (String result : validateDataExtraction()) {
      report.append("  ").append(result).append("\n");
    }
    report.append("\n");
    
    report.append("AI Client Factory:\n");
    for (String result : validateAIClientFactory()) {
      report.append("  ").append(result).append("\n");
    }
    report.append("\n");
    
    report.append("Provider Presets:\n");
    for (String result : validateProviderPresets()) {
      report.append("  ").append(result).append("\n");
    }
    report.append("\n");
    
    report.append("Validation completed.\n");
    return report.toString();
  }
}
