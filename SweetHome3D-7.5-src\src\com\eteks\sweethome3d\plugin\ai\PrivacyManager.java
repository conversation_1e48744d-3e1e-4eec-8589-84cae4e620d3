/*
 * PrivacyManager.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import javax.swing.JCheckBox;
import javax.swing.JOptionPane;
import java.awt.Component;
import java.util.regex.Pattern;

/**
 * Manages privacy settings and data sanitization for AI analysis.
 * Provides options to exclude personal information from floor plan data.
 * 
 * <AUTHOR>
 */
public class PrivacyManager {
  
  // Patterns for detecting potentially personal information
  private static final Pattern NAME_PATTERN = Pattern.compile("\"name\"\\s*:\\s*\"[^\"]*\"");
  private static final Pattern DESCRIPTION_PATTERN = Pattern.compile("\"description\"\\s*:\\s*\"[^\"]*\"");
  
  /**
   * Sanitizes floor plan data by removing or anonymizing personal information.
   * 
   * @param floorPlanData The original floor plan data
   * @param includePersonalInfo Whether to include personal information
   * @return Sanitized floor plan data
   */
  public String sanitizeFloorPlanData(String floorPlanData, boolean includePersonalInfo) {
    if (includePersonalInfo) {
      return floorPlanData;
    }
    
    String sanitized = floorPlanData;
    
    // Replace specific names with generic terms
    sanitized = NAME_PATTERN.matcher(sanitized).replaceAll("\"name\":\"Room\"");
    
    // Replace descriptions with generic terms
    sanitized = DESCRIPTION_PATTERN.matcher(sanitized).replaceAll("\"description\":\"Furniture\"");
    
    // Remove any custom properties that might contain personal data
    sanitized = removeCustomProperties(sanitized);
    
    return sanitized;
  }
  
  /**
   * Shows a privacy notice dialog and returns user preferences.
   * 
   * @param parent The parent component for the dialog
   * @return true if the user wants to exclude personal information, false otherwise
   */
  public boolean showPrivacyNotice(Component parent) {
    String message = "This feature will send your floor plan data to the configured AI service.\n" +
                    "Please ensure you trust the AI provider with your data.\n\n" +
                    "Personal information (room names, furniture descriptions) can be excluded from the analysis.";
    
    JCheckBox excludePersonalInfo = new JCheckBox("Exclude personal information (recommended)", true);
    Object[] components = {message, excludePersonalInfo};
    
    int result = JOptionPane.showConfirmDialog(parent, components,
        "Privacy Notice", JOptionPane.OK_CANCEL_OPTION, JOptionPane.INFORMATION_MESSAGE);
    
    if (result == JOptionPane.OK_OPTION) {
      return excludePersonalInfo.isSelected();
    } else {
      // User cancelled, assume they want privacy
      return true;
    }
  }
  
  /**
   * Shows a privacy notice for first-time users.
   * 
   * @param parent The parent component for the dialog
   * @return true if user agrees to proceed, false if they cancel
   */
  public boolean showFirstTimePrivacyNotice(Component parent) {
    String message = "Welcome to AI Floor Plan Analysis!\n\n" +
                    "This feature analyzes your floor plan using artificial intelligence to provide\n" +
                    "insights and suggestions for improvement.\n\n" +
                    "Important Privacy Information:\n" +
                    "• Your floor plan data will be sent to your configured AI provider\n" +
                    "• No data is stored permanently by the AI service\n" +
                    "• You can exclude personal information (names, descriptions)\n" +
                    "• Local AI providers (Ollama, LM Studio) keep data on your computer\n\n" +
                    "Do you want to proceed with the analysis?";
    
    JCheckBox excludePersonalInfo = new JCheckBox("Exclude personal information from analysis", true);
    JCheckBox dontShowAgain = new JCheckBox("Don't show this notice again", false);
    
    Object[] components = {message, excludePersonalInfo, dontShowAgain};
    
    int result = JOptionPane.showConfirmDialog(parent, components,
        "AI Analysis Privacy Notice", JOptionPane.YES_NO_OPTION, JOptionPane.INFORMATION_MESSAGE);
    
    if (result == JOptionPane.YES_OPTION) {
      // Store user preferences
      storePrivacyPreferences(excludePersonalInfo.isSelected(), dontShowAgain.isSelected());
      return true;
    }
    
    return false;
  }
  
  /**
   * Checks if the privacy notice should be shown.
   * 
   * @return true if the notice should be shown, false otherwise
   */
  public boolean shouldShowPrivacyNotice() {
    // Check if user has opted out of seeing the notice
    java.util.prefs.Preferences prefs = java.util.prefs.Preferences.userNodeForPackage(PrivacyManager.class);
    return !prefs.getBoolean("privacyNoticeShown", false);
  }
  
  /**
   * Gets the user's preference for excluding personal information.
   * 
   * @return true if personal information should be excluded, false otherwise
   */
  public boolean shouldExcludePersonalInfo() {
    java.util.prefs.Preferences prefs = java.util.prefs.Preferences.userNodeForPackage(PrivacyManager.class);
    return prefs.getBoolean("excludePersonalInfo", true); // Default to true for privacy
  }
  
  /**
   * Stores user privacy preferences.
   * 
   * @param excludePersonalInfo Whether to exclude personal information
   * @param dontShowAgain Whether to show the privacy notice again
   */
  private void storePrivacyPreferences(boolean excludePersonalInfo, boolean dontShowAgain) {
    java.util.prefs.Preferences prefs = java.util.prefs.Preferences.userNodeForPackage(PrivacyManager.class);
    prefs.putBoolean("excludePersonalInfo", excludePersonalInfo);
    if (dontShowAgain) {
      prefs.putBoolean("privacyNoticeShown", true);
    }
  }
  
  /**
   * Removes custom properties from JSON data that might contain personal information.
   * 
   * @param jsonData The JSON data to sanitize
   * @return Sanitized JSON data
   */
  private String removeCustomProperties(String jsonData) {
    // Simple implementation - in a production system, you'd use a proper JSON parser
    // This removes any "properties" objects that might contain custom user data
    return jsonData.replaceAll("\"properties\"\\s*:\\s*\\{[^}]*\\}", "\"properties\":{}");
  }
  
  /**
   * Validates that the data doesn't contain obvious personal information.
   * 
   * @param data The data to validate
   * @return true if the data appears to be sanitized, false otherwise
   */
  public boolean validateDataPrivacy(String data) {
    // Check for common personal information patterns
    String lowerData = data.toLowerCase();
    
    // Check for email addresses
    if (lowerData.matches(".*\\b[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}\\b.*")) {
      return false;
    }
    
    // Check for phone numbers (simple pattern)
    if (lowerData.matches(".*\\b\\d{3}[-.]?\\d{3}[-.]?\\d{4}\\b.*")) {
      return false;
    }
    
    // Check for common personal names in room descriptions
    String[] personalTerms = {"bedroom", "john", "mary", "master", "guest", "office"};
    for (String term : personalTerms) {
      if (lowerData.contains("\"" + term + "\"")) {
        // This might be personal information, but it's also common architectural terminology
        // We'll allow it but could flag for review
      }
    }
    
    return true;
  }
}
