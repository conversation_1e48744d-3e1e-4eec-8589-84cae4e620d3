# ApplicationPlugin_pl.properties
# Sweet Home 3D AI Plugin Configuration - Polish
# Copyright (c) 2025 Samuel <PERSON>

# Plugin identification
name=Analiza AI Planów
description=Analizuj plany pięter używając sztucznej inteligencji, aby uzyskać wgląd i sugestie ulepszeń
provider=<PERSON>

# Features
features=Analiza AI, Wgląd w Plany, Wielu Dostawców AI, Kontrola Prywatności

# Requirements
requirements=Połączenie internetowe dla dostawców AI w chmurze (opcjonalne dla dostawców lokalnych)

# UI Strings for internationalization
# Action properties
AIAction.Name=Analiza AI
AIAction.ShortDescription=Analizuj plan z AI
AIAction.Menu=Narzędzia

# Dialog titles
AIChatDialog.title=Analiza AI Planów
AISettingsDialog.title=Ustawienia AI

# Button labels
button.send=Wyślij
button.newAnalysis=Nowa Analiza
button.settings=Ustawienia
button.testConnection=Test Połączenia
button.save=Zapis<PERSON>
button.cancel=Anuluj

# Labels
label.provider=Dostawca:
label.baseUrl=Bazowy URL:
label.apiKey=Klucz API:
label.model=Model:
label.temperature=Temperatura:
label.maxTokens=Maks Tokenów:
label.status=Status:

# Messages
message.analyzing=Analizuję plan...
message.processingQuestion=Przetwarzam pytanie...
message.testingConnection=Testuję połączenie...
message.connectionSuccessful=Połączenie udane!
message.connectionFailed=Połączenie nieudane: {0}
message.configurationSaved=Konfiguracja zapisana pomyślnie
message.validationError=Błędy konfiguracji:\n{0}
message.noConfiguration=Dostawca AI nie jest skonfigurowany. Proszę najpierw skonfigurować ustawienia.

# Analysis prompt
analysis.prompt=Proszę przeanalizować ten plan i dostarczyć kompleksowy wgląd obejmujący:\n1. Efektywność układu i wykorzystanie przestrzeni\n2. Przepływ ruchu i wzorce cyrkulacji\n3. Możliwości naturalnego oświetlenia i wentylacji\n4. Rozważania dotyczące dostępności\n5. Funkcjonalne relacje między przestrzeniami\n6. Sugestie ulepszeń\n7. Zgodność z powszechnymi standardami budowlanymi\n8. Rozważania dotyczące efektywności energetycznej\n\nProszę dostarczyć konkretne, wykonalne rekomendacje, które poprawiłyby funkcjonalność, komfort i estetyczną atrakcyjność tej przestrzeni.

# Error messages
error.analysisError=Błąd analizy: {0}
error.configurationError=Błąd konfiguracji: {0}
error.connectionError=Błąd połączenia: {0}
error.invalidConfiguration=Nieprawidłowa konfiguracja
error.missingApiKey=Klucz API jest wymagany
error.missingBaseUrl=Bazowy URL jest wymagany
error.missingModel=Wybór modelu jest wymagany

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Lokalny)
provider.lmstudio=LM Studio (Lokalny)
provider.anythingllm=AnythingLLM (Lokalny)
provider.jan=Jan (Lokalny)
provider.custom=Niestandardowy
