# ApplicationPlugin_vi.properties
# Sweet Home 3D AI Plugin Configuration - Vietnamese
# Copyright (c) 2025 Samuel <PERSON>

# Plugin identification
name=Phân Tích AI Bản Vẽ
description=Phân tích bản vẽ mặt bằng sử dụng trí tuệ nhân tạo để cung cấp thông tin chi tiết và đề xuất cải thiện
provider=<PERSON>

# Features
features=Phân Tích AI, Thông Tin Chi Tiết Bản Vẽ, <PERSON>hiề<PERSON> Nhà Cung Cấp AI, Kiểm Soát Quyền Riêng Tư

# Requirements
requirements=Kết nối Internet cho các nhà cung cấp AI đám mây (tùy chọn cho nhà cung cấp cục bộ)

# UI Strings for internationalization
# Action properties
AIAction.Name=Phân Tích AI
AIAction.ShortDescription=Phân tích bản vẽ với AI
AIAction.Menu=Công Cụ

# Dialog titles
AIChatDialog.title=Phân Tích AI Bản Vẽ
AISettingsDialog.title=<PERSON>ài Đặt AI

# Button labels
button.send=Gửi
button.newAnalysis=Phân Tích Mới
button.settings=Cài Đặt
button.testConnection=Kiểm Tra Kết Nối
button.save=Lưu
button.cancel=Hủy

# Labels
label.provider=Nhà Cung Cấp:
label.baseUrl=URL Cơ Sở:
label.apiKey=Khóa API:
label.model=Mô Hình:
label.temperature=Nhiệt Độ:
label.maxTokens=Token Tối Đa:
label.status=Trạng Thái:

# Messages
message.analyzing=Đang phân tích bản vẽ...
message.processingQuestion=Đang xử lý câu hỏi...
message.testingConnection=Đang kiểm tra kết nối...
message.connectionSuccessful=Kết nối thành công!
message.connectionFailed=Kết nối thất bại: {0}
message.configurationSaved=Cấu hình đã được lưu thành công
message.validationError=Lỗi cấu hình:\n{0}
message.noConfiguration=Nhà cung cấp AI chưa được cấu hình. Vui lòng cấu hình cài đặt trước.

# Analysis prompt
analysis.prompt=Vui lòng phân tích bản vẽ này và cung cấp thông tin chi tiết toàn diện bao gồm:\n1. Hiệu quả bố cục và sử dụng không gian\n2. Luồng giao thông và mô hình lưu thông\n3. Cơ hội ánh sáng tự nhiên và thông gió\n4. Các cân nhắc về khả năng tiếp cận\n5. Mối quan hệ chức năng giữa các không gian\n6. Đề xuất cải thiện\n7. Tuân thủ các tiêu chuẩn xây dựng thông thường\n8. Các cân nhắc về hiệu quả năng lượng\n\nVui lòng cung cấp các khuyến nghị cụ thể, có thể thực hiện được để cải thiện chức năng, sự thoải mái và sức hấp dẫn thẩm mỹ của không gian này.

# Error messages
error.analysisError=Lỗi phân tích: {0}
error.configurationError=Lỗi cấu hình: {0}
error.connectionError=Lỗi kết nối: {0}
error.invalidConfiguration=Cấu hình không hợp lệ
error.missingApiKey=Cần có khóa API
error.missingBaseUrl=Cần có URL cơ sở
error.missingModel=Cần chọn mô hình

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Cục Bộ)
provider.lmstudio=LM Studio (Cục Bộ)
provider.anythingllm=AnythingLLM (Cục Bộ)
provider.jan=Jan (Cục Bộ)
provider.custom=Tùy Chỉnh
