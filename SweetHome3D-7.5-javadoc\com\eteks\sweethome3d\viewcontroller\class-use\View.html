<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Interface com.eteks.sweethome3d.viewcontroller.View (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface com.eteks.sweethome3d.viewcontroller.View (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/viewcontroller/class-use/View.html" target="_top">Frames</a></li>
<li><a href="View.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface com.eteks.sweethome3d.viewcontroller.View" class="title">Uses of Interface<br>com.eteks.sweethome3d.viewcontroller.View</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d">com.eteks.sweethome3d</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as a stand-alone application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a> in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a> that implement <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/HomeFramePane.html" title="class in com.eteks.sweethome3d">HomeFramePane</a></span></code>
<div class="block">A pane that displays a
 <a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html" title="class in com.eteks.sweethome3d.swing"><code>home pane</code></a> in a frame.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a> that return <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeFrameController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/HomeFrameController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a> in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">AppletContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">showOpenDialog</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the name chosen by user with an open dialog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">AppletContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletContentManager.html#showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">showSaveDialog</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
              java.lang.String&nbsp;name)</code>
<div class="block">Returns the name chosen by user with a save dialog.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> that implement <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/BackgroundImageWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">BackgroundImageWizardStepsPanel</a></span></code>
<div class="block">Wizard panel for background image choice.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/BaseboardChoiceComponent.html" title="class in com.eteks.sweethome3d.swing">BaseboardChoiceComponent</a></span></code>
<div class="block">Baseboard editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/CompassPanel.html" title="class in com.eteks.sweethome3d.swing">CompassPanel</a></span></code>
<div class="block">Compass editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html" title="class in com.eteks.sweethome3d.swing">DimensionLinePanel</a></span></code>
<div class="block">Dimension line editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureCatalogListPanel.html" title="class in com.eteks.sweethome3d.swing">FurnitureCatalogListPanel</a></span></code>
<div class="block">A furniture catalog view that displays furniture in a list, with a combo and search text field.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html" title="class in com.eteks.sweethome3d.swing">FurnitureCatalogTree</a></span></code>
<div class="block">A tree displaying furniture catalog by category.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureTable.html" title="class in com.eteks.sweethome3d.swing">FurnitureTable</a></span></code>
<div class="block">A table displaying home furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html" title="class in com.eteks.sweethome3d.swing">FurnitureTablePanel</a></span></code>
<div class="block">A panel displaying home furniture table and other information like totals.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HelpPane.html" title="class in com.eteks.sweethome3d.swing">HelpPane</a></span></code>
<div class="block">A pane displaying Sweet Home 3D help.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/Home3DAttributesPanel.html" title="class in com.eteks.sweethome3d.swing">Home3DAttributesPanel</a></span></code>
<div class="block">Home 3D attributes editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html" title="class in com.eteks.sweethome3d.swing">HomeComponent3D</a></span></code>
<div class="block">A component that displays home walls, rooms and furniture with Java 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeFurniturePanel.html" title="class in com.eteks.sweethome3d.swing">HomeFurniturePanel</a></span></code>
<div class="block">Home furniture editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html" title="class in com.eteks.sweethome3d.swing">HomePane</a></span></code>
<div class="block">The MVC view that edits a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ImportedFurnitureWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">ImportedFurnitureWizardStepsPanel</a></span></code>
<div class="block">Wizard panel for furniture import.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ImportedTextureWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">ImportedTextureWizardStepsPanel</a></span></code>
<div class="block">Wizard panel for background image choice.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/LabelPanel.html" title="class in com.eteks.sweethome3d.swing">LabelPanel</a></span></code>
<div class="block">Label editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/LevelPanel.html" title="class in com.eteks.sweethome3d.swing">LevelPanel</a></span></code>
<div class="block">Level editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ModelMaterialsComponent.html" title="class in com.eteks.sweethome3d.swing">ModelMaterialsComponent</a></span></code>
<div class="block">Button giving access to materials editor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html" title="class in com.eteks.sweethome3d.swing">MultipleLevelsPlanPanel</a></span></code>
<div class="block">A panel for multiple levels plans where users can select the displayed level.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ObserverCameraPanel.html" title="class in com.eteks.sweethome3d.swing">ObserverCameraPanel</a></span></code>
<div class="block">Observer camera editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PageSetupPanel.html" title="class in com.eteks.sweethome3d.swing">PageSetupPanel</a></span></code>
<div class="block">Home page setup editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotoPanel.html" title="class in com.eteks.sweethome3d.swing">PhotoPanel</a></span></code>
<div class="block">A panel to edit photo creation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotosPanel.html" title="class in com.eteks.sweethome3d.swing">PhotosPanel</a></span></code>
<div class="block">A panel to edit photos created at home points of view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html" title="class in com.eteks.sweethome3d.swing">PlanComponent</a></span></code>
<div class="block">A component displaying the plan of a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.PlanRulerComponent.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.PlanRulerComponent</a></span></code>
<div class="block">A component displaying the plan horizontal or vertical ruler associated to this plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PolylinePanel.html" title="class in com.eteks.sweethome3d.swing">PolylinePanel</a></span></code>
<div class="block">User preferences panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PrintPreviewPanel.html" title="class in com.eteks.sweethome3d.swing">PrintPreviewPanel</a></span></code>
<div class="block">Home print preview editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/RoomPanel.html" title="class in com.eteks.sweethome3d.swing">RoomPanel</a></span></code>
<div class="block">Room editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/TextureChoiceComponent.html" title="class in com.eteks.sweethome3d.swing">TextureChoiceComponent</a></span></code>
<div class="block">Button displaying a texture as an icon.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ThreadedTaskPanel.html" title="class in com.eteks.sweethome3d.swing">ThreadedTaskPanel</a></span></code>
<div class="block">A MVC view of a threaded task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/UserPreferencesPanel.html" title="class in com.eteks.sweethome3d.swing">UserPreferencesPanel</a></span></code>
<div class="block">User preferences panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/VideoPanel.html" title="class in com.eteks.sweethome3d.swing">VideoPanel</a></span></code>
<div class="block">A panel used for video creation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/WallPanel.html" title="class in com.eteks.sweethome3d.swing">WallPanel</a></span></code>
<div class="block">Wall editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/WizardPane.html" title="class in com.eteks.sweethome3d.swing">WizardPane</a></span></code>
<div class="block">Wizard pane.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> that return <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">createBackgroundImageWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;backgroundImageWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps user to choose a background image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">createBaseboardChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;baseboardChoiceController)</code>
<div class="block">Returns a new view that edits the baseboard of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">createFurnitureCatalogView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;furnitureCatalogController)</code>
<div class="block">Returns a new view that displays furniture <code>catalog</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController)</code>
<div class="block">Returns a new table that displays <code>home</code> furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">createImportedTextureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                                    java.lang.String&nbsp;textureName,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;importedTextureWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to import a texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">createModelMaterialsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;controller)</code>
<div class="block">Returns a new view that edits the materials of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">SwingViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">createView3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
            <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
            <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;homeController3D)</code>
<div class="block">Returns a new view that displays <code>home</code> in 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getHorizontalRuler--">getHorizontalRuler</a></span>()</code>
<div class="block">Returns the component used as an horizontal ruler for this plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getHorizontalRuler--">getHorizontalRuler</a></span>()</code>
<div class="block">Returns the component used as an horizontal ruler for the plan displayed by this component.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanComponent.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getVerticalRuler--">getVerticalRuler</a></span>()</code>
<div class="block">Returns the component used as a vertical ruler for this plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">MultipleLevelsPlanPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getVerticalRuler--">getVerticalRuler</a></span>()</code>
<div class="block">Returns the component used as a vertical ruler for the plan displayed by this component.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Attaches the given <code>view</code> to home view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FileContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FileContentManager.html#confirmOverwrite-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-">confirmOverwrite</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
                java.lang.String&nbsp;path)</code>
<div class="block">Displays a dialog that let user choose whether he wants to overwrite
 file <code>path</code> or not.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Detaches the given <code>view</code> from home view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PrintPreviewPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PrintPreviewPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal resizable dialog box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">VideoPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/VideoPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a non modal dialog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">CompassPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/CompassPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PhotoPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotoPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a non modal dialog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ObserverCameraPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ObserverCameraPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DimensionLinePanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">RoomPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/RoomPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home3DAttributesPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/Home3DAttributesPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">WizardPane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/WizardPane.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this wizard view in a modal dialog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">LabelPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/LabelPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">LevelPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/LevelPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PolylinePanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PolylinePanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a dialog box.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">WallPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/WallPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PhotosPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PhotosPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a non modal dialog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferencesPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/UserPreferencesPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a dialog box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PageSetupPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/PageSetupPanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurniturePanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomeFurniturePanel.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this panel in a modal dialog box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ThreadedTaskPanel.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/ThreadedTaskPanel.html#setTaskRunning-boolean-com.eteks.sweethome3d.viewcontroller.View-">setTaskRunning</a></span>(boolean&nbsp;taskRunning,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;executingView)</code>
<div class="block">Sets the running status of the threaded task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">FileContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FileContentManager.html#showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">showOpenDialog</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the file path chosen by user with an open file dialog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">FileContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FileContentManager.html#showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">showSaveDialog</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
              java.lang.String&nbsp;path)</code>
<div class="block">Returns the file path chosen by user with a save file dialog.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subinterfaces, and an explanation">
<caption><span>Subinterfaces of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Interface and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></span></code>
<div class="block">The view that displays a pane in a dialog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></span></code>
<div class="block">A view able to export data in an output stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a></span></code>
<div class="block">The view that displays the furniture of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a></span></code>
<div class="block">A view that displays Sweet Home 3D help.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></span></code>
<div class="block">The main view that displays a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a></span></code>
<div class="block">A view that displays the different steps that helps the user to import a piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></span></code>
<div class="block">The view that displays the plan of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TextureChoiceView</a></span></code>
<div class="block">A view that edits the texture of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskView</a></span></code>
<div class="block">A view of a threaded task.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></span></code>
<div class="block">A view able to transfer data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View3D.html" title="interface in com.eteks.sweethome3d.viewcontroller">View3D</a></span></code>
<div class="block">The view that displays the 3D view of a home.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">createBackgroundImageWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;backgroundImageWizardController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createBackgroundImageWizardStepsView-com.eteks.sweethome3d.model.BackgroundImage-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BackgroundImageWizardController-">createBackgroundImageWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>&nbsp;backgroundImageWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to choose a background image.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">createBaseboardChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;baseboardChoiceController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createBaseboardChoiceView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController-">createBaseboardChoiceView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;baseboardChoiceController)</code>
<div class="block">Returns a new view that edits the baseboard of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">createFurnitureCatalogView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;furnitureCatalogController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createFurnitureCatalogView-com.eteks.sweethome3d.model.FurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureCatalogController-">createFurnitureCatalogView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog,
                          <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                          <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureCatalogController</a>&nbsp;furnitureCatalogController)</code>
<div class="block">Returns a new view that displays furniture <code>catalog</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createFurnitureView-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController)</code>
<div class="block">Returns a new view that displays <code>home</code> furniture list.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">createImportedTextureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                                    java.lang.String&nbsp;textureName,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;importedTextureWizardController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createImportedTextureWizardStepsView-com.eteks.sweethome3d.model.CatalogTexture-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ImportedTextureWizardController-">createImportedTextureWizardStepsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture,
                                    java.lang.String&nbsp;textureName,
                                    <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a>&nbsp;importedTextureWizardController)</code>
<div class="block">Returns a new view that displays the different steps that helps the user to import a texture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">createModelMaterialsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;modelMaterialsController)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createModelMaterialsView-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ModelMaterialsController-">createModelMaterialsView</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html" title="class in com.eteks.sweethome3d.viewcontroller">ModelMaterialsController</a>&nbsp;modelMaterialsController)</code>
<div class="block">Returns a new view that edits the materials of its controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactoryAdapter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactoryAdapter.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">createView3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
            <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
            <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ViewFactory.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html#createView3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController3D-">createView3D</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
            <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
            <a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController3D</a>&nbsp;homeController3D)</code>
<div class="block">Returns a new view that displays <code>home</code> in 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">AbstractPhotoController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/AbstractPhotoController.html#get3DView--">get3DView</a></span>()</code>
<div class="block">Returns the 3D view used to compute aspect ratio bound to it.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getHorizontalRuler--">getHorizontalRuler</a></span>()</code>
<div class="block">Returns the component used as an horizontal ruler for this plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getHorizontalRulerView--">getHorizontalRulerView</a></span>()</code>
<div class="block">Returns the horizontal ruler of the plan view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">BackgroundImageWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html#getStepsView--">getStepsView</a></span>()</code>
<div class="block">Returns the unique wizard view used for all steps.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ImportedTextureWizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html#getStepsView--">getStepsView</a></span>()</code>
<div class="block">Returns the unique wizard view used for all steps.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">WizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getStepView--">getStepView</a></span>()</code>
<div class="block">Returns the current step view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getVerticalRuler--">getVerticalRuler</a></span>()</code>
<div class="block">Returns the component used as a vertical ruler for this plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getVerticalRulerView--">getVerticalRulerView</a></span>()</code>
<div class="block">Returns the vertical ruler of the plan view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html#getView--">getView</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureCatalogController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">Controller.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">BaseboardChoiceController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">WizardController.WizardControllerStepState.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html#getView--">getView</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ModelMaterialsController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ModelMaterialsController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">BackgroundImageWizardController.BackgroundImageWizardStepState.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.BackgroundImageWizardStepState.html#getView--">getView</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">ImportedTextureWizardController.ImportedTextureWizardStepState.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.ImportedTextureWizardStepState.html#getView--">getView</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeController3D.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController3D.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Attaches the given <code>view</code> to home view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Attaches the given <code>view</code> to home view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Detaches the given <code>view</code> from home view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Detaches the given <code>view</code> from home view.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">LabelController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">WallController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WallController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PrintPreviewController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">LevelController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/LevelController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PageSetupController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PageSetupController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PhotosController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home3DAttributesController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/Home3DAttributesController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">VideoController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/VideoController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DialogView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays this wizard view in a modal dialog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferencesController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ObserverCameraController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ObserverCameraController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">WizardController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PolylineController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PolylineController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">CompassController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/CompassController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">RoomController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PhotoController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DimensionLineController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-float-float-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Adds items to home, moves them of (dx, dy)
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Level-float-float-java.lang.Float-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    <a href="../../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level,
    float&nbsp;dx,
    float&nbsp;dy,
    java.lang.Float&nbsp;dz)</code>
<div class="block">Adds items to home, moves them of (dx, dy, dz) delta vector
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Selectable-">drop</a></span>(java.util.List&lt;? extends <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
    <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;destinationView,
    <a href="../../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;beforeItem)</code>
<div class="block">Adds items to home before the given item
 and posts a drop operation to undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ThreadedTaskController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskController.html#executeTask-com.eteks.sweethome3d.viewcontroller.View-">executeTask</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;executingView)</code>
<div class="block">Executes in a separate thread the task given in constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#focusedViewChanged-com.eteks.sweethome3d.viewcontroller.View-">focusedViewChanged</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;focusedView)</code>
<div class="block">Updates actions when focused view changed.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ThreadedTaskView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html#setTaskRunning-boolean-com.eteks.sweethome3d.viewcontroller.View-">setTaskRunning</a></span>(boolean&nbsp;taskRunning,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;executingView)</code>
<div class="block">Sets the running status of the threaded task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">showOpenDialog</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the content location chosen by user with an open content dialog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><span class="typeNameLabel">ContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">showSaveDialog</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
              java.lang.String&nbsp;location)</code>
<div class="block">Returns the content location chosen by user with a save content dialog.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/AbstractPhotoController.html#AbstractPhotoController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.viewcontroller.ContentManager-">AbstractPhotoController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view3D,
                       <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotoController.html#PhotoController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">PhotoController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view3D,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PhotosController.html#PhotosController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">PhotosController</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view3D,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                <a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/viewcontroller/class-use/View.html" target="_top">Frames</a></li>
<li><a href="View.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
