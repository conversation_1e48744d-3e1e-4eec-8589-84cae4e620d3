# ApplicationPlugin_ja.properties
# Sweet Home 3D AI Plugin Configuration - Japanese
# Copyright (c) 2025 Samuel Kpassegna

# Plugin identification
name=AI間取り分析
description=人工知能を使用して間取りを分析し、洞察と改善提案を提供します
provider=Samuel Kpassegna

# Features
features=AI分析、間取り洞察、複数AIプロバイダー、プライバシー制御

# Requirements
requirements=クラウドAIプロバイダーにはインターネット接続が必要（ローカルプロバイダーはオプション）

# UI Strings for internationalization
# Action properties
AIAction.Name=AI分析
AIAction.ShortDescription=AIで間取りを分析
AIAction.Menu=ツール

# Dialog titles
AIChatDialog.title=AI間取り分析
AISettingsDialog.title=AI設定

# Button labels
button.send=送信
button.newAnalysis=新しい分析
button.settings=設定
button.testConnection=接続テスト
button.save=保存
button.cancel=キャンセル

# Labels
label.provider=プロバイダー:
label.baseUrl=ベースURL:
label.apiKey=APIキー:
label.model=モデル:
label.temperature=温度:
label.maxTokens=最大トークン:
label.status=ステータス:

# Messages
message.analyzing=間取りを分析中...
message.processingQuestion=質問を処理中...
message.testingConnection=接続をテスト中...
message.connectionSuccessful=接続成功！
message.connectionFailed=接続失敗: {0}
message.configurationSaved=設定が正常に保存されました
message.validationError=設定エラー:\n{0}
message.noConfiguration=AIプロバイダーが設定されていません。まず設定を構成してください。

# Analysis prompt
analysis.prompt=この間取りを分析し、以下を含む包括的な洞察を提供してください:\n1. レイアウトの効率性と空間利用\n2. 交通の流れと循環パターン\n3. 自然光と換気の機会\n4. アクセシビリティの考慮事項\n5. 空間間の機能的関係\n6. 改善提案\n7. 一般的な建築基準への準拠\n8. エネルギー効率の考慮事項\n\nこの空間の機能性、快適性、美的魅力を向上させる具体的で実行可能な推奨事項を提供してください。

# Error messages
error.analysisError=分析エラー: {0}
error.configurationError=設定エラー: {0}
error.connectionError=接続エラー: {0}
error.invalidConfiguration=無効な設定
error.missingApiKey=APIキーが必要です
error.missingBaseUrl=ベースURLが必要です
error.missingModel=モデル選択が必要です

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (ローカル)
provider.lmstudio=LM Studio (ローカル)
provider.anythingllm=AnythingLLM (ローカル)
provider.jan=Jan (ローカル)
provider.custom=カスタム
