<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:50 CEST 2024 -->
<title>ResourceAction (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ResourceAction (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResourceAction.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.Constraints.html" title="enum in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.ButtonAction.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ResourceAction.html" target="_top">Frames</a></li>
<li><a href="ResourceAction.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class ResourceAction" class="title">Class ResourceAction</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>javax.swing.AbstractAction</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.ResourceAction</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.awt.event.ActionListener, java.io.Serializable, java.lang.Cloneable, java.util.EventListener, javax.swing.Action</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/swing/ControllerAction.html" title="class in com.eteks.sweethome3d.swing">ControllerAction</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ResourceAction</span>
extends javax.swing.AbstractAction</pre>
<div class="block">An action with properties read from a resource bundle file.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.ResourceAction">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.ButtonAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.ButtonAction</a></span></code>
<div class="block">An action decorator for  buttons.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.MenuItemAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.MenuItemAction</a></span></code>
<div class="block">An action decorator for menu items.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.PopupMenuItemAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.PopupMenuItemAction</a></span></code>
<div class="block">An action decorator for popup menu items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.ToolBarAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.ToolBarAction</a></span></code>
<div class="block">An action decorator for tool bar buttons.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#POPUP">POPUP</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#RESOURCE_CLASS">RESOURCE_CLASS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#RESOURCE_PREFIX">RESOURCE_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#TOGGLE_BUTTON_MODEL">TOGGLE_BUTTON_MODEL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#TOOL_BAR_ICON">TOOL_BAR_ICON</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#UNLOCALIZED_NAME">UNLOCALIZED_NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#VISIBLE">VISIBLE</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.AbstractAction">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.AbstractAction</h3>
<code>changeSupport, enabled</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.Action">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;javax.swing.Action</h3>
<code>ACCELERATOR_KEY, ACTION_COMMAND_KEY, DEFAULT, DISPLAYED_MNEMONIC_INDEX_KEY, LARGE_ICON_KEY, LONG_DESCRIPTION, MNEMONIC_KEY, NAME, SELECTED_KEY, SHORT_DESCRIPTION, SMALL_ICON</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#ResourceAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-">ResourceAction</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Class&lt;?&gt;&nbsp;resourceClass,
              java.lang.String&nbsp;actionPrefix)</code>
<div class="block">Creates a disabled action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#ResourceAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-boolean-">ResourceAction</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Class&lt;?&gt;&nbsp;resourceClass,
              java.lang.String&nbsp;actionPrefix,
              boolean&nbsp;enabled)</code>
<div class="block">Creates an action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#actionPerformed-java.awt.event.ActionEvent-">actionPerformed</a></span>(java.awt.event.ActionEvent&nbsp;ev)</code>
<div class="block">Unsupported operation.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.AbstractAction">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.AbstractAction</h3>
<code>addPropertyChangeListener, clone, firePropertyChange, getKeys, getPropertyChangeListeners, getValue, isEnabled, putValue, removePropertyChangeListener, setEnabled</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="RESOURCE_CLASS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_CLASS</h4>
<pre>public static final&nbsp;java.lang.String RESOURCE_CLASS</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.swing.ResourceAction.RESOURCE_CLASS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="RESOURCE_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESOURCE_PREFIX</h4>
<pre>public static final&nbsp;java.lang.String RESOURCE_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.swing.ResourceAction.RESOURCE_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="VISIBLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VISIBLE</h4>
<pre>public static final&nbsp;java.lang.String VISIBLE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.swing.ResourceAction.VISIBLE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="POPUP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>POPUP</h4>
<pre>public static final&nbsp;java.lang.String POPUP</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.swing.ResourceAction.POPUP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TOGGLE_BUTTON_MODEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TOGGLE_BUTTON_MODEL</h4>
<pre>public static final&nbsp;java.lang.String TOGGLE_BUTTON_MODEL</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.swing.ResourceAction.TOGGLE_BUTTON_MODEL">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TOOL_BAR_ICON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TOOL_BAR_ICON</h4>
<pre>public static final&nbsp;java.lang.String TOOL_BAR_ICON</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.swing.ResourceAction.TOOL_BAR_ICON">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="UNLOCALIZED_NAME">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UNLOCALIZED_NAME</h4>
<pre>public static final&nbsp;java.lang.String UNLOCALIZED_NAME</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.swing.ResourceAction.UNLOCALIZED_NAME">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ResourceAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ResourceAction</h4>
<pre>public&nbsp;ResourceAction(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                      java.lang.String&nbsp;actionPrefix)</pre>
<div class="block">Creates a disabled action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferences</code> - user preferences used to retrieve localized properties of the action</dd>
<dd><code>resourceClass</code> - the class used as a context to retrieve localized properties of the action</dd>
<dd><code>actionPrefix</code> - prefix used in resource bundle to search action properties</dd>
</dl>
</li>
</ul>
<a name="ResourceAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ResourceAction</h4>
<pre>public&nbsp;ResourceAction(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                      java.lang.String&nbsp;actionPrefix,
                      boolean&nbsp;enabled)</pre>
<div class="block">Creates an action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferences</code> - user preferences used to retrieve localized description of the action</dd>
<dd><code>resourceClass</code> - the class used as a context to retrieve localized properties of the action</dd>
<dd><code>actionPrefix</code> - prefix used in resource bundle to search action properties</dd>
<dd><code>enabled</code> - <code>true</code> if the action should be enabled at creation.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="actionPerformed-java.awt.event.ActionEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>actionPerformed</h4>
<pre>public&nbsp;void&nbsp;actionPerformed(java.awt.event.ActionEvent&nbsp;ev)</pre>
<div class="block">Unsupported operation. Subclasses should override this method if they want
 to associate a real action to this class.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResourceAction.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.Constraints.html" title="enum in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.ButtonAction.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ResourceAction.html" target="_top">Frames</a></li>
<li><a href="ResourceAction.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
