<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>DimensionLine (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DimensionLine (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DimensionLine.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/DamagedHomeRecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/DimensionLine.html" target="_top">Frames</a></li>
<li><a href="DimensionLine.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class DimensionLine" class="title">Class DimensionLine</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.DimensionLine</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DimensionLine</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>
implements <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, <a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></pre>
<div class="block">A dimension line in plan.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.DimensionLine">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.Property.html" title="enum in com.eteks.sweethome3d.model">DimensionLine.Property</a></span></code>
<div class="block">The properties of a dimension line that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#DimensionLine-float-float-float-float-float-">DimensionLine</a></span>(float&nbsp;xStart,
             float&nbsp;yStart,
             float&nbsp;xEnd,
             float&nbsp;yEnd,
             float&nbsp;offset)</code>
<div class="block">Creates a dimension line from (<code>xStart</code>, <code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>), with a given offset.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#DimensionLine-float-float-float-float-float-float-float-">DimensionLine</a></span>(float&nbsp;xStart,
             float&nbsp;yStart,
             float&nbsp;elevationStart,
             float&nbsp;xEnd,
             float&nbsp;yEnd,
             float&nbsp;elevationEnd,
             float&nbsp;offset)</code>
<div class="block">Creates a dimension line from (<code>xStart</code>, <code>yStart</code>, <code>elevationStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>, <code>elevationEnd</code>), with a given offset.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#DimensionLine-java.lang.String-float-float-float-float-float-">DimensionLine</a></span>(java.lang.String&nbsp;id,
             float&nbsp;xStart,
             float&nbsp;yStart,
             float&nbsp;xEnd,
             float&nbsp;yEnd,
             float&nbsp;offset)</code>
<div class="block">Creates a dimension line from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>), with a given offset.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#DimensionLine-java.lang.String-float-float-float-float-float-float-float-">DimensionLine</a></span>(java.lang.String&nbsp;id,
             float&nbsp;xStart,
             float&nbsp;yStart,
             float&nbsp;elevationStart,
             float&nbsp;xEnd,
             float&nbsp;yEnd,
             float&nbsp;elevationEnd,
             float&nbsp;offset)</code>
<div class="block">Creates a dimension line from (<code>xStart</code>, <code>yStart</code>, <code>elevationStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>, <code>elevationEnd</code>), with a given offset.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this dimension line.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#containsEndExtensionLineAt-float-float-float-">containsEndExtensionLineAt</a></span>(float&nbsp;x,
                          float&nbsp;y,
                          float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the extension line at the end of this dimension line
 contains the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code> around the extension line.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#containsPoint-float-float-float-">containsPoint</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this dimension line contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#containsStartExtensionLinetAt-float-float-float-">containsStartExtensionLinetAt</a></span>(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the extension line at the start of this dimension line
 contains the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code> around the extension line.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the color used to display the text of this dimension line.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getElevationEnd--">getElevationEnd</a></span>()</code>
<div class="block">Returns the end point elevation of this dimension line.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getElevationStart--">getElevationStart</a></span>()</code>
<div class="block">Returns the start point elevation of this dimension line.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getEndMarkSize--">getEndMarkSize</a></span>()</code>
<div class="block">Returns the size of marks drawn at the end of the dimension line.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getLength--">getLength</a></span>()</code>
<div class="block">Returns the length of this dimension line.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getLengthStyle--">getLengthStyle</a></span>()</code>
<div class="block">Returns the text style used to display dimension line length.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getLevel--">getLevel</a></span>()</code>
<div class="block">Returns the level which this dimension line belongs to.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getOffset--">getOffset</a></span>()</code>
<div class="block">Returns the offset of this dimension line.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns the pitch angle in radians of this dimension line around its axis.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getPoints--">getPoints</a></span>()</code>
<div class="block">Returns the points of the rectangle surrounding
 this dimension line and its extension lines.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getXEnd--">getXEnd</a></span>()</code>
<div class="block">Returns the end point abscissa of this dimension line.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getXStart--">getXStart</a></span>()</code>
<div class="block">Returns the start point abscissa of this dimension line.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getYEnd--">getYEnd</a></span>()</code>
<div class="block">Returns the end point ordinate of this dimension line.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#getYStart--">getYStart</a></span>()</code>
<div class="block">Returns the start point ordinate of this dimension line.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></span>(float&nbsp;x0,
                   float&nbsp;y0,
                   float&nbsp;x1,
                   float&nbsp;y1)</code>
<div class="block">Returns <code>true</code> if this dimension line intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Returns <code>true</code> if this dimension line is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index
 or if the elevation of its highest end is higher than <code>level</code> elevation.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#isBottomPointAt-float-float-float-">isBottomPointAt</a></span>(float&nbsp;x,
               float&nbsp;y,
               float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the bottom left point of this dimension line is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#isElevationDimensionLine--">isElevationDimensionLine</a></span>()</code>
<div class="block">Returns <code>true</code> if this dimension line is an elevation (vertical) dimension line.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#isLeftPointAt-float-float-float-">isLeftPointAt</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the left point of this dimension line is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#isMiddlePointAt-float-float-float-">isMiddlePointAt</a></span>(float&nbsp;x,
               float&nbsp;y,
               float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the middle point of this dimension line
 is the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#isRightPointAt-float-float-float-">isRightPointAt</a></span>(float&nbsp;x,
              float&nbsp;y,
              float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the right point of this dimension line is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#isTopPointAt-float-float-float-">isTopPointAt</a></span>(float&nbsp;x,
            float&nbsp;y,
            float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the top point of this dimension line is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#isVisibleIn3D--">isVisibleIn3D</a></span>()</code>
<div class="block">Returns <code>true</code> if this dimension line should be displayed in 3D.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#move-float-float-">move</a></span>(float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Moves this dimension line of (<code>dx</code>, <code>dy</code>) units.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the color used to display the text of this dimension line.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setElevationEnd-float-">setElevationEnd</a></span>(float&nbsp;elevationEnd)</code>
<div class="block">Sets the end point elevation of this dimension line.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setElevationStart-float-">setElevationStart</a></span>(float&nbsp;elevationStart)</code>
<div class="block">Sets the start point elevation of this dimension line.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setEndMarkSize-float-">setEndMarkSize</a></span>(float&nbsp;endMarkSize)</code>
<div class="block">Sets the size of marks drawn at the end of the dimension line.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setLengthStyle-com.eteks.sweethome3d.model.TextStyle-">setLengthStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;lengthStyle)</code>
<div class="block">Sets the text style used to display dimension line length.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Sets the level of this dimension line.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setOffset-float-">setOffset</a></span>(float&nbsp;offset)</code>
<div class="block">Sets the offset of this dimension line.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setPitch-float-">setPitch</a></span>(float&nbsp;pitch)</code>
<div class="block">Sets the pitch angle of this dimension line.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setVisibleIn3D-boolean-">setVisibleIn3D</a></span>(boolean&nbsp;visibleIn3D)</code>
<div class="block">Sets whether this dimension line should be displayed in 3D.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setXEnd-float-">setXEnd</a></span>(float&nbsp;xEnd)</code>
<div class="block">Sets the end point abscissa of this dimension line.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setXStart-float-">setXStart</a></span>(float&nbsp;xStart)</code>
<div class="block">Sets the start point abscissa of this dimension line.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setYEnd-float-">setYEnd</a></span>(float&nbsp;yEnd)</code>
<div class="block">Sets the end point ordinate of this dimension line.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html#setYStart-float-">setYStart</a></span>(float&nbsp;yStart)</code>
<div class="block">Sets the start point ordinate of this dimension line.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DimensionLine-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DimensionLine</h4>
<pre>public&nbsp;DimensionLine(float&nbsp;xStart,
                     float&nbsp;yStart,
                     float&nbsp;xEnd,
                     float&nbsp;yEnd,
                     float&nbsp;offset)</pre>
<div class="block">Creates a dimension line from (<code>xStart</code>, <code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>), with a given offset.</div>
</li>
</ul>
<a name="DimensionLine-float-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DimensionLine</h4>
<pre>public&nbsp;DimensionLine(float&nbsp;xStart,
                     float&nbsp;yStart,
                     float&nbsp;elevationStart,
                     float&nbsp;xEnd,
                     float&nbsp;yEnd,
                     float&nbsp;elevationEnd,
                     float&nbsp;offset)</pre>
<div class="block">Creates a dimension line from (<code>xStart</code>, <code>yStart</code>, <code>elevationStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>, <code>elevationEnd</code>), with a given offset.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="DimensionLine-java.lang.String-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DimensionLine</h4>
<pre>public&nbsp;DimensionLine(java.lang.String&nbsp;id,
                     float&nbsp;xStart,
                     float&nbsp;yStart,
                     float&nbsp;xEnd,
                     float&nbsp;yEnd,
                     float&nbsp;offset)</pre>
<div class="block">Creates a dimension line from (<code>xStart</code>,<code>yStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>), with a given offset.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="DimensionLine-java.lang.String-float-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DimensionLine</h4>
<pre>public&nbsp;DimensionLine(java.lang.String&nbsp;id,
                     float&nbsp;xStart,
                     float&nbsp;yStart,
                     float&nbsp;elevationStart,
                     float&nbsp;xEnd,
                     float&nbsp;yEnd,
                     float&nbsp;elevationEnd,
                     float&nbsp;offset)</pre>
<div class="block">Creates a dimension line from (<code>xStart</code>, <code>yStart</code>, <code>elevationStart</code>)
 to (<code>xEnd</code>, <code>yEnd</code>, <code>elevationEnd</code>), with a given offset.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getXStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXStart</h4>
<pre>public&nbsp;float&nbsp;getXStart()</pre>
<div class="block">Returns the start point abscissa of this dimension line.</div>
</li>
</ul>
<a name="setXStart-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXStart</h4>
<pre>public&nbsp;void&nbsp;setXStart(float&nbsp;xStart)</pre>
<div class="block">Sets the start point abscissa of this dimension line. Once this dimension line
 is updated, listeners added to this dimension line will receive a change notification.</div>
</li>
</ul>
<a name="getYStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYStart</h4>
<pre>public&nbsp;float&nbsp;getYStart()</pre>
<div class="block">Returns the start point ordinate of this dimension line.</div>
</li>
</ul>
<a name="setYStart-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYStart</h4>
<pre>public&nbsp;void&nbsp;setYStart(float&nbsp;yStart)</pre>
<div class="block">Sets the start point ordinate of this dimension line. Once this dimension line
 is updated, listeners added to this dimension line will receive a change notification.</div>
</li>
</ul>
<a name="getElevationStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevationStart</h4>
<pre>public&nbsp;float&nbsp;getElevationStart()</pre>
<div class="block">Returns the start point elevation of this dimension line.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setElevationStart-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevationStart</h4>
<pre>public&nbsp;void&nbsp;setElevationStart(float&nbsp;elevationStart)</pre>
<div class="block">Sets the start point elevation of this dimension line. Once this dimension line
 is updated, listeners added to this dimension line will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getXEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXEnd</h4>
<pre>public&nbsp;float&nbsp;getXEnd()</pre>
<div class="block">Returns the end point abscissa of this dimension line.</div>
</li>
</ul>
<a name="setXEnd-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXEnd</h4>
<pre>public&nbsp;void&nbsp;setXEnd(float&nbsp;xEnd)</pre>
<div class="block">Sets the end point abscissa of this dimension line. Once this dimension line
 is updated, listeners added to this dimension line will receive a change notification.</div>
</li>
</ul>
<a name="getYEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYEnd</h4>
<pre>public&nbsp;float&nbsp;getYEnd()</pre>
<div class="block">Returns the end point ordinate of this dimension line.</div>
</li>
</ul>
<a name="setYEnd-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYEnd</h4>
<pre>public&nbsp;void&nbsp;setYEnd(float&nbsp;yEnd)</pre>
<div class="block">Sets the end point ordinate of this dimension line. Once this dimension line
 is updated, listeners added to this dimension line will receive a change notification.</div>
</li>
</ul>
<a name="getElevationEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevationEnd</h4>
<pre>public&nbsp;float&nbsp;getElevationEnd()</pre>
<div class="block">Returns the end point elevation of this dimension line.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setElevationEnd-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevationEnd</h4>
<pre>public&nbsp;void&nbsp;setElevationEnd(float&nbsp;elevationEnd)</pre>
<div class="block">Sets the end point elevation of this dimension line. Once this dimension line
 is updated, listeners added to this dimension line will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isElevationDimensionLine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isElevationDimensionLine</h4>
<pre>public&nbsp;boolean&nbsp;isElevationDimensionLine()</pre>
<div class="block">Returns <code>true</code> if this dimension line is an elevation (vertical) dimension line.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOffset</h4>
<pre>public&nbsp;float&nbsp;getOffset()</pre>
<div class="block">Returns the offset of this dimension line.</div>
</li>
</ul>
<a name="setOffset-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOffset</h4>
<pre>public&nbsp;void&nbsp;setOffset(float&nbsp;offset)</pre>
<div class="block">Sets the offset of this dimension line. Once this dimension line
 is updated, listeners added to this dimension line will receive a change notification.</div>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;float&nbsp;getPitch()</pre>
<div class="block">Returns the pitch angle in radians of this dimension line around its axis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setPitch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitch</h4>
<pre>public&nbsp;void&nbsp;setPitch(float&nbsp;pitch)</pre>
<div class="block">Sets the pitch angle of this dimension line. Once this dimension line
 is updated, listeners added to this dimension line will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLength</h4>
<pre>public&nbsp;float&nbsp;getLength()</pre>
<div class="block">Returns the length of this dimension line.</div>
</li>
</ul>
<a name="getLengthStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLengthStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;getLengthStyle()</pre>
<div class="block">Returns the text style used to display dimension line length.</div>
</li>
</ul>
<a name="setLengthStyle-com.eteks.sweethome3d.model.TextStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLengthStyle</h4>
<pre>public&nbsp;void&nbsp;setLengthStyle(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;lengthStyle)</pre>
<div class="block">Sets the text style used to display dimension line length.
 Once this dimension line is updated, listeners added to it will receive a change notification.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the color used to display the text of this dimension line.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the color used to display the text of this dimension line.
 Once this dimension line is updated, listeners added to this dimension line
 will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getEndMarkSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndMarkSize</h4>
<pre>public&nbsp;float&nbsp;getEndMarkSize()</pre>
<div class="block">Returns the size of marks drawn at the end of the dimension line.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setEndMarkSize-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEndMarkSize</h4>
<pre>public&nbsp;void&nbsp;setEndMarkSize(float&nbsp;endMarkSize)</pre>
<div class="block">Sets the size of marks drawn at the end of the dimension line.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isVisibleIn3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVisibleIn3D</h4>
<pre>public&nbsp;boolean&nbsp;isVisibleIn3D()</pre>
<div class="block">Returns <code>true</code> if this dimension line should be displayed in 3D.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setVisibleIn3D-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisibleIn3D</h4>
<pre>public&nbsp;void&nbsp;setVisibleIn3D(boolean&nbsp;visibleIn3D)</pre>
<div class="block">Sets whether this dimension line should be displayed in 3D.
 Once this dimension line is updated, listeners added to this dimension line
 will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;getLevel()</pre>
<div class="block">Returns the level which this dimension line belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#getLevel--">getLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevel</h4>
<pre>public&nbsp;void&nbsp;setLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Sets the level of this dimension line. Once this dimension line is updated,
 listeners added to this dimension line will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isAtLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAtLevel</h4>
<pre>public&nbsp;boolean&nbsp;isAtLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Returns <code>true</code> if this dimension line is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index
 or if the elevation of its highest end is higher than <code>level</code> elevation.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public&nbsp;float[][]&nbsp;getPoints()</pre>
<div class="block">Returns the points of the rectangle surrounding
 this dimension line and its extension lines.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#getPoints--">getPoints</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of the 4 (x,y) coordinates of the rectangle.</dd>
</dl>
</li>
</ul>
<a name="intersectsRectangle-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersectsRectangle</h4>
<pre>public&nbsp;boolean&nbsp;intersectsRectangle(float&nbsp;x0,
                                   float&nbsp;y0,
                                   float&nbsp;x1,
                                   float&nbsp;y1)</pre>
<div class="block">Returns <code>true</code> if this dimension line intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="containsPoint-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsPoint</h4>
<pre>public&nbsp;boolean&nbsp;containsPoint(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this dimension line contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#containsPoint-float-float-float-">containsPoint</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="isMiddlePointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMiddlePointAt</h4>
<pre>public&nbsp;boolean&nbsp;isMiddlePointAt(float&nbsp;x,
                               float&nbsp;y,
                               float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the middle point of this dimension line
 is the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</li>
</ul>
<a name="containsStartExtensionLinetAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsStartExtensionLinetAt</h4>
<pre>public&nbsp;boolean&nbsp;containsStartExtensionLinetAt(float&nbsp;x,
                                             float&nbsp;y,
                                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the extension line at the start of this dimension line
 contains the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code> around the extension line.</div>
</li>
</ul>
<a name="containsEndExtensionLineAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsEndExtensionLineAt</h4>
<pre>public&nbsp;boolean&nbsp;containsEndExtensionLineAt(float&nbsp;x,
                                          float&nbsp;y,
                                          float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the extension line at the end of this dimension line
 contains the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code> around the extension line.</div>
</li>
</ul>
<a name="isTopPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTopPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isTopPointAt(float&nbsp;x,
                            float&nbsp;y,
                            float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the top point of this dimension line is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isRightPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRightPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isRightPointAt(float&nbsp;x,
                              float&nbsp;y,
                              float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the right point of this dimension line is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isBottomPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBottomPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isBottomPointAt(float&nbsp;x,
                               float&nbsp;y,
                               float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the bottom left point of this dimension line is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isLeftPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLeftPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isLeftPointAt(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the left point of this dimension line is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="move-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>move</h4>
<pre>public&nbsp;void&nbsp;move(float&nbsp;dx,
                 float&nbsp;dy)</pre>
<div class="block">Moves this dimension line of (<code>dx</code>, <code>dy</code>) units.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#move-float-float-">move</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this dimension line.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DimensionLine.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/DamagedHomeRecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/DimensionLine.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/DimensionLine.html" target="_top">Frames</a></li>
<li><a href="DimensionLine.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
