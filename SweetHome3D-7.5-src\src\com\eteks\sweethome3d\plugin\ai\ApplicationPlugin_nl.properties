# ApplicationPlugin_nl.properties
# Sweet Home 3D AI Plugin Configuration - Dutch
# Copyright (c) 2025 Samuel <PERSON>egna

# Plugin identification
name=AI Plattegrond Analyse
description=Analyseer plattegronden met kunstmatige intelligentie om inzichten en verbeteringsvoorstellen te bieden
provider=<PERSON> Kpassegna

# Features
features=AI Analyse, Plattegrond Inzichten, Meerdere AI Providers, Privacy Controles

# Requirements
requirements=Internetverbinding voor cloud AI providers (optioneel voor lokale providers)

# UI Strings for internationalization
# Action properties
AIAction.Name=AI Analyse
AIAction.ShortDescription=Plattegrond analyseren met AI
AIAction.Menu=Gereedschappen

# Dialog titles
AIChatDialog.title=AI Plattegrond Analyse
AISettingsDialog.title=AI Instellingen

# Button labels
button.send=Verzenden
button.newAnalysis=Nieuwe Analyse
button.settings=Instellingen
button.testConnection=Verbinding Testen
button.save=Opslaan
button.cancel=Annuleren

# Labels
label.provider=Provider:
label.baseUrl=Basis URL:
label.apiKey=API Sleutel:
label.model=Model:
label.temperature=Temperatuur:
label.maxTokens=Max Tokens:
label.status=Status:

# Messages
message.analyzing=Plattegrond analyseren...
message.processingQuestion=Vraag verwerken...
message.testingConnection=Verbinding testen...
message.connectionSuccessful=Verbinding succesvol!
message.connectionFailed=Verbinding mislukt: {0}
message.configurationSaved=Configuratie succesvol opgeslagen
message.validationError=Configuratiefouten:\n{0}
message.noConfiguration=AI provider niet geconfigureerd. Configureer eerst de instellingen.

# Analysis prompt
analysis.prompt=Analyseer deze plattegrond en geef uitgebreide inzichten inclusief:\n1. Layout efficiëntie en ruimtegebruik\n2. Verkeersstromen en circulatiepatronen\n3. Natuurlijke verlichting en ventilatiemogelijkheden\n4. Toegankelijkheidsoverwegingen\n5. Functionele relaties tussen ruimtes\n6. Verbeteringsvoorstellen\n7. Naleving van algemene bouwstandaarden\n8. Energie-efficiëntieoverwegingen\n\nGeef specifieke, uitvoerbare aanbevelingen die de functionaliteit, het comfort en de esthetische aantrekkingskracht van deze ruimte zouden verbeteren.

# Error messages
error.analysisError=Analysefout: {0}
error.configurationError=Configuratiefout: {0}
error.connectionError=Verbindingsfout: {0}
error.invalidConfiguration=Ongeldige configuratie
error.missingApiKey=API sleutel is vereist
error.missingBaseUrl=Basis URL is vereist
error.missingModel=Modelselectie is vereist

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Lokaal)
provider.lmstudio=LM Studio (Lokaal)
provider.anythingllm=AnythingLLM (Lokaal)
provider.jan=Jan (Lokaal)
provider.custom=Aangepast
