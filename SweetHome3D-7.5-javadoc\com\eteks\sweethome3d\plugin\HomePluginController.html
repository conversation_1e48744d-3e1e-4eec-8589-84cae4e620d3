<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomePluginController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomePluginController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePluginController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/plugin/HomePluginController.html" target="_top">Frames</a></li>
<li><a href="HomePluginController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.plugin</div>
<h2 title="Class HomePluginController" class="title">Class HomePluginController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller.HomeController</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.plugin.HomePluginController</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/applet/HomeAppletController.html" title="class in com.eteks.sweethome3d.applet">HomeAppletController</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomePluginController</span>
extends <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></pre>
<div class="block">A MVC controller for the home view able to manage plug-ins.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html#HomePluginController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-">HomePluginController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                    <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                    <a href="../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager)</code>
<div class="block">Creates the controller of home view.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html" title="class in com.eteks.sweethome3d.plugin">Plugin</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html#getPlugins--">getPlugins</a></span>()</code>
<div class="block">Returns the plug-ins available with this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/HomePluginController.html#importPlugin-java.lang.String-">importPlugin</a></span>(java.lang.String&nbsp;pluginLocation)</code>
<div class="block">Imports the plugin at the given location.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.HomeController">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#about--">about</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#addFurnitureToGroup--">addFurnitureToGroup</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#addHomeFurniture--">addHomeFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#checkUpdates-boolean-">checkUpdates</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#close--">close</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#close-java.lang.Runnable-">close</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createPhoto--">createPhoto</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createPhotos--">createPhotos</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.TransferObserver-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType...-">createTransferData</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#createVideo--">createVideo</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#cut-java.util.List-">cut</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#delete--">delete</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteBackgroundImage--">deleteBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteCameras--">deleteCameras</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#deleteRecentHomes--">deleteRecentHomes</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#disableMagnetism--">disableMagnetism</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-float-float-">drop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-float-float-">drop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Level-float-float-java.lang.Float-">drop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#drop-java.util.List-com.eteks.sweethome3d.viewcontroller.View-com.eteks.sweethome3d.model.Selectable-">drop</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#dropFiles-java.util.List-float-float-">dropFiles</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#editPreferences--">editPreferences</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableActionsBoundToSelection--">enableActionsBoundToSelection</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableMagnetism--">enableMagnetism</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enablePasteAction--">enablePasteAction</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#enableSelectAllAction--">enableSelectAllAction</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exit--">exit</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToCSV--">exportToCSV</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToOBJ--">exportToOBJ</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#exportToSVG--">exportToSVG</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#focusedViewChanged-com.eteks.sweethome3d.viewcontroller.View-">focusedViewChanged</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getContentManager--">getContentManager</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getFurnitureCatalogController--">getFurnitureCatalogController</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getFurnitureController--">getFurnitureController</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getHomeController3D--">getHomeController3D</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getPlanController--">getPlanController</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getRecentHomes--">getRecentHomes</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getUndoableEditSupport--">getUndoableEditSupport</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getVersion--">getVersion</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#getView--">getView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#help--">help</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#hideBackgroundImage--">hideBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importBackgroundImage--">importBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurniture--">importFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurnitureLibrary--">importFurnitureLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importFurnitureLibrary-java.lang.String-">importFurnitureLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importLanguageLibrary--">importLanguageLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importLanguageLibrary-java.lang.String-">importLanguageLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexture--">importTexture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexturesLibrary--">importTexturesLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#importTexturesLibrary-java.lang.String-">importTexturesLibrary</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#modifyBackgroundImage--">modifyBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#modifySelectedFurniture--">modifySelectedFurniture</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#newHome--">newHome</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#newHomeFromExample--">newHomeFromExample</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#open--">open</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#open-java.lang.String-">open</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#paste-java.util.List-">paste</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#pasteStyle--">pasteStyle</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#pasteToGroup--">pasteToGroup</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#previewPrint--">previewPrint</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#print--">print</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#printToPDF--">printToPDF</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#redo--">redo</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#save--">save</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAndCompress--">saveAndCompress</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAs--">saveAs</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAs-com.eteks.sweethome3d.model.HomeRecorder.Type-java.lang.Runnable-">saveAs</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#saveAsAndCompress--">saveAsAndCompress</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#selectAll--">selectAll</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setHomeProperty-java.lang.String-java.lang.String-">setHomeProperty</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setupPage--">setupPage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setVisualProperty-java.lang.String-java.lang.Object-">setVisualProperty</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#showBackgroundImage--">showBackgroundImage</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#storeCamera--">storeCamera</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#undo--">undo</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#zoomIn--">zoomIn</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#zoomOut--">zoomOut</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomePluginController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.HomeApplication-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.plugin.PluginManager-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomePluginController</h4>
<pre>public&nbsp;HomePluginController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                            <a href="../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                            <a href="../../../../com/eteks/sweethome3d/plugin/PluginManager.html" title="class in com.eteks.sweethome3d.plugin">PluginManager</a>&nbsp;pluginManager)</pre>
<div class="block">Creates the controller of home view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home edited by this controller and its view.</dd>
<dd><code>application</code> - the instance of current application.</dd>
<dd><code>viewFactory</code> - a factory able to create views.</dd>
<dd><code>contentManager</code> - the content manager of the application.</dd>
<dd><code>pluginManager</code> - the plug-in manager of the application.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPlugins--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlugins</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html" title="class in com.eteks.sweethome3d.plugin">Plugin</a>&gt;&nbsp;getPlugins()</pre>
<div class="block">Returns the plug-ins available with this controller.</div>
</li>
</ul>
<a name="importPlugin-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>importPlugin</h4>
<pre>public&nbsp;void&nbsp;importPlugin(java.lang.String&nbsp;pluginLocation)</pre>
<div class="block">Imports the plugin at the given location.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePluginController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/plugin/HomePluginController.html" target="_top">Frames</a></li>
<li><a href="HomePluginController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
