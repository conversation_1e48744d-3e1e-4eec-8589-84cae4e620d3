<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>CatalogPieceOfFurniture (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CatalogPieceOfFurniture (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CatalogPieceOfFurniture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogLight.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" target="_top">Frames</a></li>
<li><a href="CatalogPieceOfFurniture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class CatalogPieceOfFurniture" class="title">Class CatalogPieceOfFurniture</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.CatalogPieceOfFurniture</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, java.lang.Cloneable, java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&gt;</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">CatalogDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogLight.html" title="class in com.eteks.sweethome3d.model">CatalogLight</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html" title="class in com.eteks.sweethome3d.model">CatalogShelfUnit</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">CatalogPieceOfFurniture</span>
extends java.lang.Object
implements java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&gt;, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a>, java.lang.Cloneable</pre>
<div class="block">A catalog piece of furniture.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-boolean-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       boolean&nbsp;movable,
                       boolean&nbsp;doorOrWindow)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-boolean-java.lang.Integer-float:A:A-boolean-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       boolean&nbsp;doorOrWindow,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       float&nbsp;iconYaw,
                       boolean&nbsp;proportional)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.Integer-float:A:A-boolean-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       float&nbsp;iconYaw,
                       boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-boolean-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       float&nbsp;iconYaw,
                       boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-boolean-java.lang.Long-java.lang.String-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       float&nbsp;iconYaw,
                       boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-int-java.lang.Long-java.lang.String-float-float-float-boolean-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;name,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       java.lang.Integer&nbsp;color,
                       float[][]&nbsp;modelRotation,
                       int&nbsp;modelFlags,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       float&nbsp;iconYaw,
                       float&nbsp;iconPitch,
                       float&nbsp;iconScale,
                       boolean&nbsp;proportional)</code>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       boolean&nbsp;doorOrWindow,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency,
                       java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       boolean&nbsp;backFaceShown,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       int&nbsp;modelFlags,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency,
                       java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogPieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                       java.lang.String&nbsp;name,
                       java.lang.String&nbsp;description,
                       java.lang.String&nbsp;information,
                       java.lang.String&nbsp;license,
                       java.lang.String[]&nbsp;tags,
                       java.lang.Long&nbsp;creationDate,
                       java.lang.Float&nbsp;grade,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                       <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                       float&nbsp;width,
                       float&nbsp;depth,
                       float&nbsp;height,
                       float&nbsp;elevation,
                       float&nbsp;dropOnTopElevation,
                       boolean&nbsp;movable,
                       java.lang.String&nbsp;staircaseCutOutShape,
                       float[][]&nbsp;modelRotation,
                       int&nbsp;modelFlags,
                       java.lang.Long&nbsp;modelSize,
                       java.lang.String&nbsp;creator,
                       boolean&nbsp;resizable,
                       boolean&nbsp;deformable,
                       boolean&nbsp;texturable,
                       boolean&nbsp;horizontallyRotatable,
                       java.math.BigDecimal&nbsp;price,
                       java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                       java.lang.String&nbsp;currency,
                       java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                       java.util.Map&lt;java.lang.String,<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this piece.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#compareTo-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-">compareTo</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Compares the names of this piece and the one in parameter.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">Returns <code>true</code> if this piece and the one in parameter are the same objects.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getCategory--">getCategory</a></span>()</code>
<div class="block">Returns the category of this piece of furniture.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the color of this piece of furniture.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the value of an additional content <code>name</code> associated to this piece.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getCreationDate--">getCreationDate</a></span>()</code>
<div class="block">Returns the creation date of this piece in milliseconds since the epoch,
 or <code>null</code> if no date is given to this piece.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getCreator--">getCreator</a></span>()</code>
<div class="block">Returns the creator of this piece.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getCurrency--">getCurrency</a></span>()</code>
<div class="block">Returns the price currency, noted with ISO 4217 code, or <code>null</code>
 if it has no price or default currency should be used.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getDepth--">getDepth</a></span>()</code>
<div class="block">Returns the depth of this piece of furniture.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Returns the description of this piece of furniture.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a></span>()</code>
<div class="block">Returns the elevation at which should be placed an object dropped on this piece.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the elevation of this piece of furniture.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getFilterCriteria--">getFilterCriteria</a></span>()</code>
<div class="block">Returns the strings used as criteria for filtering (name, category, creator, license, description and tags).</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getGrade--">getGrade</a></span>()</code>
<div class="block">Returns the grade of this piece, or <code>null</code> if no grade is given to this piece.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height of this piece of furniture.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIconPitch--">getIconPitch</a></span>()</code>
<div class="block">Returns the pitch angle used to create the piece icon.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIconScale--">getIconScale</a></span>()</code>
<div class="block">Returns the scale used to create the piece icon.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getIconYaw--">getIconYaw</a></span>()</code>
<div class="block">Returns the yaw angle used to create the piece icon.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getId--">getId</a></span>()</code>
<div class="block">Returns the ID of this piece of furniture or <code>null</code>.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getInformation--">getInformation</a></span>()</code>
<div class="block">Returns the additional information associated to this piece, or <code>null</code>.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getLicense--">getLicense</a></span>()</code>
<div class="block">Returns the license associated to this piece, or <code>null</code>.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModelFlags--">getModelFlags</a></span>()</code>
<div class="block">Returns the flags which should be applied to the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModelRotation--">getModelRotation</a></span>()</code>
<div class="block">Returns the rotation 3 by 3 matrix of this piece of furniture that ensures
 its model is correctly oriented.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getModelSize--">getModelSize</a></span>()</code>
<div class="block">Returns the size of the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this piece of furniture.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getPrice--">getPrice</a></span>()</code>
<div class="block">Returns the price of this piece of furniture or <code>null</code>.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the value of an additional property <code>name</code> of this piece.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.util.Collection&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getPropertyNames--">getPropertyNames</a></span>()</code>
<div class="block">Returns the names of the additional properties of this piece.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></span>()</code>
<div class="block">Returns the shape used to cut out upper levels when they intersect with the piece
 like a staircase.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getTags--">getTags</a></span>()</code>
<div class="block">Returns the tags associated to this piece.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></span>()</code>
<div class="block">Returns the Value Added Tax percentage applied to the price of this piece of furniture.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the width of this piece of furniture.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#hashCode--">hashCode</a></span>()</code>
<div class="block">Returns default hash code.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a></span>()</code>
<div class="block">Returns <code>true</code> if the back face of the piece of furniture
 model should be displayed.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns <code>true</code> if the type of given additional property is a content.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isDeformable--">isDeformable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece of furniture is a door or a window.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></span>()</code>
<div class="block">Returns <code>false</code> if this piece should not rotate around an horizontal axis.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isModifiable--">isModifiable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is modifiable (not read from resources).</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isMovable--">isMovable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece of furniture is movable.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isProportional--">isProportional</a></span>()</code>
<div class="block">Returns <code>true</code> if size proportions should be kept.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isResizable--">isResizable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is resizable.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isTexturable--">isTexturable</a></span>()</code>
<div class="block">Returns <code>false</code> if this piece should always keep the same color or texture.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#matchesFilter-java.lang.String-">matchesFilter</a></span>(java.lang.String&nbsp;filter)</code>
<div class="block">Returns <code>true</code> if this piece matches the given <code>filter</code> text.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>finalize, getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;name,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               boolean&nbsp;movable,
                               boolean&nbsp;doorOrWindow)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
<div class="block">Creates a catalog piece of furniture.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>doorOrWindow</code> - if <code>true</code>, the new piece is a door or a window</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               boolean&nbsp;doorOrWindow,
                               float[][]&nbsp;modelRotation,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>doorOrWindow</code> - if <code>true</code>, the new piece is a door or a window</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               float[][]&nbsp;modelRotation,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               float[][]&nbsp;modelRotation,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               float[][]&nbsp;modelRotation,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               float[][]&nbsp;modelRotation,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                               java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               java.lang.String&nbsp;information,
                               java.lang.String[]&nbsp;tags,
                               java.lang.Long&nbsp;creationDate,
                               java.lang.Float&nbsp;grade,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               float[][]&nbsp;modelRotation,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                               java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>information</code> - additional information associated to the new piece</dd>
<dd><code>tags</code> - tags associated to the new piece</dd>
<dd><code>creationDate</code> - creation date of the new piece in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the piece of furniture or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               java.lang.String&nbsp;information,
                               java.lang.String[]&nbsp;tags,
                               java.lang.Long&nbsp;creationDate,
                               java.lang.Float&nbsp;grade,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               float&nbsp;dropOnTopElevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               float[][]&nbsp;modelRotation,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                               java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>information</code> - additional information associated to the new piece</dd>
<dd><code>tags</code> - tags associated to the new piece</dd>
<dd><code>creationDate</code> - creation date of the new piece in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the piece of furniture or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               java.lang.String&nbsp;information,
                               java.lang.String[]&nbsp;tags,
                               java.lang.Long&nbsp;creationDate,
                               java.lang.Float&nbsp;grade,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               float&nbsp;dropOnTopElevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               float[][]&nbsp;modelRotation,
                               boolean&nbsp;backFaceShown,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                               java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>information</code> - additional information associated to the new piece</dd>
<dd><code>tags</code> - tags associated to the new piece</dd>
<dd><code>creationDate</code> - creation date of the new piece in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the piece of furniture or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown instead of front faces</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture.</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               java.lang.String&nbsp;information,
                               java.lang.String[]&nbsp;tags,
                               java.lang.Long&nbsp;creationDate,
                               java.lang.Float&nbsp;grade,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               float&nbsp;dropOnTopElevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               float[][]&nbsp;modelRotation,
                               boolean&nbsp;backFaceShown,
                               java.lang.Long&nbsp;modelSize,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               boolean&nbsp;horizontallyRotatable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                               java.lang.String&nbsp;currency)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>information</code> - additional information associated to the new piece</dd>
<dd><code>tags</code> - tags associated to the new piece</dd>
<dd><code>creationDate</code> - creation date of the new piece in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the piece of furniture or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown instead of front faces</dd>
<dd><code>modelSize</code> - size of the 3D model of the new piece</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture</dd>
<dd><code>horizontallyRotatable</code> - if <code>false</code> this piece
            should not rotate around an horizontal axis</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               java.lang.String&nbsp;information,
                               java.lang.String[]&nbsp;tags,
                               java.lang.Long&nbsp;creationDate,
                               java.lang.Float&nbsp;grade,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               float&nbsp;dropOnTopElevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               float[][]&nbsp;modelRotation,
                               boolean&nbsp;backFaceShown,
                               java.lang.Long&nbsp;modelSize,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               boolean&nbsp;horizontallyRotatable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                               java.lang.String&nbsp;currency,
                               java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>information</code> - additional information associated to the new piece</dd>
<dd><code>tags</code> - tags associated to the new piece</dd>
<dd><code>creationDate</code> - creation date of the new piece in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the piece of furniture or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown instead of front faces</dd>
<dd><code>modelSize</code> - size of the 3D model of the new piece</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture</dd>
<dd><code>horizontallyRotatable</code> - if <code>false</code> this piece
            should not rotate around an horizontal axis</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dd><code>properties</code> - additional properties associating a key to a value or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.7</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               java.lang.String&nbsp;information,
                               java.lang.String[]&nbsp;tags,
                               java.lang.Long&nbsp;creationDate,
                               java.lang.Float&nbsp;grade,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               float&nbsp;dropOnTopElevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               float[][]&nbsp;modelRotation,
                               int&nbsp;modelFlags,
                               java.lang.Long&nbsp;modelSize,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               boolean&nbsp;horizontallyRotatable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                               java.lang.String&nbsp;currency,
                               java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>information</code> - additional information associated to the new piece</dd>
<dd><code>tags</code> - tags associated to the new piece</dd>
<dd><code>creationDate</code> - creation date of the new piece in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the piece of furniture or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>modelFlags</code> - flags which should be applied to piece model</dd>
<dd><code>modelSize</code> - size of the 3D model of the new piece</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture</dd>
<dd><code>horizontallyRotatable</code> - if <code>false</code> this piece
            should not rotate around an horizontal axis</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dd><code>properties</code> - additional properties associating a key to a value or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;id,
                               java.lang.String&nbsp;name,
                               java.lang.String&nbsp;description,
                               java.lang.String&nbsp;information,
                               java.lang.String&nbsp;license,
                               java.lang.String[]&nbsp;tags,
                               java.lang.Long&nbsp;creationDate,
                               java.lang.Float&nbsp;grade,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               float&nbsp;dropOnTopElevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               float[][]&nbsp;modelRotation,
                               int&nbsp;modelFlags,
                               java.lang.Long&nbsp;modelSize,
                               java.lang.String&nbsp;creator,
                               boolean&nbsp;resizable,
                               boolean&nbsp;deformable,
                               boolean&nbsp;texturable,
                               boolean&nbsp;horizontallyRotatable,
                               java.math.BigDecimal&nbsp;price,
                               java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
                               java.lang.String&nbsp;currency,
                               java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
                               java.util.Map&lt;java.lang.String,<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</pre>
<div class="block">Creates an unmodifiable catalog piece of furniture of the default catalog.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the id of the new piece or <code>null</code></dd>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>description</code> - the description of the new piece</dd>
<dd><code>information</code> - additional information associated to the new piece</dd>
<dd><code>license</code> - license of the new piece</dd>
<dd><code>tags</code> - tags associated to the new piece</dd>
<dd><code>creationDate</code> - creation date of the new piece in milliseconds since the epoch</dd>
<dd><code>grade</code> - grade of the piece of furniture or <code>null</code></dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>planIcon</code> - content of the icon of the new piece displayed in plan</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>dropOnTopElevation</code> - a percentage of the height at which should be placed
            an object dropped on the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>modelFlags</code> - flags which should be applied to piece model</dd>
<dd><code>modelSize</code> - size of the 3D model of the new piece</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>resizable</code> - if <code>true</code>, the size of the new piece may be edited</dd>
<dd><code>deformable</code> - if <code>true</code>, the width, depth and height of the new piece may
            change independently from each other</dd>
<dd><code>texturable</code> - if <code>false</code> this piece should always keep the same color or texture</dd>
<dd><code>horizontallyRotatable</code> - if <code>false</code> this piece
            should not rotate around an horizontal axis</dd>
<dd><code>price</code> - the price of the new piece or <code>null</code></dd>
<dd><code>valueAddedTaxPercentage</code> - the Value Added Tax percentage applied to the
             price of the new piece or <code>null</code></dd>
<dd><code>currency</code> - the price currency, noted with ISO 4217 code, or <code>null</code></dd>
<dd><code>properties</code> - additional properties associating a key to a value or <code>null</code></dd>
<dd><code>contents</code> - additional contents associating a key to a value or <code>null</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-boolean-java.lang.Integer-float:A:A-boolean-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;name,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               boolean&nbsp;doorOrWindow,
                               java.lang.Integer&nbsp;color,
                               float[][]&nbsp;modelRotation,
                               boolean&nbsp;backFaceShown,
                               float&nbsp;iconYaw,
                               boolean&nbsp;proportional)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">As of version 1.7, use constructor without <code>doorOrWindow</code>
             parameter since a catalog door and window is supposed to be an instance
             of <a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model"><code>CatalogDoorOrWindow</code></a></span></div>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>doorOrWindow</code> - if <code>true</code>, the new piece is a door or a window</dd>
<dd><code>color</code> - the color of the piece as RGB code or <code>null</code> if piece color is unchanged</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown instead of front faces</dd>
<dd><code>iconYaw</code> - the yaw angle used to create the piece icon</dd>
<dd><code>proportional</code> - if <code>true</code>, size proportions will be kept</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.Integer-float:A:A-boolean-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;name,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               java.lang.Integer&nbsp;color,
                               float[][]&nbsp;modelRotation,
                               boolean&nbsp;backFaceShown,
                               float&nbsp;iconYaw,
                               boolean&nbsp;proportional)</pre>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>color</code> - the color of the piece as RGB code or <code>null</code> if piece color is unchanged</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown</dd>
<dd><code>iconYaw</code> - the yaw angle used to create the piece icon</dd>
<dd><code>proportional</code> - if <code>true</code>, size proportions will be kept</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-boolean-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;name,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               java.lang.Integer&nbsp;color,
                               float[][]&nbsp;modelRotation,
                               boolean&nbsp;backFaceShown,
                               float&nbsp;iconYaw,
                               boolean&nbsp;proportional)</pre>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>color</code> - the color of the piece as RGB code or <code>null</code> if piece color is unchanged</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown</dd>
<dd><code>iconYaw</code> - the yaw angle used to create the piece icon</dd>
<dd><code>proportional</code> - if <code>true</code>, size proportions will be kept</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-boolean-java.lang.Long-java.lang.String-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;name,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               java.lang.Integer&nbsp;color,
                               float[][]&nbsp;modelRotation,
                               boolean&nbsp;backFaceShown,
                               java.lang.Long&nbsp;modelSize,
                               java.lang.String&nbsp;creator,
                               float&nbsp;iconYaw,
                               boolean&nbsp;proportional)</pre>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>color</code> - the color of the piece as RGB code or <code>null</code> if piece color is unchanged</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>backFaceShown</code> - <code>true</code> if back face should be shown</dd>
<dd><code>modelSize</code> - size of the 3D model of the new piece</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>iconYaw</code> - the yaw angle used to create the piece icon</dd>
<dd><code>proportional</code> - if <code>true</code>, size proportions will be kept</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="CatalogPieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-java.lang.String-java.lang.Integer-float:A:A-int-java.lang.Long-java.lang.String-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CatalogPieceOfFurniture</h4>
<pre>public&nbsp;CatalogPieceOfFurniture(java.lang.String&nbsp;name,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
                               <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
                               float&nbsp;width,
                               float&nbsp;depth,
                               float&nbsp;height,
                               float&nbsp;elevation,
                               boolean&nbsp;movable,
                               java.lang.String&nbsp;staircaseCutOutShape,
                               java.lang.Integer&nbsp;color,
                               float[][]&nbsp;modelRotation,
                               int&nbsp;modelFlags,
                               java.lang.Long&nbsp;modelSize,
                               java.lang.String&nbsp;creator,
                               float&nbsp;iconYaw,
                               float&nbsp;iconPitch,
                               float&nbsp;iconScale,
                               boolean&nbsp;proportional)</pre>
<div class="block">Creates a modifiable catalog piece of furniture with all its values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the new piece</dd>
<dd><code>icon</code> - content of the icon of the new piece</dd>
<dd><code>model</code> - content of the 3D model of the new piece</dd>
<dd><code>width</code> - the width in centimeters of the new piece</dd>
<dd><code>depth</code> - the depth in centimeters of the new piece</dd>
<dd><code>height</code> - the height in centimeters of the new piece</dd>
<dd><code>elevation</code> - the elevation in centimeters of the new piece</dd>
<dd><code>movable</code> - if <code>true</code>, the new piece is movable</dd>
<dd><code>staircaseCutOutShape</code> - the shape used to cut out upper levels when they intersect
            with the piece like a staircase</dd>
<dd><code>color</code> - the color of the piece as RGB code or <code>null</code> if piece color is unchanged</dd>
<dd><code>modelRotation</code> - the rotation 3 by 3 matrix applied to the piece model</dd>
<dd><code>modelFlags</code> - flags which should be applied to piece model</dd>
<dd><code>modelSize</code> - size of the 3D model of the new piece</dd>
<dd><code>creator</code> - the creator of the model</dd>
<dd><code>iconYaw</code> - the yaw angle used to create the piece icon</dd>
<dd><code>iconPitch</code> - the pich angle used to create the piece icon</dd>
<dd><code>iconScale</code> - the scale used to create the piece icon</dd>
<dd><code>proportional</code> - if <code>true</code>, size proportions will be kept</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getId()</pre>
<div class="block">Returns the ID of this piece of furniture or <code>null</code>.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDescription()</pre>
<div class="block">Returns the description of this piece of furniture.
 The returned value may be <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDescription--">getDescription</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getInformation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInformation</h4>
<pre>public&nbsp;java.lang.String&nbsp;getInformation()</pre>
<div class="block">Returns the additional information associated to this piece, or <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getLicense--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLicense</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLicense()</pre>
<div class="block">Returns the license associated to this piece, or <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getLicense--">getLicense</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getTags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTags</h4>
<pre>public&nbsp;java.lang.String[]&nbsp;getTags()</pre>
<div class="block">Returns the tags associated to this piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getCreationDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreationDate</h4>
<pre>public&nbsp;java.lang.Long&nbsp;getCreationDate()</pre>
<div class="block">Returns the creation date of this piece in milliseconds since the epoch,
 or <code>null</code> if no date is given to this piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getGrade--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGrade</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getGrade()</pre>
<div class="block">Returns the grade of this piece, or <code>null</code> if no grade is given to this piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getDepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepth</h4>
<pre>public&nbsp;float&nbsp;getDepth()</pre>
<div class="block">Returns the depth of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;float&nbsp;getHeight()</pre>
<div class="block">Returns the height of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;float&nbsp;getWidth()</pre>
<div class="block">Returns the width of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>public&nbsp;float&nbsp;getElevation()</pre>
<div class="block">Returns the elevation of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getElevation--">getElevation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getDropOnTopElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDropOnTopElevation</h4>
<pre>public&nbsp;float&nbsp;getDropOnTopElevation()</pre>
<div class="block">Returns the elevation at which should be placed an object dropped on this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a percentage of the height of this piece. A negative value means that the piece
         should be ignored when an object is dropped on it.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="isMovable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMovable</h4>
<pre>public&nbsp;boolean&nbsp;isMovable()</pre>
<div class="block">Returns <code>true</code> if this piece of furniture is movable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="isDoorOrWindow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDoorOrWindow</h4>
<pre>public&nbsp;boolean&nbsp;isDoorOrWindow()</pre>
<div class="block">Returns <code>true</code> if this piece of furniture is a door or a window.
 As this method existed before <a href="../../../../com/eteks/sweethome3d/model/CatalogDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">CatalogDoorOrWindow</a> class,
 you shouldn't rely on the value returned by this method to guess if a piece
 is an instance of <code>DoorOrWindow</code> class.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getIcon()</pre>
<div class="block">Returns the icon of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html#getIcon--">getIcon</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getPlanIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlanIcon</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getPlanIcon()</pre>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="getModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getModel()</pre>
<div class="block">Returns the 3D model of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getModelFlags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelFlags</h4>
<pre>public&nbsp;int&nbsp;getModelFlags()</pre>
<div class="block">Returns the flags which should be applied to the 3D model of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getModelSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelSize</h4>
<pre>public&nbsp;java.lang.Long&nbsp;getModelSize()</pre>
<div class="block">Returns the size of the 3D model of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getModelRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelRotation</h4>
<pre>public&nbsp;float[][]&nbsp;getModelRotation()</pre>
<div class="block">Returns the rotation 3 by 3 matrix of this piece of furniture that ensures
 its model is correctly oriented.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getStaircaseCutOutShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaircaseCutOutShape</h4>
<pre>public&nbsp;java.lang.String&nbsp;getStaircaseCutOutShape()</pre>
<div class="block">Returns the shape used to cut out upper levels when they intersect with the piece
 like a staircase.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getCreator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreator</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCreator()</pre>
<div class="block">Returns the creator of this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html#getCreator--">getCreator</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/CatalogItem.html" title="interface in com.eteks.sweethome3d.model">CatalogItem</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="isBackFaceShown--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackFaceShown</h4>
<pre>public&nbsp;boolean&nbsp;isBackFaceShown()</pre>
<div class="block">Returns <code>true</code> if the back face of the piece of furniture
 model should be displayed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the color of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getIconYaw--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIconYaw</h4>
<pre>public&nbsp;float&nbsp;getIconYaw()</pre>
<div class="block">Returns the yaw angle used to create the piece icon.</div>
</li>
</ul>
<a name="getIconPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIconPitch</h4>
<pre>public&nbsp;float&nbsp;getIconPitch()</pre>
<div class="block">Returns the pitch angle used to create the piece icon.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getIconScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIconScale</h4>
<pre>public&nbsp;float&nbsp;getIconScale()</pre>
<div class="block">Returns the scale used to create the piece icon.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="isProportional--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProportional</h4>
<pre>public&nbsp;boolean&nbsp;isProportional()</pre>
<div class="block">Returns <code>true</code> if size proportions should be kept.</div>
</li>
</ul>
<a name="isModifiable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isModifiable</h4>
<pre>public&nbsp;boolean&nbsp;isModifiable()</pre>
<div class="block">Returns <code>true</code> if this piece is modifiable (not read from resources).</div>
</li>
</ul>
<a name="isResizable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isResizable</h4>
<pre>public&nbsp;boolean&nbsp;isResizable()</pre>
<div class="block">Returns <code>true</code> if this piece is resizable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="isDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeformable</h4>
<pre>public&nbsp;boolean&nbsp;isDeformable()</pre>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isWidthDepthDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWidthDepthDeformable</h4>
<pre>public&nbsp;boolean&nbsp;isWidthDepthDeformable()</pre>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isTexturable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTexturable</h4>
<pre>public&nbsp;boolean&nbsp;isTexturable()</pre>
<div class="block">Returns <code>false</code> if this piece should always keep the same color or texture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isHorizontallyRotatable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHorizontallyRotatable</h4>
<pre>public&nbsp;boolean&nbsp;isHorizontallyRotatable()</pre>
<div class="block">Returns <code>false</code> if this piece should not rotate around an horizontal axis.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getPrice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrice</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getPrice()</pre>
<div class="block">Returns the price of this piece of furniture or <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getValueAddedTaxPercentage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueAddedTaxPercentage</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getValueAddedTaxPercentage()</pre>
<div class="block">Returns the Value Added Tax percentage applied to the price of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getCurrency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrency</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCurrency()</pre>
<div class="block">Returns the price currency, noted with ISO 4217 code, or <code>null</code>
 if it has no price or default currency should be used.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProperty</h4>
<pre>public&nbsp;java.lang.String&nbsp;getProperty(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the value of an additional property <code>name</code> of this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the value of the property or <code>null</code> if it doesn't exist or if it's not a string.</dd>
</dl>
</li>
</ul>
<a name="getPropertyNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPropertyNames</h4>
<pre>public&nbsp;java.util.Collection&lt;java.lang.String&gt;&nbsp;getPropertyNames()</pre>
<div class="block">Returns the names of the additional properties of this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a collection of all the names of the properties</dd>
</dl>
</li>
</ul>
<a name="getContentProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentProperty</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getContentProperty(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the value of an additional content <code>name</code> associated to this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the value of the content or <code>null</code> if it doesn't exist or if it's not a content.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isContentProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isContentProperty</h4>
<pre>public&nbsp;boolean&nbsp;isContentProperty(java.lang.String&nbsp;name)</pre>
<div class="block">Returns <code>true</code> if the type of given additional property is a content.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategory</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a>&nbsp;getCategory()</pre>
<div class="block">Returns the category of this piece of furniture.</div>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">Returns <code>true</code> if this piece and the one in parameter are the same objects.
 Note that, from version 3.6, two pieces of furniture can have the same name.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block">Returns default hash code.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="compareTo-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compareTo</h4>
<pre>public&nbsp;int&nbsp;compareTo(<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Compares the names of this piece and the one in parameter.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>compareTo</code>&nbsp;in interface&nbsp;<code>java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&gt;</code></dd>
</dl>
</li>
</ul>
<a name="matchesFilter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>matchesFilter</h4>
<pre>public&nbsp;boolean&nbsp;matchesFilter(java.lang.String&nbsp;filter)</pre>
<div class="block">Returns <code>true</code> if this piece matches the given <code>filter</code> text.
 Each substring of the <code>filter</code> is considered as a search criterion that can match
 the name, the category name, the creator, the license, the description or the tags of this piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="getFilterCriteria--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilterCriteria</h4>
<pre>protected&nbsp;java.lang.String[]&nbsp;getFilterCriteria()</pre>
<div class="block">Returns the strings used as criteria for filtering (name, category, creator, license, description and tags).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.2</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html#matchesFilter-java.lang.String-"><code>matchesFilter(String)</code></a></dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>clone</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.8</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CatalogPieceOfFurniture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogLight.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" target="_top">Frames</a></li>
<li><a href="CatalogPieceOfFurniture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
