<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:48 CEST 2024 -->
<title>HomePane (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomePane (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePane.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/HomeFurniturePanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/HomePDFPrinter.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/HomePane.html" target="_top">Frames</a></li>
<li><a href="HomePane.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JRootPane">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JRootPane">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class HomePane" class="title">Class HomePane</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>javax.swing.JComponent</li>
<li>
<ul class="inheritance">
<li>javax.swing.JRootPane</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.HomePane</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>, java.awt.image.ImageObserver, java.awt.MenuContainer, java.io.Serializable, javax.accessibility.Accessible</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomePane</span>
extends javax.swing.JRootPane
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></pre>
<div class="block">The MVC view that edits a home.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.HomePane">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JRootPane">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JRootPane</h3>
<code>javax.swing.JRootPane.AccessibleJRootPane, javax.swing.JRootPane.RootLayout</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JComponent</h3>
<code>javax.swing.JComponent.AccessibleJComponent</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.HomeView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.SaveAnswer</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JRootPane">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JRootPane</h3>
<code>COLOR_CHOOSER_DIALOG, contentPane, defaultButton, defaultPressAction, defaultReleaseAction, ERROR_DIALOG, FILE_CHOOSER_DIALOG, FRAME, glassPane, INFORMATION_DIALOG, layeredPane, menuBar, NONE, PLAIN_DIALOG, QUESTION_DIALOG, WARNING_DIALOG</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JComponent</h3>
<code>listenerList, TOOL_TIP_TEXT_KEY, ui, UNDEFINED_CONDITION, WHEN_ANCESTOR_OF_FOCUSED_COMPONENT, WHEN_FOCUSED, WHEN_IN_FOCUSED_WINDOW</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>accessibleContext, BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.viewcontroller.HomeView">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#DISPLAY_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX">DISPLAY_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#SORT_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX">SORT_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#HomePane-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">HomePane</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
        <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
        <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;controller)</code>
<div class="block">Creates home view associated with its controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Attaches the given <code>view</code> to home view.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmDeleteCatalogSelection--">confirmDeleteCatalogSelection</a></span>()</code>
<div class="block">Displays a dialog that let user choose whether he wants to delete
 the selected furniture from catalog or not.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmExit--">confirmExit</a></span>()</code>
<div class="block">Displays a dialog that let user choose whether he wants to exit
 application or not.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmOpenDamagedHome-java.lang.String-com.eteks.sweethome3d.model.Home-java.util.List-">confirmOpenDamagedHome</a></span>(java.lang.String&nbsp;homeName,
                      <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                      java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent)</code>
<div class="block">Displays a dialog that lets user choose what he wants to do with a damaged home he tries to open it.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmReplaceFurnitureLibrary-java.lang.String-">confirmReplaceFurnitureLibrary</a></span>(java.lang.String&nbsp;furnitureLibraryName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing furniture library or not.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmReplaceLanguageLibrary-java.lang.String-">confirmReplaceLanguageLibrary</a></span>(java.lang.String&nbsp;languageLibraryName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing language library or not.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmReplacePlugin-java.lang.String-">confirmReplacePlugin</a></span>(java.lang.String&nbsp;pluginName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing plug-in or not.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmReplaceTexturesLibrary-java.lang.String-">confirmReplaceTexturesLibrary</a></span>(java.lang.String&nbsp;texturesLibraryName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing textures library or not.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.SaveAnswer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmSave-java.lang.String-">confirmSave</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to save
 the current home or not.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#confirmSaveNewerHome-java.lang.String-">confirmSaveNewerHome</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Displays a dialog that let user choose whether he wants to save
 a home that was created with a newer version of Sweet Home 3D.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Detaches the given <code>view</code> from home view.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#exportToCSV-java.lang.String-">exportToCSV</a></span>(java.lang.String&nbsp;csvFile)</code>
<div class="block">Exports furniture list to a given CSV file.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#exportToOBJ-java.lang.String-">exportToOBJ</a></span>(java.lang.String&nbsp;objFile)</code>
<div class="block">Exports the objects of the 3D view to the given OBJ file.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#exportToOBJ-java.lang.String-com.eteks.sweethome3d.viewcontroller.Object3DFactory-">exportToOBJ</a></span>(java.lang.String&nbsp;objFile,
           <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory)</code>
<div class="block">Exports to an OBJ file the objects of the 3D view created with the given factory.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#exportToSVG-java.lang.String-">exportToSVG</a></span>(java.lang.String&nbsp;svgFile)</code>
<div class="block">Exports the plan objects to a given SVG file.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#getClipboardItems--">getClipboardItems</a></span>()</code>
<div class="block">Returns the list of selectable items that are currently in clipboard
 or <code>null</code> if clipboard doesn't contain any selectable item.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#invokeLater-java.lang.Runnable-">invokeLater</a></span>(java.lang.Runnable&nbsp;runnable)</code>
<div class="block">Execute <code>runnable</code> asynchronously in the thread
 that manages toolkit events.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#isClipboardEmpty--">isClipboardEmpty</a></span>()</code>
<div class="block">Returns <code>true</code> if clipboard doesn't contain data that
 components are able to handle.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#printToPDF-java.lang.String-">printToPDF</a></span>(java.lang.String&nbsp;pdfFile)</code>
<div class="block">Prints a home to a given PDF file.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#setActionEnabled-java.lang.String-boolean-">setActionEnabled</a></span>(java.lang.String&nbsp;actionKey,
                boolean&nbsp;enabled)</code>
<div class="block">Enables or disables the action matching <code>actionKey</code>.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#setEnabled-com.eteks.sweethome3d.viewcontroller.HomeView.ActionType-boolean-">setEnabled</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>&nbsp;actionType,
          boolean&nbsp;enabled)</code>
<div class="block">Enables or disables the action matching <code>actionType</code>.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#setTransferEnabled-boolean-">setTransferEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">Enables or disables transfer between components.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#setUndoRedoName-java.lang.String-java.lang.String-">setUndoRedoName</a></span>(java.lang.String&nbsp;undoText,
               java.lang.String&nbsp;redoText)</code>
<div class="block">Sets the <code>NAME</code> and <code>SHORT_DESCRIPTION</code> properties value
 of undo and redo actions.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showAboutDialog--">showAboutDialog</a></span>()</code>
<div class="block">Displays an about dialog.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showActionTipMessage-java.lang.String-">showActionTipMessage</a></span>(java.lang.String&nbsp;actionTipKey)</code>
<div class="block">Displays the tip matching <code>actionTipKey</code> and
 returns <code>true</code> if the user chose not to display again the tip.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showDeletedCamerasDialog--">showDeletedCamerasDialog</a></span>()</code>
<div class="block">Displays a dialog showing the list of cameras stored in home
 and returns the ones selected by the user to be deleted.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showError-java.lang.String-">showError</a></span>(java.lang.String&nbsp;message)</code>
<div class="block">Displays <code>message</code> in an error message box.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showExportToCSVDialog-java.lang.String-">showExportToCSVDialog</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Shows a content chooser save dialog to export furniture list in a CSV file.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showExportToOBJDialog-java.lang.String-">showExportToOBJDialog</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Shows a content chooser save dialog to export a 3D home in a OBJ file.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showExportToSVGDialog-java.lang.String-">showExportToSVGDialog</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Shows a content chooser save dialog to export a home plan in a SVG file.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showImportFurnitureLibraryDialog--">showImportFurnitureLibraryDialog</a></span>()</code>
<div class="block">Displays a content chooser open dialog to choose a furniture library.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showImportLanguageLibraryDialog--">showImportLanguageLibraryDialog</a></span>()</code>
<div class="block">Displays a content chooser open dialog to choose a language library.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showImportTexturesLibraryDialog--">showImportTexturesLibraryDialog</a></span>()</code>
<div class="block">Displays a content chooser open dialog to choose a textures library.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showLibraryFolderInSystem-java.lang.String-">showLibraryFolderInSystem</a></span>(java.lang.String&nbsp;libraryLocation)</code>
<div class="block">Opens the folder containing the given library in a system window if possible.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showMessage-java.lang.String-">showMessage</a></span>(java.lang.String&nbsp;message)</code>
<div class="block">Displays <code>message</code> in a message box.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showNewHomeFromExampleDialog--">showNewHomeFromExampleDialog</a></span>()</code>
<div class="block">Displays a dialog to let the user choose a home example.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showOpenDialog--">showOpenDialog</a></span>()</code>
<div class="block">Displays a content chooser open dialog to choose the name of a home.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>java.util.concurrent.Callable&lt;java.lang.Void&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showPrintDialog--">showPrintDialog</a></span>()</code>
<div class="block">Shows a print dialog to print the home displayed by this pane.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showPrintToPDFDialog-java.lang.String-">showPrintToPDFDialog</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Shows a content chooser save dialog to print a home in a PDF file.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showSaveDialog-java.lang.String-">showSaveDialog</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Displays a content chooser save dialog to choose the name of a home.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showStoreCameraDialog-java.lang.String-">showStoreCameraDialog</a></span>(java.lang.String&nbsp;cameraName)</code>
<div class="block">Displays a dialog that lets the user choose a name for the current camera.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#showUpdatesMessage-java.lang.String-boolean-">showUpdatesMessage</a></span>(java.lang.String&nbsp;updatesMessage,
                  boolean&nbsp;showOnlyMessage)</code>
<div class="block">Displays the given message and returns <code>false</code> if the user
 doesn't want to be informed of the shown updates anymore.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html#updateOpenRecentHomeMenu-javax.swing.JMenu-com.eteks.sweethome3d.viewcontroller.HomeController-">updateOpenRecentHomeMenu</a></span>(javax.swing.JMenu&nbsp;openRecentHomeMenu,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;controller)</code>
<div class="block">Updates <code>openRecentHomeMenu</code> from current recent homes in preferences.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JRootPane">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JRootPane</h3>
<code>addImpl, addNotify, createContentPane, createGlassPane, createLayeredPane, createRootLayout, getAccessibleContext, getContentPane, getDefaultButton, getGlassPane, getJMenuBar, getLayeredPane, getMenuBar, getUI, getUIClassID, getWindowDecorationStyle, isOptimizedDrawingEnabled, isValidateRoot, paramString, removeNotify, setContentPane, setDefaultButton, setDoubleBuffered, setGlassPane, setJMenuBar, setLayeredPane, setMenuBar, setUI, setWindowDecorationStyle, updateUI</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JComponent</h3>
<code>addAncestorListener, addVetoableChangeListener, computeVisibleRect, contains, createToolTip, disable, enable, firePropertyChange, firePropertyChange, firePropertyChange, fireVetoableChange, getActionForKeyStroke, getActionMap, getAlignmentX, getAlignmentY, getAncestorListeners, getAutoscrolls, getBaseline, getBaselineResizeBehavior, getBorder, getBounds, getClientProperty, getComponentGraphics, getComponentPopupMenu, getConditionForKeyStroke, getDebugGraphicsOptions, getDefaultLocale, getFontMetrics, getGraphics, getHeight, getInheritsPopupMenu, getInputMap, getInputMap, getInputVerifier, getInsets, getInsets, getListeners, getLocation, getMaximumSize, getMinimumSize, getNextFocusableComponent, getPopupLocation, getPreferredSize, getRegisteredKeyStrokes, getRootPane, getSize, getToolTipLocation, getToolTipText, getToolTipText, getTopLevelAncestor, getTransferHandler, getVerifyInputWhenFocusTarget, getVetoableChangeListeners, getVisibleRect, getWidth, getX, getY, grabFocus, hide, isDoubleBuffered, isLightweightComponent, isManagingFocus, isOpaque, isPaintingForPrint, isPaintingOrigin, isPaintingTile, isRequestFocusEnabled, paint, paintBorder, paintChildren, paintComponent, paintImmediately, paintImmediately, print, printAll, printBorder, printChildren, printComponent, processComponentKeyEvent, processKeyBinding, processKeyEvent, processMouseEvent, processMouseMotionEvent, putClientProperty, registerKeyboardAction, registerKeyboardAction, removeAncestorListener, removeVetoableChangeListener, repaint, repaint, requestDefaultFocus, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, resetKeyboardActions, reshape, revalidate, scrollRectToVisible, setActionMap, setAlignmentX, setAlignmentY, setAutoscrolls, setBackground, setBorder, setComponentPopupMenu, setDebugGraphicsOptions, setDefaultLocale, setEnabled, setFocusTraversalKeys, setFont, setForeground, setInheritsPopupMenu, setInputMap, setInputVerifier, setMaximumSize, setMinimumSize, setNextFocusableComponent, setOpaque, setPreferredSize, setRequestFocusEnabled, setToolTipText, setTransferHandler, setUI, setVerifyInputWhenFocusTarget, setVisible, unregisterKeyboardAction, update</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, doLayout, findComponentAt, findComponentAt, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getLayout, getMousePosition, insets, invalidate, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paintComponents, preferredSize, printComponents, processContainerEvent, processEvent, remove, remove, removeAll, removeContainerListener, setComponentZOrder, setFocusCycleRoot, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setLayout, transferFocusDownCycle, validate, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addFocusListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseListener, addMouseMotionListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, createImage, createImage, createVolatileImage, createVolatileImage, disableEvents, dispatchEvent, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getBackground, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getCursor, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getForeground, getGraphicsConfiguration, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocale, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getToolkit, getTreeLock, gotFocus, handleEvent, hasFocus, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseListener, removeMouseMotionListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, resize, resize, setBounds, setBounds, setComponentOrientation, setCursor, setDropTarget, setFocusable, setFocusTraversalKeysEnabled, setIgnoreRepaint, setLocale, setLocation, setLocation, setName, setSize, setSize, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomePane-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.HomeController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomePane</h4>
<pre>public&nbsp;HomePane(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;controller)</pre>
<div class="block">Creates home view associated with its controller.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="updateOpenRecentHomeMenu-javax.swing.JMenu-com.eteks.sweethome3d.viewcontroller.HomeController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateOpenRecentHomeMenu</h4>
<pre>protected&nbsp;void&nbsp;updateOpenRecentHomeMenu(javax.swing.JMenu&nbsp;openRecentHomeMenu,
                                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;controller)</pre>
<div class="block">Updates <code>openRecentHomeMenu</code> from current recent homes in preferences.</div>
</li>
</ul>
<a name="setEnabled-com.eteks.sweethome3d.viewcontroller.HomeView.ActionType-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnabled</h4>
<pre>public&nbsp;void&nbsp;setEnabled(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>&nbsp;actionType,
                       boolean&nbsp;enabled)</pre>
<div class="block">Enables or disables the action matching <code>actionType</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#setEnabled-com.eteks.sweethome3d.viewcontroller.HomeView.ActionType-boolean-">setEnabled</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="setActionEnabled-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActionEnabled</h4>
<pre>public&nbsp;void&nbsp;setActionEnabled(java.lang.String&nbsp;actionKey,
                             boolean&nbsp;enabled)</pre>
<div class="block">Enables or disables the action matching <code>actionKey</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#setActionEnabled-java.lang.String-boolean-">setActionEnabled</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="setUndoRedoName-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUndoRedoName</h4>
<pre>public&nbsp;void&nbsp;setUndoRedoName(java.lang.String&nbsp;undoText,
                            java.lang.String&nbsp;redoText)</pre>
<div class="block">Sets the <code>NAME</code> and <code>SHORT_DESCRIPTION</code> properties value
 of undo and redo actions. If a parameter is null,
 the properties will be reset to their initial values.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#setUndoRedoName-java.lang.String-java.lang.String-">setUndoRedoName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="setTransferEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransferEnabled</h4>
<pre>public&nbsp;void&nbsp;setTransferEnabled(boolean&nbsp;enabled)</pre>
<div class="block">Enables or disables transfer between components.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#setTransferEnabled-boolean-">setTransferEnabled</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="detachView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detachView</h4>
<pre>public&nbsp;void&nbsp;detachView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</pre>
<div class="block">Detaches the given <code>view</code> from home view.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="attachView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>attachView</h4>
<pre>public&nbsp;void&nbsp;attachView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</pre>
<div class="block">Attaches the given <code>view</code> to home view.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showOpenDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showOpenDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showOpenDialog()</pre>
<div class="block">Displays a content chooser open dialog to choose the name of a home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showOpenDialog--">showOpenDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showNewHomeFromExampleDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showNewHomeFromExampleDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showNewHomeFromExampleDialog()</pre>
<div class="block">Displays a dialog to let the user choose a home example.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showNewHomeFromExampleDialog--">showNewHomeFromExampleDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="confirmOpenDamagedHome-java.lang.String-com.eteks.sweethome3d.model.Home-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmOpenDamagedHome</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a>&nbsp;confirmOpenDamagedHome(java.lang.String&nbsp;homeName,
                                                             <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                                                             java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent)</pre>
<div class="block">Displays a dialog that lets user choose what he wants to do with a damaged home he tries to open it.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmOpenDamagedHome-java.lang.String-com.eteks.sweethome3d.model.Home-java.util.List-">confirmOpenDamagedHome</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html#REMOVE_DAMAGED_ITEMS"><code>HomeView.OpenDamagedHomeAnswer.REMOVE_DAMAGED_ITEMS</code></a>
 if the user chose to remove damaged items,
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html#REPLACE_DAMAGED_ITEMS"><code>HomeView.OpenDamagedHomeAnswer.REPLACE_DAMAGED_ITEMS</code></a>
 if he doesn't want to replace damaged items by red images and red boxes,
 or <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html#DO_NOT_OPEN_HOME"><code>HomeView.OpenDamagedHomeAnswer.DO_NOT_OPEN_HOME</code></a>
 if he doesn't want to open damaged home.</dd>
</dl>
</li>
</ul>
<a name="showImportLanguageLibraryDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImportLanguageLibraryDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showImportLanguageLibraryDialog()</pre>
<div class="block">Displays a content chooser open dialog to choose a language library.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showImportLanguageLibraryDialog--">showImportLanguageLibraryDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="confirmReplaceLanguageLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReplaceLanguageLibrary</h4>
<pre>public&nbsp;boolean&nbsp;confirmReplaceLanguageLibrary(java.lang.String&nbsp;languageLibraryName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing language library or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmReplaceLanguageLibrary-java.lang.String-">confirmReplaceLanguageLibrary</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showImportFurnitureLibraryDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImportFurnitureLibraryDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showImportFurnitureLibraryDialog()</pre>
<div class="block">Displays a content chooser open dialog to choose a furniture library.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showImportFurnitureLibraryDialog--">showImportFurnitureLibraryDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="confirmReplaceFurnitureLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReplaceFurnitureLibrary</h4>
<pre>public&nbsp;boolean&nbsp;confirmReplaceFurnitureLibrary(java.lang.String&nbsp;furnitureLibraryName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing furniture library or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmReplaceFurnitureLibrary-java.lang.String-">confirmReplaceFurnitureLibrary</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showImportTexturesLibraryDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImportTexturesLibraryDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showImportTexturesLibraryDialog()</pre>
<div class="block">Displays a content chooser open dialog to choose a textures library.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showImportTexturesLibraryDialog--">showImportTexturesLibraryDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="confirmReplaceTexturesLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReplaceTexturesLibrary</h4>
<pre>public&nbsp;boolean&nbsp;confirmReplaceTexturesLibrary(java.lang.String&nbsp;texturesLibraryName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing textures library or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmReplaceTexturesLibrary-java.lang.String-">confirmReplaceTexturesLibrary</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="confirmReplacePlugin-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReplacePlugin</h4>
<pre>public&nbsp;boolean&nbsp;confirmReplacePlugin(java.lang.String&nbsp;pluginName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing plug-in or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmReplacePlugin-java.lang.String-">confirmReplacePlugin</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showSaveDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showSaveDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showSaveDialog(java.lang.String&nbsp;homeName)</pre>
<div class="block">Displays a content chooser save dialog to choose the name of a home.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showSaveDialog-java.lang.String-">showSaveDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showError-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showError</h4>
<pre>public&nbsp;void&nbsp;showError(java.lang.String&nbsp;message)</pre>
<div class="block">Displays <code>message</code> in an error message box.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showError-java.lang.String-">showError</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showMessage</h4>
<pre>public&nbsp;void&nbsp;showMessage(java.lang.String&nbsp;message)</pre>
<div class="block">Displays <code>message</code> in a message box.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showMessage-java.lang.String-">showMessage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showActionTipMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showActionTipMessage</h4>
<pre>public&nbsp;boolean&nbsp;showActionTipMessage(java.lang.String&nbsp;actionTipKey)</pre>
<div class="block">Displays the tip matching <code>actionTipKey</code> and
 returns <code>true</code> if the user chose not to display again the tip.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showActionTipMessage-java.lang.String-">showActionTipMessage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="confirmSave-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmSave</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.SaveAnswer</a>&nbsp;confirmSave(java.lang.String&nbsp;homeName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to save
 the current home or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmSave-java.lang.String-">confirmSave</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html#SAVE"><code>HomeView.SaveAnswer.SAVE</code></a>
 if the user chose to save home,
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html#DO_NOT_SAVE"><code>HomeView.SaveAnswer.DO_NOT_SAVE</code></a>
 if he doesn't want to save home,
 or <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html#CANCEL"><code>HomeView.SaveAnswer.CANCEL</code></a>
 if he doesn't want to continue current operation.</dd>
</dl>
</li>
</ul>
<a name="confirmSaveNewerHome-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmSaveNewerHome</h4>
<pre>public&nbsp;boolean&nbsp;confirmSaveNewerHome(java.lang.String&nbsp;homeName)</pre>
<div class="block">Displays a dialog that let user choose whether he wants to save
 a home that was created with a newer version of Sweet Home 3D.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmSaveNewerHome-java.lang.String-">confirmSaveNewerHome</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if user confirmed to save.</dd>
</dl>
</li>
</ul>
<a name="confirmExit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmExit</h4>
<pre>public&nbsp;boolean&nbsp;confirmExit()</pre>
<div class="block">Displays a dialog that let user choose whether he wants to exit
 application or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmExit--">confirmExit</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if user confirmed to exit.</dd>
</dl>
</li>
</ul>
<a name="showAboutDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showAboutDialog</h4>
<pre>public&nbsp;void&nbsp;showAboutDialog()</pre>
<div class="block">Displays an about dialog.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showAboutDialog--">showAboutDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="showLibraryFolderInSystem-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showLibraryFolderInSystem</h4>
<pre>protected&nbsp;void&nbsp;showLibraryFolderInSystem(java.lang.String&nbsp;libraryLocation)</pre>
<div class="block">Opens the folder containing the given library in a system window if possible.</div>
</li>
</ul>
<a name="showUpdatesMessage-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showUpdatesMessage</h4>
<pre>public&nbsp;boolean&nbsp;showUpdatesMessage(java.lang.String&nbsp;updatesMessage,
                                  boolean&nbsp;showOnlyMessage)</pre>
<div class="block">Displays the given message and returns <code>false</code> if the user
 doesn't want to be informed of the shown updates anymore.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showUpdatesMessage-java.lang.String-boolean-">showUpdatesMessage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>updatesMessage</code> - the message to display</dd>
<dd><code>showOnlyMessage</code> - if <code>false</code> a check box proposing not to display
                    again shown updates will be shown.</dd>
</dl>
</li>
</ul>
<a name="showPrintDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showPrintDialog</h4>
<pre>public&nbsp;java.util.concurrent.Callable&lt;java.lang.Void&gt;&nbsp;showPrintDialog()</pre>
<div class="block">Shows a print dialog to print the home displayed by this pane.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showPrintDialog--">showPrintDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a print task to execute or <code>null</code> if the user canceled print.
    The <code>call</code> method of the returned task may throw a
    <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model"><code>RecorderException</code></a> exception if print failed
    or an <a href="../../../../com/eteks/sweethome3d/model/InterruptedRecorderException.html" title="class in com.eteks.sweethome3d.model"><code>InterruptedRecorderException</code></a>
    exception if it was interrupted.</dd>
</dl>
</li>
</ul>
<a name="showPrintToPDFDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showPrintToPDFDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showPrintToPDFDialog(java.lang.String&nbsp;homeName)</pre>
<div class="block">Shows a content chooser save dialog to print a home in a PDF file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showPrintToPDFDialog-java.lang.String-">showPrintToPDFDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="printToPDF-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>printToPDF</h4>
<pre>public&nbsp;void&nbsp;printToPDF(java.lang.String&nbsp;pdfFile)
                throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Prints a home to a given PDF file. This method may be overridden
 to write to another kind of output stream.
 Caution !!! This method may be called from an other thread than EDT.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#printToPDF-java.lang.String-">printToPDF</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="showExportToCSVDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportToCSVDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showExportToCSVDialog(java.lang.String&nbsp;homeName)</pre>
<div class="block">Shows a content chooser save dialog to export furniture list in a CSV file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showExportToCSVDialog-java.lang.String-">showExportToCSVDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="exportToCSV-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToCSV</h4>
<pre>public&nbsp;void&nbsp;exportToCSV(java.lang.String&nbsp;csvFile)
                 throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Exports furniture list to a given CSV file.
 Caution !!! This method may be called from an other thread than EDT.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToCSV-java.lang.String-">exportToCSV</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="showExportToSVGDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportToSVGDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showExportToSVGDialog(java.lang.String&nbsp;homeName)</pre>
<div class="block">Shows a content chooser save dialog to export a home plan in a SVG file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showExportToSVGDialog-java.lang.String-">showExportToSVGDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="exportToSVG-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToSVG</h4>
<pre>public&nbsp;void&nbsp;exportToSVG(java.lang.String&nbsp;svgFile)
                 throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Exports the plan objects to a given SVG file.
 Caution !!! This method may be called from an other thread than EDT.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToSVG-java.lang.String-">exportToSVG</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="showExportToOBJDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportToOBJDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showExportToOBJDialog(java.lang.String&nbsp;homeName)</pre>
<div class="block">Shows a content chooser save dialog to export a 3D home in a OBJ file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showExportToOBJDialog-java.lang.String-">showExportToOBJDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="exportToOBJ-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToOBJ</h4>
<pre>public&nbsp;void&nbsp;exportToOBJ(java.lang.String&nbsp;objFile)
                 throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Exports the objects of the 3D view to the given OBJ file.
 Caution !!! This method may be called from an other thread than EDT.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToOBJ-java.lang.String-">exportToOBJ</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="exportToOBJ-java.lang.String-com.eteks.sweethome3d.viewcontroller.Object3DFactory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToOBJ</h4>
<pre>protected&nbsp;void&nbsp;exportToOBJ(java.lang.String&nbsp;objFile,
                           <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory)
                    throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Exports to an OBJ file the objects of the 3D view created with the given factory.
 Caution !!! This method may be called from an other thread than EDT.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="confirmDeleteCatalogSelection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmDeleteCatalogSelection</h4>
<pre>public&nbsp;boolean&nbsp;confirmDeleteCatalogSelection()</pre>
<div class="block">Displays a dialog that let user choose whether he wants to delete
 the selected furniture from catalog or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmDeleteCatalogSelection--">confirmDeleteCatalogSelection</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if user confirmed to delete.</dd>
</dl>
</li>
</ul>
<a name="showStoreCameraDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showStoreCameraDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showStoreCameraDialog(java.lang.String&nbsp;cameraName)</pre>
<div class="block">Displays a dialog that lets the user choose a name for the current camera.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showStoreCameraDialog-java.lang.String-">showStoreCameraDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the chosen name or <code>null</code> if the user canceled.</dd>
</dl>
</li>
</ul>
<a name="showDeletedCamerasDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showDeletedCamerasDialog</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;showDeletedCamerasDialog()</pre>
<div class="block">Displays a dialog showing the list of cameras stored in home
 and returns the ones selected by the user to be deleted.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showDeletedCamerasDialog--">showDeletedCamerasDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="isClipboardEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isClipboardEmpty</h4>
<pre>public&nbsp;boolean&nbsp;isClipboardEmpty()</pre>
<div class="block">Returns <code>true</code> if clipboard doesn't contain data that
 components are able to handle.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#isClipboardEmpty--">isClipboardEmpty</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="getClipboardItems--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClipboardItems</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;getClipboardItems()</pre>
<div class="block">Returns the list of selectable items that are currently in clipboard
 or <code>null</code> if clipboard doesn't contain any selectable item.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#getClipboardItems--">getClipboardItems</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
<a name="invokeLater-java.lang.Runnable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>invokeLater</h4>
<pre>public&nbsp;void&nbsp;invokeLater(java.lang.Runnable&nbsp;runnable)</pre>
<div class="block">Execute <code>runnable</code> asynchronously in the thread
 that manages toolkit events.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#invokeLater-java.lang.Runnable-">invokeLater</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePane.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/HomeFurniturePanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/HomePDFPrinter.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/HomePane.html" target="_top">Frames</a></li>
<li><a href="HomePane.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JRootPane">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JRootPane">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
