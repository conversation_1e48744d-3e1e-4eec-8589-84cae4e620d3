<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>HomeXMLHandler (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeXMLHandler (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeXMLHandler.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/HomeXMLHandler.html" target="_top">Frames</a></li>
<li><a href="HomeXMLHandler.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class HomeXMLHandler" class="title">Class HomeXMLHandler</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.xml.sax.helpers.DefaultHandler</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.HomeXMLHandler</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>org.xml.sax.ContentHandler, org.xml.sax.DTDHandler, org.xml.sax.EntityResolver, org.xml.sax.ErrorHandler</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeXMLHandler</span>
extends org.xml.sax.helpers.DefaultHandler</pre>
<div class="block">SAX handler for Sweet Home 3D XML stream. Read home should respect the following DTD:<pre>
 &lt;!ELEMENT home (property*, furnitureVisibleProperty*, environment?, backgroundImage?, print?, compass?, (camera | observerCamera)*, level*,
       (pieceOfFurniture | doorOrWindow | furnitureGroup | light)*, wall*, room*, polyline*, dimensionLine*, label*)>
 &lt;!ATTLIST home
       version CDATA #IMPLIED
       name CDATA #IMPLIED
       camera (observerCamera | topCamera) "topCamera"
       selectedLevel CDATA #IMPLIED
       wallHeight CDATA #IMPLIED
       basePlanLocked (false | true) "false"
       furnitureSortedProperty CDATA #IMPLIED
       furnitureDescendingSorted (false | true) "false">

 &lt;!ELEMENT property EMPTY>
 &lt;!ATTLIST property
       name CDATA #REQUIRED
       value CDATA #REQUIRED
       type (STRING|CONTENT) "STRING">

 &lt;!ELEMENT furnitureVisibleProperty EMPTY>
 &lt;!ATTLIST furnitureVisibleProperty name CDATA #REQUIRED>

 &lt;!ELEMENT environment (property*, (camera | observerCamera)*, texture?, texture?) >
 &lt;!ATTLIST environment
       groundColor CDATA #IMPLIED
       backgroundImageVisibleOnGround3D (false | true) "false"
       skyColor CDATA #IMPLIED
       lightColor CDATA #IMPLIED
       wallsAlpha CDATA "0"
       allLevelsVisible (false | true) "false"
       observerCameraElevationAdjusted (false | true) "true"
       ceillingLightColor CDATA #IMPLIED
       drawingMode (FILL | OUTLINE | FILL_AND_OUTLINE) "FILL"
       subpartSizeUnderLight CDATA "0"
       photoWidth CDATA "400"
       photoHeight CDATA "300"
       photoAspectRatio (FREE_RATIO | VIEW_3D_RATIO | RATIO_4_3 | RATIO_3_2 | RATIO_16_9 | RATIO_2_1 | RATIO_24_10 | SQUARE_RATIO) "VIEW_3D_RATIO"
       photoQuality CDATA "0"
       videoWidth CDATA "320"
       videoAspectRatio (RATIO_4_3 | RATIO_16_9 | RATIO_24_10) "RATIO_4_3"
       videoQuality CDATA "0"
       videoSpeed CDATA #IMPLIED
       videoFrameRate CDATA "25">

 &lt;!ELEMENT backgroundImage EMPTY>
 &lt;!ATTLIST backgroundImage
       image CDATA #REQUIRED
       scaleDistance CDATA #REQUIRED
       scaleDistanceXStart CDATA #REQUIRED
       scaleDistanceYStart CDATA #REQUIRED
       scaleDistanceXEnd CDATA #REQUIRED
       scaleDistanceYEnd CDATA #REQUIRED
       xOrigin CDATA "0"
       yOrigin CDATA "0"
       visible (false | true) "true">

 &lt;!ELEMENT print (printedLevel*)>
 &lt;!ATTLIST print
       headerFormat CDATA #IMPLIED
       footerFormat CDATA #IMPLIED
       planScale CDATA #IMPLIED
       furniturePrinted (false | true) "true"
       planPrinted (false | true) "true"
       view3DPrinted (false | true) "true"
       paperWidth CDATA #REQUIRED
       paperHeight CDATA #REQUIRED
       paperTopMargin CDATA #REQUIRED
       paperLeftMargin CDATA #REQUIRED
       paperBottomMargin CDATA #REQUIRED
       paperRightMargin CDATA #REQUIRED
       paperOrientation (PORTRAIT | LANDSCAPE | REVERSE_LANDSCAPE) #REQUIRED>

 &lt;!ELEMENT printedLevel EMPTY>
 &lt;!ATTLIST printedLevel level ID #REQUIRED>

 &lt;!ELEMENT compass (property*)>
 &lt;!ATTLIST compass
       x CDATA #REQUIRED
       y CDATA #REQUIRED
       diameter CDATA #REQUIRED
       northDirection CDATA "0"
       longitude CDATA #IMPLIED
       latitude CDATA #IMPLIED
       timeZone CDATA #IMPLIED
       visible (false | true) "true">

 &lt;!ENTITY % cameraCommonAttributes
      'id ID #IMPLIED
       name CDATA #IMPLIED
       lens (PINHOLE | NORMAL | FISHEYE | SPHERICAL) "PINHOLE"
       x CDATA #REQUIRED
       y CDATA #REQUIRED
       z CDATA #REQUIRED
       yaw CDATA #REQUIRED
       pitch CDATA #REQUIRED
       time CDATA #IMPLIED
       fieldOfView CDATA #REQUIRED
       renderer CADATA #IMPLIED'>

 &lt;!ELEMENT camera (property*)>
 &lt;!ATTLIST camera
       %cameraCommonAttributes;
       attribute (topCamera | storedCamera | cameraPath) #REQUIRED>

 &lt;!ELEMENT observerCamera (property*)>
 &lt;!ATTLIST observerCamera
       %cameraCommonAttributes;
       attribute (observerCamera | storedCamera | cameraPath) #REQUIRED
       fixedSize (false | true) "false">

 &lt;!ELEMENT level (property*, backgroundImage?)>
 &lt;!ATTLIST level
       id ID #REQUIRED
       name CDATA #REQUIRED
       elevation CDATA #REQUIRED
       floorThickness CDATA #REQUIRED
       height CDATA #REQUIRED
       elevationIndex CDATA "-1"
       visible (false | true) "true"
       viewable (false | true) "true">

 &lt;!ENTITY % furnitureCommonAttributes
      'id ID #IMPLIED
       name CDATA #REQUIRED
       angle CDATA "0"
       visible (false | true) "true"
       movable (false | true) "true"
       description CDATA #IMPLIED
       information CDATA #IMPLIED
       license CDATA #IMPLIED
       creator CDATA #IMPLIED
       modelMirrored (false | true) "false"
       nameVisible (false | true) "false"
       nameAngle CDATA "0"
       nameXOffset CDATA "0"
       nameYOffset CDATA "0"
       price CDATA #IMPLIED'>

 &lt;!ELEMENT furnitureGroup ((pieceOfFurniture | doorOrWindow | furnitureGroup | light)*, property*, textStyle?)>
 &lt;!ATTLIST furnitureGroup
       %furnitureCommonAttributes;
       level IDREF #IMPLIED
       x CDATA #IMPLIED
       y CDATA #IMPLIED
       elevation CDATA #IMPLIED
       width CDATA #IMPLIED
       depth CDATA #IMPLIED
       height CDATA #IMPLIED
       dropOnTopElevation CDATA #IMPLIED>

 &lt;!ENTITY % pieceOfFurnitureCommonAttributes
      'level IDREF #IMPLIED
       catalogId CDATA #IMPLIED
       x CDATA #REQUIRED
       y CDATA #REQUIRED
       elevation CDATA "0"
       width CDATA #REQUIRED
       depth CDATA #REQUIRED
       height CDATA #REQUIRED
       dropOnTopElevation CDATA "1"
       model CDATA #IMPLIED
       icon CDATA #IMPLIED
       planIcon CDATA #IMPLIED
       modelRotation CDATA "1 0 0 0 1 0 0 0 1"
       modelCenteredAtOrigin CDATA #IMPLIED
       backFaceShown (false | true) "false"
       modelFlags CDATA #IMPLIED
       modelSize CDATA #IMPLIED
       doorOrWindow (false | true) "false"
       resizable (false | true) "true"
       deformable (false | true) "true"
       texturable (false | true) "true"
       staircaseCutOutShape CDATA #IMPLIED
       color CDATA #IMPLIED
       shininess CDATA #IMPLIED
       valueAddedTaxPercentage CDATA #IMPLIED
       currency CDATA #IMPLIED'>

 &lt;!ENTITY % pieceOfFurnitureHorizontalRotationAttributes
      'horizontallyRotatable (false | true) "true"
       pitch CDATA "0"
       roll CDATA "0"
       widthInPlan CDATA #IMPLIED
       depthInPlan CDATA #IMPLIED
       heightInPlan CDATA #IMPLIED'>

 &lt;!ELEMENT pieceOfFurniture (property*, textStyle?, texture?, material*, transformation*)>
 &lt;!ATTLIST pieceOfFurniture
       %furnitureCommonAttributes;
       %pieceOfFurnitureCommonAttributes;
       %pieceOfFurnitureHorizontalRotationAttributes;>

 &lt;!ELEMENT doorOrWindow (sash*, property*, textStyle?, texture?, material*, transformation*)>
 &lt;!ATTLIST doorOrWindow
       %furnitureCommonAttributes;
       %pieceOfFurnitureCommonAttributes;
       wallThickness CDATA "1"
       wallDistance CDATA "0"
       wallWidth CDATA "1"
       wallLeft CDATA "0"
       wallHeight CDATA "1"
       wallTop CDATA "0"
       wallCutOutOnBothSides (false | true) "false"
       widthDepthDeformable (false | true) "true"
       cutOutShape CDATA #IMPLIED
       boundToWall (false | true) "true">

 &lt;!ELEMENT sash EMPTY>
 &lt;!ATTLIST sash
       xAxis CDATA #REQUIRED
       yAxis CDATA #REQUIRED
       width CDATA #REQUIRED
       startAngle CDATA #REQUIRED
       endAngle CDATA #REQUIRED>

 &lt;!ELEMENT light (lightSource*, lightSourceMaterial*, property*, textStyle?, texture?, material*, transformation*)>
 &lt;!ATTLIST light
       %furnitureCommonAttributes;
       %pieceOfFurnitureCommonAttributes;
       %pieceOfFurnitureHorizontalRotationAttributes;
       power CDATA "0.5">

 &lt;!ELEMENT lightSource EMPTY>
 &lt;!ATTLIST lightSource
       x CDATA #REQUIRED
       y CDATA #REQUIRED
       z CDATA #REQUIRED
       color CDATA #REQUIRED
       diameter CDATA #IMPLIED>

 &lt;!ELEMENT lightSourceMaterial EMPTY>
 &lt;!ATTLIST lightSourceMaterial
       name #REQUIRED>

 &lt;!ELEMENT shelfUnit (shelf*, property*, textStyle?, texture?, material*, transformation*)>
 &lt;!ATTLIST shelfUnit
       %furnitureCommonAttributes;
       %pieceOfFurnitureCommonAttributes;
       %pieceOfFurnitureHorizontalRotationAttributes;>

 &lt;!ELEMENT shelf EMPTY>
 &lt;!ATTLIST shelf
       elevation CDATA #IMPLIED
       xLower CDATA #IMPLIED
       yLower CDATA #IMPLIED
       zLower CDATA #IMPLIED
       xUpper CDATA #IMPLIED
       yUpper CDATA #IMPLIED
       zUpper CDATA #IMPLIED>

 &lt;!ELEMENT textStyle EMPTY>
 &lt;!ATTLIST textStyle
       attribute (nameStyle | areaStyle | lengthStyle) #IMPLIED
       fontName CDATA #IMPLIED
       fontSize CDATA #REQUIRED
       bold (false | true) "false"
       italic (false | true) "false"
       alignment (LEFT | CENTER | RIGHT) "CENTER">

 &lt;!ELEMENT texture EMPTY>
 &lt;!ATTLIST texture
       attribute (groundTexture | skyTexture | leftSideTexture | rightSideTexture | floorTexture | ceilingTexture) #IMPLIED
       catalogId CDATA #IMPLIED
       name CDATA #REQUIRED
       width CDATA #REQUIRED
       height CDATA #REQUIRED
       xOffset CDATA "0"
       yOffset CDATA "0"
       angle CDATA "0"
       scale CDATA "1"
       creator CDATA #IMPLIED
       fittingArea (false | true) "false"
       leftToRightOriented (true | false) "true"
       image CDATA #REQUIRED>

 &lt;!ELEMENT material (texture?)>
 &lt;!ATTLIST material
       name CDATA #REQUIRED
       key CDATA #IMPLIED
       color CDATA #IMPLIED
       shininess CDATA #IMPLIED>

 &lt;!ELEMENT transformation EMPTY>
 &lt;!ATTLIST transformation
       name CDATA #REQUIRED
       matrix CDATA #REQUIRED>

 &lt;!ELEMENT wall (property*, texture?, texture?, baseboard?, baseboard?)>
 &lt;!ATTLIST wall
       id ID #REQUIRED
       level IDREF #IMPLIED
       wallAtStart IDREF #IMPLIED
       wallAtEnd IDREF #IMPLIED
       xStart CDATA #REQUIRED
       yStart CDATA #REQUIRED
       xEnd CDATA #REQUIRED
       yEnd CDATA #REQUIRED
       height CDATA #IMPLIED
       heightAtEnd CDATA #IMPLIED
       thickness CDATA #REQUIRED
       arcExtent CDATA #IMPLIED
       pattern CDATA #IMPLIED
       topColor CDATA #IMPLIED
       leftSideColor CDATA #IMPLIED
       leftSideShininess CDATA "0"
       rightSideColor CDATA #IMPLIED
       rightSideShininess CDATA "0">

 &lt;!ELEMENT baseboard (texture?)>
 &lt;!ATTLIST baseboard
       attribute (leftSideBaseboard | rightSideBaseboard) #REQUIRED
       thickness CDATA #REQUIRED
       height CDATA #REQUIRED
       color CDATA #IMPLIED>

 &lt;!ELEMENT room (property*, textStyle?, textStyle?, texture?, texture?, point+)>
 &lt;!ATTLIST room
       id ID #IMPLIED
       level IDREF #IMPLIED
       name CDATA #IMPLIED
       nameAngle CDATA "0"
       nameXOffset CDATA "0"
       nameYOffset CDATA "-40"
       areaVisible (false | true) "false"
       areaAngle CDATA "0"
       areaXOffset CDATA "0"
       areaYOffset CDATA "0"
       floorVisible (false | true) "true"
       floorColor CDATA #IMPLIED
       floorShininess CDATA "0"
       ceilingVisible (false | true) "true"
       ceilingColor CDATA #IMPLIED
       ceilingShininess CDATA "0"
       ceilingFlat (false | true) "false">

 &lt;!ELEMENT point EMPTY>
 &lt;!ATTLIST point
       x CDATA #REQUIRED
       y CDATA #REQUIRED>

 &lt;!ELEMENT polyline (property*, point+)>
 &lt;!ATTLIST polyline
       id ID #IMPLIED
       level IDREF #IMPLIED
       thickness CDATA "1"
       capStyle (BUTT | SQUARE | ROUND) "BUTT"
       joinStyle (BEVEL | MITER | ROUND | CURVED) "MITER"
       dashStyle (SOLID | DOT | DASH | DASH_DOT | DASH_DOT_DOT | CUSTOMIZED) "SOLID"
       dashPattern CDATA #IMPLIED
       dashOffset CDATA "0"
       startArrowStyle (NONE | DELTA | OPEN | DISC) "NONE"
       endArrowStyle (NONE | DELTA | OPEN | DISC) "NONE"
       elevation CDATA #IMPLIED
       color CDATA #IMPLIED
       closedPath (false | true) "false">

 &lt;!ELEMENT dimensionLine (property*, textStyle?)>
 &lt;!ATTLIST dimensionLine
       id ID #IMPLIED
       level IDREF #IMPLIED
       xStart CDATA #REQUIRED
       yStart CDATA #REQUIRED
       elevationStart CDATA "0"
       xEnd CDATA #REQUIRED
       yEnd CDATA #REQUIRED
       elevationEnd CDATA "0"
       offset CDATA #REQUIRED
       endMarkSize CDATA "10";
       angle CDATA "0"
       color CDATA #IMPLIED
       visibleIn3D (false | true) "false">

 &lt;!ELEMENT label (property*, textStyle?, text)>
 &lt;!ATTLIST label
       id ID #IMPLIED
       level IDREF #IMPLIED
       x CDATA #REQUIRED
       y CDATA #REQUIRED
       angle CDATA "0"
       elevation CDATA "0"
       pitch CDATA #IMPLIED
       color CDATA #IMPLIED
       outlineColor CDATA #IMPLIED>

 &lt;!ELEMENT text (#PCDATA)>
 </pre>
 with <code>home</code> as root element.
 Attributes named <code>attribute</code> indicate the names of the object fields
 where some elements should be stored.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#HomeXMLHandler--">HomeXMLHandler</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-">HomeXMLHandler</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#characters-char:A-int-int-">characters</a></span>(char[]&nbsp;ch,
          int&nbsp;start,
          int&nbsp;length)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#createPrint-java.util.Map-">createPrint</a></span>(java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Returns a new <a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model"><code>HomePrint</code></a> instance initialized from the given <code>attributes</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endDocument--">endDocument</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-">endElement</a></span>(java.lang.String&nbsp;uri,
          java.lang.String&nbsp;localName,
          java.lang.String&nbsp;name)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#getHome--">getHome</a></span>()</code>
<div class="block">Returns the home read by this handler.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#parseContent-java.lang.String-java.util.Map-java.lang.String-">parseContent</a></span>(java.lang.String&nbsp;elementName,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes,
            java.lang.String&nbsp;attributeName)</code>
<div class="block">Returns the content object matching the attribute named <code>attributeName</code> in the given element.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#resolveObject-java.lang.Object-java.lang.String-java.util.Map-">resolveObject</a></span>(java.lang.Object&nbsp;elementObject,
             java.lang.String&nbsp;elementName,
             java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Returns the object that will be stored in a home.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setCameraAttributes-com.eteks.sweethome3d.model.Camera-java.lang.String-java.util.Map-">setCameraAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
                   java.lang.String&nbsp;elementName,
                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>camera</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setCompassAttributes-com.eteks.sweethome3d.model.Compass-java.lang.String-java.util.Map-">setCompassAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a>&nbsp;compass,
                    java.lang.String&nbsp;elementName,
                    java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>compass</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setDimensionLineAttributes-com.eteks.sweethome3d.model.DimensionLine-java.lang.String-java.util.Map-">setDimensionLineAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine,
                          java.lang.String&nbsp;elementName,
                          java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given dimension line.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setHome-com.eteks.sweethome3d.model.Home-">setHome</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Sets the home that will be updated by this handler.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setHomeAttributes-com.eteks.sweethome3d.model.Home-java.lang.String-java.util.Map-">setHomeAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                 java.lang.String&nbsp;elementName,
                 java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>home</code>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setLabelAttributes-com.eteks.sweethome3d.model.Label-java.lang.String-java.util.Map-">setLabelAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label,
                  java.lang.String&nbsp;elementName,
                  java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>label</code>.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setLevelAttributes-com.eteks.sweethome3d.model.Level-java.lang.String-java.util.Map-">setLevelAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level,
                  java.lang.String&nbsp;elementName,
                  java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>level</code>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setPieceOfFurnitureAttributes-com.eteks.sweethome3d.model.HomePieceOfFurniture-java.lang.String-java.util.Map-">setPieceOfFurnitureAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                             java.lang.String&nbsp;elementName,
                             java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>piece</code>.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setPolylineAttributes-com.eteks.sweethome3d.model.Polyline-java.lang.String-java.util.Map-">setPolylineAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline,
                     java.lang.String&nbsp;elementName,
                     java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>polyline</code>.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setRoomAttributes-com.eteks.sweethome3d.model.Room-java.lang.String-java.util.Map-">setRoomAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
                 java.lang.String&nbsp;elementName,
                 java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>room</code>.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#setWallAttributes-com.eteks.sweethome3d.model.Wall-java.lang.String-java.util.Map-">setWallAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall,
                 java.lang.String&nbsp;elementName,
                 java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</code>
<div class="block">Sets the attributes of the given <code>wall</code>.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#startDocument--">startDocument</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#startElement-java.lang.String-java.lang.String-java.lang.String-org.xml.sax.Attributes-">startElement</a></span>(java.lang.String&nbsp;uri,
            java.lang.String&nbsp;localName,
            java.lang.String&nbsp;name,
            org.xml.sax.Attributes&nbsp;attributes)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.xml.sax.helpers.DefaultHandler">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.xml.sax.helpers.DefaultHandler</h3>
<code>endPrefixMapping, error, fatalError, ignorableWhitespace, notationDecl, processingInstruction, resolveEntity, setDocumentLocator, skippedEntity, startPrefixMapping, unparsedEntityDecl, warning</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeXMLHandler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeXMLHandler</h4>
<pre>public&nbsp;HomeXMLHandler()</pre>
</li>
</ul>
<a name="HomeXMLHandler-com.eteks.sweethome3d.model.UserPreferences-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeXMLHandler</h4>
<pre>public&nbsp;HomeXMLHandler(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="startDocument--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDocument</h4>
<pre>public&nbsp;void&nbsp;startDocument()
                   throws org.xml.sax.SAXException</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>startDocument</code>&nbsp;in interface&nbsp;<code>org.xml.sax.ContentHandler</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>startDocument</code>&nbsp;in class&nbsp;<code>org.xml.sax.helpers.DefaultHandler</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="startElement-java.lang.String-java.lang.String-java.lang.String-org.xml.sax.Attributes-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startElement</h4>
<pre>public&nbsp;void&nbsp;startElement(java.lang.String&nbsp;uri,
                         java.lang.String&nbsp;localName,
                         java.lang.String&nbsp;name,
                         org.xml.sax.Attributes&nbsp;attributes)
                  throws org.xml.sax.SAXException</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>startElement</code>&nbsp;in interface&nbsp;<code>org.xml.sax.ContentHandler</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>startElement</code>&nbsp;in class&nbsp;<code>org.xml.sax.helpers.DefaultHandler</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="characters-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>characters</h4>
<pre>public&nbsp;void&nbsp;characters(char[]&nbsp;ch,
                       int&nbsp;start,
                       int&nbsp;length)
                throws org.xml.sax.SAXException</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>characters</code>&nbsp;in interface&nbsp;<code>org.xml.sax.ContentHandler</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>characters</code>&nbsp;in class&nbsp;<code>org.xml.sax.helpers.DefaultHandler</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="endElement-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endElement</h4>
<pre>public&nbsp;void&nbsp;endElement(java.lang.String&nbsp;uri,
                       java.lang.String&nbsp;localName,
                       java.lang.String&nbsp;name)
                throws org.xml.sax.SAXException</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>endElement</code>&nbsp;in interface&nbsp;<code>org.xml.sax.ContentHandler</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>endElement</code>&nbsp;in class&nbsp;<code>org.xml.sax.helpers.DefaultHandler</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="endDocument--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endDocument</h4>
<pre>public&nbsp;void&nbsp;endDocument()
                 throws org.xml.sax.SAXException</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>endDocument</code>&nbsp;in interface&nbsp;<code>org.xml.sax.ContentHandler</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>endDocument</code>&nbsp;in class&nbsp;<code>org.xml.sax.helpers.DefaultHandler</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="resolveObject-java.lang.Object-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resolveObject</h4>
<pre>protected&nbsp;java.lang.Object&nbsp;resolveObject(java.lang.Object&nbsp;elementObject,
                                         java.lang.String&nbsp;elementName,
                                         java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)</pre>
<div class="block">Returns the object that will be stored in a home. This method is called for each home object created by this handler
 after its instantiation and returns <code>elementObject</code>. It might be overridden to substitute an object
 parsed from an XML element and its attributes for an other one of a different subclass if needed.</div>
</li>
</ul>
<a name="setHomeAttributes-com.eteks.sweethome3d.model.Home-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHomeAttributes</h4>
<pre>protected&nbsp;void&nbsp;setHomeAttributes(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                 java.lang.String&nbsp;elementName,
                                 java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                          throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>home</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="createPrint-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPrint</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a>&nbsp;createPrint(java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                         throws org.xml.sax.SAXException</pre>
<div class="block">Returns a new <a href="../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model"><code>HomePrint</code></a> instance initialized from the given <code>attributes</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setCompassAttributes-com.eteks.sweethome3d.model.Compass-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompassAttributes</h4>
<pre>protected&nbsp;void&nbsp;setCompassAttributes(<a href="../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a>&nbsp;compass,
                                    java.lang.String&nbsp;elementName,
                                    java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                             throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>compass</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setCameraAttributes-com.eteks.sweethome3d.model.Camera-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCameraAttributes</h4>
<pre>protected&nbsp;void&nbsp;setCameraAttributes(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
                                   java.lang.String&nbsp;elementName,
                                   java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                            throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>camera</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setLevelAttributes-com.eteks.sweethome3d.model.Level-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevelAttributes</h4>
<pre>protected&nbsp;void&nbsp;setLevelAttributes(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level,
                                  java.lang.String&nbsp;elementName,
                                  java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                           throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>level</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setPieceOfFurnitureAttributes-com.eteks.sweethome3d.model.HomePieceOfFurniture-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPieceOfFurnitureAttributes</h4>
<pre>protected&nbsp;void&nbsp;setPieceOfFurnitureAttributes(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece,
                                             java.lang.String&nbsp;elementName,
                                             java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                                      throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>piece</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setWallAttributes-com.eteks.sweethome3d.model.Wall-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallAttributes</h4>
<pre>protected&nbsp;void&nbsp;setWallAttributes(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall,
                                 java.lang.String&nbsp;elementName,
                                 java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                          throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>wall</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setRoomAttributes-com.eteks.sweethome3d.model.Room-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoomAttributes</h4>
<pre>protected&nbsp;void&nbsp;setRoomAttributes(<a href="../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room,
                                 java.lang.String&nbsp;elementName,
                                 java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                          throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>room</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setPolylineAttributes-com.eteks.sweethome3d.model.Polyline-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPolylineAttributes</h4>
<pre>protected&nbsp;void&nbsp;setPolylineAttributes(<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline,
                                     java.lang.String&nbsp;elementName,
                                     java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                              throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>polyline</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setDimensionLineAttributes-com.eteks.sweethome3d.model.DimensionLine-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDimensionLineAttributes</h4>
<pre>protected&nbsp;void&nbsp;setDimensionLineAttributes(<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine,
                                          java.lang.String&nbsp;elementName,
                                          java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                                   throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given dimension line.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setLabelAttributes-com.eteks.sweethome3d.model.Label-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLabelAttributes</h4>
<pre>protected&nbsp;void&nbsp;setLabelAttributes(<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label,
                                  java.lang.String&nbsp;elementName,
                                  java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes)
                           throws org.xml.sax.SAXException</pre>
<div class="block">Sets the attributes of the given <code>label</code>.
 If needed, this method should be called from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#endElement-java.lang.String-java.lang.String-java.lang.String-"><code>endElement(java.lang.String, java.lang.String, java.lang.String)</code></a>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="parseContent-java.lang.String-java.util.Map-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>parseContent</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;parseContent(java.lang.String&nbsp;elementName,
                               java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;attributes,
                               java.lang.String&nbsp;attributeName)
                        throws org.xml.sax.SAXException</pre>
<div class="block">Returns the content object matching the attribute named <code>attributeName</code> in the given element.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.xml.sax.SAXException</code></dd>
</dl>
</li>
</ul>
<a name="setHome-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHome</h4>
<pre>protected&nbsp;void&nbsp;setHome(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</pre>
<div class="block">Sets the home that will be updated by this handler.
 If a subclass of this handler uses a root element different from <code>home</code>,
 it should call this method from <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html#startElement-java.lang.String-java.lang.String-java.lang.String-org.xml.sax.Attributes-"><code>startElement(java.lang.String, java.lang.String, java.lang.String, org.xml.sax.Attributes)</code></a> to store the
 <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model"><code>Home</code></a> subclass instance read from the XML stream.</div>
</li>
</ul>
<a name="getHome--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getHome</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;getHome()</pre>
<div class="block">Returns the home read by this handler.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeXMLHandler.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/HomeXMLHandler.html" target="_top">Frames</a></li>
<li><a href="HomeXMLHandler.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
