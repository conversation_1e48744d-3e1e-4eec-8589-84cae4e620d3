<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>ExtensionsClassLoader (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ExtensionsClassLoader (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ExtensionsClassLoader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/tools/ExtensionsClassLoader.html" target="_top">Frames</a></li>
<li><a href="ExtensionsClassLoader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.tools</div>
<h2 title="Class ExtensionsClassLoader" class="title">Class ExtensionsClassLoader</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.ClassLoader</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.tools.ExtensionsClassLoader</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ExtensionsClassLoader</span>
extends java.lang.ClassLoader</pre>
<div class="block">Class loader able to load classes and DLLs with a higher priority from a given set of JARs.
 Its bytecode is Java 1.1 compatible to be loadable by old JVMs.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html#ExtensionsClassLoader-java.lang.ClassLoader-java.security.ProtectionDomain-java.lang.String:A-java.lang.String:A-">ExtensionsClassLoader</a></span>(java.lang.ClassLoader&nbsp;parent,
                     java.security.ProtectionDomain&nbsp;protectionDomain,
                     java.lang.String[]&nbsp;extensionJarsAndDlls,
                     java.lang.String[]&nbsp;applicationPackages)</code>
<div class="block">Creates a class loader.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html#ExtensionsClassLoader-java.lang.ClassLoader-java.security.ProtectionDomain-java.lang.String:A-java.net.URL:A-java.lang.String:A-java.io.File-java.lang.String-">ExtensionsClassLoader</a></span>(java.lang.ClassLoader&nbsp;parent,
                     java.security.ProtectionDomain&nbsp;protectionDomain,
                     java.lang.String[]&nbsp;extensionJarAndDllResources,
                     java.net.URL[]&nbsp;extensionJarAndDllUrls,
                     java.lang.String[]&nbsp;applicationPackages,
                     java.io.File&nbsp;cacheFolder,
                     java.lang.String&nbsp;cachedFilesPrefix)</code>
<div class="block">Creates a class loader.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html#ExtensionsClassLoader-java.lang.ClassLoader-java.security.ProtectionDomain-java.lang.String:A-java.net.URL:A-java.lang.String:A-java.io.File-java.lang.String-boolean-">ExtensionsClassLoader</a></span>(java.lang.ClassLoader&nbsp;parent,
                     java.security.ProtectionDomain&nbsp;protectionDomain,
                     java.lang.String[]&nbsp;extensionJarAndDllResources,
                     java.net.URL[]&nbsp;extensionJarAndDllUrls,
                     java.lang.String[]&nbsp;applicationPackages,
                     java.io.File&nbsp;cacheFolder,
                     java.lang.String&nbsp;cachedFilesPrefix,
                     boolean&nbsp;cacheOnlyJars)</code>
<div class="block">Creates a class loader.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html#copyInputStreamToFile-java.io.InputStream-java.io.File-">copyInputStreamToFile</a></span>(java.io.InputStream&nbsp;input,
                     java.io.File&nbsp;file)</code>
<div class="block">Copies the <code>input</code> content to the given file.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected java.lang.Class</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html#findClass-java.lang.String-">findClass</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Finds and defines the given class among the extension JARs
 given in constructor, then among resources.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html#findLibrary-java.lang.String-">findLibrary</a></span>(java.lang.String&nbsp;libname)</code>
<div class="block">Returns the library path of an extension DLL.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected java.net.URL</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html#findResource-java.lang.String-">findResource</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the URL of the given resource searching first if it exists among
 the extension JARs given in constructor.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected java.lang.Class</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html#loadClass-java.lang.String-boolean-">loadClass</a></span>(java.lang.String&nbsp;name,
         boolean&nbsp;resolve)</code>
<div class="block">Loads a class with this class loader if its package belongs to <code>applicationPackages</code>
 given in constructor.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.ClassLoader">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.ClassLoader</h3>
<code>clearAssertionStatus, defineClass, defineClass, defineClass, defineClass, definePackage, findLoadedClass, findResources, findSystemClass, getClassLoadingLock, getPackage, getPackages, getParent, getResource, getResourceAsStream, getResources, getSystemClassLoader, getSystemResource, getSystemResourceAsStream, getSystemResources, loadClass, registerAsParallelCapable, resolveClass, setClassAssertionStatus, setDefaultAssertionStatus, setPackageAssertionStatus, setSigners</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ExtensionsClassLoader-java.lang.ClassLoader-java.security.ProtectionDomain-java.lang.String:A-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ExtensionsClassLoader</h4>
<pre>public&nbsp;ExtensionsClassLoader(java.lang.ClassLoader&nbsp;parent,
                             java.security.ProtectionDomain&nbsp;protectionDomain,
                             java.lang.String[]&nbsp;extensionJarsAndDlls,
                             java.lang.String[]&nbsp;applicationPackages)</pre>
<div class="block">Creates a class loader. It will consider JARs and DLLs of <code>extensionJarsAndDlls</code> accessed as resources
 as classpath and libclasspath elements with a higher priority than the ones of default classpath,
 and will load itself all the classes belonging to packages of <code>applicationPackages</code>.
 No cache will be used.</div>
</li>
</ul>
<a name="ExtensionsClassLoader-java.lang.ClassLoader-java.security.ProtectionDomain-java.lang.String:A-java.net.URL:A-java.lang.String:A-java.io.File-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ExtensionsClassLoader</h4>
<pre>public&nbsp;ExtensionsClassLoader(java.lang.ClassLoader&nbsp;parent,
                             java.security.ProtectionDomain&nbsp;protectionDomain,
                             java.lang.String[]&nbsp;extensionJarAndDllResources,
                             java.net.URL[]&nbsp;extensionJarAndDllUrls,
                             java.lang.String[]&nbsp;applicationPackages,
                             java.io.File&nbsp;cacheFolder,
                             java.lang.String&nbsp;cachedFilesPrefix)</pre>
<div class="block">Creates a class loader. It will consider JARs and DLLs of <code>extensionJarAndDllResources</code>
 and <code>extensionJarAndDllUrls</code> as classpath and libclasspath elements with a higher priority
 than the ones of default classpath, and will load itself all the classes belonging to packages of
 <code>applicationPackages</code>.<br>
 Copies of <code>extensionJarAndDllResources</code> and <code>extensionJarAndDllUrls</code> will be stored
 in the given cache folder if possible, each file being prefixed by <code>cachedFilesPrefix</code>.</div>
</li>
</ul>
<a name="ExtensionsClassLoader-java.lang.ClassLoader-java.security.ProtectionDomain-java.lang.String:A-java.net.URL:A-java.lang.String:A-java.io.File-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ExtensionsClassLoader</h4>
<pre>public&nbsp;ExtensionsClassLoader(java.lang.ClassLoader&nbsp;parent,
                             java.security.ProtectionDomain&nbsp;protectionDomain,
                             java.lang.String[]&nbsp;extensionJarAndDllResources,
                             java.net.URL[]&nbsp;extensionJarAndDllUrls,
                             java.lang.String[]&nbsp;applicationPackages,
                             java.io.File&nbsp;cacheFolder,
                             java.lang.String&nbsp;cachedFilesPrefix,
                             boolean&nbsp;cacheOnlyJars)</pre>
<div class="block">Creates a class loader. It will consider JARs and DLLs of <code>extensionJarAndDllResources</code>
 and <code>extensionJarAndDllUrls</code> as classpath and libclasspath elements with a higher priority
 than the ones of default classpath, and will load itself all the classes belonging to packages of
 <code>applicationPackages</code>.<br>
 Copies of <code>extensionJarAndDllResources</code> and <code>extensionJarAndDllUrls</code> will be stored
 in the given cache folder if possible, each file being prefixed by <code>cachedFilesPrefix</code>.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="copyInputStreamToFile-java.io.InputStream-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>copyInputStreamToFile</h4>
<pre>public&nbsp;void&nbsp;copyInputStreamToFile(java.io.InputStream&nbsp;input,
                                  java.io.File&nbsp;file)
                           throws java.io.FileNotFoundException,
                                  java.io.IOException</pre>
<div class="block">Copies the <code>input</code> content to the given file.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code></dd>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="findClass-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findClass</h4>
<pre>protected&nbsp;java.lang.Class&nbsp;findClass(java.lang.String&nbsp;name)
                             throws java.lang.ClassNotFoundException</pre>
<div class="block">Finds and defines the given class among the extension JARs
 given in constructor, then among resources.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>findClass</code>&nbsp;in class&nbsp;<code>java.lang.ClassLoader</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.ClassNotFoundException</code></dd>
</dl>
</li>
</ul>
<a name="findLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findLibrary</h4>
<pre>protected&nbsp;java.lang.String&nbsp;findLibrary(java.lang.String&nbsp;libname)</pre>
<div class="block">Returns the library path of an extension DLL.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>findLibrary</code>&nbsp;in class&nbsp;<code>java.lang.ClassLoader</code></dd>
</dl>
</li>
</ul>
<a name="findResource-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findResource</h4>
<pre>protected&nbsp;java.net.URL&nbsp;findResource(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the URL of the given resource searching first if it exists among
 the extension JARs given in constructor.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>findResource</code>&nbsp;in class&nbsp;<code>java.lang.ClassLoader</code></dd>
</dl>
</li>
</ul>
<a name="loadClass-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>loadClass</h4>
<pre>protected&nbsp;java.lang.Class&nbsp;loadClass(java.lang.String&nbsp;name,
                                    boolean&nbsp;resolve)
                             throws java.lang.ClassNotFoundException</pre>
<div class="block">Loads a class with this class loader if its package belongs to <code>applicationPackages</code>
 given in constructor.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>loadClass</code>&nbsp;in class&nbsp;<code>java.lang.ClassLoader</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.ClassNotFoundException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ExtensionsClassLoader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/tools/ExtensionsClassLoader.html" target="_top">Frames</a></li>
<li><a href="ExtensionsClassLoader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
