<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:50 CEST 2024 -->
<title>ScaledImageComponent (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ScaledImageComponent (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScaledImageComponent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/RoomPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ScaledImageComponent.html" target="_top">Frames</a></li>
<li><a href="ScaledImageComponent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JComponent">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class ScaledImageComponent" class="title">Class ScaledImageComponent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>javax.swing.JComponent</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.ScaledImageComponent</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.awt.image.ImageObserver, java.awt.MenuContainer, java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ScaledImageComponent</span>
extends javax.swing.JComponent</pre>
<div class="block">Component displaying an image scaled to fit within its bound.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.ScaledImageComponent">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JComponent</h3>
<code>javax.swing.JComponent.AccessibleJComponent</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JComponent</h3>
<code>listenerList, TOOL_TIP_TEXT_KEY, ui, UNDEFINED_CONDITION, WHEN_ANCESTOR_OF_FOCUSED_COMPONENT, WHEN_FOCUSED, WHEN_IN_FOCUSED_WINDOW</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>accessibleContext, BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#ScaledImageComponent--">ScaledImageComponent</a></span>()</code>
<div class="block">Creates a component that will display no image.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#ScaledImageComponent-java.awt.image.BufferedImage-">ScaledImageComponent</a></span>(java.awt.image.BufferedImage&nbsp;image)</code>
<div class="block">Creates a component that will display the given <code>image</code> 
 at a maximum scale equal to 1.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#ScaledImageComponent-java.awt.image.BufferedImage-boolean-">ScaledImageComponent</a></span>(java.awt.image.BufferedImage&nbsp;image,
                    boolean&nbsp;imageEnlargementEnabled)</code>
<div class="block">Creates a component that will display the given <code>image</code> 
 with no maximum scale if <code>imageEnlargementEnabled</code> is <code>true</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the image drawn by this component.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#getImageScale--">getImageScale</a></span>()</code>
<div class="block">Returns the scale used to draw the image of this component.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected java.awt.Point</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#getImageTranslation--">getImageTranslation</a></span>()</code>
<div class="block">Returns the origin point where the image of this component is drawn.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected java.awt.Point</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#getPointConstrainedInImage-int-int-">getPointConstrainedInImage</a></span>(int&nbsp;x,
                          int&nbsp;y)</code>
<div class="block">Returns a point with (<code>x</code>, <code>y</code>) coordinates constrained 
 in the image displayed by this component.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#getPreferredSize--">getPreferredSize</a></span>()</code>
<div class="block">Returns the preferred size of this component.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#getScaleMultiplier--">getScaleMultiplier</a></span>()</code>
<div class="block">Returns the multiplier of the default scale.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#isPointInImage-int-int-">isPointInImage</a></span>(int&nbsp;x,
              int&nbsp;y)</code>
<div class="block">Returns <code>true</code> if point at (<code>x</code>, <code>y</code>)
 is in the image displayed by this component.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#paintComponent-java.awt.Graphics-">paintComponent</a></span>(java.awt.Graphics&nbsp;g)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#paintImage-java.awt.Graphics-java.awt.AlphaComposite-">paintImage</a></span>(java.awt.Graphics&nbsp;g,
          java.awt.AlphaComposite&nbsp;composite)</code>
<div class="block">Paints the image with a given <code>composite</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#setImage-java.awt.image.BufferedImage-">setImage</a></span>(java.awt.image.BufferedImage&nbsp;image)</code>
<div class="block">Sets the image drawn by this component.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html#setScaleMultiplier-float-">setScaleMultiplier</a></span>(float&nbsp;scaleMultiplier)</code>
<div class="block">Sets the multiplier of the default scale.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JComponent</h3>
<code>addAncestorListener, addNotify, addVetoableChangeListener, computeVisibleRect, contains, createToolTip, disable, enable, firePropertyChange, firePropertyChange, firePropertyChange, fireVetoableChange, getActionForKeyStroke, getActionMap, getAlignmentX, getAlignmentY, getAncestorListeners, getAutoscrolls, getBaseline, getBaselineResizeBehavior, getBorder, getBounds, getClientProperty, getComponentGraphics, getComponentPopupMenu, getConditionForKeyStroke, getDebugGraphicsOptions, getDefaultLocale, getFontMetrics, getGraphics, getHeight, getInheritsPopupMenu, getInputMap, getInputMap, getInputVerifier, getInsets, getInsets, getListeners, getLocation, getMaximumSize, getMinimumSize, getNextFocusableComponent, getPopupLocation, getRegisteredKeyStrokes, getRootPane, getSize, getToolTipLocation, getToolTipText, getToolTipText, getTopLevelAncestor, getTransferHandler, getUIClassID, getVerifyInputWhenFocusTarget, getVetoableChangeListeners, getVisibleRect, getWidth, getX, getY, grabFocus, hide, isDoubleBuffered, isLightweightComponent, isManagingFocus, isOpaque, isOptimizedDrawingEnabled, isPaintingForPrint, isPaintingOrigin, isPaintingTile, isRequestFocusEnabled, isValidateRoot, paint, paintBorder, paintChildren, paintImmediately, paintImmediately, paramString, print, printAll, printBorder, printChildren, printComponent, processComponentKeyEvent, processKeyBinding, processKeyEvent, processMouseEvent, processMouseMotionEvent, putClientProperty, registerKeyboardAction, registerKeyboardAction, removeAncestorListener, removeNotify, removeVetoableChangeListener, repaint, repaint, requestDefaultFocus, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, resetKeyboardActions, reshape, revalidate, scrollRectToVisible, setActionMap, setAlignmentX, setAlignmentY, setAutoscrolls, setBackground, setBorder, setComponentPopupMenu, setDebugGraphicsOptions, setDefaultLocale, setDoubleBuffered, setEnabled, setFocusTraversalKeys, setFont, setForeground, setInheritsPopupMenu, setInputMap, setInputVerifier, setMaximumSize, setMinimumSize, setNextFocusableComponent, setOpaque, setPreferredSize, setRequestFocusEnabled, setToolTipText, setTransferHandler, setUI, setVerifyInputWhenFocusTarget, setVisible, unregisterKeyboardAction, update, updateUI</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addImpl, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, doLayout, findComponentAt, findComponentAt, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getLayout, getMousePosition, insets, invalidate, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paintComponents, preferredSize, printComponents, processContainerEvent, processEvent, remove, remove, removeAll, removeContainerListener, setComponentZOrder, setFocusCycleRoot, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setLayout, transferFocusDownCycle, validate, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addFocusListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseListener, addMouseMotionListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, createImage, createImage, createVolatileImage, createVolatileImage, disableEvents, dispatchEvent, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getAccessibleContext, getBackground, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getCursor, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getForeground, getGraphicsConfiguration, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocale, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getToolkit, getTreeLock, gotFocus, handleEvent, hasFocus, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseListener, removeMouseMotionListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, resize, resize, setBounds, setBounds, setComponentOrientation, setCursor, setDropTarget, setFocusable, setFocusTraversalKeysEnabled, setIgnoreRepaint, setLocale, setLocation, setLocation, setName, setSize, setSize, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ScaledImageComponent--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ScaledImageComponent</h4>
<pre>public&nbsp;ScaledImageComponent()</pre>
<div class="block">Creates a component that will display no image.</div>
</li>
</ul>
<a name="ScaledImageComponent-java.awt.image.BufferedImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ScaledImageComponent</h4>
<pre>public&nbsp;ScaledImageComponent(java.awt.image.BufferedImage&nbsp;image)</pre>
<div class="block">Creates a component that will display the given <code>image</code> 
 at a maximum scale equal to 1.</div>
</li>
</ul>
<a name="ScaledImageComponent-java.awt.image.BufferedImage-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ScaledImageComponent</h4>
<pre>public&nbsp;ScaledImageComponent(java.awt.image.BufferedImage&nbsp;image,
                            boolean&nbsp;imageEnlargementEnabled)</pre>
<div class="block">Creates a component that will display the given <code>image</code> 
 with no maximum scale if <code>imageEnlargementEnabled</code> is <code>true</code>.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPreferredSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreferredSize</h4>
<pre>public&nbsp;java.awt.Dimension&nbsp;getPreferredSize()</pre>
<div class="block">Returns the preferred size of this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getPreferredSize</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="paintComponent-java.awt.Graphics-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paintComponent</h4>
<pre>protected&nbsp;void&nbsp;paintComponent(java.awt.Graphics&nbsp;g)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>paintComponent</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="paintImage-java.awt.Graphics-java.awt.AlphaComposite-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paintImage</h4>
<pre>protected&nbsp;void&nbsp;paintImage(java.awt.Graphics&nbsp;g,
                          java.awt.AlphaComposite&nbsp;composite)</pre>
<div class="block">Paints the image with a given <code>composite</code>. 
 Image is scaled to fill width of the component.</div>
</li>
</ul>
<a name="setImage-java.awt.image.BufferedImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setImage</h4>
<pre>public&nbsp;void&nbsp;setImage(java.awt.image.BufferedImage&nbsp;image)</pre>
<div class="block">Sets the image drawn by this component.</div>
</li>
</ul>
<a name="getImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public&nbsp;java.awt.image.BufferedImage&nbsp;getImage()</pre>
<div class="block">Returns the image drawn by this component.</div>
</li>
</ul>
<a name="getImageScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImageScale</h4>
<pre>protected&nbsp;float&nbsp;getImageScale()</pre>
<div class="block">Returns the scale used to draw the image of this component.</div>
</li>
</ul>
<a name="getScaleMultiplier--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScaleMultiplier</h4>
<pre>public&nbsp;float&nbsp;getScaleMultiplier()</pre>
<div class="block">Returns the multiplier of the default scale.</div>
</li>
</ul>
<a name="setScaleMultiplier-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScaleMultiplier</h4>
<pre>public&nbsp;void&nbsp;setScaleMultiplier(float&nbsp;scaleMultiplier)</pre>
<div class="block">Sets the multiplier of the default scale.</div>
</li>
</ul>
<a name="getImageTranslation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImageTranslation</h4>
<pre>protected&nbsp;java.awt.Point&nbsp;getImageTranslation()</pre>
<div class="block">Returns the origin point where the image of this component is drawn.</div>
</li>
</ul>
<a name="isPointInImage-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPointInImage</h4>
<pre>protected&nbsp;boolean&nbsp;isPointInImage(int&nbsp;x,
                                 int&nbsp;y)</pre>
<div class="block">Returns <code>true</code> if point at (<code>x</code>, <code>y</code>)
 is in the image displayed by this component.</div>
</li>
</ul>
<a name="getPointConstrainedInImage-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPointConstrainedInImage</h4>
<pre>protected&nbsp;java.awt.Point&nbsp;getPointConstrainedInImage(int&nbsp;x,
                                                    int&nbsp;y)</pre>
<div class="block">Returns a point with (<code>x</code>, <code>y</code>) coordinates constrained 
 in the image displayed by this component.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ScaledImageComponent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/RoomPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ScaledImageComponent.html" target="_top">Frames</a></li>
<li><a href="ScaledImageComponent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JComponent">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
