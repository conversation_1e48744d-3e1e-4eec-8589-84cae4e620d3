<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>UserPreferencesController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UserPreferencesController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UserPreferencesController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" target="_top">Frames</a></li>
<li><a href="UserPreferencesController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class UserPreferencesController" class="title">Class UserPreferencesController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.UserPreferencesController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">UserPreferencesController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for user preferences view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">UserPreferencesController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#UserPreferencesController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">UserPreferencesController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates the controller of user preferences view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#UserPreferencesController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.viewcontroller.HomeController-">UserPreferencesController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</code>
<div class="block">Creates the controller of user preferences view.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.UserPreferencesController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">UserPreferencesController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#checkUpdates--">checkUpdates</a></span>()</code>
<div class="block">Checks if some updates are available.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getAutoSaveDelayForRecovery--">getAutoSaveDelayForRecovery</a></span>()</code>
<div class="block">Returns the edited auto recovery save delay.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getCurrency--">getCurrency</a></span>()</code>
<div class="block">Returns the edited currency.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getDefaultFontName--">getDefaultFontName</a></span>()</code>
<div class="block">Returns the name of the font that should be used by default or <code>null</code>
 if the default font should be the default one in the application.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getFurnitureModelIconSize--">getFurnitureModelIconSize</a></span>()</code>
<div class="block">Returns the size used to generate icons of furniture viewed from top.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getLanguage--">getLanguage</a></span>()</code>
<div class="block">Returns the edited language.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getNewFloorThickness--">getNewFloorThickness</a></span>()</code>
<div class="block">Returns the edited new floor thickness.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getNewWallHeight--">getNewWallHeight</a></span>()</code>
<div class="block">Returns the edited new wall height.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getNewWallPattern--">getNewWallPattern</a></span>()</code>
<div class="block">Returns the edited new wall top pattern in plan.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getNewWallThickness--">getNewWallThickness</a></span>()</code>
<div class="block">Returns the edited new wall thickness.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getUnit--">getUnit</a></span>()</code>
<div class="block">Returns the edited unit.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#getWallPattern--">getWallPattern</a></span>()</code>
<div class="block">Returns the default walls top pattern in plan.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#importLanguageLibrary--">importLanguageLibrary</a></span>()</code>
<div class="block">Imports a language library chosen by the user.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isAerialViewCenteredOnSelectionEnabled--">isAerialViewCenteredOnSelectionEnabled</a></span>()</code>
<div class="block">Returns whether aerial view should be centered on selection or not.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isAutoSaveForRecoveryEnabled--">isAutoSaveForRecoveryEnabled</a></span>()</code>
<div class="block">Returns <code>true</code> if auto recovery save is enabled.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isCheckUpdatesEnabled--">isCheckUpdatesEnabled</a></span>()</code>
<div class="block">Returns <code>true</code> if updates should be checked.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isEditingIn3DViewEnabled--">isEditingIn3DViewEnabled</a></span>()</code>
<div class="block">Returns whether interactive editing in 3D view is enabled or not.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isFurnitureCatalogViewedInTree--">isFurnitureCatalogViewedInTree</a></span>()</code>
<div class="block">Returns <code>true</code> if furniture catalog should be viewed in a tree.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isFurnitureViewedFromTop--">isFurnitureViewedFromTop</a></span>()</code>
<div class="block">Returns how furniture should be displayed in plan.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isGridVisible--">isGridVisible</a></span>()</code>
<div class="block">Returns whether grid is visible or not.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isMagnetismEnabled--">isMagnetismEnabled</a></span>()</code>
<div class="block">Returns whether magnetism is enabled or not.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isNavigationPanelVisible--">isNavigationPanelVisible</a></span>()</code>
<div class="block">Returns <code>true</code> if the navigation panel should be displayed.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isObserverCameraSelectedAtChange--">isObserverCameraSelectedAtChange</a></span>()</code>
<div class="block">Returns whether the observer camera should be selected at each change.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isPropertyEditable-com.eteks.sweethome3d.viewcontroller.UserPreferencesController.Property-">isPropertyEditable</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">UserPreferencesController.Property</a>&nbsp;property)</code>
<div class="block">Returns <code>true</code> if the given <code>property</code> is editable.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isRoomFloorColoredOrTextured--">isRoomFloorColoredOrTextured</a></span>()</code>
<div class="block">Returns <code>true</code> if floor texture is visible in plan.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isRulersVisible--">isRulersVisible</a></span>()</code>
<div class="block">Returns whether rulers are visible or not.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#isValueAddedTaxEnabled--">isValueAddedTaxEnabled</a></span>()</code>
<div class="block">Returns <code>true</code> if Value Added Tax should be taken in account in prices.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#mayImportLanguageLibrary--">mayImportLanguageLibrary</a></span>()</code>
<div class="block">Returns <code>true</code> if language libraries can be imported.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#modifyUserPreferences--">modifyUserPreferences</a></span>()</code>
<div class="block">Controls the modification of user preferences.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.UserPreferencesController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">UserPreferencesController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#resetDisplayedActionTips--">resetDisplayedActionTips</a></span>()</code>
<div class="block">Resets the displayed flags of action tips.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setAerialViewCenteredOnSelectionEnabled-boolean-">setAerialViewCenteredOnSelectionEnabled</a></span>(boolean&nbsp;aerialViewCenteredOnSelectionEnabled)</code>
<div class="block">Sets whether aerial view should be centered on selection or not.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setAutoSaveDelayForRecovery-int-">setAutoSaveDelayForRecovery</a></span>(int&nbsp;autoSaveDelayForRecovery)</code>
<div class="block">Sets the edited auto recovery save delay.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setAutoSaveForRecoveryEnabled-boolean-">setAutoSaveForRecoveryEnabled</a></span>(boolean&nbsp;autoSaveForRecoveryEnabled)</code>
<div class="block">Sets whether auto recovery save is enabled or not.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setCheckUpdatesEnabled-boolean-">setCheckUpdatesEnabled</a></span>(boolean&nbsp;updatesChecked)</code>
<div class="block">Sets whether updates should be checked or not.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setCurrency-java.lang.String-">setCurrency</a></span>(java.lang.String&nbsp;currency)</code>
<div class="block">Sets the edited currency.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setDefaultFontName-java.lang.String-">setDefaultFontName</a></span>(java.lang.String&nbsp;defaultFontName)</code>
<div class="block">Sets the name of the font that should be used by default.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setEditingIn3DViewEnabled-boolean-">setEditingIn3DViewEnabled</a></span>(boolean&nbsp;editingIn3DViewEnabled)</code>
<div class="block">Sets whether interactive editing in 3D view is enabled or not.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setFurnitureCatalogViewedInTree-boolean-">setFurnitureCatalogViewedInTree</a></span>(boolean&nbsp;furnitureCatalogViewedInTree)</code>
<div class="block">Sets whether the furniture catalog should be viewed in a tree or a different way.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setFurnitureModelIconSize-int-">setFurnitureModelIconSize</a></span>(int&nbsp;furnitureModelIconSize)</code>
<div class="block">Sets the size used to generate icons of furniture viewed from top.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setFurnitureViewedFromTop-boolean-">setFurnitureViewedFromTop</a></span>(boolean&nbsp;furnitureViewedFromTop)</code>
<div class="block">Sets how furniture should be displayed in plan.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setGridVisible-boolean-">setGridVisible</a></span>(boolean&nbsp;gridVisible)</code>
<div class="block">Sets whether grid is visible or not.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setLanguage-java.lang.String-">setLanguage</a></span>(java.lang.String&nbsp;language)</code>
<div class="block">Sets the edited language.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setMagnetismEnabled-boolean-">setMagnetismEnabled</a></span>(boolean&nbsp;magnetismEnabled)</code>
<div class="block">Sets whether magnetism is enabled or not.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setNavigationPanelVisible-boolean-">setNavigationPanelVisible</a></span>(boolean&nbsp;navigationPanelVisible)</code>
<div class="block">Sets whether the navigation panel should be displayed or not.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setNewFloorThickness-float-">setNewFloorThickness</a></span>(float&nbsp;newFloorThickness)</code>
<div class="block">Sets the edited new floor thickness.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setNewWallHeight-float-">setNewWallHeight</a></span>(float&nbsp;newWallHeight)</code>
<div class="block">Sets the edited new wall height.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setNewWallPattern-com.eteks.sweethome3d.model.TextureImage-">setNewWallPattern</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;newWallPattern)</code>
<div class="block">Sets the edited new wall top pattern in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setNewWallThickness-float-">setNewWallThickness</a></span>(float&nbsp;newWallThickness)</code>
<div class="block">Sets the edited new wall thickness.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setObserverCameraSelectedAtChange-boolean-">setObserverCameraSelectedAtChange</a></span>(boolean&nbsp;observerCameraSelectedAtChange)</code>
<div class="block">Sets whether the observer camera should be selected at each change.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setRoomFloorColoredOrTextured-boolean-">setRoomFloorColoredOrTextured</a></span>(boolean&nbsp;floorTextureVisible)</code>
<div class="block">Sets whether floor texture is visible in plan or not.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setRulersVisible-boolean-">setRulersVisible</a></span>(boolean&nbsp;rulersVisible)</code>
<div class="block">Sets whether rulers are visible or not.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setUnit-com.eteks.sweethome3d.model.LengthUnit-">setUnit</a></span>(<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&nbsp;unit)</code>
<div class="block">Sets the edited unit.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setValueAddedTaxEnabled-boolean-">setValueAddedTaxEnabled</a></span>(boolean&nbsp;valueAddedTaxEnabled)</code>
<div class="block">Sets whether Value Added Tax should be taken in account in prices.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#setWallPattern-com.eteks.sweethome3d.model.TextureImage-">setWallPattern</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;wallPattern)</code>
<div class="block">Sets default walls top pattern in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates preferences properties edited by this controller.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UserPreferencesController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>UserPreferencesController</h4>
<pre>public&nbsp;UserPreferencesController(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
<div class="block">Creates the controller of user preferences view.</div>
</li>
</ul>
<a name="UserPreferencesController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-com.eteks.sweethome3d.viewcontroller.HomeController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UserPreferencesController</h4>
<pre>public&nbsp;UserPreferencesController(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html" title="class in com.eteks.sweethome3d.viewcontroller">HomeController</a>&nbsp;homeController)</pre>
<div class="block">Creates the controller of user preferences view.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.UserPreferencesController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">UserPreferencesController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.UserPreferencesController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">UserPreferencesController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates preferences properties edited by this controller.</div>
</li>
</ul>
<a name="isPropertyEditable-com.eteks.sweethome3d.viewcontroller.UserPreferencesController.Property-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPropertyEditable</h4>
<pre>public&nbsp;boolean&nbsp;isPropertyEditable(<a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">UserPreferencesController.Property</a>&nbsp;property)</pre>
<div class="block">Returns <code>true</code> if the given <code>property</code> is editable.
 Depending on whether a property is editable or not, the view associated to this controller
 may render it differently.
 The implementation of this method always returns <code>true</code> except for <code>LANGUAGE</code> if it's not editable.</div>
</li>
</ul>
<a name="setLanguage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLanguage</h4>
<pre>public&nbsp;void&nbsp;setLanguage(java.lang.String&nbsp;language)</pre>
<div class="block">Sets the edited language.</div>
</li>
</ul>
<a name="getLanguage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLanguage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLanguage()</pre>
<div class="block">Returns the edited language.</div>
</li>
</ul>
<a name="setUnit-com.eteks.sweethome3d.model.LengthUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnit</h4>
<pre>public&nbsp;void&nbsp;setUnit(<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&nbsp;unit)</pre>
<div class="block">Sets the edited unit.</div>
</li>
</ul>
<a name="getUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnit</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&nbsp;getUnit()</pre>
<div class="block">Returns the edited unit.</div>
</li>
</ul>
<a name="setCurrency-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrency</h4>
<pre>public&nbsp;void&nbsp;setCurrency(java.lang.String&nbsp;currency)</pre>
<div class="block">Sets the edited currency.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getCurrency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrency</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCurrency()</pre>
<div class="block">Returns the edited currency.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setValueAddedTaxEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValueAddedTaxEnabled</h4>
<pre>public&nbsp;void&nbsp;setValueAddedTaxEnabled(boolean&nbsp;valueAddedTaxEnabled)</pre>
<div class="block">Sets whether Value Added Tax should be taken in account in prices.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="isValueAddedTaxEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isValueAddedTaxEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isValueAddedTaxEnabled()</pre>
<div class="block">Returns <code>true</code> if Value Added Tax should be taken in account in prices.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setFurnitureCatalogViewedInTree-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureCatalogViewedInTree</h4>
<pre>public&nbsp;void&nbsp;setFurnitureCatalogViewedInTree(boolean&nbsp;furnitureCatalogViewedInTree)</pre>
<div class="block">Sets whether the furniture catalog should be viewed in a tree or a different way.</div>
</li>
</ul>
<a name="isFurnitureCatalogViewedInTree--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFurnitureCatalogViewedInTree</h4>
<pre>public&nbsp;boolean&nbsp;isFurnitureCatalogViewedInTree()</pre>
<div class="block">Returns <code>true</code> if furniture catalog should be viewed in a tree.</div>
</li>
</ul>
<a name="setNavigationPanelVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNavigationPanelVisible</h4>
<pre>public&nbsp;void&nbsp;setNavigationPanelVisible(boolean&nbsp;navigationPanelVisible)</pre>
<div class="block">Sets whether the navigation panel should be displayed or not.</div>
</li>
</ul>
<a name="isNavigationPanelVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNavigationPanelVisible</h4>
<pre>public&nbsp;boolean&nbsp;isNavigationPanelVisible()</pre>
<div class="block">Returns <code>true</code> if the navigation panel should be displayed.</div>
</li>
</ul>
<a name="setEditingIn3DViewEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditingIn3DViewEnabled</h4>
<pre>public&nbsp;void&nbsp;setEditingIn3DViewEnabled(boolean&nbsp;editingIn3DViewEnabled)</pre>
<div class="block">Sets whether interactive editing in 3D view is enabled or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isEditingIn3DViewEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditingIn3DViewEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isEditingIn3DViewEnabled()</pre>
<div class="block">Returns whether interactive editing in 3D view is enabled or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setAerialViewCenteredOnSelectionEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAerialViewCenteredOnSelectionEnabled</h4>
<pre>public&nbsp;void&nbsp;setAerialViewCenteredOnSelectionEnabled(boolean&nbsp;aerialViewCenteredOnSelectionEnabled)</pre>
<div class="block">Sets whether aerial view should be centered on selection or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="isAerialViewCenteredOnSelectionEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAerialViewCenteredOnSelectionEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isAerialViewCenteredOnSelectionEnabled()</pre>
<div class="block">Returns whether aerial view should be centered on selection or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setObserverCameraSelectedAtChange-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setObserverCameraSelectedAtChange</h4>
<pre>public&nbsp;void&nbsp;setObserverCameraSelectedAtChange(boolean&nbsp;observerCameraSelectedAtChange)</pre>
<div class="block">Sets whether the observer camera should be selected at each change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isObserverCameraSelectedAtChange--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isObserverCameraSelectedAtChange</h4>
<pre>public&nbsp;boolean&nbsp;isObserverCameraSelectedAtChange()</pre>
<div class="block">Returns whether the observer camera should be selected at each change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setMagnetismEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMagnetismEnabled</h4>
<pre>public&nbsp;void&nbsp;setMagnetismEnabled(boolean&nbsp;magnetismEnabled)</pre>
<div class="block">Sets whether magnetism is enabled or not.</div>
</li>
</ul>
<a name="isMagnetismEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMagnetismEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isMagnetismEnabled()</pre>
<div class="block">Returns whether magnetism is enabled or not.</div>
</li>
</ul>
<a name="setRulersVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRulersVisible</h4>
<pre>public&nbsp;void&nbsp;setRulersVisible(boolean&nbsp;rulersVisible)</pre>
<div class="block">Sets whether rulers are visible or not.</div>
</li>
</ul>
<a name="isRulersVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRulersVisible</h4>
<pre>public&nbsp;boolean&nbsp;isRulersVisible()</pre>
<div class="block">Returns whether rulers are visible or not.</div>
</li>
</ul>
<a name="setGridVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGridVisible</h4>
<pre>public&nbsp;void&nbsp;setGridVisible(boolean&nbsp;gridVisible)</pre>
<div class="block">Sets whether grid is visible or not.</div>
</li>
</ul>
<a name="isGridVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGridVisible</h4>
<pre>public&nbsp;boolean&nbsp;isGridVisible()</pre>
<div class="block">Returns whether grid is visible or not.</div>
</li>
</ul>
<a name="setDefaultFontName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultFontName</h4>
<pre>public&nbsp;void&nbsp;setDefaultFontName(java.lang.String&nbsp;defaultFontName)</pre>
<div class="block">Sets the name of the font that should be used by default.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getDefaultFontName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFontName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultFontName()</pre>
<div class="block">Returns the name of the font that should be used by default or <code>null</code>
 if the default font should be the default one in the application.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setFurnitureViewedFromTop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureViewedFromTop</h4>
<pre>public&nbsp;void&nbsp;setFurnitureViewedFromTop(boolean&nbsp;furnitureViewedFromTop)</pre>
<div class="block">Sets how furniture should be displayed in plan.</div>
</li>
</ul>
<a name="isFurnitureViewedFromTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFurnitureViewedFromTop</h4>
<pre>public&nbsp;boolean&nbsp;isFurnitureViewedFromTop()</pre>
<div class="block">Returns how furniture should be displayed in plan.</div>
</li>
</ul>
<a name="setFurnitureModelIconSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureModelIconSize</h4>
<pre>public&nbsp;void&nbsp;setFurnitureModelIconSize(int&nbsp;furnitureModelIconSize)</pre>
<div class="block">Sets the size used to generate icons of furniture viewed from top.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getFurnitureModelIconSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureModelIconSize</h4>
<pre>public&nbsp;int&nbsp;getFurnitureModelIconSize()</pre>
<div class="block">Returns the size used to generate icons of furniture viewed from top.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setRoomFloorColoredOrTextured-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoomFloorColoredOrTextured</h4>
<pre>public&nbsp;void&nbsp;setRoomFloorColoredOrTextured(boolean&nbsp;floorTextureVisible)</pre>
<div class="block">Sets whether floor texture is visible in plan or not.</div>
</li>
</ul>
<a name="isRoomFloorColoredOrTextured--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRoomFloorColoredOrTextured</h4>
<pre>public&nbsp;boolean&nbsp;isRoomFloorColoredOrTextured()</pre>
<div class="block">Returns <code>true</code> if floor texture is visible in plan.</div>
</li>
</ul>
<a name="setWallPattern-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallPattern</h4>
<pre>public&nbsp;void&nbsp;setWallPattern(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;wallPattern)</pre>
<div class="block">Sets default walls top pattern in plan, and notifies
 listeners of this change.</div>
</li>
</ul>
<a name="getWallPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallPattern</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;getWallPattern()</pre>
<div class="block">Returns the default walls top pattern in plan.</div>
</li>
</ul>
<a name="setNewWallPattern-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWallPattern</h4>
<pre>public&nbsp;void&nbsp;setNewWallPattern(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;newWallPattern)</pre>
<div class="block">Sets the edited new wall top pattern in plan, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getNewWallPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewWallPattern</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;getNewWallPattern()</pre>
<div class="block">Returns the edited new wall top pattern in plan.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setNewWallThickness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWallThickness</h4>
<pre>public&nbsp;void&nbsp;setNewWallThickness(float&nbsp;newWallThickness)</pre>
<div class="block">Sets the edited new wall thickness.</div>
</li>
</ul>
<a name="getNewWallThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewWallThickness</h4>
<pre>public&nbsp;float&nbsp;getNewWallThickness()</pre>
<div class="block">Returns the edited new wall thickness.</div>
</li>
</ul>
<a name="setNewWallHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWallHeight</h4>
<pre>public&nbsp;void&nbsp;setNewWallHeight(float&nbsp;newWallHeight)</pre>
<div class="block">Sets the edited new wall height.</div>
</li>
</ul>
<a name="getNewWallHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewWallHeight</h4>
<pre>public&nbsp;float&nbsp;getNewWallHeight()</pre>
<div class="block">Returns the edited new wall height.</div>
</li>
</ul>
<a name="setNewFloorThickness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewFloorThickness</h4>
<pre>public&nbsp;void&nbsp;setNewFloorThickness(float&nbsp;newFloorThickness)</pre>
<div class="block">Sets the edited new floor thickness.</div>
</li>
</ul>
<a name="getNewFloorThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewFloorThickness</h4>
<pre>public&nbsp;float&nbsp;getNewFloorThickness()</pre>
<div class="block">Returns the edited new floor thickness.</div>
</li>
</ul>
<a name="setCheckUpdatesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCheckUpdatesEnabled</h4>
<pre>public&nbsp;void&nbsp;setCheckUpdatesEnabled(boolean&nbsp;updatesChecked)</pre>
<div class="block">Sets whether updates should be checked or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="isCheckUpdatesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCheckUpdatesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isCheckUpdatesEnabled()</pre>
<div class="block">Returns <code>true</code> if updates should be checked.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setAutoSaveDelayForRecovery-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoSaveDelayForRecovery</h4>
<pre>public&nbsp;void&nbsp;setAutoSaveDelayForRecovery(int&nbsp;autoSaveDelayForRecovery)</pre>
<div class="block">Sets the edited auto recovery save delay.</div>
</li>
</ul>
<a name="getAutoSaveDelayForRecovery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoSaveDelayForRecovery</h4>
<pre>public&nbsp;int&nbsp;getAutoSaveDelayForRecovery()</pre>
<div class="block">Returns the edited auto recovery save delay.</div>
</li>
</ul>
<a name="setAutoSaveForRecoveryEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoSaveForRecoveryEnabled</h4>
<pre>public&nbsp;void&nbsp;setAutoSaveForRecoveryEnabled(boolean&nbsp;autoSaveForRecoveryEnabled)</pre>
<div class="block">Sets whether auto recovery save is enabled or not.</div>
</li>
</ul>
<a name="isAutoSaveForRecoveryEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAutoSaveForRecoveryEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isAutoSaveForRecoveryEnabled()</pre>
<div class="block">Returns <code>true</code> if auto recovery save is enabled.</div>
</li>
</ul>
<a name="checkUpdates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>checkUpdates</h4>
<pre>public&nbsp;void&nbsp;checkUpdates()</pre>
<div class="block">Checks if some updates are available.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="mayImportLanguageLibrary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mayImportLanguageLibrary</h4>
<pre>public&nbsp;boolean&nbsp;mayImportLanguageLibrary()</pre>
<div class="block">Returns <code>true</code> if language libraries can be imported.</div>
</li>
</ul>
<a name="importLanguageLibrary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importLanguageLibrary</h4>
<pre>public&nbsp;void&nbsp;importLanguageLibrary()</pre>
<div class="block">Imports a language library chosen by the user.</div>
</li>
</ul>
<a name="resetDisplayedActionTips--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetDisplayedActionTips</h4>
<pre>public&nbsp;void&nbsp;resetDisplayedActionTips()</pre>
<div class="block">Resets the displayed flags of action tips.</div>
</li>
</ul>
<a name="modifyUserPreferences--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>modifyUserPreferences</h4>
<pre>public&nbsp;void&nbsp;modifyUserPreferences()</pre>
<div class="block">Controls the modification of user preferences.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UserPreferencesController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/UserPreferencesController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/UserPreferencesController.html" target="_top">Frames</a></li>
<li><a href="UserPreferencesController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
