<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>BaseboardChoiceController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="BaseboardChoiceController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BaseboardChoiceController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" target="_top">Frames</a></li>
<li><a href="BaseboardChoiceController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class BaseboardChoiceController" class="title">Class BaseboardChoiceController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">BaseboardChoiceController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for baseboard choice view.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.BaseboardPaint</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getPaint--">paint type</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#BaseboardChoiceController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">BaseboardChoiceController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates the controller of room view with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the edited color of the baseboard.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the edited height of the baseboard.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getMaxHeight--">getMaxHeight</a></span>()</code>
<div class="block">Returns the maximum height allowed for the edited baseboard.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.BaseboardPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getPaint--">getPaint</a></span>()</code>
<div class="block">Returns whether the baseboard side is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getTextureController--">getTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the baseboard.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getThickness--">getThickness</a></span>()</code>
<div class="block">Returns the edited thickness of the baseboard.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#getVisible--">getVisible</a></span>()</code>
<div class="block">Returns <code>true</code> if the baseboard should be visible.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#setBaseboard-com.eteks.sweethome3d.model.Baseboard-">setBaseboard</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;baseboard)</code>
<div class="block">Set controller properties from the given <code>baseboard</code>.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;baseboardColor)</code>
<div class="block">Sets the edited color of the baseboard.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#setHeight-java.lang.Float-">setHeight</a></span>(java.lang.Float&nbsp;baseboardHeight)</code>
<div class="block">Sets the edited height of the baseboard.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#setMaxHeight-java.lang.Float-">setMaxHeight</a></span>(java.lang.Float&nbsp;maxHeight)</code>
<div class="block">Sets the maximum height allowed for the edited baseboard.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#setPaint-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController.BaseboardPaint-">setPaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.BaseboardPaint</a>&nbsp;baseboardPaint)</code>
<div class="block">Sets whether the baseboard is as its wall, colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#setThickness-java.lang.Float-">setThickness</a></span>(java.lang.Float&nbsp;baseboardThickness)</code>
<div class="block">Sets the edited thickness of the baseboard.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html#setVisible-java.lang.Boolean-">setVisible</a></span>(java.lang.Boolean&nbsp;baseboardVisible)</code>
<div class="block">Sets whether the baseboard should be visible.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="BaseboardChoiceController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>BaseboardChoiceController</h4>
<pre>public&nbsp;BaseboardChoiceController(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
<div class="block">Creates the controller of room view with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getTextureController()</pre>
<div class="block">Returns the texture controller of the baseboard.</div>
</li>
</ul>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="getVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVisible</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getVisible()</pre>
<div class="block">Returns <code>true</code> if the baseboard should be visible.</div>
</li>
</ul>
<a name="setVisible-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>public&nbsp;void&nbsp;setVisible(java.lang.Boolean&nbsp;baseboardVisible)</pre>
<div class="block">Sets whether the baseboard should be visible.</div>
</li>
</ul>
<a name="setThickness-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThickness</h4>
<pre>public&nbsp;void&nbsp;setThickness(java.lang.Float&nbsp;baseboardThickness)</pre>
<div class="block">Sets the edited thickness of the baseboard.</div>
</li>
</ul>
<a name="getThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThickness</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getThickness()</pre>
<div class="block">Returns the edited thickness of the baseboard.</div>
</li>
</ul>
<a name="setHeight-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public&nbsp;void&nbsp;setHeight(java.lang.Float&nbsp;baseboardHeight)</pre>
<div class="block">Sets the edited height of the baseboard.</div>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getHeight()</pre>
<div class="block">Returns the edited height of the baseboard.</div>
</li>
</ul>
<a name="setMaxHeight-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMaxHeight</h4>
<pre>public&nbsp;void&nbsp;setMaxHeight(java.lang.Float&nbsp;maxHeight)</pre>
<div class="block">Sets the maximum height allowed for the edited baseboard.</div>
</li>
</ul>
<a name="getMaxHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaxHeight</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getMaxHeight()</pre>
<div class="block">Returns the maximum height allowed for the edited baseboard.</div>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;baseboardColor)</pre>
<div class="block">Sets the edited color of the baseboard.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the edited color of the baseboard.</div>
</li>
</ul>
<a name="setPaint-com.eteks.sweethome3d.viewcontroller.BaseboardChoiceController.BaseboardPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPaint</h4>
<pre>public&nbsp;void&nbsp;setPaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.BaseboardPaint</a>&nbsp;baseboardPaint)</pre>
<div class="block">Sets whether the baseboard is as its wall, colored, textured or unknown painted.</div>
</li>
</ul>
<a name="getPaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController.BaseboardPaint</a>&nbsp;getPaint()</pre>
<div class="block">Returns whether the baseboard side is colored, textured or unknown painted.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html#DEFAULT"><code>BaseboardChoiceController.BaseboardPaint.DEFAULT</code></a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html#COLORED"><code>BaseboardChoiceController.BaseboardPaint.COLORED</code></a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html#TEXTURED"><code>BaseboardChoiceController.BaseboardPaint.TEXTURED</code></a> or <code>null</code></dd>
</dl>
</li>
</ul>
<a name="setBaseboard-com.eteks.sweethome3d.model.Baseboard-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setBaseboard</h4>
<pre>public&nbsp;void&nbsp;setBaseboard(<a href="../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;baseboard)</pre>
<div class="block">Set controller properties from the given <code>baseboard</code>.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/BaseboardChoiceController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.BaseboardPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" target="_top">Frames</a></li>
<li><a href="BaseboardChoiceController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
