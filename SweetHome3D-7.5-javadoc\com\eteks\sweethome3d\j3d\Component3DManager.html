<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>Component3DManager (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Component3DManager (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Component3DManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingErrorObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/Component3DManager.html" target="_top">Frames</a></li>
<li><a href="Component3DManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class Component3DManager" class="title">Class Component3DManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.Component3DManager</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Component3DManager</span>
extends java.lang.Object</pre>
<div class="block">Manager of <code>Canvas3D</code> instantiations and Java 3D error listeners.
 Note: this class is compatible with Java 3D 1.3 at runtime but requires Java 3D 1.5 to compile.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingErrorObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingErrorObserver</a></span></code>
<div class="block">An observer that receives error notifications in Java 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingObserver</a></span></code>
<div class="block">An observer that receives notifications during the different steps
 of the loop rendering a canvas 3D.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#getDepthSize--">getDepthSize</a></span>()</code>
<div class="block">Returns the depth bits size of the Z-buffer.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html" title="class in com.eteks.sweethome3d.j3d">Component3DManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#getInstance--">getInstance</a></span>()</code>
<div class="block">Returns an instance of this singleton.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>javax.media.j3d.Canvas3D</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#getOffScreenCanvas3D-int-int-">getOffScreenCanvas3D</a></span>(int&nbsp;width,
                    int&nbsp;height)</code>
<div class="block">Returns a new off screen <code>canva3D</code> at the given size.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#getOffScreenImage-javax.media.j3d.View-int-int-">getOffScreenImage</a></span>(javax.media.j3d.View&nbsp;view,
                 int&nbsp;width,
                 int&nbsp;height)</code>
<div class="block">Returns an image at the given size of the 3D <code>view</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>javax.media.j3d.Canvas3D</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#getOnscreenCanvas3D--">getOnscreenCanvas3D</a></span>()</code>
<div class="block">Returns a new on screen <code>canva3D</code> instance.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>javax.media.j3d.Canvas3D</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#getOnscreenCanvas3D-com.eteks.sweethome3d.j3d.Component3DManager.RenderingObserver-">getOnscreenCanvas3D</a></span>(<a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingObserver</a>&nbsp;renderingObserver)</code>
<div class="block">Returns a new on screen <code>canva3D</code> instance which rendering will be observed
 with the given rendering observer.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>javax.media.j3d.Canvas3D</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#getOnscreenCanvas3D-java.awt.GraphicsConfiguration-com.eteks.sweethome3d.j3d.Component3DManager.RenderingObserver-">getOnscreenCanvas3D</a></span>(java.awt.GraphicsConfiguration&nbsp;deviceConfiguration,
                   <a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingObserver</a>&nbsp;renderingObserver)</code>
<div class="block">Returns a new on screen <code>canva3D</code> instance which rendering will be observed
 with the given rendering observer.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingErrorObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingErrorObserver</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#getRenderingErrorObserver--">getRenderingErrorObserver</a></span>()</code>
<div class="block">Returns the current rendering error listener bound to <code>VirtualUniverse</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#isOffScreenImageSupported--">isOffScreenImageSupported</a></span>()</code>
<div class="block">Returns <code>true</code> if offscreen is supported in Java 3D on user system.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#setRenderingErrorObserver-com.eteks.sweethome3d.j3d.Component3DManager.RenderingErrorObserver-">setRenderingErrorObserver</a></span>(<a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingErrorObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingErrorObserver</a>&nbsp;observer)</code>
<div class="block">Sets the current rendering error listener bound to <code>VirtualUniverse</code>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDepthSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepthSize</h4>
<pre>public&nbsp;int&nbsp;getDepthSize()</pre>
<div class="block">Returns the depth bits size of the Z-buffer.</div>
</li>
</ul>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html" title="class in com.eteks.sweethome3d.j3d">Component3DManager</a>&nbsp;getInstance()</pre>
<div class="block">Returns an instance of this singleton.</div>
</li>
</ul>
<a name="setRenderingErrorObserver-com.eteks.sweethome3d.j3d.Component3DManager.RenderingErrorObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRenderingErrorObserver</h4>
<pre>public&nbsp;void&nbsp;setRenderingErrorObserver(<a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingErrorObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingErrorObserver</a>&nbsp;observer)</pre>
<div class="block">Sets the current rendering error listener bound to <code>VirtualUniverse</code>.</div>
</li>
</ul>
<a name="getRenderingErrorObserver--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRenderingErrorObserver</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingErrorObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingErrorObserver</a>&nbsp;getRenderingErrorObserver()</pre>
<div class="block">Returns the current rendering error listener bound to <code>VirtualUniverse</code>.</div>
</li>
</ul>
<a name="isOffScreenImageSupported--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOffScreenImageSupported</h4>
<pre>public&nbsp;boolean&nbsp;isOffScreenImageSupported()</pre>
<div class="block">Returns <code>true</code> if offscreen is supported in Java 3D on user system.
 Will always return <code>false</code> if <code>com.eteks.sweethome3d.j3d.checkOffScreenSupport</code>
 system is equal to <code>false</code>. By default, <code>com.eteks.sweethome3d.j3d.checkOffScreenSupport</code>
 is equal to <code>true</code>.</div>
</li>
</ul>
<a name="getOnscreenCanvas3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOnscreenCanvas3D</h4>
<pre>public&nbsp;javax.media.j3d.Canvas3D&nbsp;getOnscreenCanvas3D()</pre>
<div class="block">Returns a new on screen <code>canva3D</code> instance. The returned canvas 3D will be associated
 with the graphics configuration of the default screen device.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>javax.media.j3d.IllegalRenderingStateException</code> - if the canvas 3D couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="getOnscreenCanvas3D-com.eteks.sweethome3d.j3d.Component3DManager.RenderingObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOnscreenCanvas3D</h4>
<pre>public&nbsp;javax.media.j3d.Canvas3D&nbsp;getOnscreenCanvas3D(<a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingObserver</a>&nbsp;renderingObserver)</pre>
<div class="block">Returns a new on screen <code>canva3D</code> instance which rendering will be observed
 with the given rendering observer. The returned canvas 3D will be associated with the
 graphics configuration of the default screen device.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>renderingObserver</code> - an observer of the 3D rendering process of the returned canvas.
            Caution: The methods of the observer will be called in 3D rendering loop thread.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>javax.media.j3d.IllegalRenderingStateException</code> - if the canvas 3D couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="getOnscreenCanvas3D-java.awt.GraphicsConfiguration-com.eteks.sweethome3d.j3d.Component3DManager.RenderingObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOnscreenCanvas3D</h4>
<pre>public&nbsp;javax.media.j3d.Canvas3D&nbsp;getOnscreenCanvas3D(java.awt.GraphicsConfiguration&nbsp;deviceConfiguration,
                                                    <a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingObserver.html" title="interface in com.eteks.sweethome3d.j3d">Component3DManager.RenderingObserver</a>&nbsp;renderingObserver)</pre>
<div class="block">Returns a new on screen <code>canva3D</code> instance which rendering will be observed
 with the given rendering observer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>renderingObserver</code> - an observer of the 3D rendering process of the returned canvas.
            Caution: The methods of the observer will be called in 3D rendering loop thread.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>javax.media.j3d.IllegalRenderingStateException</code> - if the canvas 3D couldn't be created.</dd>
</dl>
</li>
</ul>
<a name="getOffScreenCanvas3D-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOffScreenCanvas3D</h4>
<pre>public&nbsp;javax.media.j3d.Canvas3D&nbsp;getOffScreenCanvas3D(int&nbsp;width,
                                                     int&nbsp;height)</pre>
<div class="block">Returns a new off screen <code>canva3D</code> at the given size.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>javax.media.j3d.IllegalRenderingStateException</code> - if the canvas 3D couldn't be created.
    To avoid this exception, call <a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.html#isOffScreenImageSupported--"><code>isOffScreenImageSupported()</code></a> first.</dd>
</dl>
</li>
</ul>
<a name="getOffScreenImage-javax.media.j3d.View-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getOffScreenImage</h4>
<pre>public&nbsp;java.awt.image.BufferedImage&nbsp;getOffScreenImage(javax.media.j3d.View&nbsp;view,
                                                      int&nbsp;width,
                                                      int&nbsp;height)</pre>
<div class="block">Returns an image at the given size of the 3D <code>view</code>.
 This image is created with an off screen canvas.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>javax.media.j3d.IllegalRenderingStateException</code> - if the image couldn't be created.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Component3DManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/Component3DManager.RenderingErrorObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/Component3DManager.html" target="_top">Frames</a></li>
<li><a href="Component3DManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
