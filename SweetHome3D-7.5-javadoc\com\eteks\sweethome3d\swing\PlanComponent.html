<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:50 CEST 2024 -->
<title>PlanComponent (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PlanComponent (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":42,"i42":10,"i43":10,"i44":10,"i45":10,"i46":42,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanComponent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/PhotosPanel.LanguageChangeListener.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/PlanComponent.html" target="_top">Frames</a></li>
<li><a href="PlanComponent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class PlanComponent" class="title">Class PlanComponent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>javax.swing.JComponent</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.PlanComponent</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>, java.awt.image.ImageObserver, java.awt.MenuContainer, java.awt.print.Printable, java.io.Serializable, javax.swing.Scrollable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">PlanComponent</span>
extends javax.swing.JComponent
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a>, javax.swing.Scrollable, java.awt.print.Printable</pre>
<div class="block">A component displaying the plan of a home.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.PlanComponent">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></span></code>
<div class="block">Indicator types that may be displayed on selected items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a></span></code>
<div class="block">The circumstances under which the home items displayed by this component will be painted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PlanRulerComponent.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.PlanRulerComponent</a></span></code>
<div class="block">A component displaying the plan horizontal or vertical ruler associated to this plan.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JComponent</h3>
<code>javax.swing.JComponent.AccessibleJComponent</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.PlanView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.TransferableView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView.TransferObserver</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.ExportableView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JComponent</h3>
<code>listenerList, TOOL_TIP_TEXT_KEY, ui, UNDEFINED_CONDITION, WHEN_ANCESTOR_OF_FOCUSED_COMPONENT, WHEN_FOCUSED, WHEN_IN_FOCUSED_WINDOW</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>accessibleContext, BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.print.Printable">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.print.Printable</h3>
<code>NO_SUCH_PAGE, PAGE_EXISTS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#PlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.PlanController-">PlanComponent</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
             <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
             <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
             <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</code>
<div class="block">Creates a new plan that displays <code>home</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#PlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">PlanComponent</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
             <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
             <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</code>
<div class="block">Creates a new plan that displays <code>home</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#canImportDraggedItems-java.util.List-int-int-">canImportDraggedItems</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                     int&nbsp;x,
                     int&nbsp;y)</code>
<div class="block">Returns <code>true</code>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#convertXModelToScreen-float-">convertXModelToScreen</a></span>(float&nbsp;x)</code>
<div class="block">Returns <code>x</code> converted in screen coordinates space.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#convertXPixelToModel-int-">convertXPixelToModel</a></span>(int&nbsp;x)</code>
<div class="block">Returns <code>x</code> converted in model coordinates space.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#convertYModelToScreen-float-">convertYModelToScreen</a></span>(float&nbsp;y)</code>
<div class="block">Returns <code>y</code> converted in screen coordinates space.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#convertYPixelToModel-int-">convertYPixelToModel</a></span>(int&nbsp;y)</code>
<div class="block">Returns <code>y</code> converted in model coordinates space.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected java.awt.Cursor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#createCustomCursor-java.net.URL-java.net.URL-float-float-java.lang.String-java.awt.Cursor-">createCustomCursor</a></span>(java.net.URL&nbsp;smallCursorImageUrl,
                  java.net.URL&nbsp;largeCursorImageUrl,
                  float&nbsp;xCursorHotSpot,
                  float&nbsp;yCursorHotSpot,
                  java.lang.String&nbsp;cursorName,
                  java.awt.Cursor&nbsp;defaultCursor)</code>
<div class="block">Returns a custom cursor created from images in parameters.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">createTransferData</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>&nbsp;dataType)</code>
<div class="block">Returns an image of selected items in plan for transfer purpose.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#deleteFeedback--">deleteFeedback</a></span>()</code>
<div class="block">Deletes all elements shown as feedback.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#deleteToolTipFeedback--">deleteToolTipFeedback</a></span>()</code>
<div class="block">Deletes tool tip text from screen.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">exportData</a></span>(java.io.OutputStream&nbsp;out,
          <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType,
          java.util.Properties&nbsp;settings)</code>
<div class="block">Writes this plan in the given output stream at SVG (Scalable Vector Graphics) format if this is the requested format.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#exportToSVG-java.io.OutputStream-">exportToSVG</a></span>(java.io.OutputStream&nbsp;out)</code>
<div class="block">Writes this plan in the given output stream at SVG (Scalable Vector Graphics) format.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>protected java.awt.Color</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getBackgroundColor-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-">getBackgroundColor</a></span>(<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>&nbsp;mode)</code>
<div class="block">Returns the background color used to draw content.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getClipboardImage--">getClipboardImage</a></span>()</code>
<div class="block">Returns an image of the selected items displayed by this component
 (camera excepted) with no outline at scale 1/1 (1 pixel = 1cm)
 or at a smaller scale if image is larger than 100m x 100m
 or if free memory is missing.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected java.awt.Font</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getFont-java.awt.Font-com.eteks.sweethome3d.model.TextStyle-">getFont</a></span>(java.awt.Font&nbsp;defaultFont,
       <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle)</code>
<div class="block">Returns the AWT font matching a given text style.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>protected java.awt.FontMetrics</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getFontMetrics-java.awt.Font-com.eteks.sweethome3d.model.TextStyle-">getFontMetrics</a></span>(java.awt.Font&nbsp;defaultFont,
              <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle)</code>
<div class="block">Returns the font metrics matching a given text style.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>protected java.awt.Color</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getForegroundColor-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-">getForegroundColor</a></span>(<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>&nbsp;mode)</code>
<div class="block">Returns the foreground color used to draw content.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>protected java.awt.Color</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getFurnitureOutlineColor--">getFurnitureOutlineColor</a></span>()</code>
<div class="block">Returns the color used to draw furniture outline of
 the shape where a user can click to select a piece of furniture.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getHorizontalRuler--">getHorizontalRuler</a></span>()</code>
<div class="block">Returns the component used as an horizontal ruler for this plan.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>protected java.awt.Shape</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getIndicator-com.eteks.sweethome3d.model.Selectable-com.eteks.sweethome3d.swing.PlanComponent.IndicatorType-">getIndicator</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
            <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a>&nbsp;indicatorType)</code>
<div class="block">Returns the shape of the given indicator type.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>protected java.awt.geom.Rectangle2D</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getItemBounds-java.awt.Graphics-com.eteks.sweethome3d.model.Selectable-">getItemBounds</a></span>(java.awt.Graphics&nbsp;g,
             <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</code>
<div class="block">Returns the bounds of the given <code>item</code>.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPaintedItems--">getPaintedItems</a></span>()</code>
<div class="block">Returns the collection of walls, furniture, rooms and dimension lines of the home
 painted by this component wherever the level they belong to is selected or not.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getPieceOfFurnitureSizeInPlan</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan,
 or <code>null</code> if the view isn't able to compute such a value.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPixelLength--">getPixelLength</a></span>()</code>
<div class="block">Returns the length in centimeters of a pixel with the current scale.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPreferredScrollableViewportSize--">getPreferredScrollableViewportSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPreferredSize--">getPreferredSize</a></span>()</code>
<div class="block">Returns the preferred size of this component.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPrintPreferredScale-float-float-">getPrintPreferredScale</a></span>(float&nbsp;preferredWidth,
                      float&nbsp;preferredHeight)</code>
<div class="block">Returns the preferred scale to ensure it can be fully printed on the given print zone.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getPrintPreferredScale-java.awt.Graphics-java.awt.print.PageFormat-">getPrintPreferredScale</a></span>(java.awt.Graphics&nbsp;g,
                      java.awt.print.PageFormat&nbsp;pageFormat)</code>
<div class="block">Returns the print preferred scale of the plan drawn in this component
 to make it fill <code>pageFormat</code> imageable size.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getScale--">getScale</a></span>()</code>
<div class="block">Returns the scale used to display the plan.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getScrollableBlockIncrement-java.awt.Rectangle-int-int-">getScrollableBlockIncrement</a></span>(java.awt.Rectangle&nbsp;visibleRect,
                           int&nbsp;orientation,
                           int&nbsp;direction)</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getScrollableTracksViewportHeight--">getScrollableTracksViewportHeight</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getScrollableTracksViewportWidth--">getScrollableTracksViewportWidth</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getScrollableUnitIncrement-java.awt.Rectangle-int-int-">getScrollableUnitIncrement</a></span>(java.awt.Rectangle&nbsp;visibleRect,
                          int&nbsp;orientation,
                          int&nbsp;direction)</code>&nbsp;</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>protected java.awt.Color</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getSelectionColor--">getSelectionColor</a></span>()</code>
<div class="block">Returns the color used to draw selection outlines.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">getTextBounds</a></span>(java.lang.String&nbsp;text,
             <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
             float&nbsp;x,
             float&nbsp;y,
             float&nbsp;angle)</code>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> centered at
 the point (<code>x</code>,<code>y</code>).</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#getVerticalRuler--">getVerticalRuler</a></span>()</code>
<div class="block">Returns the component used as a vertical ruler for this plan.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#invalidate--">invalidate</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#isBackgroundPainted--">isBackgroundPainted</a></span>()</code>
<div class="block">Returns <code>true</code> if plan's background should be painted.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">isFormatTypeSupported</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType)</code>
<div class="block">Returns <code>true</code> if the given format is SVG.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#isFurnitureSizeInPlanSupported--">isFurnitureSizeInPlanSupported</a></span>()</code>
<div class="block">Returns <code>true</code> if this component is able to compute the size of horizontally rotated furniture.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#isSelectedItemsOutlinePainted--">isSelectedItemsOutlinePainted</a></span>()</code>
<div class="block">Returns <code>true</code> if the outline of home selected items should be painted.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#isViewableAtLevel-com.eteks.sweethome3d.model.Elevatable-com.eteks.sweethome3d.model.Level-">isViewableAtLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>&nbsp;item,
                 <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Returns <code>true</code> if the given item can be viewed in the plan at a level.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#isViewableAtSelectedLevel-com.eteks.sweethome3d.model.Elevatable-">isViewableAtSelectedLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>&nbsp;item)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Override <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#isViewableAtLevel-com.eteks.sweethome3d.model.Elevatable-com.eteks.sweethome3d.model.Level-"><code>isViewableAtLevel(Elevatable, Level)</code></a> if you want to print different levels</span></div>
</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#makePointVisible-float-float-">makePointVisible</a></span>(float&nbsp;x,
                float&nbsp;y)</code>
<div class="block">Ensures the point at (<code>x</code>, <code>y</code>) is visible,
 moving scroll bars if needed.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#makeSelectionVisible--">makeSelectionVisible</a></span>()</code>
<div class="block">Ensures selected items are visible at screen and moves
 scroll bars if needed.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#moveView-float-float-">moveView</a></span>(float&nbsp;dx,
        float&nbsp;dy)</code>
<div class="block">Moves the view from (dx, dy) unit in the scrolling zone it belongs to.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#paintComponent-java.awt.Graphics-">paintComponent</a></span>(java.awt.Graphics&nbsp;g)</code>
<div class="block">Paints this component.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#paintHomeItems-java.awt.Graphics-float-java.awt.Color-java.awt.Color-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-">paintHomeItems</a></span>(java.awt.Graphics&nbsp;g,
              float&nbsp;planScale,
              java.awt.Color&nbsp;backgroundColor,
              java.awt.Color&nbsp;foregroundColor,
              <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>&nbsp;paintMode)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Override <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#paintHomeItems-java.awt.Graphics-com.eteks.sweethome3d.model.Level-float-java.awt.Color-java.awt.Color-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-"><code>paintHomeItems(Graphics, Level, float, Color, Color, PaintMode)</code></a> if you want to print different levels</span></div>
</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#paintHomeItems-java.awt.Graphics-com.eteks.sweethome3d.model.Level-float-java.awt.Color-java.awt.Color-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-">paintHomeItems</a></span>(java.awt.Graphics&nbsp;g,
              <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level,
              float&nbsp;planScale,
              java.awt.Color&nbsp;backgroundColor,
              java.awt.Color&nbsp;foregroundColor,
              <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>&nbsp;paintMode)</code>
<div class="block">Paints home items at the given scale, and with background and foreground colors.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#print-java.awt.Graphics-java.awt.print.PageFormat-int-">print</a></span>(java.awt.Graphics&nbsp;g,
     java.awt.print.PageFormat&nbsp;pageFormat,
     int&nbsp;pageIndex)</code>
<div class="block">Prints this component plan at the scale given in the home print attributes or at a scale
 that makes it fill <code>pageFormat</code> imageable size if this attribute is <code>null</code>.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#revalidate--">revalidate</a></span>()</code>
<div class="block">Revalidates and repaints this component and its rulers.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPointFeedback)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setAngleFeedback-float-float-float-float-float-float-">setAngleFeedback</a></span>(float&nbsp;xCenter,
                float&nbsp;yCenter,
                float&nbsp;x1,
                float&nbsp;y1,
                float&nbsp;x2,
                float&nbsp;y2)</code>
<div class="block">Sets the points used to draw an angle in plan view.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setBackgroundPainted-boolean-">setBackgroundPainted</a></span>(boolean&nbsp;backgroundPainted)</code>
<div class="block">Sets whether plan's background should be painted or not.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setCursor-com.eteks.sweethome3d.viewcontroller.PlanView.CursorType-">setCursor</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a>&nbsp;cursorType)</code>
<div class="block">Sets the cursor of this component.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setDimensionLinesFeedback-java.util.List-">setDimensionLinesFeedback</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;dimensionLines)</code>
<div class="block">Sets the given dimension lines to be drawn as feedback.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setDraggedItemsFeedback-java.util.List-">setDraggedItemsFeedback</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</code>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of plan view.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setRectangleFeedback-float-float-float-float-">setRectangleFeedback</a></span>(float&nbsp;x0,
                    float&nbsp;y0,
                    float&nbsp;x1,
                    float&nbsp;y1)</code>
<div class="block">Sets rectangle selection feedback coordinates.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setResizeIndicatorVisible-boolean-">setResizeIndicatorVisible</a></span>(boolean&nbsp;resizeIndicatorVisible)</code>
<div class="block">Sets whether the resize indicator of selected wall or piece of furniture
 should be visible or not.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setScale-float-">setScale</a></span>(float&nbsp;scale)</code>
<div class="block">Sets the scale used to display the plan.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setSelectedItemsOutlinePainted-boolean-">setSelectedItemsOutlinePainted</a></span>(boolean&nbsp;selectedItemsOutlinePainted)</code>
<div class="block">Sets whether the outline of home selected items should be painted or not.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setToolTipEditedProperties-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty:A-java.lang.Object:A-float-float-">setToolTipEditedProperties</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>[]&nbsp;toolTipEditedProperties,
                          java.lang.Object[]&nbsp;toolTipPropertyValues,
                          float&nbsp;x,
                          float&nbsp;y)</code>
<div class="block">Set tool tip edition.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setToolTipEditedPropertyValue-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">setToolTipEditedPropertyValue</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;toolTipEditedProperty,
                             java.lang.Object&nbsp;toolTipPropertyValue)</code>
<div class="block">Sets the value of a property edited in tool tip.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#setToolTipFeedback-java.lang.String-float-float-">setToolTipFeedback</a></span>(java.lang.String&nbsp;toolTipFeedback,
                  float&nbsp;x,
                  float&nbsp;y)</code>
<div class="block">Sets tool tip text displayed as feedback.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#validate--">validate</a></span>()</code>
<div class="block">Validates this component and updates viewport position if it's displayed in a scrolled pane.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JComponent</h3>
<code>addAncestorListener, addNotify, addVetoableChangeListener, computeVisibleRect, contains, createToolTip, disable, enable, firePropertyChange, firePropertyChange, firePropertyChange, fireVetoableChange, getActionForKeyStroke, getActionMap, getAlignmentX, getAlignmentY, getAncestorListeners, getAutoscrolls, getBaseline, getBaselineResizeBehavior, getBorder, getBounds, getClientProperty, getComponentGraphics, getComponentPopupMenu, getConditionForKeyStroke, getDebugGraphicsOptions, getDefaultLocale, getFontMetrics, getGraphics, getHeight, getInheritsPopupMenu, getInputMap, getInputMap, getInputVerifier, getInsets, getInsets, getListeners, getLocation, getMaximumSize, getMinimumSize, getNextFocusableComponent, getPopupLocation, getRegisteredKeyStrokes, getRootPane, getSize, getToolTipLocation, getToolTipText, getToolTipText, getTopLevelAncestor, getTransferHandler, getUIClassID, getVerifyInputWhenFocusTarget, getVetoableChangeListeners, getVisibleRect, getWidth, getX, getY, grabFocus, hide, isDoubleBuffered, isLightweightComponent, isManagingFocus, isOpaque, isOptimizedDrawingEnabled, isPaintingForPrint, isPaintingOrigin, isPaintingTile, isRequestFocusEnabled, isValidateRoot, paint, paintBorder, paintChildren, paintImmediately, paintImmediately, paramString, print, printAll, printBorder, printChildren, printComponent, processComponentKeyEvent, processKeyBinding, processKeyEvent, processMouseEvent, processMouseMotionEvent, putClientProperty, registerKeyboardAction, registerKeyboardAction, removeAncestorListener, removeNotify, removeVetoableChangeListener, repaint, repaint, requestDefaultFocus, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, resetKeyboardActions, reshape, scrollRectToVisible, setActionMap, setAlignmentX, setAlignmentY, setAutoscrolls, setBackground, setBorder, setComponentPopupMenu, setDebugGraphicsOptions, setDefaultLocale, setDoubleBuffered, setEnabled, setFocusTraversalKeys, setFont, setForeground, setInheritsPopupMenu, setInputMap, setInputVerifier, setMaximumSize, setMinimumSize, setNextFocusableComponent, setOpaque, setPreferredSize, setRequestFocusEnabled, setToolTipText, setTransferHandler, setUI, setVerifyInputWhenFocusTarget, setVisible, unregisterKeyboardAction, update, updateUI</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addImpl, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, doLayout, findComponentAt, findComponentAt, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getLayout, getMousePosition, insets, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paintComponents, preferredSize, printComponents, processContainerEvent, processEvent, remove, remove, removeAll, removeContainerListener, setComponentZOrder, setFocusCycleRoot, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setLayout, transferFocusDownCycle, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addFocusListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseListener, addMouseMotionListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, createImage, createImage, createVolatileImage, createVolatileImage, disableEvents, dispatchEvent, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getAccessibleContext, getBackground, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getCursor, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getForeground, getGraphicsConfiguration, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocale, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getToolkit, getTreeLock, gotFocus, handleEvent, hasFocus, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseListener, removeMouseMotionListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, resize, resize, setBounds, setBounds, setComponentOrientation, setCursor, setDropTarget, setFocusable, setFocusTraversalKeysEnabled, setIgnoreRepaint, setLocale, setLocation, setLocation, setName, setSize, setSize, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PlanComponent</h4>
<pre>public&nbsp;PlanComponent(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                     <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                     <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</pre>
<div class="block">Creates a new plan that displays <code>home</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home to display</dd>
<dd><code>preferences</code> - user preferences to retrieve used unit, grid visibility...</dd>
<dd><code>controller</code> - the optional controller used to manage home items modification</dd>
</dl>
</li>
</ul>
<a name="PlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.viewcontroller.PlanController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PlanComponent</h4>
<pre>public&nbsp;PlanComponent(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                     <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                     <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
                     <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</pre>
<div class="block">Creates a new plan that displays <code>home</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home to display</dd>
<dd><code>preferences</code> - user preferences to retrieve used unit, grid visibility...</dd>
<dd><code>object3dFactory</code> - a factory able to create 3D objects from <code>home</code> furniture.
            The <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html#createObject3D-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.Selectable-boolean-"><code>createObject3D</code></a> of
            this factory is expected to return an instance of <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html" title="class in com.eteks.sweethome3d.j3d"><code>Object3DBranch</code></a> in current implementation.</dd>
<dd><code>controller</code> - the optional controller used to manage home items modification</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="revalidate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>revalidate</h4>
<pre>public&nbsp;void&nbsp;revalidate()</pre>
<div class="block">Revalidates and repaints this component and its rulers.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>revalidate</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="invalidate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invalidate</h4>
<pre>public&nbsp;void&nbsp;invalidate()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>invalidate</code>&nbsp;in class&nbsp;<code>java.awt.Container</code></dd>
</dl>
</li>
</ul>
<a name="validate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>validate</h4>
<pre>public&nbsp;void&nbsp;validate()</pre>
<div class="block">Validates this component and updates viewport position if it's displayed in a scrolled pane.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>validate</code>&nbsp;in class&nbsp;<code>java.awt.Container</code></dd>
</dl>
</li>
</ul>
<a name="createCustomCursor-java.net.URL-java.net.URL-float-float-java.lang.String-java.awt.Cursor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCustomCursor</h4>
<pre>protected&nbsp;java.awt.Cursor&nbsp;createCustomCursor(java.net.URL&nbsp;smallCursorImageUrl,
                                             java.net.URL&nbsp;largeCursorImageUrl,
                                             float&nbsp;xCursorHotSpot,
                                             float&nbsp;yCursorHotSpot,
                                             java.lang.String&nbsp;cursorName,
                                             java.awt.Cursor&nbsp;defaultCursor)</pre>
<div class="block">Returns a custom cursor created from images in parameters.</div>
</li>
</ul>
<a name="getPreferredSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreferredSize</h4>
<pre>public&nbsp;java.awt.Dimension&nbsp;getPreferredSize()</pre>
<div class="block">Returns the preferred size of this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getPreferredSize</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="getPaintedItems--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPaintedItems</h4>
<pre>protected&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;getPaintedItems()</pre>
<div class="block">Returns the collection of walls, furniture, rooms and dimension lines of the home
 painted by this component wherever the level they belong to is selected or not.</div>
</li>
</ul>
<a name="getItemBounds-java.awt.Graphics-com.eteks.sweethome3d.model.Selectable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getItemBounds</h4>
<pre>protected&nbsp;java.awt.geom.Rectangle2D&nbsp;getItemBounds(java.awt.Graphics&nbsp;g,
                                                  <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item)</pre>
<div class="block">Returns the bounds of the given <code>item</code>.</div>
</li>
</ul>
<a name="getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextBounds</h4>
<pre>public&nbsp;float[][]&nbsp;getTextBounds(java.lang.String&nbsp;text,
                               <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
                               float&nbsp;x,
                               float&nbsp;y,
                               float&nbsp;angle)</pre>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> centered at
 the point (<code>x</code>,<code>y</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">getTextBounds</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getFont-java.awt.Font-com.eteks.sweethome3d.model.TextStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFont</h4>
<pre>protected&nbsp;java.awt.Font&nbsp;getFont(java.awt.Font&nbsp;defaultFont,
                                <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle)</pre>
<div class="block">Returns the AWT font matching a given text style.</div>
</li>
</ul>
<a name="getFontMetrics-java.awt.Font-com.eteks.sweethome3d.model.TextStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontMetrics</h4>
<pre>protected&nbsp;java.awt.FontMetrics&nbsp;getFontMetrics(java.awt.Font&nbsp;defaultFont,
                                              <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle)</pre>
<div class="block">Returns the font metrics matching a given text style.</div>
</li>
</ul>
<a name="setBackgroundPainted-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackgroundPainted</h4>
<pre>public&nbsp;void&nbsp;setBackgroundPainted(boolean&nbsp;backgroundPainted)</pre>
<div class="block">Sets whether plan's background should be painted or not.
 Background may include grid and an image.</div>
</li>
</ul>
<a name="isBackgroundPainted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackgroundPainted</h4>
<pre>public&nbsp;boolean&nbsp;isBackgroundPainted()</pre>
<div class="block">Returns <code>true</code> if plan's background should be painted.</div>
</li>
</ul>
<a name="setSelectedItemsOutlinePainted-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectedItemsOutlinePainted</h4>
<pre>public&nbsp;void&nbsp;setSelectedItemsOutlinePainted(boolean&nbsp;selectedItemsOutlinePainted)</pre>
<div class="block">Sets whether the outline of home selected items should be painted or not.</div>
</li>
</ul>
<a name="isSelectedItemsOutlinePainted--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSelectedItemsOutlinePainted</h4>
<pre>public&nbsp;boolean&nbsp;isSelectedItemsOutlinePainted()</pre>
<div class="block">Returns <code>true</code> if the outline of home selected items should be painted.</div>
</li>
</ul>
<a name="paintComponent-java.awt.Graphics-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paintComponent</h4>
<pre>protected&nbsp;void&nbsp;paintComponent(java.awt.Graphics&nbsp;g)</pre>
<div class="block">Paints this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>paintComponent</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="getPrintPreferredScale-java.awt.Graphics-java.awt.print.PageFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintPreferredScale</h4>
<pre>public&nbsp;float&nbsp;getPrintPreferredScale(java.awt.Graphics&nbsp;g,
                                    java.awt.print.PageFormat&nbsp;pageFormat)</pre>
<div class="block">Returns the print preferred scale of the plan drawn in this component
 to make it fill <code>pageFormat</code> imageable size.</div>
</li>
</ul>
<a name="getPrintPreferredScale-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintPreferredScale</h4>
<pre>public&nbsp;float&nbsp;getPrintPreferredScale(float&nbsp;preferredWidth,
                                    float&nbsp;preferredHeight)</pre>
<div class="block">Returns the preferred scale to ensure it can be fully printed on the given print zone.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPrintPreferredScale-float-float-">getPrintPreferredScale</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferredWidth</code> - width of the zone in cm</dd>
<dd><code>preferredHeight</code> - height of the zone in cm</dd>
</dl>
</li>
</ul>
<a name="print-java.awt.Graphics-java.awt.print.PageFormat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;int&nbsp;print(java.awt.Graphics&nbsp;g,
                 java.awt.print.PageFormat&nbsp;pageFormat,
                 int&nbsp;pageIndex)</pre>
<div class="block">Prints this component plan at the scale given in the home print attributes or at a scale
 that makes it fill <code>pageFormat</code> imageable size if this attribute is <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>print</code>&nbsp;in interface&nbsp;<code>java.awt.print.Printable</code></dd>
</dl>
</li>
</ul>
<a name="createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTransferData</h4>
<pre>public&nbsp;java.lang.Object&nbsp;createTransferData(<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>&nbsp;dataType)</pre>
<div class="block">Returns an image of selected items in plan for transfer purpose.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">createTransferData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></code></dd>
</dl>
</li>
</ul>
<a name="getClipboardImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClipboardImage</h4>
<pre>public&nbsp;java.awt.image.BufferedImage&nbsp;getClipboardImage()</pre>
<div class="block">Returns an image of the selected items displayed by this component
 (camera excepted) with no outline at scale 1/1 (1 pixel = 1cm)
 or at a smaller scale if image is larger than 100m x 100m
 or if free memory is missing.</div>
</li>
</ul>
<a name="isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFormatTypeSupported</h4>
<pre>public&nbsp;boolean&nbsp;isFormatTypeSupported(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType)</pre>
<div class="block">Returns <code>true</code> if the given format is SVG.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html#isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">isFormatTypeSupported</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></code></dd>
</dl>
</li>
</ul>
<a name="exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportData</h4>
<pre>public&nbsp;void&nbsp;exportData(java.io.OutputStream&nbsp;out,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType,
                       java.util.Properties&nbsp;settings)
                throws java.io.IOException</pre>
<div class="block">Writes this plan in the given output stream at SVG (Scalable Vector Graphics) format if this is the requested format.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html#exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">exportData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="exportToSVG-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToSVG</h4>
<pre>public&nbsp;void&nbsp;exportToSVG(java.io.OutputStream&nbsp;out)
                 throws java.io.IOException</pre>
<div class="block">Writes this plan in the given output stream at SVG (Scalable Vector Graphics) format.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getForegroundColor-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getForegroundColor</h4>
<pre>protected&nbsp;java.awt.Color&nbsp;getForegroundColor(<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>&nbsp;mode)</pre>
<div class="block">Returns the foreground color used to draw content.</div>
</li>
</ul>
<a name="getBackgroundColor-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBackgroundColor</h4>
<pre>protected&nbsp;java.awt.Color&nbsp;getBackgroundColor(<a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>&nbsp;mode)</pre>
<div class="block">Returns the background color used to draw content.</div>
</li>
</ul>
<a name="paintHomeItems-java.awt.Graphics-float-java.awt.Color-java.awt.Color-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paintHomeItems</h4>
<pre>protected&nbsp;void&nbsp;paintHomeItems(java.awt.Graphics&nbsp;g,
                              float&nbsp;planScale,
                              java.awt.Color&nbsp;backgroundColor,
                              java.awt.Color&nbsp;foregroundColor,
                              <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>&nbsp;paintMode)
                       throws java.io.InterruptedIOException</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Override <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#paintHomeItems-java.awt.Graphics-com.eteks.sweethome3d.model.Level-float-java.awt.Color-java.awt.Color-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-"><code>paintHomeItems(Graphics, Level, float, Color, Color, PaintMode)</code></a> if you want to print different levels</span></div>
<div class="block">Paints home items of the selected level at the given scale, and with background and foreground colors.
 Outline around selected items will be painted only under <code>PAINT</code> mode.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.InterruptedIOException</code></dd>
</dl>
</li>
</ul>
<a name="paintHomeItems-java.awt.Graphics-com.eteks.sweethome3d.model.Level-float-java.awt.Color-java.awt.Color-com.eteks.sweethome3d.swing.PlanComponent.PaintMode-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>paintHomeItems</h4>
<pre>protected&nbsp;void&nbsp;paintHomeItems(java.awt.Graphics&nbsp;g,
                              <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level,
                              float&nbsp;planScale,
                              java.awt.Color&nbsp;backgroundColor,
                              java.awt.Color&nbsp;foregroundColor,
                              <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a>&nbsp;paintMode)
                       throws java.io.InterruptedIOException</pre>
<div class="block">Paints home items at the given scale, and with background and foreground colors.
 Outline around selected items will be painted only under <code>PAINT</code> mode.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.InterruptedIOException</code></dd>
</dl>
</li>
</ul>
<a name="getSelectionColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectionColor</h4>
<pre>protected&nbsp;java.awt.Color&nbsp;getSelectionColor()</pre>
<div class="block">Returns the color used to draw selection outlines.</div>
</li>
</ul>
<a name="getFurnitureOutlineColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureOutlineColor</h4>
<pre>protected&nbsp;java.awt.Color&nbsp;getFurnitureOutlineColor()</pre>
<div class="block">Returns the color used to draw furniture outline of
 the shape where a user can click to select a piece of furniture.</div>
</li>
</ul>
<a name="getIndicator-com.eteks.sweethome3d.model.Selectable-com.eteks.sweethome3d.swing.PlanComponent.IndicatorType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIndicator</h4>
<pre>protected&nbsp;java.awt.Shape&nbsp;getIndicator(<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;item,
                                      <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a>&nbsp;indicatorType)</pre>
<div class="block">Returns the shape of the given indicator type.</div>
</li>
</ul>
<a name="isViewableAtLevel-com.eteks.sweethome3d.model.Elevatable-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isViewableAtLevel</h4>
<pre>protected&nbsp;boolean&nbsp;isViewableAtLevel(<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>&nbsp;item,
                                    <a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Returns <code>true</code> if the given item can be viewed in the plan at a level.</div>
</li>
</ul>
<a name="isViewableAtSelectedLevel-com.eteks.sweethome3d.model.Elevatable-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isViewableAtSelectedLevel</h4>
<pre>protected&nbsp;boolean&nbsp;isViewableAtSelectedLevel(<a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>&nbsp;item)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Override <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html#isViewableAtLevel-com.eteks.sweethome3d.model.Elevatable-com.eteks.sweethome3d.model.Level-"><code>isViewableAtLevel(Elevatable, Level)</code></a> if you want to print different levels</span></div>
<div class="block">Returns <code>true</code> if the given item can be viewed in the plan at the selected level.</div>
</li>
</ul>
<a name="setRectangleFeedback-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRectangleFeedback</h4>
<pre>public&nbsp;void&nbsp;setRectangleFeedback(float&nbsp;x0,
                                 float&nbsp;y0,
                                 float&nbsp;x1,
                                 float&nbsp;y1)</pre>
<div class="block">Sets rectangle selection feedback coordinates.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setRectangleFeedback-float-float-float-float-">setRectangleFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="makeSelectionVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makeSelectionVisible</h4>
<pre>public&nbsp;void&nbsp;makeSelectionVisible()</pre>
<div class="block">Ensures selected items are visible at screen and moves
 scroll bars if needed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#makeSelectionVisible--">makeSelectionVisible</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="makePointVisible-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makePointVisible</h4>
<pre>public&nbsp;void&nbsp;makePointVisible(float&nbsp;x,
                             float&nbsp;y)</pre>
<div class="block">Ensures the point at (<code>x</code>, <code>y</code>) is visible,
 moving scroll bars if needed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#makePointVisible-float-float-">makePointVisible</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="moveView-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveView</h4>
<pre>public&nbsp;void&nbsp;moveView(float&nbsp;dx,
                     float&nbsp;dy)</pre>
<div class="block">Moves the view from (dx, dy) unit in the scrolling zone it belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#moveView-float-float-">moveView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScale</h4>
<pre>public&nbsp;float&nbsp;getScale()</pre>
<div class="block">Returns the scale used to display the plan.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getScale--">getScale</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setScale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScale</h4>
<pre>public&nbsp;void&nbsp;setScale(float&nbsp;scale)</pre>
<div class="block">Sets the scale used to display the plan.
 If this component is displayed in a viewport the view position is updated
 to ensure the center's view will remain the same after the scale change.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setScale-float-">setScale</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="convertXPixelToModel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertXPixelToModel</h4>
<pre>public&nbsp;float&nbsp;convertXPixelToModel(int&nbsp;x)</pre>
<div class="block">Returns <code>x</code> converted in model coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertXPixelToModel-int-">convertXPixelToModel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="convertYPixelToModel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertYPixelToModel</h4>
<pre>public&nbsp;float&nbsp;convertYPixelToModel(int&nbsp;y)</pre>
<div class="block">Returns <code>y</code> converted in model coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertYPixelToModel-int-">convertYPixelToModel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="convertXModelToScreen-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertXModelToScreen</h4>
<pre>public&nbsp;int&nbsp;convertXModelToScreen(float&nbsp;x)</pre>
<div class="block">Returns <code>x</code> converted in screen coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertXModelToScreen-float-">convertXModelToScreen</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="convertYModelToScreen-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertYModelToScreen</h4>
<pre>public&nbsp;int&nbsp;convertYModelToScreen(float&nbsp;y)</pre>
<div class="block">Returns <code>y</code> converted in screen coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertYModelToScreen-float-">convertYModelToScreen</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getPixelLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPixelLength</h4>
<pre>public&nbsp;float&nbsp;getPixelLength()</pre>
<div class="block">Returns the length in centimeters of a pixel with the current scale.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPixelLength--">getPixelLength</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setCursor-com.eteks.sweethome3d.viewcontroller.PlanView.CursorType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCursor</h4>
<pre>public&nbsp;void&nbsp;setCursor(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a>&nbsp;cursorType)</pre>
<div class="block">Sets the cursor of this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setCursor-com.eteks.sweethome3d.viewcontroller.PlanView.CursorType-">setCursor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setToolTipFeedback-java.lang.String-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipFeedback</h4>
<pre>public&nbsp;void&nbsp;setToolTipFeedback(java.lang.String&nbsp;toolTipFeedback,
                               float&nbsp;x,
                               float&nbsp;y)</pre>
<div class="block">Sets tool tip text displayed as feedback.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipFeedback-java.lang.String-float-float-">setToolTipFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>toolTipFeedback</code> - the text displayed in the tool tip
                    or <code>null</code> to make tool tip disappear.</dd>
</dl>
</li>
</ul>
<a name="setToolTipEditedProperties-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty:A-java.lang.Object:A-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipEditedProperties</h4>
<pre>public&nbsp;void&nbsp;setToolTipEditedProperties(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>[]&nbsp;toolTipEditedProperties,
                                       java.lang.Object[]&nbsp;toolTipPropertyValues,
                                       float&nbsp;x,
                                       float&nbsp;y)</pre>
<div class="block">Set tool tip edition.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipEditedProperties-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty:A-java.lang.Object:A-float-float-">setToolTipEditedProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setToolTipEditedPropertyValue-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipEditedPropertyValue</h4>
<pre>public&nbsp;void&nbsp;setToolTipEditedPropertyValue(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;toolTipEditedProperty,
                                          java.lang.Object&nbsp;toolTipPropertyValue)</pre>
<div class="block">Sets the value of a property edited in tool tip.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipEditedPropertyValue-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">setToolTipEditedPropertyValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="deleteToolTipFeedback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteToolTipFeedback</h4>
<pre>public&nbsp;void&nbsp;deleteToolTipFeedback()</pre>
<div class="block">Deletes tool tip text from screen.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#deleteToolTipFeedback--">deleteToolTipFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setResizeIndicatorVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResizeIndicatorVisible</h4>
<pre>public&nbsp;void&nbsp;setResizeIndicatorVisible(boolean&nbsp;resizeIndicatorVisible)</pre>
<div class="block">Sets whether the resize indicator of selected wall or piece of furniture
 should be visible or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setResizeIndicatorVisible-boolean-">setResizeIndicatorVisible</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignmentFeedback</h4>
<pre>public&nbsp;void&nbsp;setAlignmentFeedback(java.lang.Class&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                                 <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                                 float&nbsp;x,
                                 float&nbsp;y,
                                 boolean&nbsp;showPointFeedback)</pre>
<div class="block">Sets the location point for alignment feedback.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setAngleFeedback-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngleFeedback</h4>
<pre>public&nbsp;void&nbsp;setAngleFeedback(float&nbsp;xCenter,
                             float&nbsp;yCenter,
                             float&nbsp;x1,
                             float&nbsp;y1,
                             float&nbsp;x2,
                             float&nbsp;y2)</pre>
<div class="block">Sets the points used to draw an angle in plan view.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setAngleFeedback-float-float-float-float-float-float-">setAngleFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setDraggedItemsFeedback-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDraggedItemsFeedback</h4>
<pre>public&nbsp;void&nbsp;setDraggedItemsFeedback(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</pre>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of plan view.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setDraggedItemsFeedback-java.util.List-">setDraggedItemsFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setDimensionLinesFeedback-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDimensionLinesFeedback</h4>
<pre>public&nbsp;void&nbsp;setDimensionLinesFeedback(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;dimensionLines)</pre>
<div class="block">Sets the given dimension lines to be drawn as feedback.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setDimensionLinesFeedback-java.util.List-">setDimensionLinesFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="deleteFeedback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteFeedback</h4>
<pre>public&nbsp;void&nbsp;deleteFeedback()</pre>
<div class="block">Deletes all elements shown as feedback.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#deleteFeedback--">deleteFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="canImportDraggedItems-java.util.List-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>canImportDraggedItems</h4>
<pre>public&nbsp;boolean&nbsp;canImportDraggedItems(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                                     int&nbsp;x,
                                     int&nbsp;y)</pre>
<div class="block">Returns <code>true</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#canImportDraggedItems-java.util.List-int-int-">canImportDraggedItems</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureSizeInPlan</h4>
<pre>public&nbsp;float[]&nbsp;getPieceOfFurnitureSizeInPlan(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan,
 or <code>null</code> if the view isn't able to compute such a value.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getPieceOfFurnitureSizeInPlan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="isFurnitureSizeInPlanSupported--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFurnitureSizeInPlanSupported</h4>
<pre>public&nbsp;boolean&nbsp;isFurnitureSizeInPlanSupported()</pre>
<div class="block">Returns <code>true</code> if this component is able to compute the size of horizontally rotated furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#isFurnitureSizeInPlanSupported--">isFurnitureSizeInPlanSupported</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getPreferredScrollableViewportSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreferredScrollableViewportSize</h4>
<pre>public&nbsp;java.awt.Dimension&nbsp;getPreferredScrollableViewportSize()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getPreferredScrollableViewportSize</code>&nbsp;in interface&nbsp;<code>javax.swing.Scrollable</code></dd>
</dl>
</li>
</ul>
<a name="getScrollableBlockIncrement-java.awt.Rectangle-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScrollableBlockIncrement</h4>
<pre>public&nbsp;int&nbsp;getScrollableBlockIncrement(java.awt.Rectangle&nbsp;visibleRect,
                                       int&nbsp;orientation,
                                       int&nbsp;direction)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getScrollableBlockIncrement</code>&nbsp;in interface&nbsp;<code>javax.swing.Scrollable</code></dd>
</dl>
</li>
</ul>
<a name="getScrollableTracksViewportHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScrollableTracksViewportHeight</h4>
<pre>public&nbsp;boolean&nbsp;getScrollableTracksViewportHeight()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getScrollableTracksViewportHeight</code>&nbsp;in interface&nbsp;<code>javax.swing.Scrollable</code></dd>
</dl>
</li>
</ul>
<a name="getScrollableTracksViewportWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScrollableTracksViewportWidth</h4>
<pre>public&nbsp;boolean&nbsp;getScrollableTracksViewportWidth()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getScrollableTracksViewportWidth</code>&nbsp;in interface&nbsp;<code>javax.swing.Scrollable</code></dd>
</dl>
</li>
</ul>
<a name="getScrollableUnitIncrement-java.awt.Rectangle-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScrollableUnitIncrement</h4>
<pre>public&nbsp;int&nbsp;getScrollableUnitIncrement(java.awt.Rectangle&nbsp;visibleRect,
                                      int&nbsp;orientation,
                                      int&nbsp;direction)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getScrollableUnitIncrement</code>&nbsp;in interface&nbsp;<code>javax.swing.Scrollable</code></dd>
</dl>
</li>
</ul>
<a name="getHorizontalRuler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHorizontalRuler</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getHorizontalRuler()</pre>
<div class="block">Returns the component used as an horizontal ruler for this plan.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getHorizontalRuler--">getHorizontalRuler</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getVerticalRuler--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getVerticalRuler</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getVerticalRuler()</pre>
<div class="block">Returns the component used as a vertical ruler for this plan.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getVerticalRuler--">getVerticalRuler</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PlanComponent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/PhotosPanel.LanguageChangeListener.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/PlanComponent.html" target="_top">Frames</a></li>
<li><a href="PlanComponent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
