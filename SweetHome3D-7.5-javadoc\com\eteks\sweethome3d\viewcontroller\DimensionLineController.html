<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>DimensionLineController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DimensionLineController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DimensionLineController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.DimensionLineOrientation.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" target="_top">Frames</a></li>
<li><a href="DimensionLineController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class DimensionLineController" class="title">Class DimensionLineController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.DimensionLineController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DimensionLineController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for dimension line view.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.DimensionLineOrientation.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.DimensionLineOrientation</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getOrientation--">dimension line type</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#DimensionLineController-com.eteks.sweethome3d.model.Home-float-float-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">DimensionLineController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       float&nbsp;x,
                       float&nbsp;y,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of dimension line view with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#DimensionLineController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">DimensionLineController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of dimension line view with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.DimensionLineController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#createDimensionLine--">createDimensionLine</a></span>()</code>
<div class="block">Controls the creation of a dimension line.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#createDimensionLine-float-float-float-float-float-float-float-">createDimensionLine</a></span>(float&nbsp;xStart,
                   float&nbsp;yStart,
                   float&nbsp;elevationStart,
                   float&nbsp;xEnd,
                   float&nbsp;yEnd,
                   float&nbsp;elevationEnd,
                   float&nbsp;offset)</code>
<div class="block">Returns a new dimension line instance added to home.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the edited color.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getDistanceToEndPoint--">getDistanceToEndPoint</a></span>()</code>
<div class="block">Returns the edited distance to end point.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getElevationEnd--">getElevationEnd</a></span>()</code>
<div class="block">Returns the edited elevation of the end point.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getElevationStart--">getElevationStart</a></span>()</code>
<div class="block">Returns the edited elevation of the start point.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getLengthFontSize--">getLengthFontSize</a></span>()</code>
<div class="block">Returns the edited font size.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getOffset--">getOffset</a></span>()</code>
<div class="block">Returns the edited offset.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.DimensionLineOrientation.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.DimensionLineOrientation</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getOrientation--">getOrientation</a></span>()</code>
<div class="block">Returns the edited orientation.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns the edited pitch.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getXEnd--">getXEnd</a></span>()</code>
<div class="block">Returns the edited abscissa of the end point.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getXStart--">getXStart</a></span>()</code>
<div class="block">Returns the edited abscissa of the start point.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getYEnd--">getYEnd</a></span>()</code>
<div class="block">Returns the edited ordinate of the end point.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#getYStart--">getYStart</a></span>()</code>
<div class="block">Returns the edited ordinate of the start point.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#isEditableDistance--">isEditableDistance</a></span>()</code>
<div class="block">Returns whether the distance can be be edited or not.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#isVisibleIn3D--">isVisibleIn3D</a></span>()</code>
<div class="block">Returns <code>Boolean.TRUE</code> if all edited dimension lines are viewed in 3D,
 or <code>Boolean.FALSE</code> if no dimension line is viewed in 3D.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#modifyDimensionLines--">modifyDimensionLines</a></span>()</code>
<div class="block">Controls the modification of selected dimension lines in edited home.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.DimensionLineController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the edited color.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setDistanceToEndPoint-java.lang.Float-">setDistanceToEndPoint</a></span>(java.lang.Float&nbsp;distanceToEndPoint)</code>
<div class="block">Sets the edited distance to end point.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setEditableDistance-boolean-">setEditableDistance</a></span>(boolean&nbsp;editableDistance)</code>
<div class="block">Sets whether the distance can be be edited or not.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setElevationEnd-java.lang.Float-">setElevationEnd</a></span>(java.lang.Float&nbsp;elevationEnd)</code>
<div class="block">Sets the edited elevation of the end point.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setElevationStart-java.lang.Float-">setElevationStart</a></span>(java.lang.Float&nbsp;elevationStart)</code>
<div class="block">Sets the edited elevation of the start point.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setLengthFontSize-java.lang.Float-">setLengthFontSize</a></span>(java.lang.Float&nbsp;lengthFontSize)</code>
<div class="block">Sets the edited font size.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setOffset-java.lang.Float-">setOffset</a></span>(java.lang.Float&nbsp;offset)</code>
<div class="block">Sets the edited offset.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setOrientation-com.eteks.sweethome3d.viewcontroller.DimensionLineController.DimensionLineOrientation-">setOrientation</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.DimensionLineOrientation.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.DimensionLineOrientation</a>&nbsp;orientation)</code>
<div class="block">Sets the edited orientation.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setPitch-java.lang.Float-">setPitch</a></span>(java.lang.Float&nbsp;pitch)</code>
<div class="block">Sets the edited pitch angle.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setVisibleIn3D-java.lang.Boolean-">setVisibleIn3D</a></span>(java.lang.Boolean&nbsp;visibleIn3D)</code>
<div class="block">Sets whether all edited dimension lines are viewed in 3D.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setXEnd-java.lang.Float-">setXEnd</a></span>(java.lang.Float&nbsp;xEnd)</code>
<div class="block">Sets the edited abscissa of the end point.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setXStart-java.lang.Float-">setXStart</a></span>(java.lang.Float&nbsp;xStart)</code>
<div class="block">Sets the edited abscissa of the start point.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setYEnd-java.lang.Float-">setYEnd</a></span>(java.lang.Float&nbsp;yEnd)</code>
<div class="block">Sets the edited ordinate of the end point.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#setYStart-java.lang.Float-">setYStart</a></span>(java.lang.Float&nbsp;yStart)</code>
<div class="block">Sets the edited ordinate of the start point.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates edited properties from selected dimension lines in the home edited by this controller.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DimensionLineController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DimensionLineController</h4>
<pre>public&nbsp;DimensionLineController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of dimension line view with undo support.</div>
</li>
</ul>
<a name="DimensionLineController-com.eteks.sweethome3d.model.Home-float-float-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DimensionLineController</h4>
<pre>public&nbsp;DimensionLineController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                               float&nbsp;x,
                               float&nbsp;y,
                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of dimension line view with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.DimensionLineController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.DimensionLineController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates edited properties from selected dimension lines in the home edited by this controller.</div>
</li>
</ul>
<a name="setXStart-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXStart</h4>
<pre>public&nbsp;void&nbsp;setXStart(java.lang.Float&nbsp;xStart)</pre>
<div class="block">Sets the edited abscissa of the start point.</div>
</li>
</ul>
<a name="getXStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXStart</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getXStart()</pre>
<div class="block">Returns the edited abscissa of the start point.</div>
</li>
</ul>
<a name="setYStart-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYStart</h4>
<pre>public&nbsp;void&nbsp;setYStart(java.lang.Float&nbsp;yStart)</pre>
<div class="block">Sets the edited ordinate of the start point.</div>
</li>
</ul>
<a name="getYStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYStart</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getYStart()</pre>
<div class="block">Returns the edited ordinate of the start point.</div>
</li>
</ul>
<a name="setElevationStart-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevationStart</h4>
<pre>public&nbsp;void&nbsp;setElevationStart(java.lang.Float&nbsp;elevationStart)</pre>
<div class="block">Sets the edited elevation of the start point.</div>
</li>
</ul>
<a name="getElevationStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevationStart</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getElevationStart()</pre>
<div class="block">Returns the edited elevation of the start point.</div>
</li>
</ul>
<a name="setXEnd-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setXEnd</h4>
<pre>public&nbsp;void&nbsp;setXEnd(java.lang.Float&nbsp;xEnd)</pre>
<div class="block">Sets the edited abscissa of the end point.</div>
</li>
</ul>
<a name="getXEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXEnd</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getXEnd()</pre>
<div class="block">Returns the edited abscissa of the end point.</div>
</li>
</ul>
<a name="setYEnd-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYEnd</h4>
<pre>public&nbsp;void&nbsp;setYEnd(java.lang.Float&nbsp;yEnd)</pre>
<div class="block">Sets the edited ordinate of the end point.</div>
</li>
</ul>
<a name="getYEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYEnd</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getYEnd()</pre>
<div class="block">Returns the edited ordinate of the end point.</div>
</li>
</ul>
<a name="setElevationEnd-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevationEnd</h4>
<pre>public&nbsp;void&nbsp;setElevationEnd(java.lang.Float&nbsp;elevationEnd)</pre>
<div class="block">Sets the edited elevation of the end point.</div>
</li>
</ul>
<a name="getElevationEnd--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevationEnd</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getElevationEnd()</pre>
<div class="block">Returns the edited elevation of the end point.</div>
</li>
</ul>
<a name="setDistanceToEndPoint-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDistanceToEndPoint</h4>
<pre>public&nbsp;void&nbsp;setDistanceToEndPoint(java.lang.Float&nbsp;distanceToEndPoint)</pre>
<div class="block">Sets the edited distance to end point.</div>
</li>
</ul>
<a name="getDistanceToEndPoint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDistanceToEndPoint</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getDistanceToEndPoint()</pre>
<div class="block">Returns the edited distance to end point.</div>
</li>
</ul>
<a name="setOrientation-com.eteks.sweethome3d.viewcontroller.DimensionLineController.DimensionLineOrientation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOrientation</h4>
<pre>public&nbsp;void&nbsp;setOrientation(<a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.DimensionLineOrientation.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.DimensionLineOrientation</a>&nbsp;orientation)</pre>
<div class="block">Sets the edited orientation.</div>
</li>
</ul>
<a name="getOrientation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOrientation</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.DimensionLineOrientation.html" title="enum in com.eteks.sweethome3d.viewcontroller">DimensionLineController.DimensionLineOrientation</a>&nbsp;getOrientation()</pre>
<div class="block">Returns the edited orientation.</div>
</li>
</ul>
<a name="setEditableDistance-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditableDistance</h4>
<pre>public&nbsp;void&nbsp;setEditableDistance(boolean&nbsp;editableDistance)</pre>
<div class="block">Sets whether the distance can be be edited or not.</div>
</li>
</ul>
<a name="isEditableDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditableDistance</h4>
<pre>public&nbsp;boolean&nbsp;isEditableDistance()</pre>
<div class="block">Returns whether the distance can be be edited or not.</div>
</li>
</ul>
<a name="setOffset-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOffset</h4>
<pre>public&nbsp;void&nbsp;setOffset(java.lang.Float&nbsp;offset)</pre>
<div class="block">Sets the edited offset.</div>
</li>
</ul>
<a name="getOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOffset</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getOffset()</pre>
<div class="block">Returns the edited offset.</div>
</li>
</ul>
<a name="setLengthFontSize-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLengthFontSize</h4>
<pre>public&nbsp;void&nbsp;setLengthFontSize(java.lang.Float&nbsp;lengthFontSize)</pre>
<div class="block">Sets the edited font size.</div>
</li>
</ul>
<a name="getLengthFontSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLengthFontSize</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getLengthFontSize()</pre>
<div class="block">Returns the edited font size.</div>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the edited color.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the edited color.</div>
</li>
</ul>
<a name="setVisibleIn3D-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisibleIn3D</h4>
<pre>public&nbsp;void&nbsp;setVisibleIn3D(java.lang.Boolean&nbsp;visibleIn3D)</pre>
<div class="block">Sets whether all edited dimension lines are viewed in 3D.</div>
</li>
</ul>
<a name="isVisibleIn3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVisibleIn3D</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;isVisibleIn3D()</pre>
<div class="block">Returns <code>Boolean.TRUE</code> if all edited dimension lines are viewed in 3D,
 or <code>Boolean.FALSE</code> if no dimension line is viewed in 3D.</div>
</li>
</ul>
<a name="setPitch-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitch</h4>
<pre>public&nbsp;void&nbsp;setPitch(java.lang.Float&nbsp;pitch)</pre>
<div class="block">Sets the edited pitch angle.</div>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getPitch()</pre>
<div class="block">Returns the edited pitch.</div>
</li>
</ul>
<a name="createDimensionLine-float-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDimensionLine</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;createDimensionLine(float&nbsp;xStart,
                                            float&nbsp;yStart,
                                            float&nbsp;elevationStart,
                                            float&nbsp;xEnd,
                                            float&nbsp;yEnd,
                                            float&nbsp;elevationEnd,
                                            float&nbsp;offset)</pre>
<div class="block">Returns a new dimension line instance added to home.</div>
</li>
</ul>
<a name="createDimensionLine--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDimensionLine</h4>
<pre>public&nbsp;void&nbsp;createDimensionLine()</pre>
<div class="block">Controls the creation of a dimension line.</div>
</li>
</ul>
<a name="modifyDimensionLines--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>modifyDimensionLines</h4>
<pre>public&nbsp;void&nbsp;modifyDimensionLines()</pre>
<div class="block">Controls the modification of selected dimension lines in edited home.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DimensionLineController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/DimensionLineController.DimensionLineOrientation.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/DimensionLineController.html" target="_top">Frames</a></li>
<li><a href="DimensionLineController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
