<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>TextStyle (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextStyle (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TextStyle.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/TextStyle.html" target="_top">Frames</a></li>
<li><a href="TextStyle.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class TextStyle" class="title">Class TextStyle</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.TextStyle</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">TextStyle</span>
extends java.lang.Object
implements java.io.Serializable</pre>
<div class="block">The different attributes that define a text style.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.TextStyle">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#TextStyle-float-">TextStyle</a></span>(float&nbsp;fontSize)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#TextStyle-float-boolean-boolean-">TextStyle</a></span>(float&nbsp;fontSize,
         boolean&nbsp;bold,
         boolean&nbsp;italic)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#TextStyle-java.lang.String-float-boolean-boolean-">TextStyle</a></span>(java.lang.String&nbsp;fontName,
         float&nbsp;fontSize,
         boolean&nbsp;bold,
         boolean&nbsp;italic)</code>
<div class="block">Creates a text style from its font's name, its size and style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#TextStyle-java.lang.String-float-boolean-boolean-com.eteks.sweethome3d.model.TextStyle.Alignment-">TextStyle</a></span>(java.lang.String&nbsp;fontName,
         float&nbsp;fontSize,
         boolean&nbsp;bold,
         boolean&nbsp;italic,
         <a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;alignment)</code>
<div class="block">Creates a text style from its font's name, its size, style and alignment.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveBoldStyle-boolean-">deriveBoldStyle</a></span>(boolean&nbsp;bold)</code>
<div class="block">Returns a derived style of this text style with a given bold style.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveItalicStyle-boolean-">deriveItalicStyle</a></span>(boolean&nbsp;italic)</code>
<div class="block">Returns a derived style of this text style with a given italic style.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveStyle-float-">deriveStyle</a></span>(float&nbsp;fontSize)</code>
<div class="block">Returns a derived style of this text style with a given font size.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveStyle-java.lang.String-">deriveStyle</a></span>(java.lang.String&nbsp;fontName)</code>
<div class="block">Returns a derived style of this text style with a given font name.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#deriveStyle-com.eteks.sweethome3d.model.TextStyle.Alignment-">deriveStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;alignment)</code>
<div class="block">Returns a derived style of this text style with a given alignment.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;object)</code>
<div class="block">Returns <code>true</code> if this text style is equal to <code>object</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#getAlignment--">getAlignment</a></span>()</code>
<div class="block">Returns the alignment applied on text using this style.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#getFontName--">getFontName</a></span>()</code>
<div class="block">Returns the font name of this text style.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#getFontSize--">getFontSize</a></span>()</code>
<div class="block">Returns the font size of this text style.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#hashCode--">hashCode</a></span>()</code>
<div class="block">Returns a hash code for this text style.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#isBold--">isBold</a></span>()</code>
<div class="block">Returns whether this text style is bold or not.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html#isItalic--">isItalic</a></span>()</code>
<div class="block">Returns whether this text style is italic or not.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TextStyle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextStyle</h4>
<pre>public&nbsp;TextStyle(float&nbsp;fontSize)</pre>
</li>
</ul>
<a name="TextStyle-float-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextStyle</h4>
<pre>public&nbsp;TextStyle(float&nbsp;fontSize,
                 boolean&nbsp;bold,
                 boolean&nbsp;italic)</pre>
</li>
</ul>
<a name="TextStyle-java.lang.String-float-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextStyle</h4>
<pre>public&nbsp;TextStyle(java.lang.String&nbsp;fontName,
                 float&nbsp;fontSize,
                 boolean&nbsp;bold,
                 boolean&nbsp;italic)</pre>
<div class="block">Creates a text style from its font's name, its size and style.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="TextStyle-java.lang.String-float-boolean-boolean-com.eteks.sweethome3d.model.TextStyle.Alignment-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TextStyle</h4>
<pre>public&nbsp;TextStyle(java.lang.String&nbsp;fontName,
                 float&nbsp;fontSize,
                 boolean&nbsp;bold,
                 boolean&nbsp;italic,
                 <a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;alignment)</pre>
<div class="block">Creates a text style from its font's name, its size, style and alignment.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFontName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFontName()</pre>
<div class="block">Returns the font name of this text style.</div>
</li>
</ul>
<a name="getFontSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontSize</h4>
<pre>public&nbsp;float&nbsp;getFontSize()</pre>
<div class="block">Returns the font size of this text style.</div>
</li>
</ul>
<a name="isBold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBold</h4>
<pre>public&nbsp;boolean&nbsp;isBold()</pre>
<div class="block">Returns whether this text style is bold or not.</div>
</li>
</ul>
<a name="isItalic--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isItalic</h4>
<pre>public&nbsp;boolean&nbsp;isItalic()</pre>
<div class="block">Returns whether this text style is italic or not.</div>
</li>
</ul>
<a name="getAlignment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlignment</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;getAlignment()</pre>
<div class="block">Returns the alignment applied on text using this style.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="deriveStyle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deriveStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;deriveStyle(java.lang.String&nbsp;fontName)</pre>
<div class="block">Returns a derived style of this text style with a given font name.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="deriveStyle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deriveStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;deriveStyle(float&nbsp;fontSize)</pre>
<div class="block">Returns a derived style of this text style with a given font size.</div>
</li>
</ul>
<a name="deriveStyle-com.eteks.sweethome3d.model.TextStyle.Alignment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deriveStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;deriveStyle(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;alignment)</pre>
<div class="block">Returns a derived style of this text style with a given alignment.</div>
</li>
</ul>
<a name="deriveBoldStyle-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deriveBoldStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;deriveBoldStyle(boolean&nbsp;bold)</pre>
<div class="block">Returns a derived style of this text style with a given bold style.</div>
</li>
</ul>
<a name="deriveItalicStyle-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deriveItalicStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;deriveItalicStyle(boolean&nbsp;italic)</pre>
<div class="block">Returns a derived style of this text style with a given italic style.</div>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;object)</pre>
<div class="block">Returns <code>true</code> if this text style is equal to <code>object</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block">Returns a hash code for this text style.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TextStyle.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/TextStyle.html" target="_top">Frames</a></li>
<li><a href="TextStyle.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
