<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>LabelController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LabelController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LabelController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/LabelController.html" target="_top">Frames</a></li>
<li><a href="LabelController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class LabelController" class="title">Class LabelController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.LabelController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">LabelController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for label view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">LabelController.Property</a></span></code>
<div class="block">The property that may be edited by the view associated to this controller.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#LabelController-com.eteks.sweethome3d.model.Home-float-float-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">LabelController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               float&nbsp;x,
               float&nbsp;y,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of label creation with undo support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#LabelController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">LabelController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
               javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of label modifications with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.LabelController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">LabelController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#createLabel--">createLabel</a></span>()</code>
<div class="block">Controls the creation of a label.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#createLabel-java.lang.String-float-float-">createLabel</a></span>(java.lang.String&nbsp;text,
           float&nbsp;x,
           float&nbsp;y)</code>
<div class="block">Returns a new label instance placed at the given coordinates and added to home.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getAlignment--">getAlignment</a></span>()</code>
<div class="block">Returns the edited text alignment.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the edited color.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the edited elevation.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getFontName--">getFontName</a></span>()</code>
<div class="block">Returns the edited font name or <code>null</code> for default system font.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getFontSize--">getFontSize</a></span>()</code>
<div class="block">Returns the edited font size.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns the edited pitch.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getText--">getText</a></span>()</code>
<div class="block">Returns the edited text.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#isFontNameSet--">isFontNameSet</a></span>()</code>
<div class="block">Returns <code>true</code> if all edited labels use the same font name.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#isLabelPartOfBasePlan-com.eteks.sweethome3d.model.Label-">isLabelPartOfBasePlan</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)</code>
<div class="block">Returns <code>true</code>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#isPitchEnabled--">isPitchEnabled</a></span>()</code>
<div class="block">Returns <code>Boolean.TRUE</code> if all edited labels are viewed in 3D,
 or <code>Boolean.FALSE</code> if no label is viewed in 3D.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#modifyLabels--">modifyLabels</a></span>()</code>
<div class="block">Controls the modification of selected labels.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.LabelController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">LabelController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#setAlignment-com.eteks.sweethome3d.model.TextStyle.Alignment-">setAlignment</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;alignment)</code>
<div class="block">Sets the edited text alignment.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the edited color.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#setElevation-java.lang.Float-">setElevation</a></span>(java.lang.Float&nbsp;elevation)</code>
<div class="block">Sets the edited elevation.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#setFontName-java.lang.String-">setFontName</a></span>(java.lang.String&nbsp;fontName)</code>
<div class="block">Sets the edited font name.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#setFontSize-java.lang.Float-">setFontSize</a></span>(java.lang.Float&nbsp;fontSize)</code>
<div class="block">Sets the edited font size.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#setPitch-java.lang.Float-">setPitch</a></span>(java.lang.Float&nbsp;pitch)</code>
<div class="block">Sets the edited pitch.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#setText-java.lang.String-">setText</a></span>(java.lang.String&nbsp;text)</code>
<div class="block">Sets the edited text.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates edited properties from selected labels in the home edited by this controller.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="LabelController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LabelController</h4>
<pre>public&nbsp;LabelController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of label modifications with undo support.</div>
</li>
</ul>
<a name="LabelController-com.eteks.sweethome3d.model.Home-float-float-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LabelController</h4>
<pre>public&nbsp;LabelController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       float&nbsp;x,
                       float&nbsp;y,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                       javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of label creation with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates edited properties from selected labels in the home edited by this controller.</div>
</li>
</ul>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.LabelController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">LabelController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.LabelController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">LabelController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="setText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setText</h4>
<pre>public&nbsp;void&nbsp;setText(java.lang.String&nbsp;text)</pre>
<div class="block">Sets the edited text.</div>
</li>
</ul>
<a name="getText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getText</h4>
<pre>public&nbsp;java.lang.String&nbsp;getText()</pre>
<div class="block">Returns the edited text.</div>
</li>
</ul>
<a name="setAlignment-com.eteks.sweethome3d.model.TextStyle.Alignment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignment</h4>
<pre>public&nbsp;void&nbsp;setAlignment(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;alignment)</pre>
<div class="block">Sets the edited text alignment.</div>
</li>
</ul>
<a name="getAlignment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlignment</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.Alignment.html" title="enum in com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;getAlignment()</pre>
<div class="block">Returns the edited text alignment.</div>
</li>
</ul>
<a name="setFontName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFontName</h4>
<pre>public&nbsp;void&nbsp;setFontName(java.lang.String&nbsp;fontName)</pre>
<div class="block">Sets the edited font name.</div>
</li>
</ul>
<a name="getFontName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getFontName()</pre>
<div class="block">Returns the edited font name or <code>null</code> for default system font.</div>
</li>
</ul>
<a name="setFontSize-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFontSize</h4>
<pre>public&nbsp;void&nbsp;setFontSize(java.lang.Float&nbsp;fontSize)</pre>
<div class="block">Sets the edited font size.</div>
</li>
</ul>
<a name="getFontSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFontSize</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getFontSize()</pre>
<div class="block">Returns the edited font size.</div>
</li>
</ul>
<a name="isFontNameSet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFontNameSet</h4>
<pre>public&nbsp;boolean&nbsp;isFontNameSet()</pre>
<div class="block">Returns <code>true</code> if all edited labels use the same font name.</div>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the edited color.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the edited color.</div>
</li>
</ul>
<a name="setPitch-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitch</h4>
<pre>public&nbsp;void&nbsp;setPitch(java.lang.Float&nbsp;pitch)</pre>
<div class="block">Sets the edited pitch.</div>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getPitch()</pre>
<div class="block">Returns the edited pitch.</div>
</li>
</ul>
<a name="isPitchEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPitchEnabled</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;isPitchEnabled()</pre>
<div class="block">Returns <code>Boolean.TRUE</code> if all edited labels are viewed in 3D,
 or <code>Boolean.FALSE</code> if no label is viewed in 3D.</div>
</li>
</ul>
<a name="setElevation-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevation</h4>
<pre>public&nbsp;void&nbsp;setElevation(java.lang.Float&nbsp;elevation)</pre>
<div class="block">Sets the edited elevation.</div>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getElevation()</pre>
<div class="block">Returns the edited elevation.</div>
</li>
</ul>
<a name="createLabel-java.lang.String-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createLabel</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;createLabel(java.lang.String&nbsp;text,
                            float&nbsp;x,
                            float&nbsp;y)</pre>
<div class="block">Returns a new label instance placed at the given coordinates and added to home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="createLabel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createLabel</h4>
<pre>public&nbsp;void&nbsp;createLabel()</pre>
<div class="block">Controls the creation of a label.</div>
</li>
</ul>
<a name="isLabelPartOfBasePlan-com.eteks.sweethome3d.model.Label-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLabelPartOfBasePlan</h4>
<pre>protected&nbsp;boolean&nbsp;isLabelPartOfBasePlan(<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)</pre>
<div class="block">Returns <code>true</code>.</div>
</li>
</ul>
<a name="modifyLabels--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>modifyLabels</h4>
<pre>public&nbsp;void&nbsp;modifyLabels()</pre>
<div class="block">Controls the modification of selected labels.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LabelController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/LabelController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/LabelController.html" target="_top">Frames</a></li>
<li><a href="LabelController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
