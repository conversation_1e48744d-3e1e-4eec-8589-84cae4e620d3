<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Package com.eteks.sweethome3d.model (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.eteks.sweethome3d.model (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.eteks.sweethome3d.model" class="title">Uses of Package<br>com.eteks.sweethome3d.model</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d">com.eteks.sweethome3d</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as a stand-alone application.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d</a></td>
<td class="colLast">
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.plugin">com.eteks.sweethome3d.plugin</a></td>
<td class="colLast">
<div class="block">Describes the super classes required to create Sweet Home 3D plug-ins.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.tools">com.eteks.sweethome3d.tools</a></td>
<td class="colLast">
<div class="block">Contains various tool classes useful in 
<a href="../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">View/Controller packages</a> and 
<a href="../../../../com/eteks/sweethome3d/io/package-summary.html">IO packages</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/package-summary.html">com.eteks.sweethome3d</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.html#com.eteks.sweethome3d">Home</a>
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeApplication.html#com.eteks.sweethome3d">HomeApplication</a>
<div class="block">Application managing a list of homes displayed at screen.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeRecorder.html#com.eteks.sweethome3d">HomeRecorder</a>
<div class="block">Homes recorder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeRecorder.Type.html#com.eteks.sweethome3d">HomeRecorder.Type</a>
<div class="block">Recorder type used as a hint to select a home recorder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.html#com.eteks.sweethome3d">UserPreferences</a>
<div class="block">User preferences.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.html#com.eteks.sweethome3d.applet">Home</a>
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeApplication.html#com.eteks.sweethome3d.applet">HomeApplication</a>
<div class="block">Application managing a list of homes displayed at screen.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeRecorder.html#com.eteks.sweethome3d.applet">HomeRecorder</a>
<div class="block">Homes recorder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeRecorder.Type.html#com.eteks.sweethome3d.applet">HomeRecorder.Type</a>
<div class="block">Recorder type used as a hint to select a home recorder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Library.html#com.eteks.sweethome3d.applet">Library</a>
<div class="block">A library able to provide additional capabilities to Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/RecorderException.html#com.eteks.sweethome3d.applet">RecorderException</a>
<div class="block">Exception thrown by methods that access to data in IO layer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.html#com.eteks.sweethome3d.applet">UserPreferences</a>
<div class="block">User preferences.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/BackgroundImage.html#com.eteks.sweethome3d.io">BackgroundImage</a>
<div class="block">The image displayed in background of the plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Baseboard.html#com.eteks.sweethome3d.io">Baseboard</a>
<div class="block">A baseboard associated to wall.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Camera.html#com.eteks.sweethome3d.io">Camera</a>
<div class="block">Camera characteristics in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogPieceOfFurniture.html#com.eteks.sweethome3d.io">CatalogPieceOfFurniture</a>
<div class="block">A catalog piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogTexture.html#com.eteks.sweethome3d.io">CatalogTexture</a>
<div class="block">A texture in textures catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Compass.html#com.eteks.sweethome3d.io">Compass</a>
<div class="block">A compass used to locate where a home is located and how it's oriented towards North.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Content.html#com.eteks.sweethome3d.io">Content</a>
<div class="block">Content for files, images...</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/DimensionLine.html#com.eteks.sweethome3d.io">DimensionLine</a>
<div class="block">A dimension line in plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/FurnitureCatalog.html#com.eteks.sweethome3d.io">FurnitureCatalog</a>
<div class="block">Furniture catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/FurnitureCategory.html#com.eteks.sweethome3d.io">FurnitureCategory</a>
<div class="block">Category of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.html#com.eteks.sweethome3d.io">Home</a>
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeApplication.html#com.eteks.sweethome3d.io">HomeApplication</a>
<div class="block">Application managing a list of homes displayed at screen.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeEnvironment.html#com.eteks.sweethome3d.io">HomeEnvironment</a>
<div class="block">The environment attributes of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeMaterial.html#com.eteks.sweethome3d.io">HomeMaterial</a>
<div class="block">The color and other properties of a material.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.html#com.eteks.sweethome3d.io">HomePieceOfFurniture</a>
<div class="block">A piece of furniture in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePrint.html#com.eteks.sweethome3d.io">HomePrint</a>
<div class="block">The print attributes for a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeRecorder.html#com.eteks.sweethome3d.io">HomeRecorder</a>
<div class="block">Homes recorder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeTexture.html#com.eteks.sweethome3d.io">HomeTexture</a>
<div class="block">An image used as texture on home 3D objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Label.html#com.eteks.sweethome3d.io">Label</a>
<div class="block">A free label.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Level.html#com.eteks.sweethome3d.io">Level</a>
<div class="block">A level in a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Library.html#com.eteks.sweethome3d.io">Library</a>
<div class="block">A library able to provide additional capabilities to Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.html#com.eteks.sweethome3d.io">Polyline</a>
<div class="block">A polyline or a polygon in a home plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/RecorderException.html#com.eteks.sweethome3d.io">RecorderException</a>
<div class="block">Exception thrown by methods that access to data in IO layer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Room.html#com.eteks.sweethome3d.io">Room</a>
<div class="block">A room or a polygon in a home plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextStyle.html#com.eteks.sweethome3d.io">TextStyle</a>
<div class="block">The different attributes that define a text style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TexturesCatalog.html#com.eteks.sweethome3d.io">TexturesCatalog</a>
<div class="block">Textures catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TexturesCategory.html#com.eteks.sweethome3d.io">TexturesCategory</a>
<div class="block">Category of textures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.html#com.eteks.sweethome3d.io">UserPreferences</a>
<div class="block">User preferences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Wall.html#com.eteks.sweethome3d.io">Wall</a>
<div class="block">A wall of a home plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.j3d">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Camera.html#com.eteks.sweethome3d.j3d">Camera</a>
<div class="block">Camera characteristics in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Content.html#com.eteks.sweethome3d.j3d">Content</a>
<div class="block">Content for files, images...</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/DimensionLine.html#com.eteks.sweethome3d.j3d">DimensionLine</a>
<div class="block">A dimension line in plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.html#com.eteks.sweethome3d.j3d">Home</a>
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeMaterial.html#com.eteks.sweethome3d.j3d">HomeMaterial</a>
<div class="block">The color and other properties of a material.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.html#com.eteks.sweethome3d.j3d">HomePieceOfFurniture</a>
<div class="block">A piece of furniture in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeTexture.html#com.eteks.sweethome3d.j3d">HomeTexture</a>
<div class="block">An image used as texture on home 3D objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Label.html#com.eteks.sweethome3d.j3d">Label</a>
<div class="block">A free label.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.html#com.eteks.sweethome3d.j3d">Polyline</a>
<div class="block">A polyline or a polygon in a home plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.CapStyle.html#com.eteks.sweethome3d.j3d">Polyline.CapStyle</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.JoinStyle.html#com.eteks.sweethome3d.j3d">Polyline.JoinStyle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Room.html#com.eteks.sweethome3d.j3d">Room</a>
<div class="block">A room or a polygon in a home plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Selectable.html#com.eteks.sweethome3d.j3d">Selectable</a>
<div class="block">An object that is selectable in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.html#com.eteks.sweethome3d.j3d">UserPreferences</a>
<div class="block">User preferences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Wall.html#com.eteks.sweethome3d.j3d">Wall</a>
<div class="block">A wall of a home plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/AspectRatio.html#com.eteks.sweethome3d.model">AspectRatio</a>
<div class="block">The aspect ratio of pictures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/BackgroundImage.html#com.eteks.sweethome3d.model">BackgroundImage</a>
<div class="block">The image displayed in background of the plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Baseboard.html#com.eteks.sweethome3d.model">Baseboard</a>
<div class="block">A baseboard associated to wall.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/BoxBounds.html#com.eteks.sweethome3d.model">BoxBounds</a>
<div class="block">Bounds of a box.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Camera.html#com.eteks.sweethome3d.model">Camera</a>
<div class="block">Camera characteristics in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Camera.Lens.html#com.eteks.sweethome3d.model">Camera.Lens</a>
<div class="block">The kind of lens that can be used with a camera.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Camera.Property.html#com.eteks.sweethome3d.model">Camera.Property</a>
<div class="block">The properties of a camera that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogItem.html#com.eteks.sweethome3d.model">CatalogItem</a>
<div class="block">An item of a catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogPieceOfFurniture.html#com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>
<div class="block">A catalog piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogTexture.html#com.eteks.sweethome3d.model">CatalogTexture</a>
<div class="block">A texture in textures catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CollectionEvent.html#com.eteks.sweethome3d.model">CollectionEvent</a>
<div class="block">Type of event notified when an item is added or deleted from a list.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CollectionEvent.Type.html#com.eteks.sweethome3d.model">CollectionEvent.Type</a>
<div class="block">The type of change in the collection.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CollectionListener.html#com.eteks.sweethome3d.model">CollectionListener</a>
<div class="block">A listener notified when items are added or removed from a collection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Compass.html#com.eteks.sweethome3d.model">Compass</a>
<div class="block">A compass used to locate where a home is located and how it's oriented towards North.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Compass.Property.html#com.eteks.sweethome3d.model">Compass.Property</a>
<div class="block">The properties of a compass that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Content.html#com.eteks.sweethome3d.model">Content</a>
<div class="block">Content for files, images...</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/DimensionLine.html#com.eteks.sweethome3d.model">DimensionLine</a>
<div class="block">A dimension line in plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/DimensionLine.Property.html#com.eteks.sweethome3d.model">DimensionLine.Property</a>
<div class="block">The properties of a dimension line that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/DoorOrWindow.html#com.eteks.sweethome3d.model">DoorOrWindow</a>
<div class="block">A piece of furniture used as a door or a window.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Elevatable.html#com.eteks.sweethome3d.model">Elevatable</a>
<div class="block">An object that belongs to a level.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/FurnitureCatalog.html#com.eteks.sweethome3d.model">FurnitureCatalog</a>
<div class="block">Furniture catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/FurnitureCategory.html#com.eteks.sweethome3d.model">FurnitureCategory</a>
<div class="block">Category of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.html#com.eteks.sweethome3d.model">Home</a>
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.Property.html#com.eteks.sweethome3d.model">Home.Property</a>
<div class="block">The properties of a home that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeDescriptor.html#com.eteks.sweethome3d.model">HomeDescriptor</a>
<div class="block">A descriptor that gives access to some data of a home not yet created.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeDoorOrWindow.Property.html#com.eteks.sweethome3d.model">HomeDoorOrWindow.Property</a>
<div class="block">The properties of a door or window that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeEnvironment.html#com.eteks.sweethome3d.model">HomeEnvironment</a>
<div class="block">The environment attributes of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeEnvironment.DrawingMode.html#com.eteks.sweethome3d.model">HomeEnvironment.DrawingMode</a>
<div class="block">The various modes used to draw home in 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeEnvironment.Property.html#com.eteks.sweethome3d.model">HomeEnvironment.Property</a>
<div class="block">The environment properties that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeFurnitureGroup.html#com.eteks.sweethome3d.model">HomeFurnitureGroup</a>
<div class="block">A group of furniture of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeLight.html#com.eteks.sweethome3d.model">HomeLight</a>
<div class="block">A light in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeLight.Property.html#com.eteks.sweethome3d.model">HomeLight.Property</a>
<div class="block">The properties of a light that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeMaterial.html#com.eteks.sweethome3d.model">HomeMaterial</a>
<div class="block">The color and other properties of a material.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeObject.html#com.eteks.sweethome3d.model">HomeObject</a>
<div class="block">An object with and ID and data where users can stored their own properties.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.html#com.eteks.sweethome3d.model">HomePieceOfFurniture</a>
<div class="block">A piece of furniture in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.Property.html#com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a>
<div class="block">The properties of a piece of furniture that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.SortableProperty.html#com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>
<div class="block">The properties on which home furniture may be sorted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePrint.html#com.eteks.sweethome3d.model">HomePrint</a>
<div class="block">The print attributes for a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePrint.PaperOrientation.html#com.eteks.sweethome3d.model">HomePrint.PaperOrientation</a>
<div class="block">Paper orientation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeRecorder.html#com.eteks.sweethome3d.model">HomeRecorder</a>
<div class="block">Homes recorder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeRecorder.Type.html#com.eteks.sweethome3d.model">HomeRecorder.Type</a>
<div class="block">Recorder type used as a hint to select a home recorder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeShelfUnit.html#com.eteks.sweethome3d.model">HomeShelfUnit</a>
<div class="block">A shelf unit in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeShelfUnit.Property.html#com.eteks.sweethome3d.model">HomeShelfUnit.Property</a>
<div class="block">The properties of a shelf unit that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeTexture.html#com.eteks.sweethome3d.model">HomeTexture</a>
<div class="block">An image used as texture on home 3D objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Label.html#com.eteks.sweethome3d.model">Label</a>
<div class="block">A free label.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Label.Property.html#com.eteks.sweethome3d.model">Label.Property</a>
<div class="block">The properties of a label that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/LengthUnit.html#com.eteks.sweethome3d.model">LengthUnit</a>
<div class="block">Unit used for lengths.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Level.html#com.eteks.sweethome3d.model">Level</a>
<div class="block">A level in a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Level.Property.html#com.eteks.sweethome3d.model">Level.Property</a>
<div class="block">The properties of a level that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Library.html#com.eteks.sweethome3d.model">Library</a>
<div class="block">A library able to provide additional capabilities to Sweet Home 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Light.html#com.eteks.sweethome3d.model">Light</a>
<div class="block">A piece of furniture that contains one or more light sources.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/LightSource.html#com.eteks.sweethome3d.model">LightSource</a>
<div class="block">A light source of a <a href="../../../../com/eteks/sweethome3d/model/Light.html" title="interface in com.eteks.sweethome3d.model">light</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/ObjectProperty.html#com.eteks.sweethome3d.model">ObjectProperty</a>
<div class="block">Information about additional property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/ObjectProperty.Type.html#com.eteks.sweethome3d.model">ObjectProperty.Type</a>
<div class="block">Property type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/ObserverCamera.html#com.eteks.sweethome3d.model">ObserverCamera</a>
<div class="block">Observer camera characteristics in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/ObserverCamera.Property.html#com.eteks.sweethome3d.model">ObserverCamera.Property</a>
<div class="block">The additional properties of an observer camera that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/PatternsCatalog.html#com.eteks.sweethome3d.model">PatternsCatalog</a>
<div class="block">A catalog of texture images used as patterns to fill plan areas.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/PieceOfFurniture.html#com.eteks.sweethome3d.model">PieceOfFurniture</a>
<div class="block">A piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.html#com.eteks.sweethome3d.model">Polyline</a>
<div class="block">A polyline or a polygon in a home plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.ArrowStyle.html#com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.CapStyle.html#com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.DashStyle.html#com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.JoinStyle.html#com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.Property.html#com.eteks.sweethome3d.model">Polyline.Property</a>
<div class="block">The properties of a polyline that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/RecorderException.html#com.eteks.sweethome3d.model">RecorderException</a>
<div class="block">Exception thrown by methods that access to data in IO layer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Room.html#com.eteks.sweethome3d.model">Room</a>
<div class="block">A room or a polygon in a home plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Room.Property.html#com.eteks.sweethome3d.model">Room.Property</a>
<div class="block">The properties of a room that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Sash.html#com.eteks.sweethome3d.model">Sash</a>
<div class="block">A sash (moving part) of a door or a window.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Selectable.html#com.eteks.sweethome3d.model">Selectable</a>
<div class="block">An object that is selectable in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/SelectionEvent.html#com.eteks.sweethome3d.model">SelectionEvent</a>
<div class="block">Type of event notified when selection changes in home or furniture catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/SelectionListener.html#com.eteks.sweethome3d.model">SelectionListener</a>
<div class="block">Listener implemented to receive notifications of selection changes in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model"><code>Home</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/ShelfUnit.html#com.eteks.sweethome3d.model">ShelfUnit</a>
<div class="block">A piece of furniture whith shelves.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextStyle.html#com.eteks.sweethome3d.model">TextStyle</a>
<div class="block">The different attributes that define a text style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextStyle.Alignment.html#com.eteks.sweethome3d.model">TextStyle.Alignment</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextureImage.html#com.eteks.sweethome3d.model">TextureImage</a>
<div class="block">An image used as texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TexturesCatalog.html#com.eteks.sweethome3d.model">TexturesCatalog</a>
<div class="block">Textures catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TexturesCategory.html#com.eteks.sweethome3d.model">TexturesCategory</a>
<div class="block">Category of textures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Transformation.html#com.eteks.sweethome3d.model">Transformation</a>
<div class="block">The transformation applied to some model parts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.html#com.eteks.sweethome3d.model">UserPreferences</a>
<div class="block">User preferences.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.Property.html#com.eteks.sweethome3d.model">UserPreferences.Property</a>
<div class="block">The properties of user preferences that may change.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Wall.html#com.eteks.sweethome3d.model">Wall</a>
<div class="block">A wall of a home plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Wall.Property.html#com.eteks.sweethome3d.model">Wall.Property</a>
<div class="block">The properties of a wall that may change.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.plugin">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.html#com.eteks.sweethome3d.plugin">Home</a>
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeApplication.html#com.eteks.sweethome3d.plugin">HomeApplication</a>
<div class="block">Application managing a list of homes displayed at screen.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Library.html#com.eteks.sweethome3d.plugin">Library</a>
<div class="block">A library able to provide additional capabilities to Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/RecorderException.html#com.eteks.sweethome3d.plugin">RecorderException</a>
<div class="block">Exception thrown by methods that access to data in IO layer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.html#com.eteks.sweethome3d.plugin">UserPreferences</a>
<div class="block">User preferences.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/BackgroundImage.html#com.eteks.sweethome3d.swing">BackgroundImage</a>
<div class="block">The image displayed in background of the plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Camera.html#com.eteks.sweethome3d.swing">Camera</a>
<div class="block">Camera characteristics in home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogItem.html#com.eteks.sweethome3d.swing">CatalogItem</a>
<div class="block">An item of a catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogPieceOfFurniture.html#com.eteks.sweethome3d.swing">CatalogPieceOfFurniture</a>
<div class="block">A catalog piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogTexture.html#com.eteks.sweethome3d.swing">CatalogTexture</a>
<div class="block">A texture in textures catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Content.html#com.eteks.sweethome3d.swing">Content</a>
<div class="block">Content for files, images...</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/DimensionLine.html#com.eteks.sweethome3d.swing">DimensionLine</a>
<div class="block">A dimension line in plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Elevatable.html#com.eteks.sweethome3d.swing">Elevatable</a>
<div class="block">An object that belongs to a level.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/FurnitureCatalog.html#com.eteks.sweethome3d.swing">FurnitureCatalog</a>
<div class="block">Furniture catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.html#com.eteks.sweethome3d.swing">Home</a>
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeMaterial.html#com.eteks.sweethome3d.swing">HomeMaterial</a>
<div class="block">The color and other properties of a material.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.html#com.eteks.sweethome3d.swing">HomePieceOfFurniture</a>
<div class="block">A piece of furniture in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePrint.html#com.eteks.sweethome3d.swing">HomePrint</a>
<div class="block">The print attributes for a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Level.html#com.eteks.sweethome3d.swing">Level</a>
<div class="block">A level in a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.CapStyle.html#com.eteks.sweethome3d.swing">Polyline.CapStyle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.DashStyle.html#com.eteks.sweethome3d.swing">Polyline.DashStyle</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.JoinStyle.html#com.eteks.sweethome3d.swing">Polyline.JoinStyle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/RecorderException.html#com.eteks.sweethome3d.swing">RecorderException</a>
<div class="block">Exception thrown by methods that access to data in IO layer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Selectable.html#com.eteks.sweethome3d.swing">Selectable</a>
<div class="block">An object that is selectable in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextStyle.html#com.eteks.sweethome3d.swing">TextStyle</a>
<div class="block">The different attributes that define a text style.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextureImage.html#com.eteks.sweethome3d.swing">TextureImage</a>
<div class="block">An image used as texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Transformation.html#com.eteks.sweethome3d.swing">Transformation</a>
<div class="block">The transformation applied to some model parts.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.html#com.eteks.sweethome3d.swing">UserPreferences</a>
<div class="block">User preferences.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.tools">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/tools/package-summary.html">com.eteks.sweethome3d.tools</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Content.html#com.eteks.sweethome3d.tools">Content</a>
<div class="block">Content for files, images...</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> used by <a href="../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/AspectRatio.html#com.eteks.sweethome3d.viewcontroller">AspectRatio</a>
<div class="block">The aspect ratio of pictures.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/BackgroundImage.html#com.eteks.sweethome3d.viewcontroller">BackgroundImage</a>
<div class="block">The image displayed in background of the plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Baseboard.html#com.eteks.sweethome3d.viewcontroller">Baseboard</a>
<div class="block">A baseboard associated to wall.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Camera.html#com.eteks.sweethome3d.viewcontroller">Camera</a>
<div class="block">Camera characteristics in home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Camera.Lens.html#com.eteks.sweethome3d.viewcontroller">Camera.Lens</a>
<div class="block">The kind of lens that can be used with a camera.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogPieceOfFurniture.html#com.eteks.sweethome3d.viewcontroller">CatalogPieceOfFurniture</a>
<div class="block">A catalog piece of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/CatalogTexture.html#com.eteks.sweethome3d.viewcontroller">CatalogTexture</a>
<div class="block">A texture in textures catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Content.html#com.eteks.sweethome3d.viewcontroller">Content</a>
<div class="block">Content for files, images...</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/DimensionLine.html#com.eteks.sweethome3d.viewcontroller">DimensionLine</a>
<div class="block">A dimension line in plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/FurnitureCatalog.html#com.eteks.sweethome3d.viewcontroller">FurnitureCatalog</a>
<div class="block">Furniture catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/FurnitureCategory.html#com.eteks.sweethome3d.viewcontroller">FurnitureCategory</a>
<div class="block">Category of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Home.html#com.eteks.sweethome3d.viewcontroller">Home</a>
<div class="block">The home managed by the application with its furniture and walls.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeApplication.html#com.eteks.sweethome3d.viewcontroller">HomeApplication</a>
<div class="block">Application managing a list of homes displayed at screen.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeFurnitureGroup.html#com.eteks.sweethome3d.viewcontroller">HomeFurnitureGroup</a>
<div class="block">A group of furniture of furniture.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeMaterial.html#com.eteks.sweethome3d.viewcontroller">HomeMaterial</a>
<div class="block">The color and other properties of a material.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.html#com.eteks.sweethome3d.viewcontroller">HomePieceOfFurniture</a>
<div class="block">A piece of furniture in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.SortableProperty.html#com.eteks.sweethome3d.viewcontroller">HomePieceOfFurniture.SortableProperty</a>
<div class="block">The properties on which home furniture may be sorted.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomePrint.html#com.eteks.sweethome3d.viewcontroller">HomePrint</a>
<div class="block">The print attributes for a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeRecorder.Type.html#com.eteks.sweethome3d.viewcontroller">HomeRecorder.Type</a>
<div class="block">Recorder type used as a hint to select a home recorder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/HomeTexture.html#com.eteks.sweethome3d.viewcontroller">HomeTexture</a>
<div class="block">An image used as texture on home 3D objects.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Label.html#com.eteks.sweethome3d.viewcontroller">Label</a>
<div class="block">A free label.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/LengthUnit.html#com.eteks.sweethome3d.viewcontroller">LengthUnit</a>
<div class="block">Unit used for lengths.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Level.html#com.eteks.sweethome3d.viewcontroller">Level</a>
<div class="block">A level in a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/ObjectProperty.html#com.eteks.sweethome3d.viewcontroller">ObjectProperty</a>
<div class="block">Information about additional property.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/PieceOfFurniture.html#com.eteks.sweethome3d.viewcontroller">PieceOfFurniture</a>
<div class="block">A piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.html#com.eteks.sweethome3d.viewcontroller">Polyline</a>
<div class="block">A polyline or a polygon in a home plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.ArrowStyle.html#com.eteks.sweethome3d.viewcontroller">Polyline.ArrowStyle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.CapStyle.html#com.eteks.sweethome3d.viewcontroller">Polyline.CapStyle</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.DashStyle.html#com.eteks.sweethome3d.viewcontroller">Polyline.DashStyle</a>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Polyline.JoinStyle.html#com.eteks.sweethome3d.viewcontroller">Polyline.JoinStyle</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/RecorderException.html#com.eteks.sweethome3d.viewcontroller">RecorderException</a>
<div class="block">Exception thrown by methods that access to data in IO layer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Room.html#com.eteks.sweethome3d.viewcontroller">Room</a>
<div class="block">A room or a polygon in a home plan.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Selectable.html#com.eteks.sweethome3d.viewcontroller">Selectable</a>
<div class="block">An object that is selectable in home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/SelectionListener.html#com.eteks.sweethome3d.viewcontroller">SelectionListener</a>
<div class="block">Listener implemented to receive notifications of selection changes in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model"><code>Home</code></a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextStyle.html#com.eteks.sweethome3d.viewcontroller">TextStyle</a>
<div class="block">The different attributes that define a text style.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextStyle.Alignment.html#com.eteks.sweethome3d.viewcontroller">TextStyle.Alignment</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TextureImage.html#com.eteks.sweethome3d.viewcontroller">TextureImage</a>
<div class="block">An image used as texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/TexturesCategory.html#com.eteks.sweethome3d.viewcontroller">TexturesCategory</a>
<div class="block">Category of textures.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Transformation.html#com.eteks.sweethome3d.viewcontroller">Transformation</a>
<div class="block">The transformation applied to some model parts.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/UserPreferences.html#com.eteks.sweethome3d.viewcontroller">UserPreferences</a>
<div class="block">User preferences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/model/class-use/Wall.html#com.eteks.sweethome3d.viewcontroller">Wall</a>
<div class="block">A wall of a home plan.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
