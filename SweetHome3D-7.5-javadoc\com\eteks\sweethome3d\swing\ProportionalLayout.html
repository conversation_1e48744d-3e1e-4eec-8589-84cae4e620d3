<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:50 CEST 2024 -->
<title>ProportionalLayout (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProportionalLayout (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProportionalLayout.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/PrintPreviewPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.Constraints.html" title="enum in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ProportionalLayout.html" target="_top">Frames</a></li>
<li><a href="ProportionalLayout.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class ProportionalLayout" class="title">Class ProportionalLayout</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.ProportionalLayout</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.awt.LayoutManager, java.awt.LayoutManager2</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ProportionalLayout</span>
extends java.lang.Object
implements java.awt.LayoutManager2</pre>
<div class="block">A layout manager that displays two components at the top of each other. 
 The component at top is sized at container width and at its preferred height.
 The component at bottom is centered in the rest of the space and sized proportionally 
 to its preferred size.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.Constraints.html" title="enum in com.eteks.sweethome3d.swing">ProportionalLayout.Constraints</a></span></code>
<div class="block">The two locations where components managed by a <a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html" title="class in com.eteks.sweethome3d.swing"><code>ProportionalLayout</code></a> instance can be placed.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#ProportionalLayout--">ProportionalLayout</a></span>()</code>
<div class="block">Creates a layout manager which layouts its component 
 with a default gap of 5 pixels between them.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#ProportionalLayout-int-">ProportionalLayout</a></span>(int&nbsp;gap)</code>
<div class="block">Creates a layout manager which layouts its component 
 with a given <code>gap</code> between them.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#addLayoutComponent-java.awt.Component-java.lang.Object-">addLayoutComponent</a></span>(java.awt.Component&nbsp;component,
                  java.lang.Object&nbsp;constraints)</code>
<div class="block">Records a given <code>component</code> in this layout manager as the component at 
 <code>Constraints.TOP</code> or at <code>Constraints.BOTTOM</code> of its container.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#addLayoutComponent-java.lang.String-java.awt.Component-">addLayoutComponent</a></span>(java.lang.String&nbsp;name,
                  java.awt.Component&nbsp;comp)</code>
<div class="block">Do not use.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#getLayoutAlignmentX-java.awt.Container-">getLayoutAlignmentX</a></span>(java.awt.Container&nbsp;target)</code>
<div class="block">Returns 0.5.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#getLayoutAlignmentY-java.awt.Container-">getLayoutAlignmentY</a></span>(java.awt.Container&nbsp;target)</code>
<div class="block">Return 0.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#invalidateLayout-java.awt.Container-">invalidateLayout</a></span>(java.awt.Container&nbsp;target)</code>
<div class="block">Invalidates layout.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#layoutContainer-java.awt.Container-">layoutContainer</a></span>(java.awt.Container&nbsp;parent)</code>
<div class="block">Layouts the container.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#maximumLayoutSize-java.awt.Container-">maximumLayoutSize</a></span>(java.awt.Container&nbsp;parent)</code>
<div class="block">Returns the largest maximum width of the components managed by this layout manager,
 and the sum of their maximum heights.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#minimumLayoutSize-java.awt.Container-">minimumLayoutSize</a></span>(java.awt.Container&nbsp;parent)</code>
<div class="block">Returns the largest minimum width of the components managed by this layout manager,
 and the sum of their minimum heights.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#preferredLayoutSize-java.awt.Container-">preferredLayoutSize</a></span>(java.awt.Container&nbsp;parent)</code>
<div class="block">Returns the largest preferred width of the components managed by this layout manager,
 and the sum of their preferred heights.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html#removeLayoutComponent-java.awt.Component-">removeLayoutComponent</a></span>(java.awt.Component&nbsp;component)</code>
<div class="block">Removes the given <code>component</code> from the ones managed by this layout manager.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ProportionalLayout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ProportionalLayout</h4>
<pre>public&nbsp;ProportionalLayout()</pre>
<div class="block">Creates a layout manager which layouts its component 
 with a default gap of 5 pixels between them.</div>
</li>
</ul>
<a name="ProportionalLayout-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ProportionalLayout</h4>
<pre>public&nbsp;ProportionalLayout(int&nbsp;gap)</pre>
<div class="block">Creates a layout manager which layouts its component 
 with a given <code>gap</code> between them.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addLayoutComponent-java.awt.Component-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLayoutComponent</h4>
<pre>public&nbsp;void&nbsp;addLayoutComponent(java.awt.Component&nbsp;component,
                               java.lang.Object&nbsp;constraints)</pre>
<div class="block">Records a given <code>component</code> in this layout manager as the component at 
 <code>Constraints.TOP</code> or at <code>Constraints.BOTTOM</code> of its container.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>addLayoutComponent</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager2</code></dd>
</dl>
</li>
</ul>
<a name="addLayoutComponent-java.lang.String-java.awt.Component-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLayoutComponent</h4>
<pre>public&nbsp;void&nbsp;addLayoutComponent(java.lang.String&nbsp;name,
                               java.awt.Component&nbsp;comp)</pre>
<div class="block">Do not use.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>addLayoutComponent</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager</code></dd>
</dl>
</li>
</ul>
<a name="removeLayoutComponent-java.awt.Component-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeLayoutComponent</h4>
<pre>public&nbsp;void&nbsp;removeLayoutComponent(java.awt.Component&nbsp;component)</pre>
<div class="block">Removes the given <code>component</code> from the ones managed by this layout manager.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>removeLayoutComponent</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager</code></dd>
</dl>
</li>
</ul>
<a name="getLayoutAlignmentX-java.awt.Container-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayoutAlignmentX</h4>
<pre>public&nbsp;float&nbsp;getLayoutAlignmentX(java.awt.Container&nbsp;target)</pre>
<div class="block">Returns 0.5.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getLayoutAlignmentX</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager2</code></dd>
</dl>
</li>
</ul>
<a name="getLayoutAlignmentY-java.awt.Container-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayoutAlignmentY</h4>
<pre>public&nbsp;float&nbsp;getLayoutAlignmentY(java.awt.Container&nbsp;target)</pre>
<div class="block">Return 0.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getLayoutAlignmentY</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager2</code></dd>
</dl>
</li>
</ul>
<a name="invalidateLayout-java.awt.Container-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>invalidateLayout</h4>
<pre>public&nbsp;void&nbsp;invalidateLayout(java.awt.Container&nbsp;target)</pre>
<div class="block">Invalidates layout.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>invalidateLayout</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager2</code></dd>
</dl>
</li>
</ul>
<a name="layoutContainer-java.awt.Container-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>layoutContainer</h4>
<pre>public&nbsp;void&nbsp;layoutContainer(java.awt.Container&nbsp;parent)</pre>
<div class="block">Layouts the container.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>layoutContainer</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager</code></dd>
</dl>
</li>
</ul>
<a name="minimumLayoutSize-java.awt.Container-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>minimumLayoutSize</h4>
<pre>public&nbsp;java.awt.Dimension&nbsp;minimumLayoutSize(java.awt.Container&nbsp;parent)</pre>
<div class="block">Returns the largest minimum width of the components managed by this layout manager,
 and the sum of their minimum heights.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>minimumLayoutSize</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager</code></dd>
</dl>
</li>
</ul>
<a name="maximumLayoutSize-java.awt.Container-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>maximumLayoutSize</h4>
<pre>public&nbsp;java.awt.Dimension&nbsp;maximumLayoutSize(java.awt.Container&nbsp;parent)</pre>
<div class="block">Returns the largest maximum width of the components managed by this layout manager,
 and the sum of their maximum heights.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>maximumLayoutSize</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager2</code></dd>
</dl>
</li>
</ul>
<a name="preferredLayoutSize-java.awt.Container-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>preferredLayoutSize</h4>
<pre>public&nbsp;java.awt.Dimension&nbsp;preferredLayoutSize(java.awt.Container&nbsp;parent)</pre>
<div class="block">Returns the largest preferred width of the components managed by this layout manager,
 and the sum of their preferred heights.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>preferredLayoutSize</code>&nbsp;in interface&nbsp;<code>java.awt.LayoutManager</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProportionalLayout.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/PrintPreviewPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.Constraints.html" title="enum in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ProportionalLayout.html" target="_top">Frames</a></li>
<li><a href="ProportionalLayout.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
