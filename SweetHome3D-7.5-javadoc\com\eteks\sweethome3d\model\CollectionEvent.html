<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>CollectionEvent (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CollectionEvent (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CollectionEvent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/CollectionEvent.html" target="_top">Frames</a></li>
<li><a href="CollectionEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.util.EventObject">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class CollectionEvent" class="title">Class CollectionEvent&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.util.EventObject</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.CollectionEvent&lt;T&gt;</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">CollectionEvent&lt;T&gt;</span>
extends java.util.EventObject</pre>
<div class="block">Type of event notified when an item is added or deleted from a list.
 <code>T</code> is the type of item stored in the collection.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.CollectionEvent">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a></span></code>
<div class="block">The type of change in the collection.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.util.EventObject">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.util.EventObject</h3>
<code>source</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#CollectionEvent-java.lang.Object-T-com.eteks.sweethome3d.model.CollectionEvent.Type-">CollectionEvent</a></span>(java.lang.Object&nbsp;source,
               <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="type parameter in CollectionEvent">T</a>&nbsp;item,
               <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;type)</code>
<div class="block">Creates an event for an item that has no index.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#CollectionEvent-java.lang.Object-T-int-com.eteks.sweethome3d.model.CollectionEvent.Type-">CollectionEvent</a></span>(java.lang.Object&nbsp;source,
               <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="type parameter in CollectionEvent">T</a>&nbsp;item,
               int&nbsp;index,
               <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;type)</code>
<div class="block">Creates an event for an item with its index.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getIndex--">getIndex</a></span>()</code>
<div class="block">Returns the index of the item in collection or -1 if this index is unknown.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="type parameter in CollectionEvent">T</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getItem--">getItem</a></span>()</code>
<div class="block">Returns the added or deleted item.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html#getType--">getType</a></span>()</code>
<div class="block">Returns the type of event.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.EventObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.util.EventObject</h3>
<code>getSource, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CollectionEvent-java.lang.Object-java.lang.Object-com.eteks.sweethome3d.model.CollectionEvent.Type-">
<!--   -->
</a><a name="CollectionEvent-java.lang.Object-T-com.eteks.sweethome3d.model.CollectionEvent.Type-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CollectionEvent</h4>
<pre>public&nbsp;CollectionEvent(java.lang.Object&nbsp;source,
                       <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="type parameter in CollectionEvent">T</a>&nbsp;item,
                       <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;type)</pre>
<div class="block">Creates an event for an item that has no index.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - the object to which an item was added or deleted</dd>
<dd><code>item</code> - the added or deleted item</dd>
<dd><code>type</code> - <code>CollectionEvent.Type.ADD</code> or <code>CollectionEvent.Type.DELETE</code></dd>
</dl>
</li>
</ul>
<a name="CollectionEvent-java.lang.Object-java.lang.Object-int-com.eteks.sweethome3d.model.CollectionEvent.Type-">
<!--   -->
</a><a name="CollectionEvent-java.lang.Object-T-int-com.eteks.sweethome3d.model.CollectionEvent.Type-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CollectionEvent</h4>
<pre>public&nbsp;CollectionEvent(java.lang.Object&nbsp;source,
                       <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="type parameter in CollectionEvent">T</a>&nbsp;item,
                       int&nbsp;index,
                       <a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;type)</pre>
<div class="block">Creates an event for an item with its index.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - the object to which an item was added or deleted</dd>
<dd><code>item</code> - the added or deleted item</dd>
<dd><code>index</code> - the index at which the item was added or deleted, or -1 if unknown</dd>
<dd><code>type</code> - <code>CollectionEvent.Type.ADD</code> or <code>CollectionEvent.Type.DELETE</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getItem--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getItem</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.html" title="type parameter in CollectionEvent">T</a>&nbsp;getItem()</pre>
<div class="block">Returns the added or deleted item.</div>
</li>
</ul>
<a name="getIndex--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIndex</h4>
<pre>public&nbsp;int&nbsp;getIndex()</pre>
<div class="block">Returns the index of the item in collection or -1 if this index is unknown.</div>
</li>
</ul>
<a name="getType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getType</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model">CollectionEvent.Type</a>&nbsp;getType()</pre>
<div class="block">Returns the type of event.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/CollectionEvent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/CollectionChangeSupport.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/CollectionEvent.Type.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/CollectionEvent.html" target="_top">Frames</a></li>
<li><a href="CollectionEvent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.util.EventObject">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
