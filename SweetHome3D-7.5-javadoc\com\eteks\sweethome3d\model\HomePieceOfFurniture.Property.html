<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomePieceOfFurniture.Property (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomePieceOfFurniture.Property (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePieceOfFurniture.Property.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture.Property.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Enum HomePieceOfFurniture.Property" class="title">Enum HomePieceOfFurniture.Property</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomePieceOfFurniture.Property</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a>&gt;</dd>
</dl>
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></dd>
</dl>
<hr>
<br>
<pre>public static enum <span class="typeNameLabel">HomePieceOfFurniture.Property</span>
extends java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a>&gt;</pre>
<div class="block">The properties of a piece of furniture that may change. <code>PropertyChangeListener</code>s added
 to a piece of furniture will be notified under a property name equal to the string value of one these properties.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#ANGLE">ANGLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#BACK_FACE_SHOWN">BACK_FACE_SHOWN</a></span></code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;&nbsp;</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#CATALOG_ID">CATALOG_ID</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#COLOR">COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#CREATOR">CREATOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#CURRENCY">CURRENCY</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#DEPTH">DEPTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#DEPTH_IN_PLAN">DEPTH_IN_PLAN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#DESCRIPTION">DESCRIPTION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#ELEVATION">ELEVATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#HEIGHT">HEIGHT</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#HEIGHT_IN_PLAN">HEIGHT_IN_PLAN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#ICON">ICON</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#INFORMATION">INFORMATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#LEVEL">LEVEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#LICENSE">LICENSE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#MODEL">MODEL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#MODEL_FLAGS">MODEL_FLAGS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#MODEL_MATERIALS">MODEL_MATERIALS</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#MODEL_MIRRORED">MODEL_MIRRORED</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#MODEL_ROTATION">MODEL_ROTATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#MODEL_TRANSFORMATIONS">MODEL_TRANSFORMATIONS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#MOVABLE">MOVABLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#NAME">NAME</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#NAME_ANGLE">NAME_ANGLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#NAME_STYLE">NAME_STYLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#NAME_VISIBLE">NAME_VISIBLE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#NAME_X_OFFSET">NAME_X_OFFSET</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#NAME_Y_OFFSET">NAME_Y_OFFSET</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#PITCH">PITCH</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#PLAN_ICON">PLAN_ICON</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#PRICE">PRICE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#ROLL">ROLL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#SHININESS">SHININESS</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#STAIRCASE_CUT_OUT_SHAPE">STAIRCASE_CUT_OUT_SHAPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#TEXTURE">TEXTURE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#VALUE_ADDED_TAX_PERCENTAGE">VALUE_ADDED_TAX_PERCENTAGE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#VISIBLE">VISIBLE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#WIDTH">WIDTH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#WIDTH_IN_PLAN">WIDTH_IN_PLAN</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#X">X</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#Y">Y</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="CATALOG_ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CATALOG_ID</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> CATALOG_ID</pre>
</li>
</ul>
<a name="NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> NAME</pre>
</li>
</ul>
<a name="NAME_VISIBLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME_VISIBLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> NAME_VISIBLE</pre>
</li>
</ul>
<a name="NAME_X_OFFSET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME_X_OFFSET</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> NAME_X_OFFSET</pre>
</li>
</ul>
<a name="NAME_Y_OFFSET">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME_Y_OFFSET</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> NAME_Y_OFFSET</pre>
</li>
</ul>
<a name="NAME_STYLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME_STYLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> NAME_STYLE</pre>
</li>
</ul>
<a name="NAME_ANGLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME_ANGLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> NAME_ANGLE</pre>
</li>
</ul>
<a name="DESCRIPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESCRIPTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> DESCRIPTION</pre>
</li>
</ul>
<a name="INFORMATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INFORMATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> INFORMATION</pre>
</li>
</ul>
<a name="CREATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> CREATOR</pre>
</li>
</ul>
<a name="LICENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LICENSE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> LICENSE</pre>
</li>
</ul>
<a name="PRICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRICE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> PRICE</pre>
</li>
</ul>
<a name="VALUE_ADDED_TAX_PERCENTAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VALUE_ADDED_TAX_PERCENTAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> VALUE_ADDED_TAX_PERCENTAGE</pre>
</li>
</ul>
<a name="CURRENCY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CURRENCY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> CURRENCY</pre>
</li>
</ul>
<a name="ICON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ICON</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> ICON</pre>
</li>
</ul>
<a name="PLAN_ICON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLAN_ICON</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> PLAN_ICON</pre>
</li>
</ul>
<a name="MODEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> MODEL</pre>
</li>
</ul>
<a name="WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WIDTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> WIDTH</pre>
</li>
</ul>
<a name="WIDTH_IN_PLAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WIDTH_IN_PLAN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> WIDTH_IN_PLAN</pre>
</li>
</ul>
<a name="DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEPTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> DEPTH</pre>
</li>
</ul>
<a name="DEPTH_IN_PLAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEPTH_IN_PLAN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> DEPTH_IN_PLAN</pre>
</li>
</ul>
<a name="HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HEIGHT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> HEIGHT</pre>
</li>
</ul>
<a name="HEIGHT_IN_PLAN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HEIGHT_IN_PLAN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> HEIGHT_IN_PLAN</pre>
</li>
</ul>
<a name="COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>COLOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> COLOR</pre>
</li>
</ul>
<a name="TEXTURE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXTURE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> TEXTURE</pre>
</li>
</ul>
<a name="MODEL_MATERIALS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_MATERIALS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> MODEL_MATERIALS</pre>
</li>
</ul>
<a name="MODEL_TRANSFORMATIONS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_TRANSFORMATIONS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> MODEL_TRANSFORMATIONS</pre>
</li>
</ul>
<a name="STAIRCASE_CUT_OUT_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STAIRCASE_CUT_OUT_SHAPE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> STAIRCASE_CUT_OUT_SHAPE</pre>
</li>
</ul>
<a name="SHININESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHININESS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> SHININESS</pre>
</li>
</ul>
<a name="VISIBLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VISIBLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> VISIBLE</pre>
</li>
</ul>
<a name="X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>X</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> X</pre>
</li>
</ul>
<a name="Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Y</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> Y</pre>
</li>
</ul>
<a name="ELEVATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ELEVATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> ELEVATION</pre>
</li>
</ul>
<a name="ANGLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ANGLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> ANGLE</pre>
</li>
</ul>
<a name="PITCH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PITCH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> PITCH</pre>
</li>
</ul>
<a name="ROLL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ROLL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> ROLL</pre>
</li>
</ul>
<a name="MODEL_ROTATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_ROTATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> MODEL_ROTATION</pre>
</li>
</ul>
<a name="MODEL_FLAGS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_FLAGS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> MODEL_FLAGS</pre>
</li>
</ul>
<a name="MODEL_MIRRORED">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_MIRRORED</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> MODEL_MIRRORED</pre>
</li>
</ul>
<a name="BACK_FACE_SHOWN">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>BACK_FACE_SHOWN</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> BACK_FACE_SHOWN</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;</div>
</li>
</ul>
<a name="MOVABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOVABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> MOVABLE</pre>
</li>
</ul>
<a name="LEVEL">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LEVEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a> LEVEL</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (HomePieceOfFurniture.Property c : HomePieceOfFurniture.Property.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePieceOfFurniture.Property.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture.Property.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
