<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>DefaultUserPreferences (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DefaultUserPreferences (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultUserPreferences.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultUserPreferences.html" target="_top">Frames</a></li>
<li><a href="DefaultUserPreferences.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class DefaultUserPreferences" class="title">Class DefaultUserPreferences</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.UserPreferences</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.DefaultUserPreferences</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DefaultUserPreferences</span>
extends <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></pre>
<div class="block">Default user preferences.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#FURNITURE_LIBRARY_TYPE">FURNITURE_LIBRARY_TYPE</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#LANGUAGE_LIBRARY_TYPE">LANGUAGE_LIBRARY_TYPE</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#TEXTURES_LIBRARY_TYPE">TEXTURES_LIBRARY_TYPE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#DefaultUserPreferences--">DefaultUserPreferences</a></span>()</code>
<div class="block">Creates default user preferences read from resource files in the default language.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage additional furniture libraries.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage additional language libraries.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage additional textures libraries.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage additional furniture libraries.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Throws an exception because default user preferences don't support libraries.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage language libraries.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage textures libraries.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#write--">write</a></span>()</code>
<div class="block">Throws an exception because default user preferences can't be written
 with this class.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addAutoCompletionString-java.lang.String-java.lang.String-">addAutoCompletionString</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addPropertyChangeListener-com.eteks.sweethome3d.model.UserPreferences.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoCompletedProperties--">getAutoCompletedProperties</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoCompletionStrings-java.lang.String-">getAutoCompletionStrings</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoSaveDelayForRecovery--">getAutoSaveDelayForRecovery</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultFontName--">getDefaultFontName</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultSupportedLanguages--">getDefaultSupportedLanguages</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultTextStyle-java.lang.Class-">getDefaultTextStyle</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultValueAddedTaxPercentage--">getDefaultValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getFurnitureCatalog--">getFurnitureCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getFurnitureModelIconSize--">getFurnitureModelIconSize</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getHomeExamples--">getHomeExamples</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLanguage--">getLanguage</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLengthUnit--">getLengthUnit</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedString-java.lang.Class-java.lang.String-java.lang.Object...-">getLocalizedString</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedString-java.lang.String-java.lang.String-java.lang.Object...-">getLocalizedString</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedStringKeys-java.lang.String-">getLocalizedStringKeys</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewFloorThickness--">getNewFloorThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewRoomFloorColor--">getNewRoomFloorColor</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallBaseboardHeight--">getNewWallBaseboardHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallBaseboardThickness--">getNewWallBaseboardThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallHeight--">getNewWallHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallPattern--">getNewWallPattern</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallThickness--">getNewWallThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getPatternsCatalog--">getPatternsCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getPhotoRenderer--">getPhotoRenderer</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentColors--">getRecentColors</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentHomes--">getRecentHomes</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentHomesMaxCount--">getRecentHomesMaxCount</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentTextures--">getRecentTextures</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getResourceClassLoaders--">getResourceClassLoaders</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getStoredCamerasMaxCount--">getStoredCamerasMaxCount</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getSupportedLanguages--">getSupportedLanguages</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getTexturesCatalog--">getTexturesCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getUpdatesMinimumDate--">getUpdatesMinimumDate</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getWallPattern--">getWallPattern</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isActionTipIgnored-java.lang.String-">isActionTipIgnored</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isAerialViewCenteredOnSelectionEnabled--">isAerialViewCenteredOnSelectionEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isCheckUpdatesEnabled--">isCheckUpdatesEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isDrawingModeEnabled--">isDrawingModeEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isEditingIn3DViewEnabled--">isEditingIn3DViewEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isFurnitureCatalogViewedInTree--">isFurnitureCatalogViewedInTree</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isFurnitureViewedFromTop--">isFurnitureViewedFromTop</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isGridVisible--">isGridVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isLanguageEditable--">isLanguageEditable</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isMagnetismEnabled--">isMagnetismEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isNavigationPanelVisible--">isNavigationPanelVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isObserverCameraSelectedAtChange--">isObserverCameraSelectedAtChange</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isRoomFloorColoredOrTextured--">isRoomFloorColoredOrTextured</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isRulersVisible--">isRulersVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isValueAddedTaxEnabled--">isValueAddedTaxEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#removePropertyChangeListener-com.eteks.sweethome3d.model.UserPreferences.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#resetIgnoredActionTips--">resetIgnoredActionTips</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setActionTipIgnored-java.lang.String-">setActionTipIgnored</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAerialViewCenteredOnSelectionEnabled-boolean-">setAerialViewCenteredOnSelectionEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAutoCompletionStrings-java.lang.String-java.util.List-">setAutoCompletionStrings</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAutoSaveDelayForRecovery-int-">setAutoSaveDelayForRecovery</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setCheckUpdatesEnabled-boolean-">setCheckUpdatesEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setCurrency-java.lang.String-">setCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setDefaultFontName-java.lang.String-">setDefaultFontName</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setDefaultValueAddedTaxPercentage-java.math.BigDecimal-">setDefaultValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setEditingIn3DViewEnabled-boolean-">setEditingIn3DViewEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFloorColoredOrTextured-boolean-">setFloorColoredOrTextured</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureCatalog-com.eteks.sweethome3d.model.FurnitureCatalog-">setFurnitureCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureCatalogViewedInTree-boolean-">setFurnitureCatalogViewedInTree</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureModelIconSize-int-">setFurnitureModelIconSize</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureViewedFromTop-boolean-">setFurnitureViewedFromTop</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setGridVisible-boolean-">setGridVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setHomeExamples-java.util.List-">setHomeExamples</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setLanguage-java.lang.String-">setLanguage</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setMagnetismEnabled-boolean-">setMagnetismEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNavigationPanelVisible-boolean-">setNavigationPanelVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewFloorThickness-float-">setNewFloorThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewRoomFloorColor-java.lang.Integer-">setNewRoomFloorColor</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallBaseboardHeight-float-">setNewWallBaseboardHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallBaseboardThickness-float-">setNewWallBaseboardThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallHeight-float-">setNewWallHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallPattern-com.eteks.sweethome3d.model.TextureImage-">setNewWallPattern</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallThickness-float-">setNewWallThickness</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setObserverCameraSelectedAtChange-boolean-">setObserverCameraSelectedAtChange</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setPatternsCatalog-com.eteks.sweethome3d.model.PatternsCatalog-">setPatternsCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setPhotoRenderer-java.lang.String-">setPhotoRenderer</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentColors-java.util.List-">setRecentColors</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentHomes-java.util.List-">setRecentHomes</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentTextures-java.util.List-">setRecentTextures</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRulersVisible-boolean-">setRulersVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setSupportedLanguages-java.lang.String:A-">setSupportedLanguages</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setTexturesCatalog-com.eteks.sweethome3d.model.TexturesCatalog-">setTexturesCatalog</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setUnit-com.eteks.sweethome3d.model.LengthUnit-">setUnit</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setUpdatesMinimumDate-java.lang.Long-">setUpdatesMinimumDate</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setValueAddedTaxEnabled-boolean-">setValueAddedTaxEnabled</a>, <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setWallPattern-com.eteks.sweethome3d.model.TextureImage-">setWallPattern</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DefaultUserPreferences--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DefaultUserPreferences</h4>
<pre>public&nbsp;DefaultUserPreferences()</pre>
<div class="block">Creates default user preferences read from resource files in the default language.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="write--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write()
           throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Throws an exception because default user preferences can't be written
 with this class.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#write--">write</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code> - if user preferences couldn'y be saved.</dd>
</dl>
</li>
</ul>
<a name="languageLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>languageLibraryExists</h4>
<pre>public&nbsp;boolean&nbsp;languageLibraryExists(java.lang.String&nbsp;name)
                              throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Throws an exception because default user preferences can't manage language libraries.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="addLanguageLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLanguageLibrary</h4>
<pre>public&nbsp;void&nbsp;addLanguageLibrary(java.lang.String&nbsp;name)
                        throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Throws an exception because default user preferences can't manage additional language libraries.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="furnitureLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>furnitureLibraryExists</h4>
<pre>public&nbsp;boolean&nbsp;furnitureLibraryExists(java.lang.String&nbsp;name)
                               throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Throws an exception because default user preferences can't manage additional furniture libraries.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="addFurnitureLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurnitureLibrary</h4>
<pre>public&nbsp;void&nbsp;addFurnitureLibrary(java.lang.String&nbsp;name)
                         throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Throws an exception because default user preferences can't manage additional furniture libraries.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="texturesLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>texturesLibraryExists</h4>
<pre>public&nbsp;boolean&nbsp;texturesLibraryExists(java.lang.String&nbsp;name)
                              throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Throws an exception because default user preferences can't manage textures libraries.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="addTexturesLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTexturesLibrary</h4>
<pre>public&nbsp;void&nbsp;addTexturesLibrary(java.lang.String&nbsp;name)
                        throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Throws an exception because default user preferences can't manage additional textures libraries.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="getLibraries--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getLibraries</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;getLibraries()</pre>
<div class="block">Throws an exception because default user preferences don't support libraries.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLibraries--">getLibraries</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultUserPreferences.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultUserPreferences.html" target="_top">Frames</a></li>
<li><a href="DefaultUserPreferences.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.model.UserPreferences">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
