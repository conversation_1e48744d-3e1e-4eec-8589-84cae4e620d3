/*
 * ValidationResult.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import java.util.Collections;
import java.util.List;

/**
 * Result of configuration validation.
 * 
 * <AUTHOR>
 */
public class ValidationResult {
  private final boolean valid;
  private final List<String> errors;
  
  /**
   * Creates a validation result.
   */
  public ValidationResult(boolean valid, List<String> errors) {
    this.valid = valid;
    this.errors = errors != null ? errors : Collections.emptyList();
  }
  
  /**
   * Returns whether the validation passed.
   */
  public boolean isValid() {
    return valid;
  }
  
  /**
   * Returns the list of validation errors.
   */
  public List<String> getErrors() {
    return Collections.unmodifiableList(errors);
  }
  
  /**
   * Returns the first error message, or null if no errors.
   */
  public String getFirstError() {
    return errors.isEmpty() ? null : errors.get(0);
  }
  
  @Override
  public String toString() {
    return "ValidationResult{" +
           "valid=" + valid +
           ", errors=" + errors +
           '}';
  }
}
