<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:48 CEST 2024 -->
<title>FurnitureTablePanel (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FurnitureTablePanel (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FurnitureTablePanel.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTable.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.UserPreferencesChangeListener.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/FurnitureTablePanel.html" target="_top">Frames</a></li>
<li><a href="FurnitureTablePanel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class FurnitureTablePanel" class="title">Class FurnitureTablePanel</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>javax.swing.JComponent</li>
<li>
<ul class="inheritance">
<li>javax.swing.JPanel</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.FurnitureTablePanel</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>, java.awt.image.ImageObserver, java.awt.MenuContainer, java.awt.print.Printable, java.io.Serializable, javax.accessibility.Accessible</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">FurnitureTablePanel</span>
extends javax.swing.JPanel
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a>, java.awt.print.Printable</pre>
<div class="block">A panel displaying home furniture table and other information like totals.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.FurnitureTablePanel">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.UserPreferencesChangeListener.html" title="class in com.eteks.sweethome3d.swing">FurnitureTablePanel.UserPreferencesChangeListener</a></span></code>
<div class="block">Preferences property listener bound to this component with a weak reference to avoid
 strong link between preferences and this component.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JPanel">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JPanel</h3>
<code>javax.swing.JPanel.AccessibleJPanel</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JComponent</h3>
<code>javax.swing.JComponent.AccessibleJComponent</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.FurnitureView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView.FurnitureFilter</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.TransferableView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView.TransferObserver</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.ExportableView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JComponent</h3>
<code>listenerList, TOOL_TIP_TEXT_KEY, ui, UNDEFINED_CONDITION, WHEN_ANCESTOR_OF_FOCUSED_COMPONENT, WHEN_FOCUSED, WHEN_IN_FOCUSED_WINDOW</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>accessibleContext, BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.print.Printable">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.print.Printable</h3>
<code>NO_SUCH_PAGE, PAGE_EXISTS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#FurnitureTablePanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">FurnitureTablePanel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#createFurnitureTable-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">createFurnitureTable</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                    <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                    <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;controller)</code>
<div class="block">Creates and returns the main furniture table displayed by this component.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">createTransferData</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>&nbsp;dataType)</code>
<div class="block">Returns a copy of the furniture data for transfer purpose.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#doLayout--">doLayout</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">exportData</a></span>(java.io.OutputStream&nbsp;out,
          <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType,
          java.util.Properties&nbsp;settings)</code>
<div class="block">Writes in the given stream the content of the table at CSV format if this is the requested format.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>javax.swing.JPopupMenu</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#getComponentPopupMenu--">getComponentPopupMenu</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView.FurnitureFilter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#getFurnitureFilter--">getFurnitureFilter</a></span>()</code>
<div class="block">Returns the filter applied to the furniture displayed in this component.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">isFormatTypeSupported</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType)</code>
<div class="block">Returns <code>true</code> if the given format is CSV.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#print-java.awt.Graphics-java.awt.print.PageFormat-int-">print</a></span>(java.awt.Graphics&nbsp;g,
     java.awt.print.PageFormat&nbsp;pageFormat,
     int&nbsp;pageIndex)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#setComponentPopupMenu-javax.swing.JPopupMenu-">setComponentPopupMenu</a></span>(javax.swing.JPopupMenu&nbsp;popup)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#setFurnitureFilter-com.eteks.sweethome3d.viewcontroller.FurnitureView.FurnitureFilter-">setFurnitureFilter</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView.FurnitureFilter</a>&nbsp;filter)</code>
<div class="block">Sets the filter applied to the furniture displayed by this component.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html#setTransferHandler-javax.swing.TransferHandler-">setTransferHandler</a></span>(javax.swing.TransferHandler&nbsp;newHandler)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JPanel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JPanel</h3>
<code>getAccessibleContext, getUI, getUIClassID, paramString, setUI, updateUI</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JComponent</h3>
<code>addAncestorListener, addNotify, addVetoableChangeListener, computeVisibleRect, contains, createToolTip, disable, enable, firePropertyChange, firePropertyChange, firePropertyChange, fireVetoableChange, getActionForKeyStroke, getActionMap, getAlignmentX, getAlignmentY, getAncestorListeners, getAutoscrolls, getBaseline, getBaselineResizeBehavior, getBorder, getBounds, getClientProperty, getComponentGraphics, getConditionForKeyStroke, getDebugGraphicsOptions, getDefaultLocale, getFontMetrics, getGraphics, getHeight, getInheritsPopupMenu, getInputMap, getInputMap, getInputVerifier, getInsets, getInsets, getListeners, getLocation, getMaximumSize, getMinimumSize, getNextFocusableComponent, getPopupLocation, getPreferredSize, getRegisteredKeyStrokes, getRootPane, getSize, getToolTipLocation, getToolTipText, getToolTipText, getTopLevelAncestor, getTransferHandler, getVerifyInputWhenFocusTarget, getVetoableChangeListeners, getVisibleRect, getWidth, getX, getY, grabFocus, hide, isDoubleBuffered, isLightweightComponent, isManagingFocus, isOpaque, isOptimizedDrawingEnabled, isPaintingForPrint, isPaintingOrigin, isPaintingTile, isRequestFocusEnabled, isValidateRoot, paint, paintBorder, paintChildren, paintComponent, paintImmediately, paintImmediately, print, printAll, printBorder, printChildren, printComponent, processComponentKeyEvent, processKeyBinding, processKeyEvent, processMouseEvent, processMouseMotionEvent, putClientProperty, registerKeyboardAction, registerKeyboardAction, removeAncestorListener, removeNotify, removeVetoableChangeListener, repaint, repaint, requestDefaultFocus, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, resetKeyboardActions, reshape, revalidate, scrollRectToVisible, setActionMap, setAlignmentX, setAlignmentY, setAutoscrolls, setBackground, setBorder, setDebugGraphicsOptions, setDefaultLocale, setDoubleBuffered, setEnabled, setFocusTraversalKeys, setFont, setForeground, setInheritsPopupMenu, setInputMap, setInputVerifier, setMaximumSize, setMinimumSize, setNextFocusableComponent, setOpaque, setPreferredSize, setRequestFocusEnabled, setToolTipText, setUI, setVerifyInputWhenFocusTarget, setVisible, unregisterKeyboardAction, update</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addImpl, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, findComponentAt, findComponentAt, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getLayout, getMousePosition, insets, invalidate, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paintComponents, preferredSize, printComponents, processContainerEvent, processEvent, remove, remove, removeAll, removeContainerListener, setComponentZOrder, setFocusCycleRoot, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setLayout, transferFocusDownCycle, validate, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addFocusListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseListener, addMouseMotionListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, createImage, createImage, createVolatileImage, createVolatileImage, disableEvents, dispatchEvent, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getBackground, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getCursor, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getForeground, getGraphicsConfiguration, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocale, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getToolkit, getTreeLock, gotFocus, handleEvent, hasFocus, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseListener, removeMouseMotionListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, resize, resize, setBounds, setBounds, setComponentOrientation, setCursor, setDropTarget, setFocusable, setFocusTraversalKeysEnabled, setIgnoreRepaint, setLocale, setLocation, setLocation, setName, setSize, setSize, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="FurnitureTablePanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FurnitureTablePanel</h4>
<pre>public&nbsp;FurnitureTablePanel(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                           <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                           <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;controller)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createFurnitureTable-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createFurnitureTable</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a>&nbsp;createFurnitureTable(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                             <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                             <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;controller)</pre>
<div class="block">Creates and returns the main furniture table displayed by this component.</div>
</li>
</ul>
<a name="doLayout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>doLayout</h4>
<pre>public&nbsp;void&nbsp;doLayout()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>doLayout</code>&nbsp;in class&nbsp;<code>java.awt.Container</code></dd>
</dl>
</li>
</ul>
<a name="print-java.awt.Graphics-java.awt.print.PageFormat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;int&nbsp;print(java.awt.Graphics&nbsp;g,
                 java.awt.print.PageFormat&nbsp;pageFormat,
                 int&nbsp;pageIndex)
          throws java.awt.print.PrinterException</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>print</code>&nbsp;in interface&nbsp;<code>java.awt.print.Printable</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.awt.print.PrinterException</code></dd>
</dl>
</li>
</ul>
<a name="setTransferHandler-javax.swing.TransferHandler-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransferHandler</h4>
<pre>public&nbsp;void&nbsp;setTransferHandler(javax.swing.TransferHandler&nbsp;newHandler)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setTransferHandler</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="setComponentPopupMenu-javax.swing.JPopupMenu-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComponentPopupMenu</h4>
<pre>public&nbsp;void&nbsp;setComponentPopupMenu(javax.swing.JPopupMenu&nbsp;popup)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setComponentPopupMenu</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="getComponentPopupMenu--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getComponentPopupMenu</h4>
<pre>public&nbsp;javax.swing.JPopupMenu&nbsp;getComponentPopupMenu()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getComponentPopupMenu</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTransferData</h4>
<pre>public&nbsp;java.lang.Object&nbsp;createTransferData(<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>&nbsp;dataType)</pre>
<div class="block">Returns a copy of the furniture data for transfer purpose.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">createTransferData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></code></dd>
</dl>
</li>
</ul>
<a name="isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFormatTypeSupported</h4>
<pre>public&nbsp;boolean&nbsp;isFormatTypeSupported(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType)</pre>
<div class="block">Returns <code>true</code> if the given format is CSV.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html#isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">isFormatTypeSupported</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></code></dd>
</dl>
</li>
</ul>
<a name="exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportData</h4>
<pre>public&nbsp;void&nbsp;exportData(java.io.OutputStream&nbsp;out,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType,
                       java.util.Properties&nbsp;settings)
                throws java.io.IOException</pre>
<div class="block">Writes in the given stream the content of the table at CSV format if this is the requested format.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html#exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">exportData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="setFurnitureFilter-com.eteks.sweethome3d.viewcontroller.FurnitureView.FurnitureFilter-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureFilter</h4>
<pre>public&nbsp;void&nbsp;setFurnitureFilter(<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView.FurnitureFilter</a>&nbsp;filter)</pre>
<div class="block">Sets the filter applied to the furniture displayed by this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html#setFurnitureFilter-com.eteks.sweethome3d.viewcontroller.FurnitureView.FurnitureFilter-">setFurnitureFilter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a></code></dd>
</dl>
</li>
</ul>
<a name="getFurnitureFilter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getFurnitureFilter</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.FurnitureFilter.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView.FurnitureFilter</a>&nbsp;getFurnitureFilter()</pre>
<div class="block">Returns the filter applied to the furniture displayed in this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html#getFurnitureFilter--">getFurnitureFilter</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FurnitureTablePanel.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTable.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.UserPreferencesChangeListener.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/FurnitureTablePanel.html" target="_top">Frames</a></li>
<li><a href="FurnitureTablePanel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
