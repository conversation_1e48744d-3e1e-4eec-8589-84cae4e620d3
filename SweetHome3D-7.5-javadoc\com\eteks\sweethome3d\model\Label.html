<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>Label (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Label (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Label.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/InterruptedRecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Label.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Label.html" target="_top">Frames</a></li>
<li><a href="Label.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class Label" class="title">Class Label</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.Label</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Label</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>
implements <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, <a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></pre>
<div class="block">A free label.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.5</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.Label">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.Property.html" title="enum in com.eteks.sweethome3d.model">Label.Property</a></span></code>
<div class="block">The properties of a label that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#Label-java.lang.String-float-float-">Label</a></span>(java.lang.String&nbsp;text,
     float&nbsp;x,
     float&nbsp;y)</code>
<div class="block">Creates a label with the given <code>text</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#Label-java.lang.String-java.lang.String-float-float-">Label</a></span>(java.lang.String&nbsp;id,
     java.lang.String&nbsp;text,
     float&nbsp;x,
     float&nbsp;y)</code>
<div class="block">Creates a label with the given <code>text</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this label.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#containsPoint-float-float-float-">containsPoint</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this text is at the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getAngle--">getAngle</a></span>()</code>
<div class="block">Returns the angle in radians around vertical axis used to display this label.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the color used to display the text of this label.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the elevation of this label on its level.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getGroundElevation--">getGroundElevation</a></span>()</code>
<div class="block">Returns the elevation of this label
 from the ground according to the elevation of its level.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getLevel--">getLevel</a></span>()</code>
<div class="block">Returns the level which this label belongs to.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getOutlineColor--">getOutlineColor</a></span>()</code>
<div class="block">Returns the color used to outline the text of this label.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns the pitch angle in radians used to rotate this label around horizontal axis in 3D.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getPoints--">getPoints</a></span>()</code>
<div class="block">Returns the point of this label.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getStyle--">getStyle</a></span>()</code>
<div class="block">Returns the style used to display the text of this label.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getText--">getText</a></span>()</code>
<div class="block">Returns the text of this label.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getX--">getX</a></span>()</code>
<div class="block">Returns the abscissa of the text of this label.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#getY--">getY</a></span>()</code>
<div class="block">Returns the ordinate of the text of this label.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></span>(float&nbsp;x0,
                   float&nbsp;y0,
                   float&nbsp;x1,
                   float&nbsp;y1)</code>
<div class="block">Returns <code>true</code> if the point of this label is contained
 in the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Returns <code>true</code> if this label is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index
 or if the elevation of this label is higher than <code>level</code> elevation.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#move-float-float-">move</a></span>(float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Moves this label of (<code>dx</code>, <code>dy</code>) units.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setAngle-float-">setAngle</a></span>(float&nbsp;angle)</code>
<div class="block">Sets the angle in radians around vertical axis used to display this label.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the color used to display the text of this label.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setElevation-float-">setElevation</a></span>(float&nbsp;elevation)</code>
<div class="block">Sets the elevation of this label on its level.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Sets the level of this label.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setOutlineColor-java.lang.Integer-">setOutlineColor</a></span>(java.lang.Integer&nbsp;outlineColor)</code>
<div class="block">Sets the color used to outline the text of this label.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setPitch-java.lang.Float-">setPitch</a></span>(java.lang.Float&nbsp;pitch)</code>
<div class="block">Sets the angle in radians used to rotate this label around horizontal axis in 3D.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setStyle-com.eteks.sweethome3d.model.TextStyle-">setStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style)</code>
<div class="block">Sets the style used to display the text of this label.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setText-java.lang.String-">setText</a></span>(java.lang.String&nbsp;text)</code>
<div class="block">Sets the text of this label.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setX-float-">setX</a></span>(float&nbsp;x)</code>
<div class="block">Sets the abscissa of the text of this label.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Label.html#setY-float-">setY</a></span>(float&nbsp;y)</code>
<div class="block">Sets the ordinate of the text of this label.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Label-java.lang.String-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Label</h4>
<pre>public&nbsp;Label(java.lang.String&nbsp;text,
             float&nbsp;x,
             float&nbsp;y)</pre>
<div class="block">Creates a label with the given <code>text</code>.</div>
</li>
</ul>
<a name="Label-java.lang.String-java.lang.String-float-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Label</h4>
<pre>public&nbsp;Label(java.lang.String&nbsp;id,
             java.lang.String&nbsp;text,
             float&nbsp;x,
             float&nbsp;y)</pre>
<div class="block">Creates a label with the given <code>text</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getText--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getText</h4>
<pre>public&nbsp;java.lang.String&nbsp;getText()</pre>
<div class="block">Returns the text of this label.</div>
</li>
</ul>
<a name="setText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setText</h4>
<pre>public&nbsp;void&nbsp;setText(java.lang.String&nbsp;text)</pre>
<div class="block">Sets the text of this label. Once this label is updated,
 listeners added to this label will receive a change notification.</div>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public&nbsp;float&nbsp;getX()</pre>
<div class="block">Returns the abscissa of the text of this label.</div>
</li>
</ul>
<a name="setX-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public&nbsp;void&nbsp;setX(float&nbsp;x)</pre>
<div class="block">Sets the abscissa of the text of this label. Once this label is updated,
 listeners added to this label will receive a change notification.</div>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public&nbsp;float&nbsp;getY()</pre>
<div class="block">Returns the ordinate of the text of this label.</div>
</li>
</ul>
<a name="setY-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public&nbsp;void&nbsp;setY(float&nbsp;y)</pre>
<div class="block">Sets the ordinate of the text of this label. Once this label is updated,
 listeners added to this label will receive a change notification.</div>
</li>
</ul>
<a name="getGroundElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroundElevation</h4>
<pre>public&nbsp;float&nbsp;getGroundElevation()</pre>
<div class="block">Returns the elevation of this label
 from the ground according to the elevation of its level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>public&nbsp;float&nbsp;getElevation()</pre>
<div class="block">Returns the elevation of this label on its level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Label.html#getPitch--"><code>getPitch()</code></a></dd>
</dl>
</li>
</ul>
<a name="setElevation-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevation</h4>
<pre>public&nbsp;void&nbsp;setElevation(float&nbsp;elevation)</pre>
<div class="block">Sets the elevation of this label on its level. Once this label is updated,
 listeners added to this label will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;getStyle()</pre>
<div class="block">Returns the style used to display the text of this label.</div>
</li>
</ul>
<a name="setStyle-com.eteks.sweethome3d.model.TextStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStyle</h4>
<pre>public&nbsp;void&nbsp;setStyle(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style)</pre>
<div class="block">Sets the style used to display the text of this label.
 Once this label is updated, listeners added to this label will receive a change notification.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the color used to display the text of this label.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the color used to display the text of this label.
 Once this label is updated, listeners added to this label will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getOutlineColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOutlineColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getOutlineColor()</pre>
<div class="block">Returns the color used to outline the text of this label.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="setOutlineColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOutlineColor</h4>
<pre>public&nbsp;void&nbsp;setOutlineColor(java.lang.Integer&nbsp;outlineColor)</pre>
<div class="block">Sets the color used to outline the text of this label.
 Once this label is updated, listeners added to this label will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="getAngle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAngle</h4>
<pre>public&nbsp;float&nbsp;getAngle()</pre>
<div class="block">Returns the angle in radians around vertical axis used to display this label.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="setAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngle</h4>
<pre>public&nbsp;void&nbsp;setAngle(float&nbsp;angle)</pre>
<div class="block">Sets the angle in radians around vertical axis used to display this label. Once this label is updated,
 listeners added to this label will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getPitch()</pre>
<div class="block">Returns the pitch angle in radians used to rotate this label around horizontal axis in 3D.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an angle in radians or <code>null</code> if the label shouldn't be displayed in 3D.
         A pitch angle equal to 0 should make this label fully visible when seen from top.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setPitch-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitch</h4>
<pre>public&nbsp;void&nbsp;setPitch(java.lang.Float&nbsp;pitch)</pre>
<div class="block">Sets the angle in radians used to rotate this label around horizontal axis in 3D. Once this label is updated,
 listeners added to this label will receive a change notification.
 Pitch axis is horizontal transverse axis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;getLevel()</pre>
<div class="block">Returns the level which this label belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#getLevel--">getLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevel</h4>
<pre>public&nbsp;void&nbsp;setLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Sets the level of this label. Once this label is updated,
 listeners added to this label will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isAtLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAtLevel</h4>
<pre>public&nbsp;boolean&nbsp;isAtLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Returns <code>true</code> if this label is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index
 or if the elevation of this label is higher than <code>level</code> elevation.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public&nbsp;float[][]&nbsp;getPoints()</pre>
<div class="block">Returns the point of this label.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#getPoints--">getPoints</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of the (x,y) coordinates of this label.</dd>
</dl>
</li>
</ul>
<a name="intersectsRectangle-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersectsRectangle</h4>
<pre>public&nbsp;boolean&nbsp;intersectsRectangle(float&nbsp;x0,
                                   float&nbsp;y0,
                                   float&nbsp;x1,
                                   float&nbsp;y1)</pre>
<div class="block">Returns <code>true</code> if the point of this label is contained
 in the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="containsPoint-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsPoint</h4>
<pre>public&nbsp;boolean&nbsp;containsPoint(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this text is at the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#containsPoint-float-float-float-">containsPoint</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="move-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>move</h4>
<pre>public&nbsp;void&nbsp;move(float&nbsp;dx,
                 float&nbsp;dy)</pre>
<div class="block">Moves this label of (<code>dx</code>, <code>dy</code>) units.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#move-float-float-">move</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this label.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Label.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/InterruptedRecorderException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Label.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Label.html" target="_top">Frames</a></li>
<li><a href="Label.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
