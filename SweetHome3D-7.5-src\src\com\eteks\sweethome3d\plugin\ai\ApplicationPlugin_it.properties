# ApplicationPlugin_it.properties
# Sweet Home 3D AI Plugin Configuration - Italian
# Copyright (c) 2025 Samuel Kpassegna

# Plugin identification
name=Analisi IA Planimetrie
description=Analizza le planimetrie utilizzando l'intelligenza artificiale per fornire approfondimenti e suggerimenti di miglioramento
provider=<PERSON> Kpassegna

# Features
features=Analisi IA, Approfondimenti Planimetrie, Fornitori IA Multipli, Controlli Privacy

# Requirements
requirements=Connessione Internet per fornitori IA cloud (opzionale per fornitori locali)

# UI Strings for internationalization
# Action properties
AIAction.Name=Analisi IA
AIAction.ShortDescription=Analizza planimetria con IA
AIAction.Menu=Strumenti

# Dialog titles
AIChatDialog.title=Analisi IA Planimetrie
AISettingsDialog.title=Impostazioni IA

# Button labels
button.send=Invia
button.newAnalysis=Nuova Analisi
button.settings=Impostazioni
button.testConnection=Testa Connessione
button.save=Salva
button.cancel=Annulla

# Labels
label.provider=Fornitore:
label.baseUrl=URL Base:
label.apiKey=Chiave API:
label.model=Modello:
label.temperature=Temperatura:
label.maxTokens=Token Max:
label.status=Stato:

# Messages
message.analyzing=Analizzando planimetria...
message.processingQuestion=Elaborando domanda...
message.testingConnection=Testando connessione...
message.connectionSuccessful=Connessione riuscita!
message.connectionFailed=Connessione fallita: {0}
message.configurationSaved=Configurazione salvata con successo
message.validationError=Errori di configurazione:\n{0}
message.noConfiguration=Fornitore IA non configurato. Si prega di configurare prima le impostazioni.

# Analysis prompt
analysis.prompt=Si prega di analizzare questa planimetria e fornire approfondimenti completi inclusi:\n1. Efficienza del layout e utilizzo dello spazio\n2. Flusso del traffico e modelli di circolazione\n3. Opportunità di illuminazione naturale e ventilazione\n4. Considerazioni di accessibilità\n5. Relazioni funzionali tra spazi\n6. Suggerimenti di miglioramento\n7. Conformità agli standard edilizi comuni\n8. Considerazioni di efficienza energetica\n\nSi prega di fornire raccomandazioni specifiche e attuabili che migliorerebbero la funzionalità, il comfort e l'attrattiva estetica di questo spazio.

# Error messages
error.analysisError=Errore di analisi: {0}
error.configurationError=Errore di configurazione: {0}
error.connectionError=Errore di connessione: {0}
error.invalidConfiguration=Configurazione non valida
error.missingApiKey=Chiave API richiesta
error.missingBaseUrl=URL base richiesto
error.missingModel=Selezione modello richiesta

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Locale)
provider.lmstudio=LM Studio (Locale)
provider.anythingllm=AnythingLLM (Locale)
provider.jan=Jan (Locale)
provider.custom=Personalizzato
