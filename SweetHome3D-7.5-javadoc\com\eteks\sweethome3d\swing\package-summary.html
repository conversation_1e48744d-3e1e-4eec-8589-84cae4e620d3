<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>com.eteks.sweethome3d.swing (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.eteks.sweethome3d.swing (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/plugin/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/eteks/sweethome3d/tools/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.eteks.sweethome3d.swing</h1>
<div class="docSummary">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/AutoCommitSpinner.html" title="class in com.eteks.sweethome3d.swing">AutoCommitSpinner</a></td>
<td class="colLast">
<div class="block">A spinner which commits its value during edition and selects
 the value displayed in its editor when it gains focus.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/AutoCommitSpinner.SpinnerModuloNumberModel.html" title="class in com.eteks.sweethome3d.swing">AutoCommitSpinner.SpinnerModuloNumberModel</a></td>
<td class="colLast">
<div class="block">A spinner number model that will reset to minimum when maximum is reached.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/AutoCompleteTextField.html" title="class in com.eteks.sweethome3d.swing">AutoCompleteTextField</a></td>
<td class="colLast">
<div class="block">A text field that suggests to the user strings stored in auto completion strings in the user preferences.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/BackgroundImageWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">BackgroundImageWizardStepsPanel</a></td>
<td class="colLast">
<div class="block">Wizard panel for background image choice.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/BaseboardChoiceComponent.html" title="class in com.eteks.sweethome3d.swing">BaseboardChoiceComponent</a></td>
<td class="colLast">
<div class="block">Baseboard editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/CatalogItemToolTip.html" title="class in com.eteks.sweethome3d.swing">CatalogItemToolTip</a></td>
<td class="colLast">
<div class="block">A tool tip displaying the information and the icon of a catalog item.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ColorButton.html" title="class in com.eteks.sweethome3d.swing">ColorButton</a></td>
<td class="colLast">
<div class="block">Button displaying a color as an icon.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/CompassPanel.html" title="class in com.eteks.sweethome3d.swing">CompassPanel</a></td>
<td class="colLast">
<div class="block">Compass editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/Component3DTransferHandler.html" title="class in com.eteks.sweethome3d.swing">Component3DTransferHandler</a></td>
<td class="colLast">
<div class="block">A transfer handler for the 3D view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ControllerAction.html" title="class in com.eteks.sweethome3d.swing">ControllerAction</a></td>
<td class="colLast">
<div class="block">An action which <code>actionPerformed</code> method
 will call a parametrizable method.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html" title="class in com.eteks.sweethome3d.swing">DimensionLinePanel</a></td>
<td class="colLast">
<div class="block">Dimension line editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html" title="class in com.eteks.sweethome3d.swing">FileContentManager</a></td>
<td class="colLast">
<div class="block">Content manager for files with Swing file choosers.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FontNameComboBox.html" title="class in com.eteks.sweethome3d.swing">FontNameComboBox</a></td>
<td class="colLast">
<div class="block">A combo box used to choose the name of a font.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogListPanel.html" title="class in com.eteks.sweethome3d.swing">FurnitureCatalogListPanel</a></td>
<td class="colLast">
<div class="block">A furniture catalog view that displays furniture in a list, with a combo and search text field.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTransferHandler.html" title="class in com.eteks.sweethome3d.swing">FurnitureCatalogTransferHandler</a></td>
<td class="colLast">
<div class="block">Catalog transfer handler.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html" title="class in com.eteks.sweethome3d.swing">FurnitureCatalogTree</a></td>
<td class="colLast">
<div class="block">A tree displaying furniture catalog by category.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTable.html" title="class in com.eteks.sweethome3d.swing">FurnitureTable</a></td>
<td class="colLast">
<div class="block">A table displaying home furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html" title="class in com.eteks.sweethome3d.swing">FurnitureTablePanel</a></td>
<td class="colLast">
<div class="block">A panel displaying home furniture table and other information like totals.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.UserPreferencesChangeListener.html" title="class in com.eteks.sweethome3d.swing">FurnitureTablePanel.UserPreferencesChangeListener</a></td>
<td class="colLast">
<div class="block">Preferences property listener bound to this component with a weak reference to avoid
 strong link between preferences and this component.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/FurnitureTransferHandler.html" title="class in com.eteks.sweethome3d.swing">FurnitureTransferHandler</a></td>
<td class="colLast">
<div class="block">Home furniture transfer handler.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HelpPane.html" title="class in com.eteks.sweethome3d.swing">HelpPane</a></td>
<td class="colLast">
<div class="block">A pane displaying Sweet Home 3D help.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/Home3DAttributesPanel.html" title="class in com.eteks.sweethome3d.swing">Home3DAttributesPanel</a></td>
<td class="colLast">
<div class="block">Home 3D attributes editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html" title="class in com.eteks.sweethome3d.swing">HomeComponent3D</a></td>
<td class="colLast">
<div class="block">A component that displays home walls, rooms and furniture with Java 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HomeFurniturePanel.html" title="class in com.eteks.sweethome3d.swing">HomeFurniturePanel</a></td>
<td class="colLast">
<div class="block">Home furniture editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html" title="class in com.eteks.sweethome3d.swing">HomePane</a></td>
<td class="colLast">
<div class="block">The MVC view that edits a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HomePDFPrinter.html" title="class in com.eteks.sweethome3d.swing">HomePDFPrinter</a></td>
<td class="colLast">
<div class="block">Home PDF printer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HomePrintableComponent.html" title="class in com.eteks.sweethome3d.swing">HomePrintableComponent</a></td>
<td class="colLast">
<div class="block">A printable component used to print or preview the furniture, the plan
 and the 3D view of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HomeTransferableList.html" title="class in com.eteks.sweethome3d.swing">HomeTransferableList</a></td>
<td class="colLast">
<div class="block">A transferable class that manages the transfer of a list of items in a home.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/IconManager.html" title="class in com.eteks.sweethome3d.swing">IconManager</a></td>
<td class="colLast">
<div class="block">Singleton managing icons cache.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ImportedFurnitureWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">ImportedFurnitureWizardStepsPanel</a></td>
<td class="colLast">
<div class="block">Wizard panel for furniture import.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ImportedTextureWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">ImportedTextureWizardStepsPanel</a></td>
<td class="colLast">
<div class="block">Wizard panel for background image choice.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/JPEGImagesToVideo.html" title="class in com.eteks.sweethome3d.swing">JPEGImagesToVideo</a></td>
<td class="colLast">
<div class="block">This program takes a list of images and convert them into a
 QuickTime movie.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/LabelPanel.html" title="class in com.eteks.sweethome3d.swing">LabelPanel</a></td>
<td class="colLast">
<div class="block">Label editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/LevelPanel.html" title="class in com.eteks.sweethome3d.swing">LevelPanel</a></td>
<td class="colLast">
<div class="block">Level editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html" title="class in com.eteks.sweethome3d.swing">LocatedTransferHandler</a></td>
<td class="colLast">
<div class="block">Transfer handler that stores the dropped location of mouse pointer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ModelMaterialsComponent.html" title="class in com.eteks.sweethome3d.swing">ModelMaterialsComponent</a></td>
<td class="colLast">
<div class="block">Button giving access to materials editor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html" title="class in com.eteks.sweethome3d.swing">ModelPreviewComponent</a></td>
<td class="colLast">
<div class="block">Super class of 3D preview component for model.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html" title="class in com.eteks.sweethome3d.swing">MultipleLevelsPlanPanel</a></td>
<td class="colLast">
<div class="block">A panel for multiple levels plans where users can select the displayed level.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/NullableCheckBox.html" title="class in com.eteks.sweethome3d.swing">NullableCheckBox</a></td>
<td class="colLast">
<div class="block">A check box that accepts <code>null</code> values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.html" title="class in com.eteks.sweethome3d.swing">NullableSpinner</a></td>
<td class="colLast">
<div class="block">Spinner that accepts empty string values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerDateModel.html" title="class in com.eteks.sweethome3d.swing">NullableSpinner.NullableSpinnerDateModel</a></td>
<td class="colLast">
<div class="block">Spinner date model that accepts <code>null</code> values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerLengthModel.html" title="class in com.eteks.sweethome3d.swing">NullableSpinner.NullableSpinnerLengthModel</a></td>
<td class="colLast">
<div class="block">Nullable spinner model displaying length values matching preferences unit.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerModuloNumberModel.html" title="class in com.eteks.sweethome3d.swing">NullableSpinner.NullableSpinnerModuloNumberModel</a></td>
<td class="colLast">
<div class="block">A nullable spinner number model that will reset to minimum when maximum is reached.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html" title="class in com.eteks.sweethome3d.swing">NullableSpinner.NullableSpinnerNumberModel</a></td>
<td class="colLast">
<div class="block">Spinner number model that accepts <code>null</code> values.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ObserverCameraPanel.html" title="class in com.eteks.sweethome3d.swing">ObserverCameraPanel</a></td>
<td class="colLast">
<div class="block">Observer camera editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PageSetupPanel.html" title="class in com.eteks.sweethome3d.swing">PageSetupPanel</a></td>
<td class="colLast">
<div class="block">Home page setup editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PhotoPanel.html" title="class in com.eteks.sweethome3d.swing">PhotoPanel</a></td>
<td class="colLast">
<div class="block">A panel to edit photo creation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PhotoPanel.LanguageChangeListener.html" title="class in com.eteks.sweethome3d.swing">PhotoPanel.LanguageChangeListener</a></td>
<td class="colLast">
<div class="block">Preferences property listener bound to this panel with a weak reference to avoid
 strong link between user preferences and this panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PhotoSizeAndQualityPanel.html" title="class in com.eteks.sweethome3d.swing">PhotoSizeAndQualityPanel</a></td>
<td class="colLast">
<div class="block">A panel to edit photo size and quality.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PhotoSizeAndQualityPanel.LanguageChangeListener.html" title="class in com.eteks.sweethome3d.swing">PhotoSizeAndQualityPanel.LanguageChangeListener</a></td>
<td class="colLast">
<div class="block">Preferences property listener bound to this panel with a weak reference to avoid
 strong link between user preferences and this panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PhotosPanel.html" title="class in com.eteks.sweethome3d.swing">PhotosPanel</a></td>
<td class="colLast">
<div class="block">A panel to edit photos created at home points of view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PhotosPanel.LanguageChangeListener.html" title="class in com.eteks.sweethome3d.swing">PhotosPanel.LanguageChangeListener</a></td>
<td class="colLast">
<div class="block">Preferences property listener bound to this panel with a weak reference to avoid
 strong link between user preferences and this panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html" title="class in com.eteks.sweethome3d.swing">PlanComponent</a></td>
<td class="colLast">
<div class="block">A component displaying the plan of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.IndicatorType.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.IndicatorType</a></td>
<td class="colLast">
<div class="block">Indicator types that may be displayed on selected items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html" title="class in com.eteks.sweethome3d.swing">PlanTransferHandler</a></td>
<td class="colLast">
<div class="block">Plan transfer handler.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PolylinePanel.html" title="class in com.eteks.sweethome3d.swing">PolylinePanel</a></td>
<td class="colLast">
<div class="block">User preferences panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PrintPreviewPanel.html" title="class in com.eteks.sweethome3d.swing">PrintPreviewPanel</a></td>
<td class="colLast">
<div class="block">Home print preview editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html" title="class in com.eteks.sweethome3d.swing">ProportionalLayout</a></td>
<td class="colLast">
<div class="block">A layout manager that displays two components at the top of each other.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction</a></td>
<td class="colLast">
<div class="block">An action with properties read from a resource bundle file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.ButtonAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.ButtonAction</a></td>
<td class="colLast">
<div class="block">An action decorator for  buttons.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.MenuItemAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.MenuItemAction</a></td>
<td class="colLast">
<div class="block">An action decorator for menu items.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.PopupMenuItemAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.PopupMenuItemAction</a></td>
<td class="colLast">
<div class="block">An action decorator for popup menu items.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.ToolBarAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.ToolBarAction</a></td>
<td class="colLast">
<div class="block">An action decorator for tool bar buttons.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/RoomPanel.html" title="class in com.eteks.sweethome3d.swing">RoomPanel</a></td>
<td class="colLast">
<div class="block">Room editing panel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html" title="class in com.eteks.sweethome3d.swing">ScaledImageComponent</a></td>
<td class="colLast">
<div class="block">Component displaying an image scaled to fit within its bound.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html" title="class in com.eteks.sweethome3d.swing">SwingTools</a></td>
<td class="colLast">
<div class="block">Gathers some useful tools for Swing.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html" title="class in com.eteks.sweethome3d.swing">SwingViewFactory</a></td>
<td class="colLast">
<div class="block">View factory that instantiates the Swing components of this package.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/TextureChoiceComponent.html" title="class in com.eteks.sweethome3d.swing">TextureChoiceComponent</a></td>
<td class="colLast">
<div class="block">Button displaying a texture as an icon.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ThreadedTaskPanel.html" title="class in com.eteks.sweethome3d.swing">ThreadedTaskPanel</a></td>
<td class="colLast">
<div class="block">A MVC view of a threaded task.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/UnfocusableToolBar.html" title="class in com.eteks.sweethome3d.swing">UnfocusableToolBar</a></td>
<td class="colLast">
<div class="block">A tool bar where all components are maintained unfocusable.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/UserPreferencesPanel.html" title="class in com.eteks.sweethome3d.swing">UserPreferencesPanel</a></td>
<td class="colLast">
<div class="block">User preferences panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/VideoPanel.html" title="class in com.eteks.sweethome3d.swing">VideoPanel</a></td>
<td class="colLast">
<div class="block">A panel used for video creation.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/VideoPanel.LanguageChangeListener.html" title="class in com.eteks.sweethome3d.swing">VideoPanel.LanguageChangeListener</a></td>
<td class="colLast">
<div class="block">Preferences property listener bound to this panel with a weak reference to avoid
 strong link between user preferences and this panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/VisualTransferHandler.html" title="class in com.eteks.sweethome3d.swing">VisualTransferHandler</a></td>
<td class="colLast">
<div class="block">Transfer handler with visual representation on systems that support it.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/WallPanel.html" title="class in com.eteks.sweethome3d.swing">WallPanel</a></td>
<td class="colLast">
<div class="block">Wall editing panel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/WizardPane.html" title="class in com.eteks.sweethome3d.swing">WizardPane</a></td>
<td class="colLast">
<div class="block">Wizard pane.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/CatalogItemToolTip.DisplayedInformation.html" title="enum in com.eteks.sweethome3d.swing">CatalogItemToolTip.DisplayedInformation</a></td>
<td class="colLast">
<div class="block">Type of information displayed by a tool tip.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.Projection.html" title="enum in com.eteks.sweethome3d.swing">HomeComponent3D.Projection</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/HomePrintableComponent.Variable.html" title="enum in com.eteks.sweethome3d.swing">HomePrintableComponent.Variable</a></td>
<td class="colLast">
<div class="block">List of the variables that the user may insert in header and footer.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PaintMode.html" title="enum in com.eteks.sweethome3d.swing">PlanComponent.PaintMode</a></td>
<td class="colLast">
<div class="block">The circumstances under which the home items displayed by this component will be painted.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.Constraints.html" title="enum in com.eteks.sweethome3d.swing">ProportionalLayout.Constraints</a></td>
<td class="colLast">
<div class="block">The two locations where components managed by a <a href="../../../../com/eteks/sweethome3d/swing/ProportionalLayout.html" title="class in com.eteks.sweethome3d.swing"><code>ProportionalLayout</code></a> instance can be placed.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Exception Summary table, listing exceptions, and an explanation">
<caption><span>Exception Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Exception</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/eteks/sweethome3d/swing/InterruptedPrinterException.html" title="class in com.eteks.sweethome3d.swing">InterruptedPrinterException</a></td>
<td class="colLast">
<div class="block">A printer exception thrown when print is interrupted.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package com.eteks.sweethome3d.swing Description">Package com.eteks.sweethome3d.swing Description</h2>
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.
<p>All these components are created on demand by the
<a href="../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html" title="class in com.eteks.sweethome3d.swing"><code>SwingViewFactory</code></a> class that implements the
<a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller"><code>ViewFactory</code></a> interface specified in the
<a href="../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html"><code>com.eteks.sweethome3d.viewcontroller</code></a> package.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/plugin/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../com/eteks/sweethome3d/tools/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
