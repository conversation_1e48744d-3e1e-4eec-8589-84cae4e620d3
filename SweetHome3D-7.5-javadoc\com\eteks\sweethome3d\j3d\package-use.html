<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Package com.eteks.sweethome3d.j3d (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package com.eteks.sweethome3d.j3d (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package com.eteks.sweethome3d.j3d" class="title">Uses of Package<br>com.eteks.sweethome3d.j3d</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d</a></td>
<td class="colLast">
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.j3d">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> used by <a href="../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/AbstractPhotoRenderer.html#com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a>
<div class="block">A renderer able to create a photo realistic image of a home.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/AbstractPhotoRenderer.Quality.html#com.eteks.sweethome3d.j3d">AbstractPhotoRenderer.Quality</a>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/Component3DManager.html#com.eteks.sweethome3d.j3d">Component3DManager</a>
<div class="block">Manager of <code>Canvas3D</code> instantiations and Java 3D error listeners.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/Component3DManager.RenderingErrorObserver.html#com.eteks.sweethome3d.j3d">Component3DManager.RenderingErrorObserver</a>
<div class="block">An observer that receives error notifications in Java 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/Component3DManager.RenderingObserver.html#com.eteks.sweethome3d.j3d">Component3DManager.RenderingObserver</a>
<div class="block">An observer that receives notifications during the different steps
 of the loop rendering a canvas 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/ModelManager.html#com.eteks.sweethome3d.j3d">ModelManager</a>
<div class="block">Singleton managing 3D models cache.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/ModelManager.ModelObserver.html#com.eteks.sweethome3d.j3d">ModelManager.ModelObserver</a>
<div class="block">An observer that receives model loading notifications.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/Object3DBranch.html#com.eteks.sweethome3d.j3d">Object3DBranch</a>
<div class="block">Root of a branch that matches a home object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/PhotoRenderer.Quality.html#com.eteks.sweethome3d.j3d">PhotoRenderer.Quality</a>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">From version 7.0, prefer use <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d"><code>AbstractPhotoRenderer.Quality</code></a> enum.</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/TextureManager.html#com.eteks.sweethome3d.j3d">TextureManager</a>
<div class="block">Singleton managing texture image cache.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../com/eteks/sweethome3d/j3d/class-use/TextureManager.TextureObserver.html#com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>
<div class="block">An observer that receives texture loading notifications.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
