<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>UserPreferences (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UserPreferences (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":6,"i2":6,"i3":10,"i4":10,"i5":6,"i6":6,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":6,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":6,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":6,"i102":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UserPreferences.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/UserPreferences.html" target="_top">Frames</a></li>
<li><a href="UserPreferences.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class UserPreferences" class="title">Class UserPreferences</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.UserPreferences</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html" title="class in com.eteks.sweethome3d.applet">AppletUserPreferences</a>, <a href="../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html" title="class in com.eteks.sweethome3d.io">DefaultUserPreferences</a>, <a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html" title="class in com.eteks.sweethome3d.io">FileUserPreferences</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">UserPreferences</span>
extends java.lang.Object</pre>
<div class="block">User preferences.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a></span></code>
<div class="block">The properties of user preferences that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#FURNITURE_LIBRARY_TYPE">FURNITURE_LIBRARY_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#LANGUAGE_LIBRARY_TYPE">LANGUAGE_LIBRARY_TYPE</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#TEXTURES_LIBRARY_TYPE">TEXTURES_LIBRARY_TYPE</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#UserPreferences--">UserPreferences</a></span>()</code>
<div class="block">Creates user preferences.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addAutoCompletionString-java.lang.String-java.lang.String-">addAutoCompletionString</a></span>(java.lang.String&nbsp;property,
                       java.lang.String&nbsp;autoCompletionString)</code>
<div class="block">Adds the given string to the list of the strings used in auto completion of a <code>property</code>
 and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></span>(java.lang.String&nbsp;furnitureLibraryLocation)</code>
<div class="block">Adds <code>furnitureLibraryName</code> to furniture catalog
 to make the furniture it contains available.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></span>(java.lang.String&nbsp;languageLibraryLocation)</code>
<div class="block">Adds the language library to make the languages it contains available to supported languages.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to these preferences.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addPropertyChangeListener-com.eteks.sweethome3d.model.UserPreferences.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the <code>listener</code> in parameter to these preferences to listen
 to the changes of the given <code>property</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></span>(java.lang.String&nbsp;texturesLibraryLocation)</code>
<div class="block">Adds the textures library at the given location to textures catalog
 to make the textures it contains available.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></span>(java.lang.String&nbsp;furnitureLibraryLocation)</code>
<div class="block">Returns <code>true</code> if the furniture library at the given location exists.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoCompletedProperties--">getAutoCompletedProperties</a></span>()</code>
<div class="block">Returns the list of properties with auto completion strings.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoCompletionStrings-java.lang.String-">getAutoCompletionStrings</a></span>(java.lang.String&nbsp;property)</code>
<div class="block">Returns the strings that may be used for the auto completion of the given <code>property</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getAutoSaveDelayForRecovery--">getAutoSaveDelayForRecovery</a></span>()</code>
<div class="block">Returns the delay between two automatic save operations of homes for recovery purpose.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getCurrency--">getCurrency</a></span>()</code>
<div class="block">Returns the currency in use, noted with ISO 4217 code, or <code>null</code>
 if prices aren't used in application.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultFontName--">getDefaultFontName</a></span>()</code>
<div class="block">Returns the name of the font that should be used by default or <code>null</code>
 if the default font should be the default one in the application.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultSupportedLanguages--">getDefaultSupportedLanguages</a></span>()</code>
<div class="block">Returns the array of default available languages in Sweet Home 3D.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultTextStyle-java.lang.Class-">getDefaultTextStyle</a></span>(java.lang.Class&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;selectableClass)</code>
<div class="block">Returns the default text style of a class of selectable item.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getDefaultValueAddedTaxPercentage--">getDefaultValueAddedTaxPercentage</a></span>()</code>
<div class="block">Returns the Value Added Tax percentage applied to prices by default, or <code>null</code>
 if VAT isn't taken into account in the application.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getFurnitureCatalog--">getFurnitureCatalog</a></span>()</code>
<div class="block">Returns the furniture catalog.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getFurnitureModelIconSize--">getFurnitureModelIconSize</a></span>()</code>
<div class="block">Returns the size used to generate icons of furniture viewed from top.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model">HomeDescriptor</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getHomeExamples--">getHomeExamples</a></span>()</code>
<div class="block">Returns the home examples available for the user.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLanguage--">getLanguage</a></span>()</code>
<div class="block">Returns the preferred language to display information, noted with an ISO 639 code
 that may be followed by an underscore and an ISO 3166 code.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLengthUnit--">getLengthUnit</a></span>()</code>
<div class="block">Returns the length unit currently in use.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>abstract java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Returns the libraries available in user preferences.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedString-java.lang.Class-java.lang.String-java.lang.Object...-">getLocalizedString</a></span>(java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                  java.lang.String&nbsp;resourceKey,
                  java.lang.Object...&nbsp;resourceParameters)</code>
<div class="block">Returns the string matching <code>resourceKey</code> in current language in the
 context of <code>resourceClass</code>.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedString-java.lang.String-java.lang.String-java.lang.Object...-">getLocalizedString</a></span>(java.lang.String&nbsp;resourceFamily,
                  java.lang.String&nbsp;resourceKey,
                  java.lang.Object...&nbsp;resourceParameters)</code>
<div class="block">Returns the string matching <code>resourceKey</code> in current language
 for the given resource family.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>java.util.Iterator&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedStringKeys-java.lang.String-">getLocalizedStringKeys</a></span>(java.lang.String&nbsp;resourceFamily)</code>
<div class="block">Returns the keys of the localized property strings of the given resource family.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewFloorThickness--">getNewFloorThickness</a></span>()</code>
<div class="block">Returns default thickness of the floor of new levels in home.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewRoomFloorColor--">getNewRoomFloorColor</a></span>()</code>
<div class="block">Returns the default color of new rooms in home.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallBaseboardHeight--">getNewWallBaseboardHeight</a></span>()</code>
<div class="block">Returns default baseboard height of new home walls.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallBaseboardThickness--">getNewWallBaseboardThickness</a></span>()</code>
<div class="block">Returns default baseboard thickness of new walls in home.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallHeight--">getNewWallHeight</a></span>()</code>
<div class="block">Returns default wall height of new home walls.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallPattern--">getNewWallPattern</a></span>()</code>
<div class="block">Returns the pattern used for new walls in plan or <code>null</code> if it's not set.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getNewWallThickness--">getNewWallThickness</a></span>()</code>
<div class="block">Returns default thickness of new walls in home.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/PatternsCatalog.html" title="class in com.eteks.sweethome3d.model">PatternsCatalog</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getPatternsCatalog--">getPatternsCatalog</a></span>()</code>
<div class="block">Returns the patterns catalog available to fill plan areas.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getPhotoRenderer--">getPhotoRenderer</a></span>()</code>
<div class="block">Returns the preferred rendering engine used to create photos.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.Integer&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentColors--">getRecentColors</a></span>()</code>
<div class="block">Returns an unmodifiable list of the recent colors.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentHomes--">getRecentHomes</a></span>()</code>
<div class="block">Returns an unmodifiable list of the recent homes.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentHomesMaxCount--">getRecentHomesMaxCount</a></span>()</code>
<div class="block">Returns the maximum count of homes that should be proposed to the user.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getRecentTextures--">getRecentTextures</a></span>()</code>
<div class="block">Returns an unmodifiable list of the recent textures.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.ClassLoader&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getResourceClassLoaders--">getResourceClassLoaders</a></span>()</code>
<div class="block">Returns the class loaders through which localized strings returned by
 <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedString-java.lang.Class-java.lang.String-java.lang.Object...-"><code>getLocalizedString</code></a> might be loaded.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getStoredCamerasMaxCount--">getStoredCamerasMaxCount</a></span>()</code>
<div class="block">Returns the maximum count of stored cameras in homes that should be proposed to the user.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getSupportedLanguages--">getSupportedLanguages</a></span>()</code>
<div class="block">Returns the array of available languages in Sweet Home 3D including languages in libraries.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getTexturesCatalog--">getTexturesCatalog</a></span>()</code>
<div class="block">Returns the textures catalog.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getUpdatesMinimumDate--">getUpdatesMinimumDate</a></span>()</code>
<div class="block">Returns the minimum date of updates that may interest user.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getWallPattern--">getWallPattern</a></span>()</code>
<div class="block">Returns the wall pattern in plan used by default.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isActionTipIgnored-java.lang.String-">isActionTipIgnored</a></span>(java.lang.String&nbsp;actionKey)</code>
<div class="block">Returns whether an action tip should be ignored or not.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isAerialViewCenteredOnSelectionEnabled--">isAerialViewCenteredOnSelectionEnabled</a></span>()</code>
<div class="block">Returns whether aerial view should be centered on selection or not.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isCheckUpdatesEnabled--">isCheckUpdatesEnabled</a></span>()</code>
<div class="block">Returns <code>true</code> if updates should be checked.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isDrawingModeEnabled--">isDrawingModeEnabled</a></span>()</code>
<div class="block">Returns <code>true</code> is <a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getDrawingMode--"><code>drawing mode</code></a>
 should be taken into account.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isEditingIn3DViewEnabled--">isEditingIn3DViewEnabled</a></span>()</code>
<div class="block">Returns whether interactive editing in 3D view is enabled or not.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isFurnitureCatalogViewedInTree--">isFurnitureCatalogViewedInTree</a></span>()</code>
<div class="block">Returns <code>true</code> if the furniture catalog should be viewed in a tree.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isFurnitureViewedFromTop--">isFurnitureViewedFromTop</a></span>()</code>
<div class="block">Returns <code>true</code> if furniture should be viewed from its top in plan.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isGridVisible--">isGridVisible</a></span>()</code>
<div class="block">Returns <code>true</code> if plan grid visible.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isLanguageEditable--">isLanguageEditable</a></span>()</code>
<div class="block">Returns <code>true</code> if the language in preferences can be set.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isMagnetismEnabled--">isMagnetismEnabled</a></span>()</code>
<div class="block">Returns <code>true</code> if magnetism is enabled.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isNavigationPanelVisible--">isNavigationPanelVisible</a></span>()</code>
<div class="block">Returns <code>true</code> if the navigation panel should be displayed.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isObserverCameraSelectedAtChange--">isObserverCameraSelectedAtChange</a></span>()</code>
<div class="block">Returns whether the observer camera should be selected at each change.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isRoomFloorColoredOrTextured--">isRoomFloorColoredOrTextured</a></span>()</code>
<div class="block">Returns <code>true</code> if room floors should be rendered with color or texture
 in plan.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isRulersVisible--">isRulersVisible</a></span>()</code>
<div class="block">Returns <code>true</code> if rulers are visible.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isValueAddedTaxEnabled--">isValueAddedTaxEnabled</a></span>()</code>
<div class="block">Returns <code>true</code> if Value Added Tax should be taken in account in prices.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></span>(java.lang.String&nbsp;languageLibraryLocation)</code>
<div class="block">Returns <code>true</code> if the language library at the given location exists.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from these preferences.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#removePropertyChangeListener-com.eteks.sweethome3d.model.UserPreferences.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the <code>listener</code> in parameter from these preferences.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#resetIgnoredActionTips--">resetIgnoredActionTips</a></span>()</code>
<div class="block">Resets the ignore flag of action tips.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setActionTipIgnored-java.lang.String-">setActionTipIgnored</a></span>(java.lang.String&nbsp;actionKey)</code>
<div class="block">Sets which action tip should be ignored.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAerialViewCenteredOnSelectionEnabled-boolean-">setAerialViewCenteredOnSelectionEnabled</a></span>(boolean&nbsp;aerialViewCenteredOnSelectionEnabled)</code>
<div class="block">Sets whether aerial view should be centered on selection or not.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAutoCompletionStrings-java.lang.String-java.util.List-">setAutoCompletionStrings</a></span>(java.lang.String&nbsp;property,
                        java.util.List&lt;java.lang.String&gt;&nbsp;autoCompletionStrings)</code>
<div class="block">Sets the auto completion strings list of the given <code>property</code> and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setAutoSaveDelayForRecovery-int-">setAutoSaveDelayForRecovery</a></span>(int&nbsp;autoSaveDelayForRecovery)</code>
<div class="block">Sets the delay between two automatic save operations of homes for recovery purpose.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setCheckUpdatesEnabled-boolean-">setCheckUpdatesEnabled</a></span>(boolean&nbsp;updatesChecked)</code>
<div class="block">Sets whether updates should be checked or not.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setCurrency-java.lang.String-">setCurrency</a></span>(java.lang.String&nbsp;currency)</code>
<div class="block">Sets the currency in use.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setDefaultFontName-java.lang.String-">setDefaultFontName</a></span>(java.lang.String&nbsp;defaultFontName)</code>
<div class="block">Sets the name of the font that should be used by default.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setDefaultValueAddedTaxPercentage-java.math.BigDecimal-">setDefaultValueAddedTaxPercentage</a></span>(java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Sets the Value Added Tax percentage applied to prices by default.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setEditingIn3DViewEnabled-boolean-">setEditingIn3DViewEnabled</a></span>(boolean&nbsp;editingIn3DViewEnabled)</code>
<div class="block">Sets whether interactive editing in 3D view is enabled or not.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFloorColoredOrTextured-boolean-">setFloorColoredOrTextured</a></span>(boolean&nbsp;roomFloorColoredOrTextured)</code>
<div class="block">Sets whether room floors should be rendered with color or texture,
 and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureCatalog-com.eteks.sweethome3d.model.FurnitureCatalog-">setFurnitureCatalog</a></span>(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog)</code>
<div class="block">Sets furniture catalog.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureCatalogViewedInTree-boolean-">setFurnitureCatalogViewedInTree</a></span>(boolean&nbsp;furnitureCatalogViewedInTree)</code>
<div class="block">Sets whether the furniture catalog should be viewed in a tree or a different way.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureModelIconSize-int-">setFurnitureModelIconSize</a></span>(int&nbsp;furnitureModelIconSize)</code>
<div class="block">Sets the name of the font that should be used by default.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setFurnitureViewedFromTop-boolean-">setFurnitureViewedFromTop</a></span>(boolean&nbsp;furnitureViewedFromTop)</code>
<div class="block">Sets how furniture icon should be displayed in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setGridVisible-boolean-">setGridVisible</a></span>(boolean&nbsp;gridVisible)</code>
<div class="block">Sets whether plan grid is visible or not, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setHomeExamples-java.util.List-">setHomeExamples</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model">HomeDescriptor</a>&gt;&nbsp;homeExamples)</code>
<div class="block">Sets the home examples available for the user.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setLanguage-java.lang.String-">setLanguage</a></span>(java.lang.String&nbsp;language)</code>
<div class="block">If <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isLanguageEditable--">language can be changed</a>, sets the preferred language to display information,
 changes current default locale accordingly and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setMagnetismEnabled-boolean-">setMagnetismEnabled</a></span>(boolean&nbsp;magnetismEnabled)</code>
<div class="block">Sets whether magnetism is enabled or not, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNavigationPanelVisible-boolean-">setNavigationPanelVisible</a></span>(boolean&nbsp;navigationPanelVisible)</code>
<div class="block">Sets whether the navigation panel should be displayed or not.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewFloorThickness-float-">setNewFloorThickness</a></span>(float&nbsp;newFloorThickness)</code>
<div class="block">Sets default thickness of the floor of new levels in home, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewRoomFloorColor-java.lang.Integer-">setNewRoomFloorColor</a></span>(java.lang.Integer&nbsp;newRoomFloorColor)</code>
<div class="block">Sets the default color of new rooms in home, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallBaseboardHeight-float-">setNewWallBaseboardHeight</a></span>(float&nbsp;newWallBaseboardHeight)</code>
<div class="block">Sets default baseboard height of new walls, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallBaseboardThickness-float-">setNewWallBaseboardThickness</a></span>(float&nbsp;newWallBaseboardThickness)</code>
<div class="block">Sets default baseboard thickness of new walls in home, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallHeight-float-">setNewWallHeight</a></span>(float&nbsp;newWallHeight)</code>
<div class="block">Sets default wall height of new walls, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallPattern-com.eteks.sweethome3d.model.TextureImage-">setNewWallPattern</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;newWallPattern)</code>
<div class="block">Sets how new walls should be displayed in plan, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setNewWallThickness-float-">setNewWallThickness</a></span>(float&nbsp;newWallThickness)</code>
<div class="block">Sets default thickness of new walls in home, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setObserverCameraSelectedAtChange-boolean-">setObserverCameraSelectedAtChange</a></span>(boolean&nbsp;observerCameraSelectedAtChange)</code>
<div class="block">Sets whether the observer camera should be selected at each change.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setPatternsCatalog-com.eteks.sweethome3d.model.PatternsCatalog-">setPatternsCatalog</a></span>(<a href="../../../../com/eteks/sweethome3d/model/PatternsCatalog.html" title="class in com.eteks.sweethome3d.model">PatternsCatalog</a>&nbsp;catalog)</code>
<div class="block">Sets the patterns available to fill plan areas.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setPhotoRenderer-java.lang.String-">setPhotoRenderer</a></span>(java.lang.String&nbsp;photoRenderer)</code>
<div class="block">Sets the preferred rendering engine used to create photos.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentColors-java.util.List-">setRecentColors</a></span>(java.util.List&lt;java.lang.Integer&gt;&nbsp;recentColors)</code>
<div class="block">Sets the recent colors list and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentHomes-java.util.List-">setRecentHomes</a></span>(java.util.List&lt;java.lang.String&gt;&nbsp;recentHomes)</code>
<div class="block">Sets the recent homes list and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRecentTextures-java.util.List-">setRecentTextures</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&gt;&nbsp;recentTextures)</code>
<div class="block">Sets the recent colors list and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setRulersVisible-boolean-">setRulersVisible</a></span>(boolean&nbsp;rulersVisible)</code>
<div class="block">Sets whether rulers are visible or not, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setSupportedLanguages-java.lang.String:A-">setSupportedLanguages</a></span>(java.lang.String[]&nbsp;supportedLanguages)</code>
<div class="block">Sets the available languages in Sweet Home 3D.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setTexturesCatalog-com.eteks.sweethome3d.model.TexturesCatalog-">setTexturesCatalog</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a>&nbsp;catalog)</code>
<div class="block">Sets textures catalog.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setUnit-com.eteks.sweethome3d.model.LengthUnit-">setUnit</a></span>(<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&nbsp;unit)</code>
<div class="block">Changes the unit currently in use, and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setUpdatesMinimumDate-java.lang.Long-">setUpdatesMinimumDate</a></span>(java.lang.Long&nbsp;updatesMinimumDate)</code>
<div class="block">Sets the minimum date of updates that may interest user, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setValueAddedTaxEnabled-boolean-">setValueAddedTaxEnabled</a></span>(boolean&nbsp;valueAddedTaxEnabled)</code>
<div class="block">Sets whether Value Added Tax should be taken in account in prices.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setWallPattern-com.eteks.sweethome3d.model.TextureImage-">setWallPattern</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;wallPattern)</code>
<div class="block">Sets how walls should be displayed in plan by default, and notifies
 listeners of this change.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></span>(java.lang.String&nbsp;texturesLibraryLocation)</code>
<div class="block">Returns <code>true</code> if the textures library at the given location exists.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#write--">write</a></span>()</code>
<div class="block">Writes user preferences.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="FURNITURE_LIBRARY_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FURNITURE_LIBRARY_TYPE</h4>
<pre>public static final&nbsp;java.lang.String FURNITURE_LIBRARY_TYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.model.UserPreferences.FURNITURE_LIBRARY_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="TEXTURES_LIBRARY_TYPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXTURES_LIBRARY_TYPE</h4>
<pre>public static final&nbsp;java.lang.String TEXTURES_LIBRARY_TYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.model.UserPreferences.TEXTURES_LIBRARY_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="LANGUAGE_LIBRARY_TYPE">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LANGUAGE_LIBRARY_TYPE</h4>
<pre>public static final&nbsp;java.lang.String LANGUAGE_LIBRARY_TYPE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.model.UserPreferences.LANGUAGE_LIBRARY_TYPE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UserPreferences--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UserPreferences</h4>
<pre>public&nbsp;UserPreferences()</pre>
<div class="block">Creates user preferences.<br>
 Caution: during creation, the default locale will be updated if it doesn't belong to the supported ones.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="write--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public abstract&nbsp;void&nbsp;write()
                    throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Writes user preferences.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code> - if user preferences couldn'y be saved.</dd>
</dl>
</li>
</ul>
<a name="addPropertyChangeListener-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to these preferences.
 <br>Caution: a user preferences instance generally exists during all the application ;
 therefore you should take care of not bounding permanently listeners to this
 object (for example, do not create anonymous listeners on user preferences
 in classes depending on an edited home).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="removePropertyChangeListener-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from these preferences.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.model.UserPreferences.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the <code>listener</code> in parameter to these preferences to listen
 to the changes of the given <code>property</code>.
 <br>Caution: a user preferences instance generally exists during all the application ;
 therefore you should take care of not bounding permanently listeners to this
 object (for example, do not create anonymous listeners on user preferences
 in classes depending on an edited home).</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.model.UserPreferences.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model">UserPreferences.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the <code>listener</code> in parameter from these preferences.</div>
</li>
</ul>
<a name="getFurnitureCatalog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureCatalog</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;getFurnitureCatalog()</pre>
<div class="block">Returns the furniture catalog.</div>
</li>
</ul>
<a name="setFurnitureCatalog-com.eteks.sweethome3d.model.FurnitureCatalog-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureCatalog</h4>
<pre>protected&nbsp;void&nbsp;setFurnitureCatalog(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a>&nbsp;catalog)</pre>
<div class="block">Sets furniture catalog.</div>
</li>
</ul>
<a name="getTexturesCatalog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTexturesCatalog</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a>&nbsp;getTexturesCatalog()</pre>
<div class="block">Returns the textures catalog.</div>
</li>
</ul>
<a name="setTexturesCatalog-com.eteks.sweethome3d.model.TexturesCatalog-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTexturesCatalog</h4>
<pre>protected&nbsp;void&nbsp;setTexturesCatalog(<a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a>&nbsp;catalog)</pre>
<div class="block">Sets textures catalog.</div>
</li>
</ul>
<a name="getPatternsCatalog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPatternsCatalog</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/PatternsCatalog.html" title="class in com.eteks.sweethome3d.model">PatternsCatalog</a>&nbsp;getPatternsCatalog()</pre>
<div class="block">Returns the patterns catalog available to fill plan areas.</div>
</li>
</ul>
<a name="setPatternsCatalog-com.eteks.sweethome3d.model.PatternsCatalog-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPatternsCatalog</h4>
<pre>protected&nbsp;void&nbsp;setPatternsCatalog(<a href="../../../../com/eteks/sweethome3d/model/PatternsCatalog.html" title="class in com.eteks.sweethome3d.model">PatternsCatalog</a>&nbsp;catalog)</pre>
<div class="block">Sets the patterns available to fill plan areas.</div>
</li>
</ul>
<a name="getLengthUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLengthUnit</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&nbsp;getLengthUnit()</pre>
<div class="block">Returns the length unit currently in use.</div>
</li>
</ul>
<a name="setUnit-com.eteks.sweethome3d.model.LengthUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnit</h4>
<pre>public&nbsp;void&nbsp;setUnit(<a href="../../../../com/eteks/sweethome3d/model/LengthUnit.html" title="enum in com.eteks.sweethome3d.model">LengthUnit</a>&nbsp;unit)</pre>
<div class="block">Changes the unit currently in use, and notifies listeners of this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>unit</code> - one of the values of Unit.</dd>
</dl>
</li>
</ul>
<a name="getLanguage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLanguage</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLanguage()</pre>
<div class="block">Returns the preferred language to display information, noted with an ISO 639 code
 that may be followed by an underscore and an ISO 3166 code.</div>
</li>
</ul>
<a name="setLanguage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLanguage</h4>
<pre>public&nbsp;void&nbsp;setLanguage(java.lang.String&nbsp;language)</pre>
<div class="block">If <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#isLanguageEditable--">language can be changed</a>, sets the preferred language to display information,
 changes current default locale accordingly and notifies listeners of this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>language</code> - an ISO 639 code that may be followed by an underscore and an ISO 3166 code
            (for example fr, de, it, en_US, zh_CN).</dd>
</dl>
</li>
</ul>
<a name="isLanguageEditable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLanguageEditable</h4>
<pre>public&nbsp;boolean&nbsp;isLanguageEditable()</pre>
<div class="block">Returns <code>true</code> if the language in preferences can be set.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> except if <code>user.language</code> System property isn't writable.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getDefaultSupportedLanguages--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultSupportedLanguages</h4>
<pre>public&nbsp;java.lang.String[]&nbsp;getDefaultSupportedLanguages()</pre>
<div class="block">Returns the array of default available languages in Sweet Home 3D.</div>
</li>
</ul>
<a name="getSupportedLanguages--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSupportedLanguages</h4>
<pre>public&nbsp;java.lang.String[]&nbsp;getSupportedLanguages()</pre>
<div class="block">Returns the array of available languages in Sweet Home 3D including languages in libraries.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setSupportedLanguages-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSupportedLanguages</h4>
<pre>protected&nbsp;void&nbsp;setSupportedLanguages(java.lang.String[]&nbsp;supportedLanguages)</pre>
<div class="block">Sets the available languages in Sweet Home 3D.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getLocalizedString-java.lang.Class-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalizedString</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLocalizedString(java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                                           java.lang.String&nbsp;resourceKey,
                                           java.lang.Object...&nbsp;resourceParameters)</pre>
<div class="block">Returns the string matching <code>resourceKey</code> in current language in the
 context of <code>resourceClass</code>.
 If <code>resourceParameters</code> isn't empty the string is considered
 as a format string, and the returned string will be formatted with these parameters.
 This implementation searches first the key in a properties file named as
 <code>resourceClass</code>, then if this file doesn't exist, it searches
 the key prefixed by <code>resourceClass</code> name and a dot in a package.properties file
 in the folder matching the package of <code>resourceClass</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if no string for the given key can be found</dd>
</dl>
</li>
</ul>
<a name="getLocalizedString-java.lang.String-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalizedString</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLocalizedString(java.lang.String&nbsp;resourceFamily,
                                           java.lang.String&nbsp;resourceKey,
                                           java.lang.Object...&nbsp;resourceParameters)</pre>
<div class="block">Returns the string matching <code>resourceKey</code> in current language
 for the given resource family.
 <code>resourceFamily</code> should match the absolute path of a .properties resource family,
 shouldn't start by a slash and may contain dots '.' or slash '/' as folder separators.
 If <code>resourceParameters</code> isn't empty the string is considered
 as a format string, and the returned string will be formatted with these parameters.
 This implementation searches the key in a properties file named as
 <code>resourceFamily</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if no string for the given key can be found</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getLocalizedStringKeys-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalizedStringKeys</h4>
<pre>public&nbsp;java.util.Iterator&lt;java.lang.String&gt;&nbsp;getLocalizedStringKeys(java.lang.String&nbsp;resourceFamily)</pre>
<div class="block">Returns the keys of the localized property strings of the given resource family.
 <code>resourceFamily</code> should match the absolute path of a .properties resource family,
 shouldn't start by a slash and may contain dots '.' or slash '/' as folder separators.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.7</dd>
</dl>
</li>
</ul>
<a name="getResourceClassLoaders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceClassLoaders</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.ClassLoader&gt;&nbsp;getResourceClassLoaders()</pre>
<div class="block">Returns the class loaders through which localized strings returned by
 <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#getLocalizedString-java.lang.Class-java.lang.String-java.lang.Object...-"><code>getLocalizedString</code></a> might be loaded.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getCurrency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrency</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCurrency()</pre>
<div class="block">Returns the currency in use, noted with ISO 4217 code, or <code>null</code>
 if prices aren't used in application.</div>
</li>
</ul>
<a name="setCurrency-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrency</h4>
<pre>public&nbsp;void&nbsp;setCurrency(java.lang.String&nbsp;currency)</pre>
<div class="block">Sets the currency in use.</div>
</li>
</ul>
<a name="isValueAddedTaxEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isValueAddedTaxEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isValueAddedTaxEnabled()</pre>
<div class="block">Returns <code>true</code> if Value Added Tax should be taken in account in prices.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setValueAddedTaxEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValueAddedTaxEnabled</h4>
<pre>public&nbsp;void&nbsp;setValueAddedTaxEnabled(boolean&nbsp;valueAddedTaxEnabled)</pre>
<div class="block">Sets whether Value Added Tax should be taken in account in prices.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>valueAddedTaxEnabled</code> - if <code>true</code> VAT will be added to prices.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getDefaultValueAddedTaxPercentage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultValueAddedTaxPercentage</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getDefaultValueAddedTaxPercentage()</pre>
<div class="block">Returns the Value Added Tax percentage applied to prices by default, or <code>null</code>
 if VAT isn't taken into account in the application.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setDefaultValueAddedTaxPercentage-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultValueAddedTaxPercentage</h4>
<pre>public&nbsp;void&nbsp;setDefaultValueAddedTaxPercentage(java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Sets the Value Added Tax percentage applied to prices by default.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>valueAddedTaxPercentage</code> - the default VAT percentage</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="isFurnitureCatalogViewedInTree--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFurnitureCatalogViewedInTree</h4>
<pre>public&nbsp;boolean&nbsp;isFurnitureCatalogViewedInTree()</pre>
<div class="block">Returns <code>true</code> if the furniture catalog should be viewed in a tree.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="setFurnitureCatalogViewedInTree-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureCatalogViewedInTree</h4>
<pre>public&nbsp;void&nbsp;setFurnitureCatalogViewedInTree(boolean&nbsp;furnitureCatalogViewedInTree)</pre>
<div class="block">Sets whether the furniture catalog should be viewed in a tree or a different way.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="isNavigationPanelVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNavigationPanelVisible</h4>
<pre>public&nbsp;boolean&nbsp;isNavigationPanelVisible()</pre>
<div class="block">Returns <code>true</code> if the navigation panel should be displayed.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="setNavigationPanelVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNavigationPanelVisible</h4>
<pre>public&nbsp;void&nbsp;setNavigationPanelVisible(boolean&nbsp;navigationPanelVisible)</pre>
<div class="block">Sets whether the navigation panel should be displayed or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="isEditingIn3DViewEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEditingIn3DViewEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isEditingIn3DViewEnabled()</pre>
<div class="block">Returns whether interactive editing in 3D view is enabled or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setEditingIn3DViewEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEditingIn3DViewEnabled</h4>
<pre>public&nbsp;void&nbsp;setEditingIn3DViewEnabled(boolean&nbsp;editingIn3DViewEnabled)</pre>
<div class="block">Sets whether interactive editing in 3D view is enabled or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="isAerialViewCenteredOnSelectionEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAerialViewCenteredOnSelectionEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isAerialViewCenteredOnSelectionEnabled()</pre>
<div class="block">Returns whether aerial view should be centered on selection or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setAerialViewCenteredOnSelectionEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAerialViewCenteredOnSelectionEnabled</h4>
<pre>public&nbsp;void&nbsp;setAerialViewCenteredOnSelectionEnabled(boolean&nbsp;aerialViewCenteredOnSelectionEnabled)</pre>
<div class="block">Sets whether aerial view should be centered on selection or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="isObserverCameraSelectedAtChange--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isObserverCameraSelectedAtChange</h4>
<pre>public&nbsp;boolean&nbsp;isObserverCameraSelectedAtChange()</pre>
<div class="block">Returns whether the observer camera should be selected at each change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setObserverCameraSelectedAtChange-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setObserverCameraSelectedAtChange</h4>
<pre>public&nbsp;void&nbsp;setObserverCameraSelectedAtChange(boolean&nbsp;observerCameraSelectedAtChange)</pre>
<div class="block">Sets whether the observer camera should be selected at each change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isMagnetismEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMagnetismEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isMagnetismEnabled()</pre>
<div class="block">Returns <code>true</code> if magnetism is enabled.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> by default.</dd>
</dl>
</li>
</ul>
<a name="setMagnetismEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMagnetismEnabled</h4>
<pre>public&nbsp;void&nbsp;setMagnetismEnabled(boolean&nbsp;magnetismEnabled)</pre>
<div class="block">Sets whether magnetism is enabled or not, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>magnetismEnabled</code> - <code>true</code> if magnetism is enabled,
          <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="isRulersVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRulersVisible</h4>
<pre>public&nbsp;boolean&nbsp;isRulersVisible()</pre>
<div class="block">Returns <code>true</code> if rulers are visible.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> by default.</dd>
</dl>
</li>
</ul>
<a name="setRulersVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRulersVisible</h4>
<pre>public&nbsp;void&nbsp;setRulersVisible(boolean&nbsp;rulersVisible)</pre>
<div class="block">Sets whether rulers are visible or not, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rulersVisible</code> - <code>true</code> if rulers are visible,
          <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="isGridVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isGridVisible</h4>
<pre>public&nbsp;boolean&nbsp;isGridVisible()</pre>
<div class="block">Returns <code>true</code> if plan grid visible.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> by default.</dd>
</dl>
</li>
</ul>
<a name="setGridVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGridVisible</h4>
<pre>public&nbsp;void&nbsp;setGridVisible(boolean&nbsp;gridVisible)</pre>
<div class="block">Sets whether plan grid is visible or not, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>gridVisible</code> - <code>true</code> if grid is visible,
          <code>false</code> otherwise.</dd>
</dl>
</li>
</ul>
<a name="isDrawingModeEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDrawingModeEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isDrawingModeEnabled()</pre>
<div class="block">Returns <code>true</code> is <a href="../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getDrawingMode--"><code>drawing mode</code></a>
 should be taken into account.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getDefaultFontName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFontName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultFontName()</pre>
<div class="block">Returns the name of the font that should be used by default or <code>null</code>
 if the default font should be the default one in the application.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setDefaultFontName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDefaultFontName</h4>
<pre>public&nbsp;void&nbsp;setDefaultFontName(java.lang.String&nbsp;defaultFontName)</pre>
<div class="block">Sets the name of the font that should be used by default.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="isFurnitureViewedFromTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFurnitureViewedFromTop</h4>
<pre>public&nbsp;boolean&nbsp;isFurnitureViewedFromTop()</pre>
<div class="block">Returns <code>true</code> if furniture should be viewed from its top in plan.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setFurnitureViewedFromTop-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureViewedFromTop</h4>
<pre>public&nbsp;void&nbsp;setFurnitureViewedFromTop(boolean&nbsp;furnitureViewedFromTop)</pre>
<div class="block">Sets how furniture icon should be displayed in plan, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureViewedFromTop</code> - if <code>true</code> the furniture
    should be viewed from its top.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getFurnitureModelIconSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureModelIconSize</h4>
<pre>public&nbsp;int&nbsp;getFurnitureModelIconSize()</pre>
<div class="block">Returns the size used to generate icons of furniture viewed from top.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setFurnitureModelIconSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureModelIconSize</h4>
<pre>public&nbsp;void&nbsp;setFurnitureModelIconSize(int&nbsp;furnitureModelIconSize)</pre>
<div class="block">Sets the name of the font that should be used by default.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isRoomFloorColoredOrTextured--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRoomFloorColoredOrTextured</h4>
<pre>public&nbsp;boolean&nbsp;isRoomFloorColoredOrTextured()</pre>
<div class="block">Returns <code>true</code> if room floors should be rendered with color or texture
 in plan.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>false</code> by default.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setFloorColoredOrTextured-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorColoredOrTextured</h4>
<pre>public&nbsp;void&nbsp;setFloorColoredOrTextured(boolean&nbsp;roomFloorColoredOrTextured)</pre>
<div class="block">Sets whether room floors should be rendered with color or texture,
 and notifies listeners of this change.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>roomFloorColoredOrTextured</code> - <code>true</code> if floor color
          or texture is used, <code>false</code> otherwise.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getWallPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallPattern</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;getWallPattern()</pre>
<div class="block">Returns the wall pattern in plan used by default.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="setWallPattern-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallPattern</h4>
<pre>public&nbsp;void&nbsp;setWallPattern(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;wallPattern)</pre>
<div class="block">Sets how walls should be displayed in plan by default, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
</dl>
</li>
</ul>
<a name="getNewWallPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewWallPattern</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;getNewWallPattern()</pre>
<div class="block">Returns the pattern used for new walls in plan or <code>null</code> if it's not set.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setNewWallPattern-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWallPattern</h4>
<pre>public&nbsp;void&nbsp;setNewWallPattern(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;newWallPattern)</pre>
<div class="block">Sets how new walls should be displayed in plan, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getNewWallThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewWallThickness</h4>
<pre>public&nbsp;float&nbsp;getNewWallThickness()</pre>
<div class="block">Returns default thickness of new walls in home.</div>
</li>
</ul>
<a name="setNewWallThickness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWallThickness</h4>
<pre>public&nbsp;void&nbsp;setNewWallThickness(float&nbsp;newWallThickness)</pre>
<div class="block">Sets default thickness of new walls in home, and notifies
 listeners of this change.</div>
</li>
</ul>
<a name="getNewWallHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewWallHeight</h4>
<pre>public&nbsp;float&nbsp;getNewWallHeight()</pre>
<div class="block">Returns default wall height of new home walls.</div>
</li>
</ul>
<a name="setNewWallHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWallHeight</h4>
<pre>public&nbsp;void&nbsp;setNewWallHeight(float&nbsp;newWallHeight)</pre>
<div class="block">Sets default wall height of new walls, and notifies
 listeners of this change.</div>
</li>
</ul>
<a name="getNewWallBaseboardThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewWallBaseboardThickness</h4>
<pre>public&nbsp;float&nbsp;getNewWallBaseboardThickness()</pre>
<div class="block">Returns default baseboard thickness of new walls in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setNewWallBaseboardThickness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWallBaseboardThickness</h4>
<pre>public&nbsp;void&nbsp;setNewWallBaseboardThickness(float&nbsp;newWallBaseboardThickness)</pre>
<div class="block">Sets default baseboard thickness of new walls in home, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getNewWallBaseboardHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewWallBaseboardHeight</h4>
<pre>public&nbsp;float&nbsp;getNewWallBaseboardHeight()</pre>
<div class="block">Returns default baseboard height of new home walls.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setNewWallBaseboardHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewWallBaseboardHeight</h4>
<pre>public&nbsp;void&nbsp;setNewWallBaseboardHeight(float&nbsp;newWallBaseboardHeight)</pre>
<div class="block">Sets default baseboard height of new walls, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getNewRoomFloorColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewRoomFloorColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getNewRoomFloorColor()</pre>
<div class="block">Returns the default color of new rooms in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="setNewRoomFloorColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewRoomFloorColor</h4>
<pre>public&nbsp;void&nbsp;setNewRoomFloorColor(java.lang.Integer&nbsp;newRoomFloorColor)</pre>
<div class="block">Sets the default color of new rooms in home, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="getNewFloorThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNewFloorThickness</h4>
<pre>public&nbsp;float&nbsp;getNewFloorThickness()</pre>
<div class="block">Returns default thickness of the floor of new levels in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setNewFloorThickness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNewFloorThickness</h4>
<pre>public&nbsp;void&nbsp;setNewFloorThickness(float&nbsp;newFloorThickness)</pre>
<div class="block">Sets default thickness of the floor of new levels in home, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isCheckUpdatesEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCheckUpdatesEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isCheckUpdatesEnabled()</pre>
<div class="block">Returns <code>true</code> if updates should be checked.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setCheckUpdatesEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCheckUpdatesEnabled</h4>
<pre>public&nbsp;void&nbsp;setCheckUpdatesEnabled(boolean&nbsp;updatesChecked)</pre>
<div class="block">Sets whether updates should be checked or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getUpdatesMinimumDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUpdatesMinimumDate</h4>
<pre>public&nbsp;java.lang.Long&nbsp;getUpdatesMinimumDate()</pre>
<div class="block">Returns the minimum date of updates that may interest user.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the date expressed in millis second since the epoch or <code>null</code> if not defined.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setUpdatesMinimumDate-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUpdatesMinimumDate</h4>
<pre>public&nbsp;void&nbsp;setUpdatesMinimumDate(java.lang.Long&nbsp;updatesMinimumDate)</pre>
<div class="block">Sets the minimum date of updates that may interest user, and notifies
 listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getAutoSaveDelayForRecovery--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoSaveDelayForRecovery</h4>
<pre>public&nbsp;int&nbsp;getAutoSaveDelayForRecovery()</pre>
<div class="block">Returns the delay between two automatic save operations of homes for recovery purpose.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a delay in milliseconds or 0 to disable auto save.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setAutoSaveDelayForRecovery-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoSaveDelayForRecovery</h4>
<pre>public&nbsp;void&nbsp;setAutoSaveDelayForRecovery(int&nbsp;autoSaveDelayForRecovery)</pre>
<div class="block">Sets the delay between two automatic save operations of homes for recovery purpose.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getRecentHomes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRecentHomes</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getRecentHomes()</pre>
<div class="block">Returns an unmodifiable list of the recent homes.</div>
</li>
</ul>
<a name="setRecentHomes-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRecentHomes</h4>
<pre>public&nbsp;void&nbsp;setRecentHomes(java.util.List&lt;java.lang.String&gt;&nbsp;recentHomes)</pre>
<div class="block">Sets the recent homes list and notifies listeners of this change.</div>
</li>
</ul>
<a name="getRecentHomesMaxCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRecentHomesMaxCount</h4>
<pre>public&nbsp;int&nbsp;getRecentHomesMaxCount()</pre>
<div class="block">Returns the maximum count of homes that should be proposed to the user.</div>
</li>
</ul>
<a name="getStoredCamerasMaxCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStoredCamerasMaxCount</h4>
<pre>public&nbsp;int&nbsp;getStoredCamerasMaxCount()</pre>
<div class="block">Returns the maximum count of stored cameras in homes that should be proposed to the user.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="setActionTipIgnored-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActionTipIgnored</h4>
<pre>public&nbsp;void&nbsp;setActionTipIgnored(java.lang.String&nbsp;actionKey)</pre>
<div class="block">Sets which action tip should be ignored.
 <br>This method should be overridden to store the ignore information.
 By default it just notifies listeners of this change.</div>
</li>
</ul>
<a name="isActionTipIgnored-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isActionTipIgnored</h4>
<pre>public&nbsp;boolean&nbsp;isActionTipIgnored(java.lang.String&nbsp;actionKey)</pre>
<div class="block">Returns whether an action tip should be ignored or not.
 <br>This method should be overridden to return the display information
 stored in <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html#setActionTipIgnored-java.lang.String-"><code>setActionTipIgnored</code></a>.
 By default it returns <code>true</code>.</div>
</li>
</ul>
<a name="resetIgnoredActionTips--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetIgnoredActionTips</h4>
<pre>public&nbsp;void&nbsp;resetIgnoredActionTips()</pre>
<div class="block">Resets the ignore flag of action tips.
 <br>This method should be overridden to clear all the display flags.
 By default it just notifies listeners of this change.</div>
</li>
</ul>
<a name="getDefaultTextStyle-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultTextStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;getDefaultTextStyle(java.lang.Class&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;selectableClass)</pre>
<div class="block">Returns the default text style of a class of selectable item.</div>
</li>
</ul>
<a name="getAutoCompletionStrings-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoCompletionStrings</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getAutoCompletionStrings(java.lang.String&nbsp;property)</pre>
<div class="block">Returns the strings that may be used for the auto completion of the given <code>property</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="addAutoCompletionString-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAutoCompletionString</h4>
<pre>public&nbsp;void&nbsp;addAutoCompletionString(java.lang.String&nbsp;property,
                                    java.lang.String&nbsp;autoCompletionString)</pre>
<div class="block">Adds the given string to the list of the strings used in auto completion of a <code>property</code>
 and notifies listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setAutoCompletionStrings-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoCompletionStrings</h4>
<pre>public&nbsp;void&nbsp;setAutoCompletionStrings(java.lang.String&nbsp;property,
                                     java.util.List&lt;java.lang.String&gt;&nbsp;autoCompletionStrings)</pre>
<div class="block">Sets the auto completion strings list of the given <code>property</code> and notifies listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getAutoCompletedProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoCompletedProperties</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getAutoCompletedProperties()</pre>
<div class="block">Returns the list of properties with auto completion strings.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getRecentColors--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRecentColors</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.Integer&gt;&nbsp;getRecentColors()</pre>
<div class="block">Returns an unmodifiable list of the recent colors.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setRecentColors-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRecentColors</h4>
<pre>public&nbsp;void&nbsp;setRecentColors(java.util.List&lt;java.lang.Integer&gt;&nbsp;recentColors)</pre>
<div class="block">Sets the recent colors list and notifies listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getRecentTextures--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRecentTextures</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&gt;&nbsp;getRecentTextures()</pre>
<div class="block">Returns an unmodifiable list of the recent textures.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="setRecentTextures-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRecentTextures</h4>
<pre>public&nbsp;void&nbsp;setRecentTextures(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&gt;&nbsp;recentTextures)</pre>
<div class="block">Sets the recent colors list and notifies listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="setHomeExamples-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHomeExamples</h4>
<pre>protected&nbsp;void&nbsp;setHomeExamples(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model">HomeDescriptor</a>&gt;&nbsp;homeExamples)</pre>
<div class="block">Sets the home examples available for the user.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getHomeExamples--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeExamples</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model">HomeDescriptor</a>&gt;&nbsp;getHomeExamples()</pre>
<div class="block">Returns the home examples available for the user.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setPhotoRenderer-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhotoRenderer</h4>
<pre>public&nbsp;void&nbsp;setPhotoRenderer(java.lang.String&nbsp;photoRenderer)</pre>
<div class="block">Sets the preferred rendering engine used to create photos.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getPhotoRenderer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhotoRenderer</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPhotoRenderer()</pre>
<div class="block">Returns the preferred rendering engine used to create photos.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="addLanguageLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addLanguageLibrary</h4>
<pre>public abstract&nbsp;void&nbsp;addLanguageLibrary(java.lang.String&nbsp;languageLibraryLocation)
                                 throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Adds the language library to make the languages it contains available to supported languages.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>languageLibraryLocation</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="languageLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>languageLibraryExists</h4>
<pre>public abstract&nbsp;boolean&nbsp;languageLibraryExists(java.lang.String&nbsp;languageLibraryLocation)
                                       throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns <code>true</code> if the language library at the given location exists.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>languageLibraryLocation</code> - the name of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="addFurnitureLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurnitureLibrary</h4>
<pre>public abstract&nbsp;void&nbsp;addFurnitureLibrary(java.lang.String&nbsp;furnitureLibraryLocation)
                                  throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Adds <code>furnitureLibraryName</code> to furniture catalog
 to make the furniture it contains available.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureLibraryLocation</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="furnitureLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>furnitureLibraryExists</h4>
<pre>public abstract&nbsp;boolean&nbsp;furnitureLibraryExists(java.lang.String&nbsp;furnitureLibraryLocation)
                                        throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns <code>true</code> if the furniture library at the given location exists.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureLibraryLocation</code> - the name of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="addTexturesLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTexturesLibrary</h4>
<pre>public abstract&nbsp;void&nbsp;addTexturesLibrary(java.lang.String&nbsp;texturesLibraryLocation)
                                 throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Adds the textures library at the given location to textures catalog
 to make the textures it contains available.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texturesLibraryLocation</code> - the location where the library can be found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="texturesLibraryExists-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>texturesLibraryExists</h4>
<pre>public abstract&nbsp;boolean&nbsp;texturesLibraryExists(java.lang.String&nbsp;texturesLibraryLocation)
                                       throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns <code>true</code> if the textures library at the given location exists.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texturesLibraryLocation</code> - the name of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getLibraries--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getLibraries</h4>
<pre>public abstract&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;getLibraries()</pre>
<div class="block">Returns the libraries available in user preferences.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UserPreferences.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/UserPreferences.html" target="_top">Frames</a></li>
<li><a href="UserPreferences.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
