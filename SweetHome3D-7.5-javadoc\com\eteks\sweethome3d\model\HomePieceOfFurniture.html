<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomePieceOfFurniture (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomePieceOfFurniture (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":9,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":42,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePieceOfFurniture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomePieceOfFurniture.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class HomePieceOfFurniture" class="title">Class HomePieceOfFurniture</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomePieceOfFurniture</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">HomeDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeLight.html" title="class in com.eteks.sweethome3d.model">HomeLight</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.html" title="class in com.eteks.sweethome3d.model">HomeShelfUnit</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomePieceOfFurniture</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>
implements <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, <a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></pre>
<div class="block">A piece of furniture in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.HomePieceOfFurniture">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.Property</a></span></code>
<div class="block">The properties of a piece of furniture that may change.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></span></code>
<div class="block">The properties on which home furniture may be sorted.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#EMPTY_PROPERTY_ARRAY">EMPTY_PROPERTY_ARRAY</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#HomePieceOfFurniture-com.eteks.sweethome3d.model.PieceOfFurniture-">HomePieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Creates a home piece of furniture from an existing piece.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#HomePieceOfFurniture-com.eteks.sweethome3d.model.PieceOfFurniture-java.lang.String:A-">HomePieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece,
                    java.lang.String[]&nbsp;copiedProperties)</code>
<div class="block">Creates a home piece of furniture from an existing piece.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#HomePieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.PieceOfFurniture-">HomePieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                    <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Creates a home piece of furniture from an existing piece.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#HomePieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.PieceOfFurniture-java.lang.String:A-">HomePieceOfFurniture</a></span>(java.lang.String&nbsp;id,
                    <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece,
                    java.lang.String[]&nbsp;copiedProperties)</code>
<div class="block">Creates a home piece of furniture from an existing piece.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this piece.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#containsPoint-float-float-float-">containsPoint</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this piece contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getAngle--">getAngle</a></span>()</code>
<div class="block">Returns the angle in radians of this piece around vertical axis.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCatalogId--">getCatalogId</a></span>()</code>
<div class="block">Returns the catalog ID of this piece of furniture or <code>null</code> if it doesn't exist.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the color of this piece of furniture.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCreator--">getCreator</a></span>()</code>
<div class="block">Returns the creator of this piece.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCurrency--">getCurrency</a></span>()</code>
<div class="block">Returns the price currency, noted with ISO 4217 code, or <code>null</code>
 if it has no price or default currency should be used.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDepth--">getDepth</a></span>()</code>
<div class="block">Returns the depth of this piece of furniture.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDepthInPlan--">getDepthInPlan</a></span>()</code>
<div class="block">Returns the depth of this piece of furniture in the horizontal plan (after pitch or roll is applied to it).</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDescription--">getDescription</a></span>()</code>
<div class="block">Returns the description of this piece of furniture.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a></span>()</code>
<div class="block">Returns the elevation at which should be placed an object dropped on this piece.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the elevation of the bottom of this piece of furniture on its level.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static java.util.Comparator&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getFurnitureComparator-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">getFurnitureComparator</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;property)</code>
<div class="block">Returns a comparator which compares furniture on a given <code>property</code> in ascending order.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getGroundElevation--">getGroundElevation</a></span>()</code>
<div class="block">Returns the elevation of the bottom of this piece of furniture
 from the ground according to the elevation of its level.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height of this piece of furniture.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getHeightInPlan--">getHeightInPlan</a></span>()</code>
<div class="block">Returns the height of this piece of furniture from the horizontal plan (after pitch or roll is applied to it).</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getIcon--">getIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getInformation--">getInformation</a></span>()</code>
<div class="block">Returns the additional information associated to this piece, or <code>null</code>.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getLevel--">getLevel</a></span>()</code>
<div class="block">Returns the level which this piece belongs to.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getLicense--">getLicense</a></span>()</code>
<div class="block">Returns the license of this piece, or <code>null</code>.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelFlags--">getModelFlags</a></span>()</code>
<div class="block">Returns the flags applied to the piece of furniture model.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelMaterials--">getModelMaterials</a></span>()</code>
<div class="block">Returns the materials applied to the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelRotation--">getModelRotation</a></span>()</code>
<div class="block">Returns the rotation 3 by 3 matrix of this piece of furniture that ensures
 its model is correctly oriented.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.lang.Long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelSize--">getModelSize</a></span>()</code>
<div class="block">Returns the size of the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelTransformations--">getModelTransformations</a></span>()</code>
<div class="block">Returns the transformations applied to the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this piece of furniture.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameAngle--">getNameAngle</a></span>()</code>
<div class="block">Returns the angle in radians used to display the piece name.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameStyle--">getNameStyle</a></span>()</code>
<div class="block">Returns the text style used to display piece name.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameXOffset--">getNameXOffset</a></span>()</code>
<div class="block">Returns the distance along x axis applied to piece abscissa to display piece name.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameYOffset--">getNameYOffset</a></span>()</code>
<div class="block">Returns the distance along y axis applied to piece ordinate
 to display piece name.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns the pitch angle in radians of this piece of furniture.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></span>()</code>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPoints--">getPoints</a></span>()</code>
<div class="block">Returns the points of each corner of a piece.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPrice--">getPrice</a></span>()</code>
<div class="block">Returns the price of this piece of furniture or <code>null</code>.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPriceValueAddedTaxIncluded--">getPriceValueAddedTaxIncluded</a></span>()</code>
<div class="block">Returns the price of this piece of furniture, Value Added Tax included.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getRoll--">getRoll</a></span>()</code>
<div class="block">Returns the roll angle in radians of this piece of furniture.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getShininess--">getShininess</a></span>()</code>
<div class="block">Returns the shininess of this piece of furniture.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></span>()</code>
<div class="block">Returns the shape used to cut out upper levels when they intersect with the piece
 like a staircase.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getTexture--">getTexture</a></span>()</code>
<div class="block">Returns the texture of this piece of furniture.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getValueAddedTax--">getValueAddedTax</a></span>()</code>
<div class="block">Returns the Value Added Tax applied to the price of this piece of furniture.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>java.math.BigDecimal</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></span>()</code>
<div class="block">Returns the Value Added Tax percentage applied to the price of this piece of furniture.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the width of this piece of furniture.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getWidthInPlan--">getWidthInPlan</a></span>()</code>
<div class="block">Returns the width of this piece of furniture in the horizontal plan (after pitch or roll is applied to it).</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getX--">getX</a></span>()</code>
<div class="block">Returns the abscissa of the center of this piece of furniture.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getY--">getY</a></span>()</code>
<div class="block">Returns the ordinate of the center of this piece of furniture.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></span>(float&nbsp;x0,
                   float&nbsp;y0,
                   float&nbsp;x1,
                   float&nbsp;y1)</code>
<div class="block">Returns <code>true</code> if this piece intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Returns <code>true</code> if this piece is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index
 or if the elevation of its highest point is higher than <code>level</code> elevation.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a></span>()</code>
<div class="block">Returns <code>true</code> if the back face of the piece of furniture
 model should be displayed.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBottomLeftPointAt-float-float-float-">isBottomLeftPointAt</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the bottom left point of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>,
 and if that point is closer to bottom left point than to top left and bottom right points.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBottomRightPointAt-float-float-float-">isBottomRightPointAt</a></span>(float&nbsp;x,
                    float&nbsp;y,
                    float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the bottom right point of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>,
 and if that point is closer to top left point than to top right and bottom left points.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isDeformable--">isDeformable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece of furniture is a door or a window.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></span>()</code>
<div class="block">Returns <code>false</code> if this piece should not rotate around an horizontal axis.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isHorizontallyRotated--">isHorizontallyRotated</a></span>()</code>
<div class="block">Returns <code>true</code> if the pitch or roll angle of this piece is different from 0.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isModelCenteredAtOrigin--">isModelCenteredAtOrigin</a></span>()</code>
<div class="block">Returns <code>true</code> if model center should be always centered at the origin
 when model rotation isn't <code>null</code>.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isModelMirrored--">isModelMirrored</a></span>()</code>
<div class="block">Returns <code>true</code> if the model of this piece should be mirrored.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isMovable--">isMovable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece of furniture is movable.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isNameCenterPointAt-float-float-float-">isNameCenterPointAt</a></span>(float&nbsp;x,
                   float&nbsp;y,
                   float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the center point at which is displayed the name
 of this piece is equal to the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isNameVisible--">isNameVisible</a></span>()</code>
<div class="block">Returns whether the name of this piece should be drawn or not.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isParallelToWall-com.eteks.sweethome3d.model.Wall-">isParallelToWall</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)</code>
<div class="block">Returns <code>true</code> if the front side of this piece is parallel to the given <code>wall</code>
 with a margin.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isPointAt-float-float-float-">isPointAt</a></span>(float&nbsp;x,
         float&nbsp;y,
         float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if one of the corner of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isResizable--">isResizable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is resizable.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTexturable--">isTexturable</a></span>()</code>
<div class="block">Returns <code>false</code> if this piece should always keep the same color or texture.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTopLeftPointAt-float-float-float-">isTopLeftPointAt</a></span>(float&nbsp;x,
                float&nbsp;y,
                float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the top left point of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>,
 and if that point is closer to top left point than to top right and bottom left points.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTopRightPointAt-float-float-float-">isTopRightPointAt</a></span>(float&nbsp;x,
                 float&nbsp;y,
                 float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if the top right point of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>,
 and if that point is closer to top right point than to top left and bottom right points.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isVisible--">isVisible</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece of furniture is visible.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></span>()</code>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#move-float-float-">move</a></span>(float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Moves this piece of (<code>dx</code>, <code>dy</code>) units.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#scale-float-">scale</a></span>(float&nbsp;scale)</code>
<div class="block">Scales this piece of furniture with the given <code>scale</code>.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setAngle-float-">setAngle</a></span>(float&nbsp;angle)</code>
<div class="block">Sets the angle of this piece around vertical axis.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setBackFaceShown-boolean-">setBackFaceShown</a></span>(boolean&nbsp;backFaceShown)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Prefer use <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelFlags-int-"><code>setModelFlags(int)</code></a> with <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE"><code>PieceOfFurniture.SHOW_BACK_FACE</code></a> flag.</span></div>
</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCatalogId-java.lang.String-">setCatalogId</a></span>(java.lang.String&nbsp;catalogId)</code>
<div class="block">Sets the catalog ID of this piece of furniture.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the color of this piece of furniture.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCreator-java.lang.String-">setCreator</a></span>(java.lang.String&nbsp;creator)</code>
<div class="block">Sets the creator of this piece.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCurrency-java.lang.String-">setCurrency</a></span>(java.lang.String&nbsp;currency)</code>
<div class="block">Sets the price currency, noted with ISO 4217 code.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDepth-float-">setDepth</a></span>(float&nbsp;depth)</code>
<div class="block">Sets the depth of this piece of furniture.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDepthInPlan-float-">setDepthInPlan</a></span>(float&nbsp;depthInPlan)</code>
<div class="block">Sets the depth of this piece of furniture in the horizontal plan (after pitch or roll is applied to it).</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDescription-java.lang.String-">setDescription</a></span>(java.lang.String&nbsp;description)</code>
<div class="block">Sets the description of this piece of furniture.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setElevation-float-">setElevation</a></span>(float&nbsp;elevation)</code>
<div class="block">Sets the elevation of this piece of furniture on its level.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setHeight-float-">setHeight</a></span>(float&nbsp;height)</code>
<div class="block">Sets the height of this piece of furniture.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setHeightInPlan-float-">setHeightInPlan</a></span>(float&nbsp;heightInPlan)</code>
<div class="block">Sets the height of this piece of furniture from the horizontal plan (after pitch or roll is applied to it).</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setIcon-com.eteks.sweethome3d.model.Content-">setIcon</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon)</code>
<div class="block">Sets the icon of this piece of furniture.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setInformation-java.lang.String-">setInformation</a></span>(java.lang.String&nbsp;information)</code>
<div class="block">Sets the additional information associated to this piece .</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Sets the level of this piece of furniture.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setLicense-java.lang.String-">setLicense</a></span>(java.lang.String&nbsp;license)</code>
<div class="block">Sets the the license of this piece .</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Sets the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelCenteredAtOrigin-boolean-">setModelCenteredAtOrigin</a></span>(boolean&nbsp;modelCenteredAtOrigin)</code>
<div class="block">Sets whether model center should be always centered at the origin
 when model rotation isn't <code>null</code>.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelFlags-int-">setModelFlags</a></span>(int&nbsp;modelFlags)</code>
<div class="block">Sets the flags applied to the piece of furniture model.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">setModelMaterials</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;modelMaterials)</code>
<div class="block">Sets the materials of the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelMirrored-boolean-">setModelMirrored</a></span>(boolean&nbsp;modelMirrored)</code>
<div class="block">Sets whether the model of this piece of furniture is mirrored or not.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelRotation-float:A:A-">setModelRotation</a></span>(float[][]&nbsp;modelRotation)</code>
<div class="block">Sets the rotation 3 by 3 matrix of this piece of furniture and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelSize-java.lang.Long-">setModelSize</a></span>(java.lang.Long&nbsp;modelSize)</code>
<div class="block">Sets the size of the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">setModelTransformations</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;modelTransformations)</code>
<div class="block">Sets the transformations applied to some parts of the 3D model of this piece of furniture.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setMovable-boolean-">setMovable</a></span>(boolean&nbsp;movable)</code>
<div class="block">Sets whether this piece is movable or not.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Sets the name of this piece of furniture.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameAngle-float-">setNameAngle</a></span>(float&nbsp;nameAngle)</code>
<div class="block">Sets the angle in radians used to display the piece name.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameStyle-com.eteks.sweethome3d.model.TextStyle-">setNameStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;nameStyle)</code>
<div class="block">Sets the text style used to display piece name.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameVisible-boolean-">setNameVisible</a></span>(boolean&nbsp;nameVisible)</code>
<div class="block">Sets whether the name of this piece is visible or not.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameXOffset-float-">setNameXOffset</a></span>(float&nbsp;nameXOffset)</code>
<div class="block">Sets the distance along x axis applied to piece abscissa to display piece name.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameYOffset-float-">setNameYOffset</a></span>(float&nbsp;nameYOffset)</code>
<div class="block">Sets the distance along y axis applied to piece ordinate to display piece name.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPitch-float-">setPitch</a></span>(float&nbsp;pitch)</code>
<div class="block">Sets the pitch angle in radians of this piece and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPlanIcon-com.eteks.sweethome3d.model.Content-">setPlanIcon</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon)</code>
<div class="block">Sets the plan icon of this piece of furniture.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPrice-java.math.BigDecimal-">setPrice</a></span>(java.math.BigDecimal&nbsp;price)</code>
<div class="block">Sets the price of this piece of furniture.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setRoll-float-">setRoll</a></span>(float&nbsp;roll)</code>
<div class="block">Sets the roll angle in radians of this piece and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setShininess-java.lang.Float-">setShininess</a></span>(java.lang.Float&nbsp;shininess)</code>
<div class="block">Sets the shininess of this piece of furniture or <code>null</code> if piece shininess is unchanged.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setStaircaseCutOutShape-java.lang.String-">setStaircaseCutOutShape</a></span>(java.lang.String&nbsp;staircaseCutOutShape)</code>
<div class="block">Sets the shape used to cut out upper levels when they intersect with the piece
 like a staircase.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setTexture-com.eteks.sweethome3d.model.HomeTexture-">setTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Sets the texture of this piece of furniture.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setValueAddedTaxPercentage-java.math.BigDecimal-">setValueAddedTaxPercentage</a></span>(java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Sets the Value Added Tax percentage applied to prices.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setVisible-boolean-">setVisible</a></span>(boolean&nbsp;visible)</code>
<div class="block">Sets whether this piece of furniture is visible or not.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setWidth-float-">setWidth</a></span>(float&nbsp;width)</code>
<div class="block">Sets the width of this piece of furniture.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setWidthInPlan-float-">setWidthInPlan</a></span>(float&nbsp;widthInPlan)</code>
<div class="block">Sets the width of this piece of furniture in the horizontal plan (after pitch or roll is applied to it).</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setX-float-">setX</a></span>(float&nbsp;x)</code>
<div class="block">Sets the abscissa of the center of this piece.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setY-float-">setY</a></span>(float&nbsp;y)</code>
<div class="block">Sets the ordinate of the center of this piece.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="EMPTY_PROPERTY_ARRAY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EMPTY_PROPERTY_ARRAY</h4>
<pre>protected static final&nbsp;java.lang.String[] EMPTY_PROPERTY_ARRAY</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomePieceOfFurniture-com.eteks.sweethome3d.model.PieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomePieceOfFurniture</h4>
<pre>public&nbsp;HomePieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Creates a home piece of furniture from an existing piece.
 No additional properties will be copied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>piece</code> - the piece from which data are copied</dd>
</dl>
</li>
</ul>
<a name="HomePieceOfFurniture-com.eteks.sweethome3d.model.PieceOfFurniture-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomePieceOfFurniture</h4>
<pre>public&nbsp;HomePieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece,
                            java.lang.String[]&nbsp;copiedProperties)</pre>
<div class="block">Creates a home piece of furniture from an existing piece.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>piece</code> - the piece from which data are copied</dd>
<dd><code>copiedProperties</code> - the names of the additional properties which should be copied from the existing piece
                         or <code>null</code> if all properties should be copied.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="HomePieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.PieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomePieceOfFurniture</h4>
<pre>public&nbsp;HomePieceOfFurniture(java.lang.String&nbsp;id,
                            <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Creates a home piece of furniture from an existing piece.
 No additional properties will be copied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the ID of the piece</dd>
<dd><code>piece</code> - the piece from which data are copied</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="HomePieceOfFurniture-java.lang.String-com.eteks.sweethome3d.model.PieceOfFurniture-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomePieceOfFurniture</h4>
<pre>public&nbsp;HomePieceOfFurniture(java.lang.String&nbsp;id,
                            <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece,
                            java.lang.String[]&nbsp;copiedProperties)</pre>
<div class="block">Creates a home piece of furniture from an existing piece.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the ID of the piece</dd>
<dd><code>piece</code> - the piece from which data are copied</dd>
<dd><code>copiedProperties</code> - the names of the additional properties which should be copied from the existing piece
                         or <code>null</code> if all properties should be copied.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCatalogId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCatalogId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCatalogId()</pre>
<div class="block">Returns the catalog ID of this piece of furniture or <code>null</code> if it doesn't exist.</div>
</li>
</ul>
<a name="setCatalogId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCatalogId</h4>
<pre>public&nbsp;void&nbsp;setCatalogId(java.lang.String&nbsp;catalogId)</pre>
<div class="block">Sets the catalog ID of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the name of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="isNameVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNameVisible</h4>
<pre>public&nbsp;boolean&nbsp;isNameVisible()</pre>
<div class="block">Returns whether the name of this piece should be drawn or not.</div>
</li>
</ul>
<a name="setNameVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameVisible</h4>
<pre>public&nbsp;void&nbsp;setNameVisible(boolean&nbsp;nameVisible)</pre>
<div class="block">Sets whether the name of this piece is visible or not. Once this piece of furniture
 is updated, listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getNameXOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameXOffset</h4>
<pre>public&nbsp;float&nbsp;getNameXOffset()</pre>
<div class="block">Returns the distance along x axis applied to piece abscissa to display piece name.</div>
</li>
</ul>
<a name="setNameXOffset-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameXOffset</h4>
<pre>public&nbsp;void&nbsp;setNameXOffset(float&nbsp;nameXOffset)</pre>
<div class="block">Sets the distance along x axis applied to piece abscissa to display piece name.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getNameYOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameYOffset</h4>
<pre>public&nbsp;float&nbsp;getNameYOffset()</pre>
<div class="block">Returns the distance along y axis applied to piece ordinate
 to display piece name.</div>
</li>
</ul>
<a name="setNameYOffset-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameYOffset</h4>
<pre>public&nbsp;void&nbsp;setNameYOffset(float&nbsp;nameYOffset)</pre>
<div class="block">Sets the distance along y axis applied to piece ordinate to display piece name.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getNameStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;getNameStyle()</pre>
<div class="block">Returns the text style used to display piece name.</div>
</li>
</ul>
<a name="setNameStyle-com.eteks.sweethome3d.model.TextStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameStyle</h4>
<pre>public&nbsp;void&nbsp;setNameStyle(<a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;nameStyle)</pre>
<div class="block">Sets the text style used to display piece name.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getNameAngle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNameAngle</h4>
<pre>public&nbsp;float&nbsp;getNameAngle()</pre>
<div class="block">Returns the angle in radians used to display the piece name.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="setNameAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNameAngle</h4>
<pre>public&nbsp;void&nbsp;setNameAngle(float&nbsp;nameAngle)</pre>
<div class="block">Sets the angle in radians used to display the piece name. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.6</dd>
</dl>
</li>
</ul>
<a name="getDescription--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDescription</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDescription()</pre>
<div class="block">Returns the description of this piece of furniture.
 The returned value may be <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDescription--">getDescription</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setDescription-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDescription</h4>
<pre>public&nbsp;void&nbsp;setDescription(java.lang.String&nbsp;description)</pre>
<div class="block">Sets the description of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getInformation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInformation</h4>
<pre>public&nbsp;java.lang.String&nbsp;getInformation()</pre>
<div class="block">Returns the additional information associated to this piece, or <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="setInformation-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setInformation</h4>
<pre>public&nbsp;void&nbsp;setInformation(java.lang.String&nbsp;information)</pre>
<div class="block">Sets the additional information associated to this piece . Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getCreator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreator</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCreator()</pre>
<div class="block">Returns the creator of this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="setCreator-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreator</h4>
<pre>public&nbsp;void&nbsp;setCreator(java.lang.String&nbsp;creator)</pre>
<div class="block">Sets the creator of this piece. Once this piece is updated, listeners added to this piece
 will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getLicense--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLicense</h4>
<pre>public&nbsp;java.lang.String&nbsp;getLicense()</pre>
<div class="block">Returns the license of this piece, or <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getLicense--">getLicense</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setLicense-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLicense</h4>
<pre>public&nbsp;void&nbsp;setLicense(java.lang.String&nbsp;license)</pre>
<div class="block">Sets the the license of this piece . Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getDepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepth</h4>
<pre>public&nbsp;float&nbsp;getDepth()</pre>
<div class="block">Returns the depth of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setDepth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepth</h4>
<pre>public&nbsp;void&nbsp;setDepth(float&nbsp;depth)</pre>
<div class="block">Sets the depth of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if this piece of furniture isn't resizable</dd>
</dl>
</li>
</ul>
<a name="getDepthInPlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepthInPlan</h4>
<pre>public&nbsp;float&nbsp;getDepthInPlan()</pre>
<div class="block">Returns the depth of this piece of furniture in the horizontal plan (after pitch or roll is applied to it).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setDepthInPlan-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepthInPlan</h4>
<pre>public&nbsp;void&nbsp;setDepthInPlan(float&nbsp;depthInPlan)</pre>
<div class="block">Sets the depth of this piece of furniture in the horizontal plan (after pitch or roll is applied to it).
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;float&nbsp;getHeight()</pre>
<div class="block">Returns the height of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public&nbsp;void&nbsp;setHeight(float&nbsp;height)</pre>
<div class="block">Sets the height of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if this piece of furniture isn't resizable</dd>
</dl>
</li>
</ul>
<a name="getHeightInPlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeightInPlan</h4>
<pre>public&nbsp;float&nbsp;getHeightInPlan()</pre>
<div class="block">Returns the height of this piece of furniture from the horizontal plan (after pitch or roll is applied to it).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setHeightInPlan-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeightInPlan</h4>
<pre>public&nbsp;void&nbsp;setHeightInPlan(float&nbsp;heightInPlan)</pre>
<div class="block">Sets the height of this piece of furniture from the horizontal plan (after pitch or roll is applied to it).
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;float&nbsp;getWidth()</pre>
<div class="block">Returns the width of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setWidth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidth</h4>
<pre>public&nbsp;void&nbsp;setWidth(float&nbsp;width)</pre>
<div class="block">Sets the width of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if this piece of furniture isn't resizable</dd>
</dl>
</li>
</ul>
<a name="getWidthInPlan--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidthInPlan</h4>
<pre>public&nbsp;float&nbsp;getWidthInPlan()</pre>
<div class="block">Returns the width of this piece of furniture in the horizontal plan (after pitch or roll is applied to it).</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setWidthInPlan-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidthInPlan</h4>
<pre>public&nbsp;void&nbsp;setWidthInPlan(float&nbsp;widthInPlan)</pre>
<div class="block">Sets the width of this piece of furniture in the horizontal plan (after pitch or roll is applied to it).
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="scale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>scale</h4>
<pre>public&nbsp;void&nbsp;scale(float&nbsp;scale)</pre>
<div class="block">Scales this piece of furniture with the given <code>scale</code>.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>public&nbsp;float&nbsp;getElevation()</pre>
<div class="block">Returns the elevation of the bottom of this piece of furniture on its level.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getElevation--">getElevation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getDropOnTopElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDropOnTopElevation</h4>
<pre>public&nbsp;float&nbsp;getDropOnTopElevation()</pre>
<div class="block">Returns the elevation at which should be placed an object dropped on this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a percentage of the height of this piece. A negative value means that the piece
         should be ignored when an object is dropped on it.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getGroundElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroundElevation</h4>
<pre>public&nbsp;float&nbsp;getGroundElevation()</pre>
<div class="block">Returns the elevation of the bottom of this piece of furniture
 from the ground according to the elevation of its level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setElevation-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevation</h4>
<pre>public&nbsp;void&nbsp;setElevation(float&nbsp;elevation)</pre>
<div class="block">Sets the elevation of this piece of furniture on its level. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="isMovable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMovable</h4>
<pre>public&nbsp;boolean&nbsp;isMovable()</pre>
<div class="block">Returns <code>true</code> if this piece of furniture is movable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setMovable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMovable</h4>
<pre>public&nbsp;void&nbsp;setMovable(boolean&nbsp;movable)</pre>
<div class="block">Sets whether this piece is movable or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isDoorOrWindow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDoorOrWindow</h4>
<pre>public&nbsp;boolean&nbsp;isDoorOrWindow()</pre>
<div class="block">Returns <code>true</code> if this piece of furniture is a door or a window.
 As this method existed before <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html" title="class in com.eteks.sweethome3d.model">HomeDoorOrWindow</a> class,
 you shouldn't rely on the value returned by this method to guess if a piece
 is an instance of <code>DoorOrWindow</code> class.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="getIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIcon</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getIcon()</pre>
<div class="block">Returns the icon of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setIcon-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIcon</h4>
<pre>public&nbsp;void&nbsp;setIcon(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon)</pre>
<div class="block">Sets the icon of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getPlanIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlanIcon</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getPlanIcon()</pre>
<div class="block">Returns the icon of this piece of furniture displayed in plan or <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.2</dd>
</dl>
</li>
</ul>
<a name="setPlanIcon-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlanIcon</h4>
<pre>public&nbsp;void&nbsp;setPlanIcon(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon)</pre>
<div class="block">Sets the plan icon of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getModel()</pre>
<div class="block">Returns the 3D model of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setModel-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModel</h4>
<pre>public&nbsp;void&nbsp;setModel(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</pre>
<div class="block">Sets the 3D model of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getModelSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelSize</h4>
<pre>public&nbsp;java.lang.Long&nbsp;getModelSize()</pre>
<div class="block">Returns the size of the 3D model of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setModelSize-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelSize</h4>
<pre>public&nbsp;void&nbsp;setModelSize(java.lang.Long&nbsp;modelSize)</pre>
<div class="block">Sets the size of the 3D model of this piece of furniture.
 This method should be called only to update a piece created with an older version.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getModelMaterials--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelMaterials</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;getModelMaterials()</pre>
<div class="block">Returns the materials applied to the 3D model of this piece of furniture.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the materials of the 3D model or <code>null</code>
 if the individual materials of the 3D model are not modified.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelMaterials</h4>
<pre>public&nbsp;void&nbsp;setModelMaterials(<a href="../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>[]&nbsp;modelMaterials)</pre>
<div class="block">Sets the materials of the 3D model of this piece of furniture.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelMaterials</code> - the materials of the 3D model or <code>null</code> if they shouldn't be changed</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if this piece of furniture isn't texturable</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the color of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the color of the piece as RGB code or <code>null</code> if piece color is unchanged.</dd>
</dl>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the color of this piece of furniture.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>color</code> - the color of this piece of furniture or <code>null</code> if piece color is the default one</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if this piece of furniture isn't texturable</dd>
</dl>
</li>
</ul>
<a name="getTexture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTexture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;getTexture()</pre>
<div class="block">Returns the texture of this piece of furniture.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the texture of the piece or <code>null</code> if piece texture is unchanged.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="setTexture-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTexture</h4>
<pre>public&nbsp;void&nbsp;setTexture(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</pre>
<div class="block">Sets the texture of this piece of furniture.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - the texture of this piece of furniture or <code>null</code> if piece texture is the default one</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if this piece of furniture isn't texturable</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="getShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShininess</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getShininess()</pre>
<div class="block">Returns the shininess of this piece of furniture.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value between 0 (matt) and 1 (very shiny) or <code>null</code> if piece shininess is unchanged.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setShininess-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setShininess</h4>
<pre>public&nbsp;void&nbsp;setShininess(java.lang.Float&nbsp;shininess)</pre>
<div class="block">Sets the shininess of this piece of furniture or <code>null</code> if piece shininess is unchanged.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if this piece of furniture isn't texturable</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isResizable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isResizable</h4>
<pre>public&nbsp;boolean&nbsp;isResizable()</pre>
<div class="block">Returns <code>true</code> if this piece is resizable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="isDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDeformable</h4>
<pre>public&nbsp;boolean&nbsp;isDeformable()</pre>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isWidthDepthDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWidthDepthDeformable</h4>
<pre>public&nbsp;boolean&nbsp;isWidthDepthDeformable()</pre>
<div class="block">Returns <code>true</code> if this piece is deformable.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isTexturable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTexturable</h4>
<pre>public&nbsp;boolean&nbsp;isTexturable()</pre>
<div class="block">Returns <code>false</code> if this piece should always keep the same color or texture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="isHorizontallyRotatable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHorizontallyRotatable</h4>
<pre>public&nbsp;boolean&nbsp;isHorizontallyRotatable()</pre>
<div class="block">Returns <code>false</code> if this piece should not rotate around an horizontal axis.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getPrice--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrice</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getPrice()</pre>
<div class="block">Returns the price of this piece of furniture or <code>null</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setPrice-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPrice</h4>
<pre>public&nbsp;void&nbsp;setPrice(java.math.BigDecimal&nbsp;price)</pre>
<div class="block">Sets the price of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getValueAddedTaxPercentage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueAddedTaxPercentage</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getValueAddedTaxPercentage()</pre>
<div class="block">Returns the Value Added Tax percentage applied to the price of this piece of furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setValueAddedTaxPercentage-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValueAddedTaxPercentage</h4>
<pre>public&nbsp;void&nbsp;setValueAddedTaxPercentage(java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</pre>
<div class="block">Sets the Value Added Tax percentage applied to prices.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getValueAddedTax--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValueAddedTax</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getValueAddedTax()</pre>
<div class="block">Returns the Value Added Tax applied to the price of this piece of furniture.</div>
</li>
</ul>
<a name="getPriceValueAddedTaxIncluded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriceValueAddedTaxIncluded</h4>
<pre>public&nbsp;java.math.BigDecimal&nbsp;getPriceValueAddedTaxIncluded()</pre>
<div class="block">Returns the price of this piece of furniture, Value Added Tax included.</div>
</li>
</ul>
<a name="getCurrency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCurrency</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCurrency()</pre>
<div class="block">Returns the price currency, noted with ISO 4217 code, or <code>null</code>
 if it has no price or default currency should be used.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setCurrency-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCurrency</h4>
<pre>public&nbsp;void&nbsp;setCurrency(java.lang.String&nbsp;currency)</pre>
<div class="block">Sets the price currency, noted with ISO 4217 code. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="isVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVisible</h4>
<pre>public&nbsp;boolean&nbsp;isVisible()</pre>
<div class="block">Returns <code>true</code> if this piece of furniture is visible.</div>
</li>
</ul>
<a name="setVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisible</h4>
<pre>public&nbsp;void&nbsp;setVisible(boolean&nbsp;visible)</pre>
<div class="block">Sets whether this piece of furniture is visible or not. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public&nbsp;float&nbsp;getX()</pre>
<div class="block">Returns the abscissa of the center of this piece of furniture.</div>
</li>
</ul>
<a name="setX-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public&nbsp;void&nbsp;setX(float&nbsp;x)</pre>
<div class="block">Sets the abscissa of the center of this piece. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public&nbsp;float&nbsp;getY()</pre>
<div class="block">Returns the ordinate of the center of this piece of furniture.</div>
</li>
</ul>
<a name="setY-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public&nbsp;void&nbsp;setY(float&nbsp;y)</pre>
<div class="block">Sets the ordinate of the center of this piece. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getAngle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAngle</h4>
<pre>public&nbsp;float&nbsp;getAngle()</pre>
<div class="block">Returns the angle in radians of this piece around vertical axis.</div>
</li>
</ul>
<a name="setAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngle</h4>
<pre>public&nbsp;void&nbsp;setAngle(float&nbsp;angle)</pre>
<div class="block">Sets the angle of this piece around vertical axis. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;float&nbsp;getPitch()</pre>
<div class="block">Returns the pitch angle in radians of this piece of furniture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setPitch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitch</h4>
<pre>public&nbsp;void&nbsp;setPitch(float&nbsp;pitch)</pre>
<div class="block">Sets the pitch angle in radians of this piece and notifies listeners of this change.
 Pitch axis is horizontal lateral (or transverse) axis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getRoll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoll</h4>
<pre>public&nbsp;float&nbsp;getRoll()</pre>
<div class="block">Returns the roll angle in radians of this piece of furniture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setRoll-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoll</h4>
<pre>public&nbsp;void&nbsp;setRoll(float&nbsp;roll)</pre>
<div class="block">Sets the roll angle in radians of this piece and notifies listeners of this change.
 Roll axis is horizontal longitudinal axis.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isHorizontallyRotated--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHorizontallyRotated</h4>
<pre>public&nbsp;boolean&nbsp;isHorizontallyRotated()</pre>
<div class="block">Returns <code>true</code> if the pitch or roll angle of this piece is different from 0.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getModelRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelRotation</h4>
<pre>public&nbsp;float[][]&nbsp;getModelRotation()</pre>
<div class="block">Returns the rotation 3 by 3 matrix of this piece of furniture that ensures
 its model is correctly oriented.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setModelRotation-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelRotation</h4>
<pre>public&nbsp;void&nbsp;setModelRotation(float[][]&nbsp;modelRotation)</pre>
<div class="block">Sets the rotation 3 by 3 matrix of this piece of furniture and notifies listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="isModelMirrored--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isModelMirrored</h4>
<pre>public&nbsp;boolean&nbsp;isModelMirrored()</pre>
<div class="block">Returns <code>true</code> if the model of this piece should be mirrored.</div>
</li>
</ul>
<a name="setModelMirrored-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelMirrored</h4>
<pre>public&nbsp;void&nbsp;setModelMirrored(boolean&nbsp;modelMirrored)</pre>
<div class="block">Sets whether the model of this piece of furniture is mirrored or not. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if this piece of furniture isn't resizable</dd>
</dl>
</li>
</ul>
<a name="isModelCenteredAtOrigin--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isModelCenteredAtOrigin</h4>
<pre>public&nbsp;boolean&nbsp;isModelCenteredAtOrigin()</pre>
<div class="block">Returns <code>true</code> if model center should be always centered at the origin
 when model rotation isn't <code>null</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>false</code> by default if version < 5.5</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setModelCenteredAtOrigin-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelCenteredAtOrigin</h4>
<pre>public&nbsp;void&nbsp;setModelCenteredAtOrigin(boolean&nbsp;modelCenteredAtOrigin)</pre>
<div class="block">Sets whether model center should be always centered at the origin
 when model rotation isn't <code>null</code>.
 This method should be called only to keep unchanged the (wrong) location
 of a rotated model created with version < 5.5.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isBackFaceShown--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackFaceShown</h4>
<pre>public&nbsp;boolean&nbsp;isBackFaceShown()</pre>
<div class="block">Returns <code>true</code> if the back face of the piece of furniture
 model should be displayed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setBackFaceShown-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackFaceShown</h4>
<pre>public&nbsp;void&nbsp;setBackFaceShown(boolean&nbsp;backFaceShown)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Prefer use <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelFlags-int-"><code>setModelFlags(int)</code></a> with <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE"><code>PieceOfFurniture.SHOW_BACK_FACE</code></a> flag.</span></div>
<div class="block">Sets whether the back face of the piece of furniture model should be displayed.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getModelFlags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelFlags</h4>
<pre>public&nbsp;int&nbsp;getModelFlags()</pre>
<div class="block">Returns the flags applied to the piece of furniture model.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setModelFlags-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelFlags</h4>
<pre>public&nbsp;void&nbsp;setModelFlags(int&nbsp;modelFlags)</pre>
<div class="block">Sets the flags applied to the piece of furniture model.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getModelTransformations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelTransformations</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;getModelTransformations()</pre>
<div class="block">Returns the transformations applied to the 3D model of this piece of furniture.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the transformations of the 3D model or <code>null</code>
 if the 3D model is not transformed.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelTransformations</h4>
<pre>public&nbsp;void&nbsp;setModelTransformations(<a href="../../../../com/eteks/sweethome3d/model/Transformation.html" title="class in com.eteks.sweethome3d.model">Transformation</a>[]&nbsp;modelTransformations)</pre>
<div class="block">Sets the transformations applied to some parts of the 3D model of this piece of furniture.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>modelTransformations</code> - the transformations of the 3D model or <code>null</code> if no transformation shouldn't be applied</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getStaircaseCutOutShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaircaseCutOutShape</h4>
<pre>public&nbsp;java.lang.String&nbsp;getStaircaseCutOutShape()</pre>
<div class="block">Returns the shape used to cut out upper levels when they intersect with the piece
 like a staircase.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setStaircaseCutOutShape-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaircaseCutOutShape</h4>
<pre>public&nbsp;void&nbsp;setStaircaseCutOutShape(java.lang.String&nbsp;staircaseCutOutShape)</pre>
<div class="block">Sets the shape used to cut out upper levels when they intersect with the piece
 like a staircase. Once this piece is updated, listeners added to this piece
 will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;getLevel()</pre>
<div class="block">Returns the level which this piece belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#getLevel--">getLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="setLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevel</h4>
<pre>public&nbsp;void&nbsp;setLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Sets the level of this piece of furniture. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="isAtLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAtLevel</h4>
<pre>public&nbsp;boolean&nbsp;isAtLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Returns <code>true</code> if this piece is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index
 or if the elevation of its highest point is higher than <code>level</code> elevation.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="getPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public&nbsp;float[][]&nbsp;getPoints()</pre>
<div class="block">Returns the points of each corner of a piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#getPoints--">getPoints</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of the 4 (x,y) coordinates of the piece corners.</dd>
</dl>
</li>
</ul>
<a name="intersectsRectangle-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersectsRectangle</h4>
<pre>public&nbsp;boolean&nbsp;intersectsRectangle(float&nbsp;x0,
                                   float&nbsp;y0,
                                   float&nbsp;x1,
                                   float&nbsp;y1)</pre>
<div class="block">Returns <code>true</code> if this piece intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="containsPoint-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsPoint</h4>
<pre>public&nbsp;boolean&nbsp;containsPoint(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this piece contains
 the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#containsPoint-float-float-float-">containsPoint</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="isPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isPointAt(float&nbsp;x,
                         float&nbsp;y,
                         float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if one of the corner of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</li>
</ul>
<a name="isTopLeftPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTopLeftPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isTopLeftPointAt(float&nbsp;x,
                                float&nbsp;y,
                                float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the top left point of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>,
 and if that point is closer to top left point than to top right and bottom left points.</div>
</li>
</ul>
<a name="isTopRightPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTopRightPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isTopRightPointAt(float&nbsp;x,
                                 float&nbsp;y,
                                 float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the top right point of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>,
 and if that point is closer to top right point than to top left and bottom right points.</div>
</li>
</ul>
<a name="isBottomLeftPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBottomLeftPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isBottomLeftPointAt(float&nbsp;x,
                                   float&nbsp;y,
                                   float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the bottom left point of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>,
 and if that point is closer to bottom left point than to top left and bottom right points.</div>
</li>
</ul>
<a name="isBottomRightPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBottomRightPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isBottomRightPointAt(float&nbsp;x,
                                    float&nbsp;y,
                                    float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the bottom right point of this piece is
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>,
 and if that point is closer to top left point than to top right and bottom left points.</div>
</li>
</ul>
<a name="isNameCenterPointAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNameCenterPointAt</h4>
<pre>public&nbsp;boolean&nbsp;isNameCenterPointAt(float&nbsp;x,
                                   float&nbsp;y,
                                   float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if the center point at which is displayed the name
 of this piece is equal to the point at (<code>x</code>, <code>y</code>)
 with a given <code>margin</code>.</div>
</li>
</ul>
<a name="isParallelToWall-com.eteks.sweethome3d.model.Wall-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isParallelToWall</h4>
<pre>public&nbsp;boolean&nbsp;isParallelToWall(<a href="../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)</pre>
<div class="block">Returns <code>true</code> if the front side of this piece is parallel to the given <code>wall</code>
 with a margin.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="move-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>move</h4>
<pre>public&nbsp;void&nbsp;move(float&nbsp;dx,
                 float&nbsp;dy)</pre>
<div class="block">Moves this piece of (<code>dx</code>, <code>dy</code>) units.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#move-float-float-">move</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this piece.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
</dl>
</li>
</ul>
<a name="getFurnitureComparator-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getFurnitureComparator</h4>
<pre>public static&nbsp;java.util.Comparator&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;getFurnitureComparator(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;property)</pre>
<div class="block">Returns a comparator which compares furniture on a given <code>property</code> in ascending order.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomePieceOfFurniture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomePieceOfFurniture.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
