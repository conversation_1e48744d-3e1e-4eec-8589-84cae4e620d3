# ApplicationPlugin_sv.properties
# Sweet Home 3D AI Plugin Configuration - Swedish
# Copyright (c) 2025 Samuel <PERSON>

# Plugin identification
name=AI Planlösningsanalys
description=Analysera planlösningar med artificiell intelligens för att ge insikter och förbättringsförslag
provider=<PERSON>

# Features
features=AI-analys, <PERSON><PERSON><PERSON><PERSON>, Flera AI-leverantörer, Integritetskontroller

# Requirements
requirements=Internetanslutning för moln-AI-leverantörer (valfritt för lokala leverantörer)

# UI Strings for internationalization
# Action properties
AIAction.Name=AI-analys
AIAction.ShortDescription=Analysera planlösning med AI
AIAction.Menu=Verktyg

# Dialog titles
AIChatDialog.title=AI Planlösningsanalys
AISettingsDialog.title=AI-inställningar

# Button labels
button.send=Skicka
button.newAnalysis=Ny Analys
button.settings=Inställningar
button.testConnection=Testa Anslutning
button.save=Spara
button.cancel=Avbryt

# Labels
label.provider=Leverantör:
label.baseUrl=Bas-URL:
label.apiKey=API-nyckel:
label.model=Modell:
label.temperature=Temperatur:
label.maxTokens=Max Tokens:
label.status=Status:

# Messages
message.analyzing=Analyserar planlösning...
message.processingQuestion=Bearbetar fråga...
message.testingConnection=Testar anslutning...
message.connectionSuccessful=Anslutning lyckades!
message.connectionFailed=Anslutning misslyckades: {0}
message.configurationSaved=Konfiguration sparad framgångsrikt
message.validationError=Konfigurationsfel:\n{0}
message.noConfiguration=AI-leverantör inte konfigurerad. Vänligen konfigurera inställningar först.

# Analysis prompt
analysis.prompt=Vänligen analysera denna planlösning och ge omfattande insikter inklusive:\n1. Layouteffektivitet och rymdutnyttjande\n2. Trafikflöde och cirkulationsmönster\n3. Möjligheter för naturligt ljus och ventilation\n4. Tillgänglighetsöverväganden\n5. Funktionella relationer mellan utrymmen\n6. Förbättringsförslag\n7. Överensstämmelse med vanliga byggstandarder\n8. Energieffektivitetsöverväganden\n\nVänligen ge specifika, genomförbara rekommendationer som skulle förbättra funktionaliteten, komforten och den estetiska attraktiviteten hos detta utrymme.

# Error messages
error.analysisError=Analysfel: {0}
error.configurationError=Konfigurationsfel: {0}
error.connectionError=Anslutningsfel: {0}
error.invalidConfiguration=Ogiltig konfiguration
error.missingApiKey=API-nyckel krävs
error.missingBaseUrl=Bas-URL krävs
error.missingModel=Modellval krävs

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Lokal)
provider.lmstudio=LM Studio (Lokal)
provider.anythingllm=AnythingLLM (Lokal)
provider.jan=Jan (Lokal)
provider.custom=Anpassad
