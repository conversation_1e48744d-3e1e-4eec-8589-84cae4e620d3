<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:47 CEST 2024 -->
<title>ControllerAction (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ControllerAction (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ControllerAction.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/Component3DTransferHandler.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ControllerAction.html" target="_top">Frames</a></li>
<li><a href="ControllerAction.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.swing.ResourceAction">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.swing.ResourceAction">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class ControllerAction" class="title">Class ControllerAction</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>javax.swing.AbstractAction</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html" title="class in com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing.ResourceAction</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.ControllerAction</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.awt.event.ActionListener, java.io.Serializable, java.lang.Cloneable, java.util.EventListener, javax.swing.Action</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ControllerAction</span>
extends <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction</a></pre>
<div class="block">An action which <code>actionPerformed</code> method
 will call a parametrizable method.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.ControllerAction">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.swing.ResourceAction">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.eteks.sweethome3d.swing.<a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.ButtonAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.ButtonAction</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.MenuItemAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.MenuItemAction</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.PopupMenuItemAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.PopupMenuItemAction</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.ToolBarAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction.ToolBarAction</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.swing.ResourceAction">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.eteks.sweethome3d.swing.<a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#POPUP">POPUP</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#RESOURCE_CLASS">RESOURCE_CLASS</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#RESOURCE_PREFIX">RESOURCE_PREFIX</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#TOGGLE_BUTTON_MODEL">TOGGLE_BUTTON_MODEL</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#TOOL_BAR_ICON">TOOL_BAR_ICON</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#UNLOCALIZED_NAME">UNLOCALIZED_NAME</a>, <a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#VISIBLE">VISIBLE</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.AbstractAction">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.AbstractAction</h3>
<code>changeSupport, enabled</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.Action">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;javax.swing.Action</h3>
<code>ACCELERATOR_KEY, ACTION_COMMAND_KEY, DEFAULT, DISPLAYED_MNEMONIC_INDEX_KEY, LARGE_ICON_KEY, LONG_DESCRIPTION, MNEMONIC_KEY, NAME, SELECTED_KEY, SHORT_DESCRIPTION, SMALL_ICON</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ControllerAction.html#ControllerAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-boolean-java.lang.Object-java.lang.String-java.lang.Object...-">ControllerAction</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                java.lang.String&nbsp;actionPrefix,
                boolean&nbsp;enabled,
                java.lang.Object&nbsp;controller,
                java.lang.String&nbsp;method,
                java.lang.Object...&nbsp;parameters)</code>
<div class="block">Creates an action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ControllerAction.html#ControllerAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-java.lang.Object-java.lang.String-java.lang.Object...-">ControllerAction</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                java.lang.String&nbsp;actionPrefix,
                java.lang.Object&nbsp;controller,
                java.lang.String&nbsp;method,
                java.lang.Object...&nbsp;parameters)</code>
<div class="block">Creates a disabled action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/ControllerAction.html#actionPerformed-java.awt.event.ActionEvent-">actionPerformed</a></span>(java.awt.event.ActionEvent&nbsp;ev)</code>
<div class="block">Calls the method on controller given in constructor.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.AbstractAction">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.AbstractAction</h3>
<code>addPropertyChangeListener, clone, firePropertyChange, getKeys, getPropertyChangeListeners, getValue, isEnabled, putValue, removePropertyChangeListener, setEnabled</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ControllerAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-java.lang.Object-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ControllerAction</h4>
<pre>public&nbsp;ControllerAction(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                        java.lang.String&nbsp;actionPrefix,
                        java.lang.Object&nbsp;controller,
                        java.lang.String&nbsp;method,
                        java.lang.Object...&nbsp;parameters)
                 throws java.lang.NoSuchMethodException</pre>
<div class="block">Creates a disabled action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferences</code> - user preferences used to retrieve localized description of the action</dd>
<dd><code>resourceClass</code> - the class used as a context to retrieve localized properties of the action</dd>
<dd><code>actionPrefix</code> - prefix used in resource bundle to search action properties</dd>
<dd><code>controller</code> - the controller on which the method will be called</dd>
<dd><code>method</code> - the name of the controller method that will be invoked
          in <a href="../../../../com/eteks/sweethome3d/swing/ControllerAction.html#actionPerformed-java.awt.event.ActionEvent-"><code>actionPerfomed</code></a></dd>
<dd><code>parameters</code> - list of parameters to be used with <code>method</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.NoSuchMethodException</code> - if <code>method</code> with a
           matching <code>parameters</code> list doesn't exist</dd>
</dl>
</li>
</ul>
<a name="ControllerAction-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-boolean-java.lang.Object-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ControllerAction</h4>
<pre>public&nbsp;ControllerAction(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                        java.lang.String&nbsp;actionPrefix,
                        boolean&nbsp;enabled,
                        java.lang.Object&nbsp;controller,
                        java.lang.String&nbsp;method,
                        java.lang.Object...&nbsp;parameters)
                 throws java.lang.NoSuchMethodException</pre>
<div class="block">Creates an action with properties retrieved from a resource bundle
 in which key starts with <code>actionPrefix</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferences</code> - user preferences used to retrieve localized description of the action</dd>
<dd><code>resourceClass</code> - the class used as a context to retrieve localized properties of the action</dd>
<dd><code>actionPrefix</code> - prefix used in resource bundle to search action properties</dd>
<dd><code>enabled</code> - <code>true</code> if the action should be enabled at creation.</dd>
<dd><code>controller</code> - the controller on which the method will be called</dd>
<dd><code>method</code> - the name of the controller method that will be invoked
          in <a href="../../../../com/eteks/sweethome3d/swing/ControllerAction.html#actionPerformed-java.awt.event.ActionEvent-"><code>actionPerfomed</code></a></dd>
<dd><code>parameters</code> - list of parameters to be used with <code>method</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.NoSuchMethodException</code> - if <code>method</code> with a
           matching <code>parameters</code> list doesn't exist</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="actionPerformed-java.awt.event.ActionEvent-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>actionPerformed</h4>
<pre>public&nbsp;void&nbsp;actionPerformed(java.awt.event.ActionEvent&nbsp;ev)</pre>
<div class="block">Calls the method on controller given in constructor.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>actionPerformed</code>&nbsp;in interface&nbsp;<code>java.awt.event.ActionListener</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html#actionPerformed-java.awt.event.ActionEvent-">actionPerformed</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/swing/ResourceAction.html" title="class in com.eteks.sweethome3d.swing">ResourceAction</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ControllerAction.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/Component3DTransferHandler.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/ControllerAction.html" target="_top">Frames</a></li>
<li><a href="ControllerAction.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.swing.ResourceAction">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.swing.ResourceAction">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
