# ApplicationPlugin_ru.properties
# Sweet Home 3D AI Plugin Configuration - Russian
# Copyright (c) 2025 Samuel Kpassegna

# Plugin identification
name=ИИ Анализ Планировок
description=Анализируйте планировки с помощью искусственного интеллекта для получения аналитических данных и предложений по улучшению
provider=Samuel Kpassegna

# Features
features=ИИ Анализ, Аналитика Планировок, Множественные ИИ Провайдеры, Контроль Конфиденциальности

# Requirements
requirements=Интернет-соединение для облачных ИИ провайдеров (опционально для локальных провайдеров)

# UI Strings for internationalization
# Action properties
AIAction.Name=ИИ Анализ
AIAction.ShortDescription=Анализировать планировку с ИИ
AIAction.Menu=Инструменты

# Dialog titles
AIChatDialog.title=ИИ Анализ Планировок
AISettingsDialog.title=Настройки ИИ

# Button labels
button.send=Отправить
button.newAnalysis=Новый Анализ
button.settings=Настройки
button.testConnection=Тест Соединения
button.save=Сохранить
button.cancel=Отмена

# Labels
label.provider=Провайдер:
label.baseUrl=Базовый URL:
label.apiKey=API Ключ:
label.model=Модель:
label.temperature=Температура:
label.maxTokens=Макс Токенов:
label.status=Статус:

# Messages
message.analyzing=Анализ планировки...
message.processingQuestion=Обработка вопроса...
message.testingConnection=Тестирование соединения...
message.connectionSuccessful=Соединение успешно!
message.connectionFailed=Соединение не удалось: {0}
message.configurationSaved=Конфигурация успешно сохранена
message.validationError=Ошибки конфигурации:\n{0}
message.noConfiguration=ИИ провайдер не настроен. Пожалуйста, сначала настройте параметры.

# Analysis prompt
analysis.prompt=Пожалуйста, проанализируйте эту планировку и предоставьте комплексную аналитику, включая:\n1. Эффективность планировки и использование пространства\n2. Потоки движения и схемы циркуляции\n3. Возможности естественного освещения и вентиляции\n4. Соображения доступности\n5. Функциональные связи между пространствами\n6. Предложения по улучшению\n7. Соответствие общим строительным стандартам\n8. Соображения энергоэффективности\n\nПожалуйста, предоставьте конкретные, практические рекомендации, которые улучшили бы функциональность, комфорт и эстетическую привлекательность этого пространства.

# Error messages
error.analysisError=Ошибка анализа: {0}
error.configurationError=Ошибка конфигурации: {0}
error.connectionError=Ошибка соединения: {0}
error.invalidConfiguration=Недействительная конфигурация
error.missingApiKey=Требуется API ключ
error.missingBaseUrl=Требуется базовый URL
error.missingModel=Требуется выбор модели

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Локальный)
provider.lmstudio=LM Studio (Локальный)
provider.anythingllm=AnythingLLM (Локальный)
provider.jan=Jan (Локальный)
provider.custom=Пользовательский
