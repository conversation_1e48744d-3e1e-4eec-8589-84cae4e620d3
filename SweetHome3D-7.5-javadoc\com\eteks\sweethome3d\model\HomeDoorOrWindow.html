<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomeDoorOrWindow (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeDoorOrWindow (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeDoorOrWindow.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeDoorOrWindow.html" target="_top">Frames</a></li>
<li><a href="HomeDoorOrWindow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class HomeDoorOrWindow" class="title">Class HomeDoorOrWindow</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomePieceOfFurniture</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomeDoorOrWindow</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeDoorOrWindow</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>
implements <a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></pre>
<div class="block">A door or a window in <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">home</a>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.7</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.HomeDoorOrWindow">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.Property.html" title="enum in com.eteks.sweethome3d.model">HomeDoorOrWindow.Property</a></span></code>
<div class="block">The properties of a door or window that may change.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#EMPTY_PROPERTY_ARRAY">EMPTY_PROPERTY_ARRAY</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#HomeDoorOrWindow-com.eteks.sweethome3d.model.DoorOrWindow-">HomeDoorOrWindow</a></span>(<a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>&nbsp;doorOrWindow)</code>
<div class="block">Creates a home door or window from an existing one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#HomeDoorOrWindow-com.eteks.sweethome3d.model.DoorOrWindow-java.lang.String:A-">HomeDoorOrWindow</a></span>(<a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>&nbsp;doorOrWindow,
                java.lang.String[]&nbsp;copiedProperties)</code>
<div class="block">Creates a home door or window from an existing one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#HomeDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.DoorOrWindow-">HomeDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                <a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>&nbsp;doorOrWindow)</code>
<div class="block">Creates a home door or window from an existing one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#HomeDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.DoorOrWindow-java.lang.String:A-">HomeDoorOrWindow</a></span>(java.lang.String&nbsp;id,
                <a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>&nbsp;doorOrWindow,
                java.lang.String[]&nbsp;copiedProperties)</code>
<div class="block">Creates a home door or window from an existing one.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#duplicate--">duplicate</a></span>()</code>
<div class="block">Returns a copy of this door or window.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#getCutOutShape--">getCutOutShape</a></span>()</code>
<div class="block">Returns the shape used to cut out walls that intersect this door or window.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#getSashes--">getSashes</a></span>()</code>
<div class="block">Returns a copy of the sashes attached to this door or window.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#getWallDistance--">getWallDistance</a></span>()</code>
<div class="block">Returns the distance between the back side of this door or window and the wall where it's located.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#getWallHeight--">getWallHeight</a></span>()</code>
<div class="block">Returns the height of the wall part in which this door or window should be placed.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#getWallLeft--">getWallLeft</a></span>()</code>
<div class="block">Returns the distance between the left side of this door or window and the wall part where it should be placed.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#getWallThickness--">getWallThickness</a></span>()</code>
<div class="block">Returns the thickness of the wall in which this door or window should be placed.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#getWallTop--">getWallTop</a></span>()</code>
<div class="block">Returns the distance between the left side of this door or window and the wall part where it should be placed.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#getWallWidth--">getWallWidth</a></span>()</code>
<div class="block">Returns the width of the wall part in which this door or window should be placed.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--">isBoundToWall</a></span>()</code>
<div class="block">Returns <code>true</code> if the location and the size of this door or window
 were bound to a wall, last time they were updated.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isDoorOrWindow--">isDoorOrWindow</a></span>()</code>
<div class="block">Returns always <code>true</code>.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isWallCutOutOnBothSides--">isWallCutOutOnBothSides</a></span>()</code>
<div class="block">Returns <code>true</code> if this door or window should cut out the both sides
 of the walls it intersects, even if its front or back side are within the wall thickness.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></span>()</code>
<div class="block">Returns <code>false</code> if the width and depth of this door or window may
 not be changed independently from each other.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setAngle-float-">setAngle</a></span>(float&nbsp;angle)</code>
<div class="block">Sets the angle of this door or window and
 resets its <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>boundToWall</code></a> flag if the angle changed.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setBoundToWall-boolean-">setBoundToWall</a></span>(boolean&nbsp;boundToWall)</code>
<div class="block">Sets whether the location and the size of this door or window
 were bound to a wall, last time they were updated.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setCutOutShape-java.lang.String-">setCutOutShape</a></span>(java.lang.String&nbsp;cutOutShape)</code>
<div class="block">Sets the shape used to cut out walls that intersect this door or window.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setDepth-float-">setDepth</a></span>(float&nbsp;depth)</code>
<div class="block">Sets the depth of this door or window and
 resets its <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>boundToWall</code></a> flag if the depth changed.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setSashes-com.eteks.sweethome3d.model.Sash:A-">setSashes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes)</code>
<div class="block">Sets the sashes attached to this door or window.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setWallCutOutOnBothSides-boolean-">setWallCutOutOnBothSides</a></span>(boolean&nbsp;wallCutOutOnBothSides)</code>
<div class="block">Sets whether the width and depth of the new door or window may
 be changed independently from each other.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setWallDistance-float-">setWallDistance</a></span>(float&nbsp;wallDistance)</code>
<div class="block">Sets the distance between the back side of this door or window and the wall where it's located.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setWallHeight-float-">setWallHeight</a></span>(float&nbsp;wallHeight)</code>
<div class="block">Sets the height of the wall part in which this door or window should be placed.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setWallLeft-float-">setWallLeft</a></span>(float&nbsp;wallLeft)</code>
<div class="block">Sets the distance between the left side of this door or window and the wall part where it should be placed.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setWallThickness-float-">setWallThickness</a></span>(float&nbsp;wallThickness)</code>
<div class="block">Sets the thickness of the wall in which this door or window should be placed.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setWallTop-float-">setWallTop</a></span>(float&nbsp;wallTop)</code>
<div class="block">Sets the distance between the top side of this door or window and the wall part where it should be placed.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setWallWidth-float-">setWallWidth</a></span>(float&nbsp;wallWidth)</code>
<div class="block">Sets the width of the wall part in which this door or window should be placed.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setWidthDepthDeformable-boolean-">setWidthDepthDeformable</a></span>(boolean&nbsp;widthDepthDeformable)</code>
<div class="block">Sets whether the width and depth of the new door or window may
 be changed independently from each other.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setX-float-">setX</a></span>(float&nbsp;x)</code>
<div class="block">Sets the abscissa of this door or window and
 resets its <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>boundToWall</code></a> flag if the abscissa changed.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#setY-float-">setY</a></span>(float&nbsp;y)</code>
<div class="block">Sets the ordinate of this door or window and
 resets its <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>boundToWall</code></a> flag if the ordinate changed.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#clone--">clone</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#containsPoint-float-float-float-">containsPoint</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getAngle--">getAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCatalogId--">getCatalogId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getColor--">getColor</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCreator--">getCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDepth--">getDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDepthInPlan--">getDepthInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getFurnitureComparator-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">getFurnitureComparator</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getGroundElevation--">getGroundElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getHeight--">getHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getHeightInPlan--">getHeightInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getIcon--">getIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getInformation--">getInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getLevel--">getLevel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModel--">getModel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelFlags--">getModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelMaterials--">getModelMaterials</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelRotation--">getModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelSize--">getModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getModelTransformations--">getModelTransformations</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameAngle--">getNameAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameStyle--">getNameStyle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameXOffset--">getNameXOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getNameYOffset--">getNameYOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPitch--">getPitch</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPlanIcon--">getPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPoints--">getPoints</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPrice--">getPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getPriceValueAddedTaxIncluded--">getPriceValueAddedTaxIncluded</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getRoll--">getRoll</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getShininess--">getShininess</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getTexture--">getTexture</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getValueAddedTax--">getValueAddedTax</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getWidth--">getWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getWidthInPlan--">getWidthInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getX--">getX</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getY--">getY</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBottomLeftPointAt-float-float-float-">isBottomLeftPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isBottomRightPointAt-float-float-float-">isBottomRightPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isDeformable--">isDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isHorizontallyRotated--">isHorizontallyRotated</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isModelCenteredAtOrigin--">isModelCenteredAtOrigin</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isModelMirrored--">isModelMirrored</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isMovable--">isMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isNameCenterPointAt-float-float-float-">isNameCenterPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isNameVisible--">isNameVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isParallelToWall-com.eteks.sweethome3d.model.Wall-">isParallelToWall</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isPointAt-float-float-float-">isPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTexturable--">isTexturable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTopLeftPointAt-float-float-float-">isTopLeftPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isTopRightPointAt-float-float-float-">isTopRightPointAt</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isVisible--">isVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#move-float-float-">move</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#scale-float-">scale</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setBackFaceShown-boolean-">setBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCatalogId-java.lang.String-">setCatalogId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setColor-java.lang.Integer-">setColor</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCreator-java.lang.String-">setCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setCurrency-java.lang.String-">setCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDepthInPlan-float-">setDepthInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDescription-java.lang.String-">setDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setElevation-float-">setElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setHeight-float-">setHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setHeightInPlan-float-">setHeightInPlan</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setIcon-com.eteks.sweethome3d.model.Content-">setIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setInformation-java.lang.String-">setInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setLicense-java.lang.String-">setLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelCenteredAtOrigin-boolean-">setModelCenteredAtOrigin</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelFlags-int-">setModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelMaterials-com.eteks.sweethome3d.model.HomeMaterial:A-">setModelMaterials</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelMirrored-boolean-">setModelMirrored</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelRotation-float:A:A-">setModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelSize-java.lang.Long-">setModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setModelTransformations-com.eteks.sweethome3d.model.Transformation:A-">setModelTransformations</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setMovable-boolean-">setMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setName-java.lang.String-">setName</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameAngle-float-">setNameAngle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameStyle-com.eteks.sweethome3d.model.TextStyle-">setNameStyle</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameVisible-boolean-">setNameVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameXOffset-float-">setNameXOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setNameYOffset-float-">setNameYOffset</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPitch-float-">setPitch</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPlanIcon-com.eteks.sweethome3d.model.Content-">setPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setPrice-java.math.BigDecimal-">setPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setRoll-float-">setRoll</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setShininess-java.lang.Float-">setShininess</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setStaircaseCutOutShape-java.lang.String-">setStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setTexture-com.eteks.sweethome3d.model.HomeTexture-">setTexture</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setValueAddedTaxPercentage-java.math.BigDecimal-">setValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setVisible-boolean-">setVisible</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setWidth-float-">setWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setWidthInPlan-float-">setWidthInPlan</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeDoorOrWindow-com.eteks.sweethome3d.model.DoorOrWindow-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeDoorOrWindow</h4>
<pre>public&nbsp;HomeDoorOrWindow(<a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>&nbsp;doorOrWindow)</pre>
<div class="block">Creates a home door or window from an existing one.
 No additional properties will be copied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>doorOrWindow</code> - the door or window from which data are copied</dd>
</dl>
</li>
</ul>
<a name="HomeDoorOrWindow-com.eteks.sweethome3d.model.DoorOrWindow-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeDoorOrWindow</h4>
<pre>public&nbsp;HomeDoorOrWindow(<a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>&nbsp;doorOrWindow,
                        java.lang.String[]&nbsp;copiedProperties)</pre>
<div class="block">Creates a home door or window from an existing one.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>doorOrWindow</code> - the door or window from which data are copied</dd>
<dd><code>copiedProperties</code> - the names of the additional properties which should be copied from the existing piece
                         or <code>null</code> if all properties should be copied.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="HomeDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.DoorOrWindow-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeDoorOrWindow</h4>
<pre>public&nbsp;HomeDoorOrWindow(java.lang.String&nbsp;id,
                        <a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>&nbsp;doorOrWindow)</pre>
<div class="block">Creates a home door or window from an existing one.
 No additional properties will be copied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the ID of the object</dd>
<dd><code>doorOrWindow</code> - the door or window from which data are copied</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="HomeDoorOrWindow-java.lang.String-com.eteks.sweethome3d.model.DoorOrWindow-java.lang.String:A-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeDoorOrWindow</h4>
<pre>public&nbsp;HomeDoorOrWindow(java.lang.String&nbsp;id,
                        <a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a>&nbsp;doorOrWindow,
                        java.lang.String[]&nbsp;copiedProperties)</pre>
<div class="block">Creates a home door or window from an existing one.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>id</code> - the ID of the object</dd>
<dd><code>doorOrWindow</code> - the door or window from which data are copied</dd>
<dd><code>copiedProperties</code> - the names of the additional properties which should be copied from the existing piece
                         or <code>null</code> if all properties should be copied.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getWallThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallThickness</h4>
<pre>public&nbsp;float&nbsp;getWallThickness()</pre>
<div class="block">Returns the thickness of the wall in which this door or window should be placed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getWallThickness--">getWallThickness</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value in percentage of the depth of the door or the window.</dd>
</dl>
</li>
</ul>
<a name="setWallThickness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallThickness</h4>
<pre>public&nbsp;void&nbsp;setWallThickness(float&nbsp;wallThickness)</pre>
<div class="block">Sets the thickness of the wall in which this door or window should be placed.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallThickness</code> - a value in percentage of the depth of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getWallDistance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallDistance</h4>
<pre>public&nbsp;float&nbsp;getWallDistance()</pre>
<div class="block">Returns the distance between the back side of this door or window and the wall where it's located.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getWallDistance--">getWallDistance</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a distance in percentage of the depth of the door or the window.</dd>
</dl>
</li>
</ul>
<a name="setWallDistance-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallDistance</h4>
<pre>public&nbsp;void&nbsp;setWallDistance(float&nbsp;wallDistance)</pre>
<div class="block">Sets the distance between the back side of this door or window and the wall where it's located.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallDistance</code> - a distance in percentage of the depth of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getWallWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallWidth</h4>
<pre>public&nbsp;float&nbsp;getWallWidth()</pre>
<div class="block">Returns the width of the wall part in which this door or window should be placed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value in percentage of the width of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setWallWidth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallWidth</h4>
<pre>public&nbsp;void&nbsp;setWallWidth(float&nbsp;wallWidth)</pre>
<div class="block">Sets the width of the wall part in which this door or window should be placed.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallWidth</code> - a value in percentage of the width of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getWallLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallLeft</h4>
<pre>public&nbsp;float&nbsp;getWallLeft()</pre>
<div class="block">Returns the distance between the left side of this door or window and the wall part where it should be placed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a distance in percentage of the width of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setWallLeft-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallLeft</h4>
<pre>public&nbsp;void&nbsp;setWallLeft(float&nbsp;wallLeft)</pre>
<div class="block">Sets the distance between the left side of this door or window and the wall part where it should be placed.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallLeft</code> - a distance in percentage of the width of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getWallHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallHeight</h4>
<pre>public&nbsp;float&nbsp;getWallHeight()</pre>
<div class="block">Returns the height of the wall part in which this door or window should be placed.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a value in percentage of the height of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setWallHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallHeight</h4>
<pre>public&nbsp;void&nbsp;setWallHeight(float&nbsp;wallHeight)</pre>
<div class="block">Sets the height of the wall part in which this door or window should be placed.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallHeight</code> - a value in percentage of the height of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getWallTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallTop</h4>
<pre>public&nbsp;float&nbsp;getWallTop()</pre>
<div class="block">Returns the distance between the left side of this door or window and the wall part where it should be placed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a distance in percentage of the height of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setWallTop-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallTop</h4>
<pre>public&nbsp;void&nbsp;setWallTop(float&nbsp;wallTop)</pre>
<div class="block">Sets the distance between the top side of this door or window and the wall part where it should be placed.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>wallTop</code> - a distance in percentage of the height of the door or the window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getSashes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSashes</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;getSashes()</pre>
<div class="block">Returns a copy of the sashes attached to this door or window.
 If no sash is defined an empty array is returned.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getSashes--">getSashes</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
</dl>
</li>
</ul>
<a name="setSashes-com.eteks.sweethome3d.model.Sash:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSashes</h4>
<pre>public&nbsp;void&nbsp;setSashes(<a href="../../../../com/eteks/sweethome3d/model/Sash.html" title="class in com.eteks.sweethome3d.model">Sash</a>[]&nbsp;sashes)</pre>
<div class="block">Sets the sashes attached to this door or window. Once this piece is updated,
 listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sashes</code> - sashes of this window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getCutOutShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCutOutShape</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCutOutShape()</pre>
<div class="block">Returns the shape used to cut out walls that intersect this door or window.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#getCutOutShape--">getCutOutShape</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>null</code> or a shape defined with the syntax of the d attribute of a 
 <a href="http://www.w3.org/TR/SVG/paths.html">SVG path element</a>
 that fits in a square spreading from (0, 0) to (1, 1) which will be 
 scaled afterwards to the real size of this door or window.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="setCutOutShape-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCutOutShape</h4>
<pre>public&nbsp;void&nbsp;setCutOutShape(java.lang.String&nbsp;cutOutShape)</pre>
<div class="block">Sets the shape used to cut out walls that intersect this door or window.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>cutOutShape</code> - a SVG path element.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="isWallCutOutOnBothSides--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWallCutOutOnBothSides</h4>
<pre>public&nbsp;boolean&nbsp;isWallCutOutOnBothSides()</pre>
<div class="block">Returns <code>true</code> if this door or window should cut out the both sides
 of the walls it intersects, even if its front or back side are within the wall thickness.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#isWallCutOutOnBothSides--">isWallCutOutOnBothSides</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setWallCutOutOnBothSides-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallCutOutOnBothSides</h4>
<pre>public&nbsp;void&nbsp;setWallCutOutOnBothSides(boolean&nbsp;wallCutOutOnBothSides)</pre>
<div class="block">Sets whether the width and depth of the new door or window may
 be changed independently from each other.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="isWidthDepthDeformable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWidthDepthDeformable</h4>
<pre>public&nbsp;boolean&nbsp;isWidthDepthDeformable()</pre>
<div class="block">Returns <code>false</code> if the width and depth of this door or window may
 not be changed independently from each other. When <code>false</code>, this door or window
 will also make a hole in the wall when it's placed whatever its depth if its
 <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>bouldToWall</code></a> flag is <code>true</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/DoorOrWindow.html" title="interface in com.eteks.sweethome3d.model">DoorOrWindow</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setWidthDepthDeformable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidthDepthDeformable</h4>
<pre>public&nbsp;void&nbsp;setWidthDepthDeformable(boolean&nbsp;widthDepthDeformable)</pre>
<div class="block">Sets whether the width and depth of the new door or window may
 be changed independently from each other.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="isBoundToWall--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBoundToWall</h4>
<pre>public&nbsp;boolean&nbsp;isBoundToWall()</pre>
<div class="block">Returns <code>true</code> if the location and the size of this door or window
 were bound to a wall, last time they were updated.</div>
</li>
</ul>
<a name="setBoundToWall-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBoundToWall</h4>
<pre>public&nbsp;void&nbsp;setBoundToWall(boolean&nbsp;boundToWall)</pre>
<div class="block">Sets whether the location and the size of this door or window
 were bound to a wall, last time they were updated.
 Once this piece is updated, listeners added to this piece will receive a change notification.</div>
</li>
</ul>
<a name="setX-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public&nbsp;void&nbsp;setX(float&nbsp;x)</pre>
<div class="block">Sets the abscissa of this door or window and
 resets its <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>boundToWall</code></a> flag if the abscissa changed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setX-float-">setX</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setY-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public&nbsp;void&nbsp;setY(float&nbsp;y)</pre>
<div class="block">Sets the ordinate of this door or window and
 resets its <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>boundToWall</code></a> flag if the ordinate changed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setY-float-">setY</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setAngle-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngle</h4>
<pre>public&nbsp;void&nbsp;setAngle(float&nbsp;angle)</pre>
<div class="block">Sets the angle of this door or window and
 resets its <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>boundToWall</code></a> flag if the angle changed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setAngle-float-">setAngle</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="setDepth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepth</h4>
<pre>public&nbsp;void&nbsp;setDepth(float&nbsp;depth)</pre>
<div class="block">Sets the depth of this door or window and
 resets its <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>boundToWall</code></a> flag if the depth changed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setDepth-float-">setDepth</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="isDoorOrWindow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDoorOrWindow</h4>
<pre>public&nbsp;boolean&nbsp;isDoorOrWindow()</pre>
<div class="block">Returns always <code>true</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></dd>
</dl>
</li>
</ul>
<a name="duplicate--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>duplicate</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>&nbsp;duplicate()</pre>
<div class="block">Returns a copy of this door or window.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeDoorOrWindow.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeDescriptor.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeDoorOrWindow.html" target="_top">Frames</a></li>
<li><a href="HomeDoorOrWindow.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.com.eteks.sweethome3d.model.HomePieceOfFurniture">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
