<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.model.LightSource (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.model.LightSource (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/LightSource.html" target="_top">Frames</a></li>
<li><a href="LightSource.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.model.LightSource" class="title">Uses of Class<br>com.eteks.sweethome3d.model.LightSource</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">CatalogLight.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#getLightSources--">getLightSources</a></span>()</code>
<div class="block">Returns the sources managed by this light.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">Light.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Light.html#getLightSources--">getLightSources</a></span>()</code>
<div class="block">Returns the sources managed by this light.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">HomeLight.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeLight.html#getLightSources--">getLightSources</a></span>()</code>
<div class="block">Returns the sources managed by this light.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeLight.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeLight.html#setLightSources-com.eteks.sweethome3d.model.LightSource:A-">setLightSources</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources)</code>
<div class="block">Sets the sources managed by this light.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-float:A:A-java.lang.String-boolean-java.math.BigDecimal-java.math.BigDecimal-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String:A-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String[]&nbsp;lightSourceMaterialNames,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            int&nbsp;modelFlags,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            boolean&nbsp;backFaceShown,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-boolean-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            boolean&nbsp;backFaceShown,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-boolean-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            boolean&nbsp;backFaceShown,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String-float:A:A-java.lang.String-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/CatalogLight.html#CatalogLight-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String:A-java.lang.Long-java.lang.Float-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.model.Content-float-float-float-float-float-boolean-com.eteks.sweethome3d.model.LightSource:A-java.lang.String:A-java.lang.String-float:A:A-int-java.lang.Long-java.lang.String-boolean-boolean-boolean-boolean-java.math.BigDecimal-java.math.BigDecimal-java.lang.String-java.util.Map-java.util.Map-">CatalogLight</a></span>(java.lang.String&nbsp;id,
            java.lang.String&nbsp;name,
            java.lang.String&nbsp;description,
            java.lang.String&nbsp;information,
            java.lang.String&nbsp;license,
            java.lang.String[]&nbsp;tags,
            java.lang.Long&nbsp;creationDate,
            java.lang.Float&nbsp;grade,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;icon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;planIcon,
            <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model,
            float&nbsp;width,
            float&nbsp;depth,
            float&nbsp;height,
            float&nbsp;elevation,
            float&nbsp;dropOnTopElevation,
            boolean&nbsp;movable,
            <a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">LightSource</a>[]&nbsp;lightSources,
            java.lang.String[]&nbsp;lightSourceMaterialNames,
            java.lang.String&nbsp;staircaseCutOutShape,
            float[][]&nbsp;modelRotation,
            int&nbsp;modelFlags,
            java.lang.Long&nbsp;modelSize,
            java.lang.String&nbsp;creator,
            boolean&nbsp;resizable,
            boolean&nbsp;deformable,
            boolean&nbsp;texturable,
            boolean&nbsp;horizontallyRotatable,
            java.math.BigDecimal&nbsp;price,
            java.math.BigDecimal&nbsp;valueAddedTaxPercentage,
            java.lang.String&nbsp;currency,
            java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;properties,
            java.util.Map&lt;java.lang.String,<a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;contents)</code>
<div class="block">Creates an unmodifiable catalog light of the default catalog.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/LightSource.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/LightSource.html" target="_top">Frames</a></li>
<li><a href="LightSource.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
