<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>HomeTexture (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeTexture (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeTexture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/IllegalHomonymException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeTexture.html" target="_top">Frames</a></li>
<li><a href="HomeTexture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class HomeTexture" class="title">Class HomeTexture</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.HomeTexture</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>, java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeTexture</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>, java.io.Serializable</pre>
<div class="block">An image used as texture on home 3D objects.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.HomeTexture">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-">HomeTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture)</code>
<div class="block">Creates a home texture from an existing one.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-">HomeTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;angle)</code>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-boolean-">HomeTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;angle,
           boolean&nbsp;leftToRightOriented)</code>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-boolean-">HomeTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;angle,
           float&nbsp;scale,
           boolean&nbsp;leftToRightOriented)</code>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-float-float-boolean-">HomeTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;xOffset,
           float&nbsp;yOffset,
           float&nbsp;angle,
           float&nbsp;scale,
           boolean&nbsp;leftToRightOriented)</code>
<div class="block">Creates a home texture from an existing one with customized parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-float-float-boolean-boolean-">HomeTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
           float&nbsp;xOffset,
           float&nbsp;yOffset,
           float&nbsp;angle,
           float&nbsp;scale,
           boolean&nbsp;fittingArea,
           boolean&nbsp;leftToRightOriented)</code>
<div class="block">Creates a home texture from an existing one with customized parameters.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#equals-java.lang.Object-">equals</a></span>(java.lang.Object&nbsp;obj)</code>
<div class="block">Returns <code>true</code> if the object in parameter is equal to this texture.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getAngle--">getAngle</a></span>()</code>
<div class="block">Returns the angle of rotation in radians applied to this texture.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getCatalogId--">getCatalogId</a></span>()</code>
<div class="block">Returns the catalog ID of this texture or <code>null</code> if it doesn't exist.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getCreator--">getCreator</a></span>()</code>
<div class="block">Returns the creator of this texture.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height of the image in centimeters.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getImage--">getImage</a></span>()</code>
<div class="block">Returns the content of the image used for this texture.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this texture.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getScale--">getScale</a></span>()</code>
<div class="block">Returns the scale applied to this texture.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the width of the image in centimeters.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getXOffset--">getXOffset</a></span>()</code>
<div class="block">Returns the offset applied to the texture along X axis in percentage of its width.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#getYOffset--">getYOffset</a></span>()</code>
<div class="block">Returns the offset applied to the texture along Y axis in percentage of its height.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#hashCode--">hashCode</a></span>()</code>
<div class="block">Returns a hash code for this texture.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#isFittingArea--">isFittingArea</a></span>()</code>
<div class="block">Returns <code>true</code> the texture should fit the area to which it's applied.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#isLeftToRightOriented--">isLeftToRightOriented</a></span>()</code>
<div class="block">Returns <code>true</code> if the objects using this texture should take into account
 the orientation of the texture.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html#setFittingArea-boolean-">setFittingArea</a></span>(boolean&nbsp;fittingArea)</code>
<div class="block">Sets whether the texture should fit the area to which it's applied.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeTexture-com.eteks.sweethome3d.model.TextureImage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeTexture</h4>
<pre>public&nbsp;HomeTexture(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture)</pre>
<div class="block">Creates a home texture from an existing one.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - the texture from which data are copied</dd>
</dl>
</li>
</ul>
<a name="HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeTexture</h4>
<pre>public&nbsp;HomeTexture(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
                   float&nbsp;angle)</pre>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - the texture from which data are copied</dd>
<dd><code>angle</code> - the rotation angle applied to the texture</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeTexture</h4>
<pre>public&nbsp;HomeTexture(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
                   float&nbsp;angle,
                   boolean&nbsp;leftToRightOriented)</pre>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - the texture from which data are copied</dd>
<dd><code>angle</code> - the rotation angle applied to the texture</dd>
<dd><code>leftToRightOriented</code> - orientation used on the texture when applied on objects seen from front</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.3</dd>
</dl>
</li>
</ul>
<a name="HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeTexture</h4>
<pre>public&nbsp;HomeTexture(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
                   float&nbsp;angle,
                   float&nbsp;scale,
                   boolean&nbsp;leftToRightOriented)</pre>
<div class="block">Creates a home texture from an existing one with customized angle and offset.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - the texture from which data are copied</dd>
<dd><code>angle</code> - the rotation angle applied to the texture</dd>
<dd><code>scale</code> - the scale applied to the texture</dd>
<dd><code>leftToRightOriented</code> - orientation used on the texture when applied on objects seen from front</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeTexture</h4>
<pre>public&nbsp;HomeTexture(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
                   float&nbsp;xOffset,
                   float&nbsp;yOffset,
                   float&nbsp;angle,
                   float&nbsp;scale,
                   boolean&nbsp;leftToRightOriented)</pre>
<div class="block">Creates a home texture from an existing one with customized parameters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - the texture from which data are copied</dd>
<dd><code>xOffset</code> - the offset applied to the texture along X axis in percentage of its width</dd>
<dd><code>yOffset</code> - the offset applied to the texture along Y axis in percentage of its height</dd>
<dd><code>angle</code> - the rotation angle applied to the texture</dd>
<dd><code>scale</code> - the scale applied to the texture</dd>
<dd><code>leftToRightOriented</code> - orientation used on the texture when applied on objects seen from front</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="HomeTexture-com.eteks.sweethome3d.model.TextureImage-float-float-float-float-boolean-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeTexture</h4>
<pre>public&nbsp;HomeTexture(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;texture,
                   float&nbsp;xOffset,
                   float&nbsp;yOffset,
                   float&nbsp;angle,
                   float&nbsp;scale,
                   boolean&nbsp;fittingArea,
                   boolean&nbsp;leftToRightOriented)</pre>
<div class="block">Creates a home texture from an existing one with customized parameters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - the texture from which data are copied</dd>
<dd><code>xOffset</code> - the offset applied to the texture along X axis in percentage of its width</dd>
<dd><code>yOffset</code> - the offset applied to the texture along Y axis in percentage of its height</dd>
<dd><code>angle</code> - the rotation angle applied to the texture</dd>
<dd><code>scale</code> - the scale applied to the texture</dd>
<dd><code>fittingArea</code> - if <code>true</code> the texture will fit at its best the area to which it's applied</dd>
<dd><code>leftToRightOriented</code> - orientation used on the texture when applied on objects seen from front</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCatalogId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCatalogId</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCatalogId()</pre>
<div class="block">Returns the catalog ID of this texture or <code>null</code> if it doesn't exist.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this texture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></dd>
</dl>
</li>
</ul>
<a name="getCreator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreator</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCreator()</pre>
<div class="block">Returns the creator of this texture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html#getCreator--">getCreator</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getImage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImage</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getImage()</pre>
<div class="block">Returns the content of the image used for this texture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html#getImage--">getImage</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;float&nbsp;getWidth()</pre>
<div class="block">Returns the width of the image in centimeters.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html#getWidth--">getWidth</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></dd>
</dl>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;float&nbsp;getHeight()</pre>
<div class="block">Returns the height of the image in centimeters.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html#getHeight--">getHeight</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a></code></dd>
</dl>
</li>
</ul>
<a name="getXOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getXOffset</h4>
<pre>public&nbsp;float&nbsp;getXOffset()</pre>
<div class="block">Returns the offset applied to the texture along X axis in percentage of its width.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getYOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYOffset</h4>
<pre>public&nbsp;float&nbsp;getYOffset()</pre>
<div class="block">Returns the offset applied to the texture along Y axis in percentage of its height.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getAngle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAngle</h4>
<pre>public&nbsp;float&nbsp;getAngle()</pre>
<div class="block">Returns the angle of rotation in radians applied to this texture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScale</h4>
<pre>public&nbsp;float&nbsp;getScale()</pre>
<div class="block">Returns the scale applied to this texture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="isFittingArea--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFittingArea</h4>
<pre>public&nbsp;boolean&nbsp;isFittingArea()</pre>
<div class="block">Returns <code>true</code> the texture should fit the area to which it's applied.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setFittingArea-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFittingArea</h4>
<pre>public&nbsp;void&nbsp;setFittingArea(boolean&nbsp;fittingArea)</pre>
<div class="block">Sets whether the texture should fit the area to which it's applied.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="isLeftToRightOriented--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLeftToRightOriented</h4>
<pre>public&nbsp;boolean&nbsp;isLeftToRightOriented()</pre>
<div class="block">Returns <code>true</code> if the objects using this texture should take into account
 the orientation of the texture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.4</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(java.lang.Object&nbsp;obj)</pre>
<div class="block">Returns <code>true</code> if the object in parameter is equal to this texture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>equals</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block">Returns a hash code for this texture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>hashCode</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeTexture.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.Property.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/IllegalHomonymException.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/HomeTexture.html" target="_top">Frames</a></li>
<li><a href="HomeTexture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
