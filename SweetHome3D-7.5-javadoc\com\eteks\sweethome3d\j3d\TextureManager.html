<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>TextureManager (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextureManager (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TextureManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/ShapeTools.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/TextureManager.html" target="_top">Frames</a></li>
<li><a href="TextureManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class TextureManager" class="title">Class TextureManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.TextureManager</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TextureManager</span>
extends java.lang.Object</pre>
<div class="block">Singleton managing texture image cache.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static interface&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a></span></code>
<div class="block">An observer that receives texture loading notifications.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#clear--">clear</a></span>()</code>
<div class="block">Shutdowns the multithreaded service that load textures.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html" title="class in com.eteks.sweethome3d.j3d">TextureManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#getInstance--">getInstance</a></span>()</code>
<div class="block">Returns an instance of this singleton.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#getRotatedTextureHeight-com.eteks.sweethome3d.model.HomeTexture-">getRotatedTextureHeight</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Returns the height of the given texture once its rotation angle is applied.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#getRotatedTextureWidth-com.eteks.sweethome3d.model.HomeTexture-">getRotatedTextureWidth</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Returns the width of the given texture once its rotation angle is applied.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#isTextureTransparent-javax.media.j3d.Texture-">isTextureTransparent</a></span>(javax.media.j3d.Texture&nbsp;texture)</code>
<div class="block">Returns <code>true</code> if the texture is shared and its image contains 
 at least one transparent pixel.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>javax.media.j3d.Texture</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#loadTexture-com.eteks.sweethome3d.model.Content-">loadTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</code>
<div class="block">Returns a texture created from the image from <code>content</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#loadTexture-com.eteks.sweethome3d.model.Content-boolean-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">loadTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
           boolean&nbsp;synchronous,
           <a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</code>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#loadTexture-com.eteks.sweethome3d.model.Content-float-boolean-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">loadTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
           float&nbsp;angle,
           boolean&nbsp;synchronous,
           <a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</code>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#loadTexture-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">loadTexture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
           <a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</code>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>
 If the texture isn't loaded in cache yet, a one pixel white image texture will be notified 
 immediately to the given <code>textureObserver</code>, then a second notification will 
 be given in Event Dispatch Thread once the image texture is loaded.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>javax.media.j3d.Texture</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html#shareTexture-javax.media.j3d.Texture-">shareTexture</a></span>(javax.media.j3d.Texture&nbsp;texture)</code>
<div class="block">Returns either the <code>texture</code> in parameter or a shared texture 
 if the same texture as the one in parameter is already shared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getInstance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInstance</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.html" title="class in com.eteks.sweethome3d.j3d">TextureManager</a>&nbsp;getInstance()</pre>
<div class="block">Returns an instance of this singleton.</div>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public&nbsp;void&nbsp;clear()</pre>
<div class="block">Shutdowns the multithreaded service that load textures.</div>
</li>
</ul>
<a name="loadTexture-com.eteks.sweethome3d.model.Content-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadTexture</h4>
<pre>public&nbsp;void&nbsp;loadTexture(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                        <a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</pre>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>
 If the texture isn't loaded in cache yet, a one pixel white image texture will be notified 
 immediately to the given <code>textureObserver</code>, then a second notification will 
 be given in Event Dispatch Thread once the image texture is loaded. If the texture is in cache, 
 it will be notified immediately to the given <code>textureObserver</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - an object containing an image</dd>
<dd><code>textureObserver</code> - the observer that will be notified once the texture is available</dd>
</dl>
</li>
</ul>
<a name="loadTexture-com.eteks.sweethome3d.model.Content-boolean-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadTexture</h4>
<pre>public&nbsp;void&nbsp;loadTexture(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                        boolean&nbsp;synchronous,
                        <a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</pre>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>. 
 If the texture isn't loaded in cache yet and <code>synchronous</code> is false, a one pixel 
 white image texture will be notified immediately to the given <code>textureObserver</code>, 
 then a second notification will be given in Event Dispatch Thread once the image texture is loaded. 
 If the texture is in cache, it will be notified immediately to the given <code>textureObserver</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - an object containing an image</dd>
<dd><code>synchronous</code> - if <code>true</code>, this method will return only once image content is loaded.</dd>
<dd><code>textureObserver</code> - the observer that will be notified once the texture is available</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if synchronous is <code>false</code> and the current thread isn't 
    the Event Dispatch Thread.</dd>
</dl>
</li>
</ul>
<a name="loadTexture-com.eteks.sweethome3d.model.Content-float-boolean-com.eteks.sweethome3d.j3d.TextureManager.TextureObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadTexture</h4>
<pre>public&nbsp;void&nbsp;loadTexture(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content,
                        float&nbsp;angle,
                        boolean&nbsp;synchronous,
                        <a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d">TextureManager.TextureObserver</a>&nbsp;textureObserver)</pre>
<div class="block">Reads a texture image from <code>content</code> notified to <code>textureObserver</code>. 
 If the texture isn't loaded in cache yet and <code>synchronous</code> is false, a one pixel 
 white image texture will be notified immediately to the given <code>textureObserver</code>, 
 then a second notification will be given in Event Dispatch Thread once the image texture is loaded. 
 If the texture is in cache, it will be notified immediately to the given <code>textureObserver</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>content</code> - an object containing an image</dd>
<dd><code>angle</code> - the rotation angle applied to the image</dd>
<dd><code>synchronous</code> - if <code>true</code>, this method will return only once image content is loaded.</dd>
<dd><code>textureObserver</code> - the observer that will be notified once the texture is available</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if synchronous is <code>false</code> and the current thread isn't 
    the Event Dispatch Thread.</dd>
</dl>
</li>
</ul>
<a name="loadTexture-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>loadTexture</h4>
<pre>public&nbsp;javax.media.j3d.Texture&nbsp;loadTexture(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;content)</pre>
<div class="block">Returns a texture created from the image from <code>content</code>.</div>
</li>
</ul>
<a name="shareTexture-javax.media.j3d.Texture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>shareTexture</h4>
<pre>public&nbsp;javax.media.j3d.Texture&nbsp;shareTexture(javax.media.j3d.Texture&nbsp;texture)</pre>
<div class="block">Returns either the <code>texture</code> in parameter or a shared texture 
 if the same texture as the one in parameter is already shared.</div>
</li>
</ul>
<a name="isTextureTransparent-javax.media.j3d.Texture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTextureTransparent</h4>
<pre>public&nbsp;boolean&nbsp;isTextureTransparent(javax.media.j3d.Texture&nbsp;texture)</pre>
<div class="block">Returns <code>true</code> if the texture is shared and its image contains 
 at least one transparent pixel.</div>
</li>
</ul>
<a name="getRotatedTextureWidth-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRotatedTextureWidth</h4>
<pre>public&nbsp;float&nbsp;getRotatedTextureWidth(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</pre>
<div class="block">Returns the width of the given texture once its rotation angle is applied.</div>
</li>
</ul>
<a name="getRotatedTextureHeight-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getRotatedTextureHeight</h4>
<pre>public&nbsp;float&nbsp;getRotatedTextureHeight(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</pre>
<div class="block">Returns the height of the given texture once its rotation angle is applied.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TextureManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/ShapeTools.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/TextureManager.TextureObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/TextureManager.html" target="_top">Frames</a></li>
<li><a href="TextureManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
