<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.SortableProperty.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture.SortableProperty.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty" class="title">Uses of Class<br>com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedProperty--">getFurnitureSortedProperty</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedProperty--"><code>Home.getFurnitureSortedProperty()</code></a> and <a href="../../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>Home.setFurnitureSortedProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedPropertyName--"><code>Home.getFurnitureSortedPropertyName()</code></a> and <a href="../../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedPropertyName-java.lang.String-"><code>Home.setFurnitureSortedPropertyName(String)</code></a>
     to allow sorting on additional properties.</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.SortableProperty.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>[]</code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.SortableProperty.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return types with arguments of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisibleProperties--">getFurnitureVisibleProperties</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisibleProperties--"><code>Home.getFurnitureVisibleProperties()</code></a> and <code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisiblePropertyNames--"><code>Home.getFurnitureVisiblePropertyNames()</code></a> and <code>#setFurnitureSortedPropertyName(List<String>)</code>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static java.util.Comparator&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getFurnitureComparator-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">getFurnitureComparator</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;property)</code>
<div class="block">Returns a comparator which compares furniture on a given <code>property</code> in ascending order.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">setFurnitureSortedProperty</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureSortedProperty)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedProperty--"><code>Home.getFurnitureSortedProperty()</code></a> and <a href="../../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>Home.setFurnitureSortedProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureSortedPropertyName--"><code>Home.getFurnitureSortedPropertyName()</code></a> and <a href="../../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureSortedPropertyName-java.lang.String-"><code>Home.setFurnitureSortedPropertyName(String)</code></a>
     to allow sorting on additional properties.</span></div>
</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Home.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#setFurnitureVisibleProperties-java.util.List-">setFurnitureVisibleProperties</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;&nbsp;furnitureVisibleProperties)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisibleProperties--"><code>Home.getFurnitureVisibleProperties()</code></a> and <code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <a href="../../../../../com/eteks/sweethome3d/model/Home.html#getFurnitureVisiblePropertyNames--"><code>Home.getFurnitureVisiblePropertyNames()</code></a> and <code>#setFurnitureSortedPropertyName(List<String>)</code>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">sortFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>FurnitureController.sortFurniture(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-java.lang.String-"><code>FurnitureController.sortFurniture(String)</code></a>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">toggleFurnitureSort</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>FurnitureController.toggleFurnitureSort(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-java.lang.String-"><code>FurnitureController.toggleFurnitureSort(String)</code></a>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">toggleFurnitureVisibleProperty</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>FurnitureController.toggleFurnitureVisibleProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-java.lang.String-"><code>FurnitureController.toggleFurnitureVisibleProperty(String)</code></a>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with type arguments of type <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FurnitureController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setFurnitureVisibleProperties-java.util.List-">setFurnitureVisibleProperties</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;&nbsp;furnitureVisibleProperties)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <code>#setFurnitureVisiblePropertyNames(List<String>)</code>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/HomePieceOfFurniture.SortableProperty.html" target="_top">Frames</a></li>
<li><a href="HomePieceOfFurniture.SortableProperty.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
