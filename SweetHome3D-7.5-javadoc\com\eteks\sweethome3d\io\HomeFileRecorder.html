<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>HomeFileRecorder (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeFileRecorder (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeFileRecorder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/HomeFileRecorder.html" target="_top">Frames</a></li>
<li><a href="HomeFileRecorder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class HomeFileRecorder" class="title">Class HomeFileRecorder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.HomeFileRecorder</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">HomeFileRecorder</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></pre>
<div class="block">Recorder that stores homes in files with <a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io"><code>DefaultHomeOutputStream</code></a> and
 <a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io"><code>DefaultHomeInputStream</code></a>.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.model.HomeRecorder">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.Type.html" title="enum in com.eteks.sweethome3d.model">HomeRecorder.Type</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder--">HomeFileRecorder</a></span>()</code>
<div class="block">Creates a home recorder able to write and read homes in uncompressed files.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder-int-">HomeFileRecorder</a></span>(int&nbsp;compressionLevel)</code>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder-int-boolean-">HomeFileRecorder</a></span>(int&nbsp;compressionLevel,
                boolean&nbsp;includeOnlyTemporaryContent)</code>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-">HomeFileRecorder</a></span>(int&nbsp;compressionLevel,
                boolean&nbsp;includeOnlyTemporaryContent,
                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                boolean&nbsp;preferPreferencesContent)</code>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-boolean-">HomeFileRecorder</a></span>(int&nbsp;compressionLevel,
                boolean&nbsp;includeOnlyTemporaryContent,
                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                boolean&nbsp;preferPreferencesContent,
                boolean&nbsp;preferXmlEntry)</code>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-boolean-boolean-">HomeFileRecorder</a></span>(int&nbsp;compressionLevel,
                boolean&nbsp;includeOnlyTemporaryContent,
                <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                boolean&nbsp;preferPreferencesContent,
                boolean&nbsp;preferXmlEntry,
                boolean&nbsp;acceptUrl)</code>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#exists-java.lang.String-">exists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns <code>true</code> if the file <code>name</code> exists.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html" title="class in com.eteks.sweethome3d.io">HomeXMLExporter</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#getHomeXMLExporter--">getHomeXMLExporter</a></span>()</code>
<div class="block">Returns an exporter able to generate the content of a <code>Home.xml</code> entry.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#getHomeXMLHandler--">getHomeXMLHandler</a></span>()</code>
<div class="block">Returns a SAX XML handler able to interpret the information contained in the
 <code>Home.xml</code> entry.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#readHome-java.lang.String-">readHome</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns a home instance read from its file <code>name</code> or an URL if it can be opened as a file.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#writeHome-com.eteks.sweethome3d.model.Home-java.lang.String-">writeHome</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
         java.lang.String&nbsp;name)</code>
<div class="block">Writes home data.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="HomeFileRecorder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFileRecorder</h4>
<pre>public&nbsp;HomeFileRecorder()</pre>
<div class="block">Creates a home recorder able to write and read homes in uncompressed files.</div>
</li>
</ul>
<a name="HomeFileRecorder-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFileRecorder</h4>
<pre>public&nbsp;HomeFileRecorder(int&nbsp;compressionLevel)</pre>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>compressionLevel</code> - 0 (uncompressed) to 9 (compressed).</dd>
</dl>
</li>
</ul>
<a name="HomeFileRecorder-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFileRecorder</h4>
<pre>public&nbsp;HomeFileRecorder(int&nbsp;compressionLevel,
                        boolean&nbsp;includeOnlyTemporaryContent)</pre>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>compressionLevel</code> - 0-9</dd>
<dd><code>includeOnlyTemporaryContent</code> - if <code>true</code>, content instances of
            <code>TemporaryURLContent</code> class referenced by the saved home
            as well as the content previously saved with it will be written.
            If <code>false</code>, all the content instances
            referenced by the saved home will be written in the zip stream.</dd>
</dl>
</li>
</ul>
<a name="HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFileRecorder</h4>
<pre>public&nbsp;HomeFileRecorder(int&nbsp;compressionLevel,
                        boolean&nbsp;includeOnlyTemporaryContent,
                        <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        boolean&nbsp;preferPreferencesContent)</pre>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>compressionLevel</code> - 0-9</dd>
<dd><code>includeOnlyTemporaryContent</code> - if <code>true</code>, content instances of
            <code>TemporaryURLContent</code> class referenced by the saved home
            as well as the content previously saved with it will be written.
            If <code>false</code>, all the content instances
            referenced by the saved home will be written in the zip stream.</dd>
<dd><code>preferences</code> - If not <code>null</code>, the furniture and textures contents
            it references might be used to replace the one of read homes
            when they are equal.</dd>
<dd><code>preferPreferencesContent</code> - If <code>true</code>, the furniture and textures contents
            referenced by <code>preferences</code> will replace the one of read homes
            as often as possible when they are equal. Otherwise, these contents will be
            used only to replace damaged content that might be found in read home files.</dd>
</dl>
</li>
</ul>
<a name="HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HomeFileRecorder</h4>
<pre>public&nbsp;HomeFileRecorder(int&nbsp;compressionLevel,
                        boolean&nbsp;includeOnlyTemporaryContent,
                        <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        boolean&nbsp;preferPreferencesContent,
                        boolean&nbsp;preferXmlEntry)</pre>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>compressionLevel</code> - 0-9</dd>
<dd><code>includeOnlyTemporaryContent</code> - if <code>true</code>, content instances of
            <code>TemporaryURLContent</code> class referenced by the saved home
            as well as the content previously saved with it will be written.
            If <code>false</code>, all the content instances
            referenced by the saved home will be written in the zip stream.</dd>
<dd><code>preferences</code> - If not <code>null</code>, the furniture and textures contents
            it references might be used to replace the one of read homes
            when they are equal.</dd>
<dd><code>preferPreferencesContent</code> - If <code>true</code>, the furniture and textures contents
            referenced by <code>preferences</code> will replace the one of read homes
            as often as possible when they are equal. Otherwise, these contents will be
            used only to replace damaged content that might be found in read home files.</dd>
<dd><code>preferXmlEntry</code> - If <code>true</code>, an additional <code>Home.xml</code> entry
            will be saved in files and read in priority from saved files.</dd>
</dl>
</li>
</ul>
<a name="HomeFileRecorder-int-boolean-com.eteks.sweethome3d.model.UserPreferences-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>HomeFileRecorder</h4>
<pre>public&nbsp;HomeFileRecorder(int&nbsp;compressionLevel,
                        boolean&nbsp;includeOnlyTemporaryContent,
                        <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        boolean&nbsp;preferPreferencesContent,
                        boolean&nbsp;preferXmlEntry,
                        boolean&nbsp;acceptUrl)</pre>
<div class="block">Creates a home recorder able to write and read homes in files compressed
 at a level from 0 to 9.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>compressionLevel</code> - 0-9</dd>
<dd><code>includeOnlyTemporaryContent</code> - if <code>true</code>, content instances of
            <code>TemporaryURLContent</code> class referenced by the saved home
            as well as the content previously saved with it will be written.
            If <code>false</code>, all the content instances
            referenced by the saved home will be written in the zip stream.</dd>
<dd><code>preferences</code> - If not <code>null</code>, the furniture and textures contents
            it references might be used to replace the one of read homes
            when they are equal.</dd>
<dd><code>preferPreferencesContent</code> - If <code>true</code>, the furniture and textures contents
            referenced by <code>preferences</code> will replace the one of read homes
            as often as possible when they are equal. Otherwise, these contents will be
            used only to replace damaged content that might be found in read home files.</dd>
<dd><code>preferXmlEntry</code> - If <code>true</code>, an additional <code>Home.xml</code> entry
            will be saved in files and read in priority from saved files.</dd>
<dd><code>acceptUrl</code> - If <code>true</code>, this recorder will try to read a home from a URL
            if the path passed as parameter to <a href="../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#readHome-java.lang.String-"><code>readHome</code></a> isn't a file.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="writeHome-com.eteks.sweethome3d.model.Home-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeHome</h4>
<pre>public&nbsp;void&nbsp;writeHome(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      java.lang.String&nbsp;name)
               throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Writes home data.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html#writeHome-com.eteks.sweethome3d.model.Home-java.lang.String-">writeHome</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home to write.</dd>
<dd><code>name</code> - the name of the resource in which the home will be written.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code> - if a problem occurred while writing home.</dd>
</dl>
</li>
</ul>
<a name="getHomeXMLExporter--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeXMLExporter</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html" title="class in com.eteks.sweethome3d.io">HomeXMLExporter</a>&nbsp;getHomeXMLExporter()</pre>
<div class="block">Returns an exporter able to generate the content of a <code>Home.xml</code> entry.</div>
</li>
</ul>
<a name="readHome-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readHome</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;readHome(java.lang.String&nbsp;name)
              throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns a home instance read from its file <code>name</code> or an URL if it can be opened as a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html#readHome-java.lang.String-">readHome</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the resource from which the home will be read.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code> - if a problem occurred while reading home,
   or if file or URL <code>name</code> doesn't exist.</dd>
</dl>
</li>
</ul>
<a name="getHomeXMLHandler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeXMLHandler</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io">HomeXMLHandler</a>&nbsp;getHomeXMLHandler()</pre>
<div class="block">Returns a SAX XML handler able to interpret the information contained in the
 <code>Home.xml</code> entry.</div>
</li>
</ul>
<a name="exists-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>exists</h4>
<pre>public&nbsp;boolean&nbsp;exists(java.lang.String&nbsp;name)
               throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns <code>true</code> if the file <code>name</code> exists.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html#exists-java.lang.String-">exists</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeRecorder.html" title="interface in com.eteks.sweethome3d.model">HomeRecorder</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the resource to check</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeFileRecorder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/FileUserPreferences.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/HomeFileRecorder.html" target="_top">Frames</a></li>
<li><a href="HomeFileRecorder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
