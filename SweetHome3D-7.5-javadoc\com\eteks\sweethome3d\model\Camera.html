<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>Camera (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Camera (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Camera.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/BoxBounds.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Camera.html" target="_top">Frames</a></li>
<li><a href="Camera.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class Camera" class="title">Class Camera</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.Camera</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/ObserverCamera.html" title="class in com.eteks.sweethome3d.model">ObserverCamera</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Camera</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></pre>
<div class="block">Camera characteristics in home.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.Camera">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a></span></code>
<div class="block">The kind of lens that can be used with a camera.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.Property.html" title="enum in com.eteks.sweethome3d.model">Camera.Property</a></span></code>
<div class="block">The properties of a camera that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#Camera-float-float-float-float-float-float-">Camera</a></span>(float&nbsp;x,
      float&nbsp;y,
      float&nbsp;z,
      float&nbsp;yaw,
      float&nbsp;pitch,
      float&nbsp;fieldOfView)</code>
<div class="block">Creates a camera at given location and angles at midday and using a pinhole lens.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#Camera-float-float-float-float-float-float-long-com.eteks.sweethome3d.model.Camera.Lens-">Camera</a></span>(float&nbsp;x,
      float&nbsp;y,
      float&nbsp;z,
      float&nbsp;yaw,
      float&nbsp;pitch,
      float&nbsp;fieldOfView,
      long&nbsp;time,
      <a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a>&nbsp;lens)</code>
<div class="block">Creates a camera at given location and angles.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#Camera-java.lang.String-float-float-float-float-float-float-">Camera</a></span>(java.lang.String&nbsp;id,
      float&nbsp;x,
      float&nbsp;y,
      float&nbsp;z,
      float&nbsp;yaw,
      float&nbsp;pitch,
      float&nbsp;fieldOfView)</code>
<div class="block">Creates a camera at given location and angles at midday and using a pinhole lens.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#Camera-java.lang.String-float-float-float-float-float-float-long-com.eteks.sweethome3d.model.Camera.Lens-">Camera</a></span>(java.lang.String&nbsp;id,
      float&nbsp;x,
      float&nbsp;y,
      float&nbsp;z,
      float&nbsp;yaw,
      float&nbsp;pitch,
      float&nbsp;fieldOfView,
      long&nbsp;time,
      <a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a>&nbsp;lens)</code>
<div class="block">Creates a camera at given location and angles.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this camera.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#convertTimeToTimeZone-long-java.lang.String-">convertTimeToTimeZone</a></span>(long&nbsp;utcTime,
                     java.lang.String&nbsp;timeZone)</code>
<div class="block">Returns a time expressed in UTC time zone converted to the given time zone.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getFieldOfView--">getFieldOfView</a></span>()</code>
<div class="block">Returns the field of view in radians of this camera.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getLens--">getLens</a></span>()</code>
<div class="block">Returns the lens of this camera.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of this camera.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getPitch--">getPitch</a></span>()</code>
<div class="block">Returns the pitch angle in radians of this camera.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getRenderer--">getRenderer</a></span>()</code>
<div class="block">Returns the rendering engine used to create photos.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getTime--">getTime</a></span>()</code>
<div class="block">Returns the time in milliseconds when this camera is used.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getX--">getX</a></span>()</code>
<div class="block">Returns the abscissa of this camera.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getY--">getY</a></span>()</code>
<div class="block">Returns the ordinate of this camera.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getYaw--">getYaw</a></span>()</code>
<div class="block">Returns the yaw angle in radians of this camera.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#getZ--">getZ</a></span>()</code>
<div class="block">Returns the elevation of this camera.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setCamera-com.eteks.sweethome3d.model.Camera-">setCamera</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera)</code>
<div class="block">Sets the location and angles of this camera from the <code>camera</code> in parameter.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setFieldOfView-float-">setFieldOfView</a></span>(float&nbsp;fieldOfView)</code>
<div class="block">Sets the field of view in radians of this camera and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setLens-com.eteks.sweethome3d.model.Camera.Lens-">setLens</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a>&nbsp;lens)</code>
<div class="block">Sets the lens of this camera and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Sets the name of this camera and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setPitch-float-">setPitch</a></span>(float&nbsp;pitch)</code>
<div class="block">Sets the pitch angle in radians of this camera and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setRenderer-java.lang.String-">setRenderer</a></span>(java.lang.String&nbsp;renderer)</code>
<div class="block">Sets the rendering engine used to create photos.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setTime-long-">setTime</a></span>(long&nbsp;time)</code>
<div class="block">Sets the use time in milliseconds since the Epoch in UTC time zone,
 and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setX-float-">setX</a></span>(float&nbsp;x)</code>
<div class="block">Sets the abscissa of this camera and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setY-float-">setY</a></span>(float&nbsp;y)</code>
<div class="block">Sets the ordinate of this camera and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setYaw-float-">setYaw</a></span>(float&nbsp;yaw)</code>
<div class="block">Sets the yaw angle in radians of this camera and notifies listeners of this change.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Camera.html#setZ-float-">setZ</a></span>(float&nbsp;z)</code>
<div class="block">Sets the elevation of this camera and notifies listeners of this change.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Camera-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Camera</h4>
<pre>public&nbsp;Camera(float&nbsp;x,
              float&nbsp;y,
              float&nbsp;z,
              float&nbsp;yaw,
              float&nbsp;pitch,
              float&nbsp;fieldOfView)</pre>
<div class="block">Creates a camera at given location and angles at midday and using a pinhole lens.</div>
</li>
</ul>
<a name="Camera-java.lang.String-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Camera</h4>
<pre>public&nbsp;Camera(java.lang.String&nbsp;id,
              float&nbsp;x,
              float&nbsp;y,
              float&nbsp;z,
              float&nbsp;yaw,
              float&nbsp;pitch,
              float&nbsp;fieldOfView)</pre>
<div class="block">Creates a camera at given location and angles at midday and using a pinhole lens.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="Camera-float-float-float-float-float-float-long-com.eteks.sweethome3d.model.Camera.Lens-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Camera</h4>
<pre>public&nbsp;Camera(float&nbsp;x,
              float&nbsp;y,
              float&nbsp;z,
              float&nbsp;yaw,
              float&nbsp;pitch,
              float&nbsp;fieldOfView,
              long&nbsp;time,
              <a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a>&nbsp;lens)</pre>
<div class="block">Creates a camera at given location and angles.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="Camera-java.lang.String-float-float-float-float-float-float-long-com.eteks.sweethome3d.model.Camera.Lens-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Camera</h4>
<pre>public&nbsp;Camera(java.lang.String&nbsp;id,
              float&nbsp;x,
              float&nbsp;y,
              float&nbsp;z,
              float&nbsp;yaw,
              float&nbsp;pitch,
              float&nbsp;fieldOfView,
              long&nbsp;time,
              <a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a>&nbsp;lens)</pre>
<div class="block">Creates a camera at given location and angles.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of this camera.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the name of this camera and notifies listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getYaw--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getYaw</h4>
<pre>public&nbsp;float&nbsp;getYaw()</pre>
<div class="block">Returns the yaw angle in radians of this camera.</div>
</li>
</ul>
<a name="setYaw-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setYaw</h4>
<pre>public&nbsp;void&nbsp;setYaw(float&nbsp;yaw)</pre>
<div class="block">Sets the yaw angle in radians of this camera and notifies listeners of this change.
 Yaw axis is vertical axis.</div>
</li>
</ul>
<a name="getPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPitch</h4>
<pre>public&nbsp;float&nbsp;getPitch()</pre>
<div class="block">Returns the pitch angle in radians of this camera.</div>
</li>
</ul>
<a name="setPitch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPitch</h4>
<pre>public&nbsp;void&nbsp;setPitch(float&nbsp;pitch)</pre>
<div class="block">Sets the pitch angle in radians of this camera and notifies listeners of this change.
 Pitch axis is horizontal transverse axis.</div>
</li>
</ul>
<a name="getFieldOfView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldOfView</h4>
<pre>public&nbsp;float&nbsp;getFieldOfView()</pre>
<div class="block">Returns the field of view in radians of this camera.</div>
</li>
</ul>
<a name="setFieldOfView-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldOfView</h4>
<pre>public&nbsp;void&nbsp;setFieldOfView(float&nbsp;fieldOfView)</pre>
<div class="block">Sets the field of view in radians of this camera and notifies listeners of this change.</div>
</li>
</ul>
<a name="getX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX</h4>
<pre>public&nbsp;float&nbsp;getX()</pre>
<div class="block">Returns the abscissa of this camera.</div>
</li>
</ul>
<a name="setX-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setX</h4>
<pre>public&nbsp;void&nbsp;setX(float&nbsp;x)</pre>
<div class="block">Sets the abscissa of this camera and notifies listeners of this change.</div>
</li>
</ul>
<a name="getY--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getY</h4>
<pre>public&nbsp;float&nbsp;getY()</pre>
<div class="block">Returns the ordinate of this camera.</div>
</li>
</ul>
<a name="setY-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setY</h4>
<pre>public&nbsp;void&nbsp;setY(float&nbsp;y)</pre>
<div class="block">Sets the ordinate of this camera and notifies listeners of this change.</div>
</li>
</ul>
<a name="getZ--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getZ</h4>
<pre>public&nbsp;float&nbsp;getZ()</pre>
<div class="block">Returns the elevation of this camera.</div>
</li>
</ul>
<a name="setZ-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setZ</h4>
<pre>public&nbsp;void&nbsp;setZ(float&nbsp;z)</pre>
<div class="block">Sets the elevation of this camera and notifies listeners of this change.</div>
</li>
</ul>
<a name="getTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTime</h4>
<pre>public&nbsp;long&nbsp;getTime()</pre>
<div class="block">Returns the time in milliseconds when this camera is used.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a time in milliseconds since the Epoch in UTC time zone</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setTime-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTime</h4>
<pre>public&nbsp;void&nbsp;setTime(long&nbsp;time)</pre>
<div class="block">Sets the use time in milliseconds since the Epoch in UTC time zone,
 and notifies listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="convertTimeToTimeZone-long-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertTimeToTimeZone</h4>
<pre>public static&nbsp;long&nbsp;convertTimeToTimeZone(long&nbsp;utcTime,
                                         java.lang.String&nbsp;timeZone)</pre>
<div class="block">Returns a time expressed in UTC time zone converted to the given time zone.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="getLens--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLens</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a>&nbsp;getLens()</pre>
<div class="block">Returns the lens of this camera.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setLens-com.eteks.sweethome3d.model.Camera.Lens-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLens</h4>
<pre>public&nbsp;void&nbsp;setLens(<a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model">Camera.Lens</a>&nbsp;lens)</pre>
<div class="block">Sets the lens of this camera and notifies listeners of this change.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>3.0</dd>
</dl>
</li>
</ul>
<a name="setRenderer-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRenderer</h4>
<pre>public&nbsp;void&nbsp;setRenderer(java.lang.String&nbsp;renderer)</pre>
<div class="block">Sets the rendering engine used to create photos.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getRenderer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRenderer</h4>
<pre>public&nbsp;java.lang.String&nbsp;getRenderer()</pre>
<div class="block">Returns the rendering engine used to create photos.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setCamera-com.eteks.sweethome3d.model.Camera-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCamera</h4>
<pre>public&nbsp;void&nbsp;setCamera(<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera)</pre>
<div class="block">Sets the location and angles of this camera from the <code>camera</code> in parameter.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this camera.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.3</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Camera.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/BoxBounds.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Camera.Lens.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Camera.html" target="_top">Frames</a></li>
<li><a href="Camera.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
