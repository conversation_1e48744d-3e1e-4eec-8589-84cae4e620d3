<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>ShelfUnit (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ShelfUnit (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ShelfUnit.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/SelectionListener.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/ShelfUnit.html" target="_top">Frames</a></li>
<li><a href="ShelfUnit.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Interface ShelfUnit" class="title">Interface ShelfUnit</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/CatalogShelfUnit.html" title="class in com.eteks.sweethome3d.model">CatalogShelfUnit</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeShelfUnit.html" title="class in com.eteks.sweethome3d.model">HomeShelfUnit</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ShelfUnit</span>
extends <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></pre>
<div class="block">A piece of furniture whith shelves.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#DEFAULT_CUT_OUT_SHAPE">DEFAULT_CUT_OUT_SHAPE</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL">HIDE_EDGE_COLOR_MATERIAL</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#IDENTITY_ROTATION">IDENTITY_ROTATION</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE">SHOW_BACK_FACE</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/BoxBounds.html" title="class in com.eteks.sweethome3d.model">BoxBounds</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html#getShelfBoxes--">getShelfBoxes</a></span>()</code>
<div class="block">Returns the coordinates of the shelf box(es) in which other objects can be placed in this shelf unit.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/ShelfUnit.html#getShelfElevations--">getShelfElevations</a></span>()</code>
<div class="block">Returns the elevation(s) at which other objects can be placed on this shelf unit.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.PieceOfFurniture">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getColor--">getColor</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCreator--">getCreator</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getCurrency--">getCurrency</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDepth--">getDepth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDescription--">getDescription</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getDropOnTopElevation--">getDropOnTopElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getElevation--">getElevation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getHeight--">getHeight</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getIcon--">getIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getInformation--">getInformation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getLicense--">getLicense</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModel--">getModel</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelFlags--">getModelFlags</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelRotation--">getModelRotation</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getModelSize--">getModelSize</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getName--">getName</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPlanIcon--">getPlanIcon</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPrice--">getPrice</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getValueAddedTaxPercentage--">getValueAddedTaxPercentage</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#getWidth--">getWidth</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isBackFaceShown--">isBackFaceShown</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDeformable--">isDeformable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isDoorOrWindow--">isDoorOrWindow</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isHorizontallyRotatable--">isHorizontallyRotatable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isMovable--">isMovable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isTexturable--">isTexturable</a>, <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#isWidthDepthDeformable--">isWidthDepthDeformable</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getShelfElevations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShelfElevations</h4>
<pre>float[]&nbsp;getShelfElevations()</pre>
<div class="block">Returns the elevation(s) at which other objects can be placed on this shelf unit.</div>
</li>
</ul>
<a name="getShelfBoxes--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getShelfBoxes</h4>
<pre><a href="../../../../com/eteks/sweethome3d/model/BoxBounds.html" title="class in com.eteks.sweethome3d.model">BoxBounds</a>[]&nbsp;getShelfBoxes()</pre>
<div class="block">Returns the coordinates of the shelf box(es) in which other objects can be placed in this shelf unit.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ShelfUnit.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/SelectionListener.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/ShelfUnit.html" target="_top">Frames</a></li>
<li><a href="ShelfUnit.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
