<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.model.RecorderException (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.model.RecorderException (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/RecorderException.html" target="_top">Frames</a></li>
<li><a href="RecorderException.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.model.RecorderException" class="title">Uses of Class<br>com.eteks.sweethome3d.model.RecorderException</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.applet">com.eteks.sweethome3d.applet</a></td>
<td class="colLast">
<div class="block">Instantiates the required classes to run Sweet Home 3D as an 
<a href="../../../../../com/eteks/sweethome3d/applet/SweetHome3DApplet.html" title="class in com.eteks.sweethome3d.applet">applet</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.plugin">com.eteks.sweethome3d.plugin</a></td>
<td class="colLast">
<div class="block">Describes the super classes required to create Sweet Home 3D plug-ins.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.swing">com.eteks.sweethome3d.swing</a></td>
<td class="colLast">
<div class="block">Implements views created by Sweet Home 3D <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">controllers</a>
with Swing components.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.applet">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a> in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/applet/package-summary.html">com.eteks.sweethome3d.applet</a> that throw <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AppletUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Throws an exception because applet user preferences can't manage additional furniture libraries.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AppletUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Throws an exception because applet user preferences can't manage language libraries.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AppletUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Throws an exception because applet user preferences can't manage additional textures libraries.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeAppletRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html#deleteHome-java.lang.String-">deleteHome</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Deletes on server a home from its file <code>name</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">HomeAppletRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html#exists-java.lang.String-">exists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns <code>true</code> if the home <code>name</code> exists.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AppletUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Returns <code>true</code> if the furniture library at the given <code>location</code> exists.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>java.lang.String[]</code></td>
<td class="colLast"><span class="typeNameLabel">HomeAppletRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html#getAvailableHomes--">getAvailableHomes</a></span>()</code>
<div class="block">Returns the available homes on server.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><span class="typeNameLabel">HomeAppletRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html#getHomeLength-com.eteks.sweethome3d.model.Home-">getHomeLength</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Returns the length of the home data that will be saved by this recorder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AppletUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Throws an exception because applet user preferences can't manage additional language libraries.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeAppletRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html#readHome-java.lang.String-">readHome</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns a home instance read from its file <code>name</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">AppletUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Returns <code>true</code> if the textures library at the given <code>location</code> exists.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">AppletUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/AppletUserPreferences.html#write--">write</a></span>()</code>
<div class="block">Writes user preferences.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeAppletRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/applet/HomeAppletRecorder.html#writeHome-com.eteks.sweethome3d.model.Home-java.lang.String-">writeHome</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
         java.lang.String&nbsp;name)</code>
<div class="block">Posts home data to the server URL returned by <code>getHomeSaveURL</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> that throw <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></span>(java.lang.String&nbsp;furnitureLibraryPath)</code>
<div class="block">Adds the file <code>furnitureLibraryPath</code> to the first furniture libraries folder
 to make the furniture library available to catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage additional furniture libraries.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></span>(java.lang.String&nbsp;languageLibraryPath)</code>
<div class="block">Adds <code>languageLibraryPath</code> to the first language libraries folder
 to make the language library it contains available to supported languages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage additional language libraries.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></span>(java.lang.String&nbsp;texturesLibraryPath)</code>
<div class="block">Adds the file <code>texturesLibraryPath</code> to the first textures libraries folder
 to make the textures library available to catalog.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage additional textures libraries.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#deleteLibraries-java.util.List-">deleteLibraries</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;libraries)</code>
<div class="block">Deletes the given <code>libraries</code> and updates user preferences.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFileRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#exists-java.lang.String-">exists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns <code>true</code> if the file <code>name</code> exists.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Returns <code>true</code> if the furniture library at the given <code>location</code> exists
 in the first furniture libraries folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage additional furniture libraries.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Returns <code>true</code> if the language library at the given location exists
 in the first language libraries folder.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage language libraries.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeFileRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#readHome-java.lang.String-">readHome</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns a home instance read from its file <code>name</code> or an URL if it can be opened as a file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></span>(java.lang.String&nbsp;location)</code>
<div class="block">Returns <code>true</code> if the textures library at the given <code>location</code> exists
 in the first textures libraries folder.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Throws an exception because default user preferences can't manage textures libraries.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">FileUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/FileUserPreferences.html#write--">write</a></span>()</code>
<div class="block">Writes user preferences in current user preferences in system.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">DefaultUserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/DefaultUserPreferences.html#write--">write</a></span>()</code>
<div class="block">Throws an exception because default user preferences can't be written
 with this class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFileRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeFileRecorder.html#writeHome-com.eteks.sweethome3d.model.Home-java.lang.String-">writeHome</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
         java.lang.String&nbsp;name)</code>
<div class="block">Writes home data.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> that throw <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/AutoRecoveryManager.html#AutoRecoveryManager-com.eteks.sweethome3d.model.HomeApplication-">AutoRecoveryManager</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeApplication.html" title="class in com.eteks.sweethome3d.model">HomeApplication</a>&nbsp;application)</code>
<div class="block">Creates a manager able to automatically recover <code>application</code> homes.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/DamagedHomeRecorderException.html" title="class in com.eteks.sweethome3d.model">DamagedHomeRecorderException</a></span></code>
<div class="block">Exception thrown when a home data read in IO layer is damaged with with possible invalid content in dependencies.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/InterruptedRecorderException.html" title="class in com.eteks.sweethome3d.model">InterruptedRecorderException</a></span></code>
<div class="block">Exception thrown when a thread is interrupted during an access to data in IO layer.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/NotEnoughSpaceRecorderException.html" title="class in com.eteks.sweethome3d.model">NotEnoughSpaceRecorderException</a></span></code>
<div class="block">Exception thrown when there's not enough space to save a home.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that throw <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#addFurnitureLibrary-java.lang.String-">addFurnitureLibrary</a></span>(java.lang.String&nbsp;furnitureLibraryLocation)</code>
<div class="block">Adds <code>furnitureLibraryName</code> to furniture catalog
 to make the furniture it contains available.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#addLanguageLibrary-java.lang.String-">addLanguageLibrary</a></span>(java.lang.String&nbsp;languageLibraryLocation)</code>
<div class="block">Adds the language library to make the languages it contains available to supported languages.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#addTexturesLibrary-java.lang.String-">addTexturesLibrary</a></span>(java.lang.String&nbsp;texturesLibraryLocation)</code>
<div class="block">Adds the textures library at the given location to textures catalog
 to make the textures it contains available.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">HomeRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html#exists-java.lang.String-">exists</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns <code>true</code> if the home with a given <code>name</code>
 exists.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#furnitureLibraryExists-java.lang.String-">furnitureLibraryExists</a></span>(java.lang.String&nbsp;furnitureLibraryLocation)</code>
<div class="block">Returns <code>true</code> if the furniture library at the given location exists.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#languageLibraryExists-java.lang.String-">languageLibraryExists</a></span>(java.lang.String&nbsp;languageLibraryLocation)</code>
<div class="block">Returns <code>true</code> if the language library at the given location exists.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html#readHome-java.lang.String-">readHome</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns a home instance read from its <code>name</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract boolean</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#texturesLibraryExists-java.lang.String-">texturesLibraryExists</a></span>(java.lang.String&nbsp;texturesLibraryLocation)</code>
<div class="block">Returns <code>true</code> if the textures library at the given location exists.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><span class="typeNameLabel">UserPreferences.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html#write--">write</a></span>()</code>
<div class="block">Writes user preferences.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeRecorder.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeRecorder.html#writeHome-com.eteks.sweethome3d.model.Home-java.lang.String-">writeHome</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
         java.lang.String&nbsp;name)</code>
<div class="block">Writes <code>home</code> data.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.plugin">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a> in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/plugin/package-summary.html">com.eteks.sweethome3d.plugin</a> that throw <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PluginManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html#addPlugin-java.lang.String-">addPlugin</a></span>(java.lang.String&nbsp;pluginPath)</code>
<div class="block">Adds the file at the given location to the first plug-ins folders if it exists.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PluginManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html#deletePlugins-java.util.List-">deletePlugins</a></span>(java.util.List&lt;<a href="../../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;libraries)</code>
<div class="block">Deletes the given plug-in <code>libraries</code> from managed plug-ins.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">PluginManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/plugin/PluginManager.html#pluginExists-java.lang.String-">pluginExists</a></span>(java.lang.String&nbsp;pluginLocation)</code>
<div class="block">Returns <code>true</code> if a plug-in in the given file name already exists
 in the first plug-ins folder.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.swing">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a> in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">com.eteks.sweethome3d.swing</a> that throw <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#exportToCSV-java.lang.String-">exportToCSV</a></span>(java.lang.String&nbsp;csvFile)</code>
<div class="block">Exports furniture list to a given CSV file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#exportToOBJ-java.lang.String-">exportToOBJ</a></span>(java.lang.String&nbsp;objFile)</code>
<div class="block">Exports the objects of the 3D view to the given OBJ file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#exportToOBJ-java.lang.String-com.eteks.sweethome3d.viewcontroller.Object3DFactory-">exportToOBJ</a></span>(java.lang.String&nbsp;objFile,
           <a href="../../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory)</code>
<div class="block">Exports to an OBJ file the objects of the 3D view created with the given factory.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#exportToSVG-java.lang.String-">exportToSVG</a></span>(java.lang.String&nbsp;svgFile)</code>
<div class="block">Exports the plan objects to a given SVG file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">FileContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getContent-java.lang.String-">getContent</a></span>(java.lang.String&nbsp;contentPath)</code>
<div class="block">Returns a <a href="../../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools"><code>URL content</code></a> object that references
 the given file path.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePane.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/swing/HomePane.html#printToPDF-java.lang.String-">printToPDF</a></span>(java.lang.String&nbsp;pdfFile)</code>
<div class="block">Prints a home to a given PDF file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that throw <a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToCSV-java.lang.String-">exportToCSV</a></span>(java.lang.String&nbsp;csvName)</code>
<div class="block">Exports furniture list to a given SVG file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToOBJ-java.lang.String-">exportToOBJ</a></span>(java.lang.String&nbsp;objFile)</code>
<div class="block">Exports the 3D home objects to a given OBJ file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToSVG-java.lang.String-">exportToSVG</a></span>(java.lang.String&nbsp;svgName)</code>
<div class="block">Exports the plan objects to a given SVG file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><span class="typeNameLabel">ContentManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#getContent-java.lang.String-">getContent</a></span>(java.lang.String&nbsp;contentLocation)</code>
<div class="block">Returns a <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model"><code>content</code></a> object that references a given content location.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeView.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#printToPDF-java.lang.String-">printToPDF</a></span>(java.lang.String&nbsp;pdfFile)</code>
<div class="block">Prints a home to a given PDF file.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/RecorderException.html" target="_top">Frames</a></li>
<li><a href="RecorderException.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
