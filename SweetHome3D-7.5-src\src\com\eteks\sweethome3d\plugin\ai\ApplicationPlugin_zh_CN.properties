# ApplicationPlugin_zh_CN.properties
# Sweet Home 3D AI Plugin Configuration - Chinese Simplified
# Copyright (c) 2025 Samuel Kpassegna

# Plugin identification
name=AI户型分析
description=使用人工智能分析户型图，提供洞察和改进建议
provider=Samuel Kpassegna

# Features
features=AI分析，户型洞察，多AI提供商，隐私控制

# Requirements
requirements=云端AI提供商需要互联网连接（本地提供商可选）

# UI Strings for internationalization
# Action properties
AIAction.Name=AI分析
AIAction.ShortDescription=用AI分析户型图
AIAction.Menu=工具

# Dialog titles
AIChatDialog.title=AI户型分析
AISettingsDialog.title=AI设置

# Button labels
button.send=发送
button.newAnalysis=新分析
button.settings=设置
button.testConnection=测试连接
button.save=保存
button.cancel=取消

# Labels
label.provider=提供商:
label.baseUrl=基础URL:
label.apiKey=API密钥:
label.model=模型:
label.temperature=温度:
label.maxTokens=最大令牌:
label.status=状态:

# Messages
message.analyzing=正在分析户型图...
message.processingQuestion=正在处理问题...
message.testingConnection=正在测试连接...
message.connectionSuccessful=连接成功！
message.connectionFailed=连接失败: {0}
message.configurationSaved=配置保存成功
message.validationError=配置错误:\n{0}
message.noConfiguration=AI提供商未配置。请先配置设置。

# Analysis prompt
analysis.prompt=请分析此户型图并提供全面的洞察，包括:\n1. 布局效率和空间利用\n2. 交通流线和循环模式\n3. 自然采光和通风机会\n4. 无障碍考虑\n5. 空间间的功能关系\n6. 改进建议\n7. 符合常见建筑标准\n8. 能效考虑\n\n请提供具体、可操作的建议，以提高此空间的功能性、舒适性和美观性。

# Error messages
error.analysisError=分析错误: {0}
error.configurationError=配置错误: {0}
error.connectionError=连接错误: {0}
error.invalidConfiguration=无效配置
error.missingApiKey=需要API密钥
error.missingBaseUrl=需要基础URL
error.missingModel=需要选择模型

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (本地)
provider.lmstudio=LM Studio (本地)
provider.anythingllm=AnythingLLM (本地)
provider.jan=Jan (本地)
provider.custom=自定义
