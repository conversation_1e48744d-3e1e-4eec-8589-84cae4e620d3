<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>TexturesCatalog (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TexturesCatalog (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TexturesCatalog.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/TexturesCatalog.html" target="_top">Frames</a></li>
<li><a href="TexturesCatalog.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class TexturesCatalog" class="title">Class TexturesCatalog</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.TexturesCatalog</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html" title="class in com.eteks.sweethome3d.io">DefaultTexturesCatalog</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">TexturesCatalog</span>
extends java.lang.Object</pre>
<div class="block">Textures catalog.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#TexturesCatalog--">TexturesCatalog</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#add-com.eteks.sweethome3d.model.TexturesCategory-com.eteks.sweethome3d.model.CatalogTexture-">add</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a>&nbsp;category,
   <a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture)</code>
<div class="block">Adds <code>texture</code> of a given <code>category</code> to this catalog.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#addTexturesListener-com.eteks.sweethome3d.model.CollectionListener-">addTexturesListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&gt;&nbsp;listener)</code>
<div class="block">Adds the texture <code>listener</code> in parameter to this catalog.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#delete-com.eteks.sweethome3d.model.CatalogTexture-">delete</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture)</code>
<div class="block">Deletes the <code>texture</code> from this catalog.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#getCategories--">getCategories</a></span>()</code>
<div class="block">Returns the categories list sorted by name.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#getCategoriesCount--">getCategoriesCount</a></span>()</code>
<div class="block">Returns the count of categories in this catalog.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#getCategory-int-">getCategory</a></span>(int&nbsp;index)</code>
<div class="block">Returns the category at a given <code>index</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#removeTexturesListener-com.eteks.sweethome3d.model.CollectionListener-">removeTexturesListener</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&gt;&nbsp;listener)</code>
<div class="block">Removes the texture <code>listener</code> in parameter from this catalog.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TexturesCatalog--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TexturesCatalog</h4>
<pre>public&nbsp;TexturesCatalog()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getCategories--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategories</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a>&gt;&nbsp;getCategories()</pre>
<div class="block">Returns the categories list sorted by name.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an unmodifiable list of categories.</dd>
</dl>
</li>
</ul>
<a name="getCategoriesCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategoriesCount</h4>
<pre>public&nbsp;int&nbsp;getCategoriesCount()</pre>
<div class="block">Returns the count of categories in this catalog.</div>
</li>
</ul>
<a name="getCategory-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategory</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a>&nbsp;getCategory(int&nbsp;index)</pre>
<div class="block">Returns the category at a given <code>index</code>.</div>
</li>
</ul>
<a name="addTexturesListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addTexturesListener</h4>
<pre>public&nbsp;void&nbsp;addTexturesListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&gt;&nbsp;listener)</pre>
<div class="block">Adds the texture <code>listener</code> in parameter to this catalog.</div>
</li>
</ul>
<a name="removeTexturesListener-com.eteks.sweethome3d.model.CollectionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeTexturesListener</h4>
<pre>public&nbsp;void&nbsp;removeTexturesListener(<a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html" title="interface in com.eteks.sweethome3d.model">CollectionListener</a>&lt;<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&gt;&nbsp;listener)</pre>
<div class="block">Removes the texture <code>listener</code> in parameter from this catalog.</div>
</li>
</ul>
<a name="add-com.eteks.sweethome3d.model.TexturesCategory-com.eteks.sweethome3d.model.CatalogTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;void&nbsp;add(<a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a>&nbsp;category,
                <a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture)</pre>
<div class="block">Adds <code>texture</code> of a given <code>category</code> to this catalog.
 Once the <code>texture</code> is added, texture listeners added to this catalog will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a> notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>category</code> - the category of the texture.</dd>
<dd><code>texture</code> - a texture.</dd>
</dl>
</li>
</ul>
<a name="delete-com.eteks.sweethome3d.model.CatalogTexture-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>delete</h4>
<pre>public&nbsp;void&nbsp;delete(<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;texture)</pre>
<div class="block">Deletes the <code>texture</code> from this catalog.
 If then texture category is empty, it will be removed from the categories of this catalog. 
 Once the <code>texture</code> is deleted, texture listeners added to this catalog will receive a
 <a href="../../../../com/eteks/sweethome3d/model/CollectionListener.html#collectionChanged-com.eteks.sweethome3d.model.CollectionEvent-"><code>collectionChanged</code></a> notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>texture</code> - a texture.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TexturesCatalog.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/TexturesCatalog.html" target="_top">Frames</a></li>
<li><a href="TexturesCatalog.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
