<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>XMLWriter (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="XMLWriter (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/XMLWriter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/XMLWriter.html" target="_top">Frames</a></li>
<li><a href="XMLWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.FilterWriter">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class XMLWriter" class="title">Class XMLWriter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.io.Writer</li>
<li>
<ul class="inheritance">
<li>java.io.FilterWriter</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.XMLWriter</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Closeable, java.io.Flushable, java.lang.Appendable, java.lang.AutoCloseable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">XMLWriter</span>
extends java.io.FilterWriter</pre>
<div class="block">A simple XML writer able to write XML elements, their attributes and texts, indenting child elements.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.io.FilterWriter">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.io.FilterWriter</h3>
<code>out</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.io.Writer">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.io.Writer</h3>
<code>lock</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#XMLWriter-java.io.OutputStream-">XMLWriter</a></span>(java.io.OutputStream&nbsp;out)</code>
<div class="block">Creates a writer in the given output stream encoded in UTF-8.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#write-char:A-int-int-">write</a></span>(char[]&nbsp;buffer,
     int&nbsp;offset,
     int&nbsp;length)</code>
<div class="block">Writes the given characters array as the content of the current element.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#write-int-">write</a></span>(int&nbsp;c)</code>
<div class="block">Writes the given character as the content of the current element.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#write-java.lang.String-int-int-">write</a></span>(java.lang.String&nbsp;str,
     int&nbsp;offset,
     int&nbsp;length)</code>
<div class="block">Writes the given string as the content of the current element.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeAttribute-java.lang.String-java.lang.String-">writeAttribute</a></span>(java.lang.String&nbsp;name,
              java.lang.String&nbsp;value)</code>
<div class="block">Writes the attribute of the given <code>name</code> with its <code>value</code>
 in the tag of the last started element.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeAttribute-java.lang.String-java.lang.String-java.lang.String-">writeAttribute</a></span>(java.lang.String&nbsp;name,
              java.lang.String&nbsp;value,
              java.lang.String&nbsp;defaultValue)</code>
<div class="block">Writes the name and the value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>defaultValue</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeBigDecimalAttribute-java.lang.String-java.math.BigDecimal-">writeBigDecimalAttribute</a></span>(java.lang.String&nbsp;name,
                        java.math.BigDecimal&nbsp;value)</code>
<div class="block">Writes the name and the value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>null</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeBooleanAttribute-java.lang.String-boolean-boolean-">writeBooleanAttribute</a></span>(java.lang.String&nbsp;name,
                     boolean&nbsp;value,
                     boolean&nbsp;defaultValue)</code>
<div class="block">Writes the name and the boolean value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>defaultValue</code>.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeColorAttribute-java.lang.String-java.lang.Integer-">writeColorAttribute</a></span>(java.lang.String&nbsp;name,
                   java.lang.Integer&nbsp;color)</code>
<div class="block">Writes the name and the color value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>null</code>.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeEndElement--">writeEndElement</a></span>()</code>
<div class="block">Writes an end tag for the given element.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeFloatAttribute-java.lang.String-float-">writeFloatAttribute</a></span>(java.lang.String&nbsp;name,
                   float&nbsp;value)</code>
<div class="block">Writes the attribute of the given <code>name</code> with its float <code>value</code>
 in the tag of the last started element.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeFloatAttribute-java.lang.String-java.lang.Float-">writeFloatAttribute</a></span>(java.lang.String&nbsp;name,
                   java.lang.Float&nbsp;value)</code>
<div class="block">Writes the name and the float value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>null</code>.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeFloatAttribute-java.lang.String-float-float-">writeFloatAttribute</a></span>(java.lang.String&nbsp;name,
                   float&nbsp;value,
                   float&nbsp;defaultValue)</code>
<div class="block">Writes the name and the float value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>defaultValue</code>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeIntegerAttribute-java.lang.String-int-">writeIntegerAttribute</a></span>(java.lang.String&nbsp;name,
                     int&nbsp;value)</code>
<div class="block">Writes the attribute of the given <code>name</code> with its integer <code>value</code>
 in the tag of the last started element.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeIntegerAttribute-java.lang.String-int-int-">writeIntegerAttribute</a></span>(java.lang.String&nbsp;name,
                     int&nbsp;value,
                     int&nbsp;defaultValue)</code>
<div class="block">Writes the name and the integer value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>defaultValue</code>.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeLongAttribute-java.lang.String-long-">writeLongAttribute</a></span>(java.lang.String&nbsp;name,
                  long&nbsp;value)</code>
<div class="block">Writes the attribute of the given <code>name</code> with its long <code>value</code>
 in the tag of the last started element.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeLongAttribute-java.lang.String-java.lang.Long-">writeLongAttribute</a></span>(java.lang.String&nbsp;name,
                  java.lang.Long&nbsp;value)</code>
<div class="block">Writes the name and the long value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>null</code>.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeStartElement-java.lang.String-">writeStartElement</a></span>(java.lang.String&nbsp;element)</code>
<div class="block">Writes a start tag for the given element.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html#writeText-java.lang.String-">writeText</a></span>(java.lang.String&nbsp;text)</code>
<div class="block">Writes the given <code>text</code> as the content of the current element.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.FilterWriter">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.io.FilterWriter</h3>
<code>close, flush</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.Writer">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.io.Writer</h3>
<code>append, append, append, write, write</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="XMLWriter-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>XMLWriter</h4>
<pre>public&nbsp;XMLWriter(java.io.OutputStream&nbsp;out)
          throws java.io.IOException</pre>
<div class="block">Creates a writer in the given output stream encoded in UTF-8.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="writeStartElement-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeStartElement</h4>
<pre>public&nbsp;void&nbsp;writeStartElement(java.lang.String&nbsp;element)
                       throws java.io.IOException</pre>
<div class="block">Writes a start tag for the given element.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeEndElement--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeEndElement</h4>
<pre>public&nbsp;void&nbsp;writeEndElement()
                     throws java.io.IOException</pre>
<div class="block">Writes an end tag for the given element.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeAttribute-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAttribute</h4>
<pre>public&nbsp;void&nbsp;writeAttribute(java.lang.String&nbsp;name,
                           java.lang.String&nbsp;value)
                    throws java.io.IOException</pre>
<div class="block">Writes the attribute of the given <code>name</code> with its <code>value</code>
 in the tag of the last started element.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeAttribute-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAttribute</h4>
<pre>public&nbsp;void&nbsp;writeAttribute(java.lang.String&nbsp;name,
                           java.lang.String&nbsp;value,
                           java.lang.String&nbsp;defaultValue)
                    throws java.io.IOException</pre>
<div class="block">Writes the name and the value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>defaultValue</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeIntegerAttribute-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeIntegerAttribute</h4>
<pre>public&nbsp;void&nbsp;writeIntegerAttribute(java.lang.String&nbsp;name,
                                  int&nbsp;value)
                           throws java.io.IOException</pre>
<div class="block">Writes the attribute of the given <code>name</code> with its integer <code>value</code>
 in the tag of the last started element.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeIntegerAttribute-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeIntegerAttribute</h4>
<pre>public&nbsp;void&nbsp;writeIntegerAttribute(java.lang.String&nbsp;name,
                                  int&nbsp;value,
                                  int&nbsp;defaultValue)
                           throws java.io.IOException</pre>
<div class="block">Writes the name and the integer value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>defaultValue</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeLongAttribute-java.lang.String-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeLongAttribute</h4>
<pre>public&nbsp;void&nbsp;writeLongAttribute(java.lang.String&nbsp;name,
                               long&nbsp;value)
                        throws java.io.IOException</pre>
<div class="block">Writes the attribute of the given <code>name</code> with its long <code>value</code>
 in the tag of the last started element.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeLongAttribute-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeLongAttribute</h4>
<pre>public&nbsp;void&nbsp;writeLongAttribute(java.lang.String&nbsp;name,
                               java.lang.Long&nbsp;value)
                        throws java.io.IOException</pre>
<div class="block">Writes the name and the long value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>null</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeFloatAttribute-java.lang.String-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeFloatAttribute</h4>
<pre>public&nbsp;void&nbsp;writeFloatAttribute(java.lang.String&nbsp;name,
                                float&nbsp;value)
                         throws java.io.IOException</pre>
<div class="block">Writes the attribute of the given <code>name</code> with its float <code>value</code>
 in the tag of the last started element.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeFloatAttribute-java.lang.String-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeFloatAttribute</h4>
<pre>public&nbsp;void&nbsp;writeFloatAttribute(java.lang.String&nbsp;name,
                                float&nbsp;value,
                                float&nbsp;defaultValue)
                         throws java.io.IOException</pre>
<div class="block">Writes the name and the float value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>defaultValue</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeFloatAttribute-java.lang.String-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeFloatAttribute</h4>
<pre>public&nbsp;void&nbsp;writeFloatAttribute(java.lang.String&nbsp;name,
                                java.lang.Float&nbsp;value)
                         throws java.io.IOException</pre>
<div class="block">Writes the name and the float value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>null</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeBigDecimalAttribute-java.lang.String-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeBigDecimalAttribute</h4>
<pre>public&nbsp;void&nbsp;writeBigDecimalAttribute(java.lang.String&nbsp;name,
                                     java.math.BigDecimal&nbsp;value)
                              throws java.io.IOException</pre>
<div class="block">Writes the name and the value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>null</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeBooleanAttribute-java.lang.String-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeBooleanAttribute</h4>
<pre>public&nbsp;void&nbsp;writeBooleanAttribute(java.lang.String&nbsp;name,
                                  boolean&nbsp;value,
                                  boolean&nbsp;defaultValue)
                           throws java.io.IOException</pre>
<div class="block">Writes the name and the boolean value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>defaultValue</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeColorAttribute-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeColorAttribute</h4>
<pre>public&nbsp;void&nbsp;writeColorAttribute(java.lang.String&nbsp;name,
                                java.lang.Integer&nbsp;color)
                         throws java.io.IOException</pre>
<div class="block">Writes the name and the color value of an attribute in the tag of the last started element,
 except if <code>value</code> equals <code>null</code>. The color is written in hexadecimal.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeText-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeText</h4>
<pre>public&nbsp;void&nbsp;writeText(java.lang.String&nbsp;text)
               throws java.io.IOException</pre>
<div class="block">Writes the given <code>text</code> as the content of the current element.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="write-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(int&nbsp;c)
           throws java.io.IOException</pre>
<div class="block">Writes the given character as the content of the current element.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>write</code>&nbsp;in class&nbsp;<code>java.io.FilterWriter</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="write-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(char[]&nbsp;buffer,
                  int&nbsp;offset,
                  int&nbsp;length)
           throws java.io.IOException</pre>
<div class="block">Writes the given characters array as the content of the current element.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>write</code>&nbsp;in class&nbsp;<code>java.io.FilterWriter</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="write-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(java.lang.String&nbsp;str,
                  int&nbsp;offset,
                  int&nbsp;length)
           throws java.io.IOException</pre>
<div class="block">Writes the given string as the content of the current element.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>write</code>&nbsp;in class&nbsp;<code>java.io.FilterWriter</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/XMLWriter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/XMLWriter.html" target="_top">Frames</a></li>
<li><a href="XMLWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.FilterWriter">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
