# ApplicationPlugin_de.properties
# Sweet Home 3D AI Plugin Configuration - German
# Copyright (c) 2025 Samuel <PERSON>na

# Plugin identification
name=KI-Grundrissanalyse
description=Analysieren Sie Grundrisse mit künstlicher Intelligenz, um Einblicke und Verbesserungsvorschläge zu erhalten
provider=<PERSON>

# Features
features=<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Datenschutzkontrollen

# Requirements
requirements=Internetverbindung für Cloud-KI-Anbieter (optional für lokale Anbieter)

# UI Strings for internationalization
# Action properties
AIAction.Name=KI-Analyse
AIAction.ShortDescription=Grundriss mit KI analysieren
AIAction.Menu=Werkzeuge

# Dialog titles
AIChatDialog.title=KI-Grundrissanalyse
AISettingsDialog.title=KI-Einstellungen

# Button labels
button.send=Senden
button.newAnalysis=Neue Analyse
button.settings=Einstellungen
button.testConnection=Verbindung testen
button.save=Speichern
button.cancel=Abbrechen

# Labels
label.provider=Anbieter:
label.baseUrl=Basis-URL:
label.apiKey=API-Schlüssel:
label.model=Modell:
label.temperature=Temperatur:
label.maxTokens=Max. Tokens:
label.status=Status:

# Messages
message.analyzing=Grundriss wird analysiert...
message.processingQuestion=Frage wird verarbeitet...
message.testingConnection=Verbindung wird getestet...
message.connectionSuccessful=Verbindung erfolgreich!
message.connectionFailed=Verbindung fehlgeschlagen: {0}
message.configurationSaved=Konfiguration erfolgreich gespeichert
message.validationError=Konfigurationsfehler:\n{0}
message.noConfiguration=KI-Anbieter nicht konfiguriert. Bitte konfigurieren Sie zuerst die Einstellungen.

# Analysis prompt
analysis.prompt=Bitte analysieren Sie diesen Grundriss und geben Sie umfassende Einblicke, einschließlich:\n1. Layout-Effizienz und Raumnutzung\n2. Verkehrsfluss und Zirkulationsmuster\n3. Natürliche Beleuchtungs- und Belüftungsmöglichkeiten\n4. Barrierefreiheitsüberlegungen\n5. Funktionale Beziehungen zwischen Räumen\n6. Verbesserungsvorschläge\n7. Einhaltung gängiger Baustandards\n8. Energieeffizienzüberlegungen\n\nBitte geben Sie spezifische, umsetzbare Empfehlungen, die die Funktionalität, den Komfort und die ästhetische Attraktivität dieses Raums verbessern würden.

# Error messages
error.analysisError=Analysefehler: {0}
error.configurationError=Konfigurationsfehler: {0}
error.connectionError=Verbindungsfehler: {0}
error.invalidConfiguration=Ungültige Konfiguration
error.missingApiKey=API-Schlüssel ist erforderlich
error.missingBaseUrl=Basis-URL ist erforderlich
error.missingModel=Modellauswahl ist erforderlich

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Lokal)
provider.lmstudio=LM Studio (Lokal)
provider.anythingllm=AnythingLLM (Lokal)
provider.jan=Jan (Lokal)
provider.custom=Benutzerdefiniert
