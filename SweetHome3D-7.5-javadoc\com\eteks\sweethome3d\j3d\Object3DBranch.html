<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>Object3DBranch (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Object3DBranch (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":42,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Object3DBranch.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranchFactory.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/Object3DBranch.html" target="_top">Frames</a></li>
<li><a href="Object3DBranch.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class Object3DBranch" class="title">Class Object3DBranch</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.SceneGraphObject</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.Node</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.Group</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.BranchGroup</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.Object3DBranch</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/j3d/DimensionLine3D.html" title="class in com.eteks.sweethome3d.j3d">DimensionLine3D</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Ground3D.html" title="class in com.eteks.sweethome3d.j3d">Ground3D</a>, <a href="../../../../com/eteks/sweethome3d/j3d/HomePieceOfFurniture3D.html" title="class in com.eteks.sweethome3d.j3d">HomePieceOfFurniture3D</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Label3D.html" title="class in com.eteks.sweethome3d.j3d">Label3D</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Polyline3D.html" title="class in com.eteks.sweethome3d.j3d">Polyline3D</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Room3D.html" title="class in com.eteks.sweethome3d.j3d">Room3D</a>, <a href="../../../../com/eteks/sweethome3d/j3d/Wall3D.html" title="class in com.eteks.sweethome3d.j3d">Wall3D</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">Object3DBranch</span>
extends javax.media.j3d.BranchGroup</pre>
<div class="block">Root of a branch that matches a home object.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#DEFAULT_AMBIENT_COLOR">DEFAULT_AMBIENT_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#DEFAULT_COLOR">DEFAULT_COLOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static javax.media.j3d.Material</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#DEFAULT_MATERIAL">DEFAULT_MATERIAL</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#LINE_WIDTH_SCALE_FACTOR">LINE_WIDTH_SCALE_FACTOR</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static javax.media.j3d.ColoringAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#OUTLINE_COLORING_ATTRIBUTES">OUTLINE_COLORING_ATTRIBUTES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static javax.media.j3d.LineAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#OUTLINE_LINE_ATTRIBUTES">OUTLINE_LINE_ATTRIBUTES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static javax.media.j3d.PolygonAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#OUTLINE_POLYGON_ATTRIBUTES">OUTLINE_POLYGON_ATTRIBUTES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static javax.media.j3d.ColoringAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#SELECTION_COLORING_ATTRIBUTES">SELECTION_COLORING_ATTRIBUTES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static javax.media.j3d.LineAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#SELECTION_LINE_ATTRIBUTES">SELECTION_LINE_ATTRIBUTES</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static javax.media.j3d.PolygonAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#SELECTION_POLYGON_ATTRIBUTES">SELECTION_POLYGON_ATTRIBUTES</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected static javax.media.j3d.TransparencyAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#SELECTION_TRANSPARENCY_ATTRIBUTES">SELECTION_TRANSPARENCY_ATTRIBUTES</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.media.j3d.BranchGroup">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.media.j3d.BranchGroup</h3>
<code>ALLOW_DETACH</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.media.j3d.Group">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.media.j3d.Group</h3>
<code>ALLOW_CHILDREN_EXTEND, ALLOW_CHILDREN_READ, ALLOW_CHILDREN_WRITE, ALLOW_COLLISION_BOUNDS_READ, ALLOW_COLLISION_BOUNDS_WRITE</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.media.j3d.Node">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.media.j3d.Node</h3>
<code>ALLOW_AUTO_COMPUTE_BOUNDS_READ, ALLOW_AUTO_COMPUTE_BOUNDS_WRITE, ALLOW_BOUNDS_READ, ALLOW_BOUNDS_WRITE, ALLOW_COLLIDABLE_READ, ALLOW_COLLIDABLE_WRITE, ALLOW_LOCAL_TO_VWORLD_READ, ALLOW_LOCALE_READ, ALLOW_PARENT_READ, ALLOW_PICKABLE_READ, ALLOW_PICKABLE_WRITE, ENABLE_COLLISION_REPORTING, ENABLE_PICK_REPORTING</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#Object3DBranch--">Object3DBranch</a></span>()</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#Object3DBranch-java.lang.Object-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-">Object3DBranch</a></span>(java.lang.Object&nbsp;item,
              <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              java.lang.Object&nbsp;context)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;float[][]&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getAreaPoints-java.awt.geom.Area-float-boolean-">getAreaPoints</a></span>(java.awt.geom.Area&nbsp;area,
             float&nbsp;flatness,
             boolean&nbsp;reversed)</code>
<div class="block">Returns the list of polygons points matching the given <code>area</code>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected java.util.List&lt;float[][]&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getAreaPoints-java.awt.geom.Area-java.util.List-java.util.List-float-boolean-">getAreaPoints</a></span>(java.awt.geom.Area&nbsp;area,
             java.util.List&lt;float[][]&gt;&nbsp;areaPoints,
             java.util.List&lt;float[][]&gt;&nbsp;areaHoles,
             float&nbsp;flatness,
             boolean&nbsp;reversed)</code>
<div class="block">Returns the list of polygons points matching the given <code>area</code> with detailed information in
 <code>areaPoints</code> and <code>areaHoles</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getContext--">getContext</a></span>()</code>
<div class="block">Returns the context in which this object is used.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected javax.media.j3d.Texture</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getContextTexture-javax.media.j3d.Texture-java.lang.Object-">getContextTexture</a></span>(javax.media.j3d.Texture&nbsp;texture,
                 java.lang.Object&nbsp;context)</code>
<div class="block">Returns a cloned instance of texture shared per <code>context</code> or
 the texture itself if <code>context</code> is <code>null</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getHome--">getHome</a></span>()</code>
<div class="block">Returns home instance or <code>null</code>.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected javax.media.j3d.Texture</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getHomeTextureClone-javax.media.j3d.Texture-com.eteks.sweethome3d.model.Home-">getHomeTextureClone</a></span>(javax.media.j3d.Texture&nbsp;texture,
                   <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">Use <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getContextTexture-javax.media.j3d.Texture-java.lang.Object-"><code>getContextTexture(Texture, Object)</code></a> which context
    parameter may be equal to different contexts for a given home</span></div>
</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected javax.media.j3d.Material</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getMaterial-java.lang.Integer-java.lang.Integer-float-">getMaterial</a></span>(java.lang.Integer&nbsp;diffuseColor,
           java.lang.Integer&nbsp;ambientColor,
           float&nbsp;shininess)</code>
<div class="block">Returns a shared material instance matching the given color.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected javax.media.j3d.Appearance</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getSelectionAppearance--">getSelectionAppearance</a></span>()</code>
<div class="block">Returns an appearance for selection shapes.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected java.awt.Shape</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getShape-float:A:A-">getShape</a></span>(float[][]&nbsp;points)</code>
<div class="block">Returns the closed shape matching the coordinates in <code>points</code> array.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected javax.media.j3d.TextureAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributes-com.eteks.sweethome3d.model.HomeTexture-">getTextureAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Returns shared texture attributes matching transformation applied to the given texture.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>protected javax.media.j3d.TextureAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributes-com.eteks.sweethome3d.model.HomeTexture-boolean-">getTextureAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
                    boolean&nbsp;scaled)</code>
<div class="block">Returns shared texture attributes matching transformation applied to the given texture
 and scaled if required.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>protected javax.media.j3d.TextureAttributes</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributesFittingArea-com.eteks.sweethome3d.model.HomeTexture-float:A:A-boolean-">getTextureAttributesFittingArea</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
                               float[][]&nbsp;areaPoints,
                               boolean&nbsp;invertY)</code>
<div class="block">Returns texture attributes with a transformation scaled to fit the surface matching <code>areaPoints</code>.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getUserPreferences--">getUserPreferences</a></span>()</code>
<div class="block">Returns user preferences.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#update--">update</a></span>()</code>
<div class="block">Updates this branch from the home object.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.BranchGroup">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.BranchGroup</h3>
<code>cloneNode, compile, detach, pickAll, pickAll, pickAllSorted, pickAllSorted, pickAny, pickAny, pickClosest, pickClosest</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.Group">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.Group</h3>
<code>addChild, getAllChildren, getAlternateCollisionTarget, getChild, getCollisionBounds, indexOfChild, insertChild, moveTo, numChildren, removeAllChildren, removeChild, removeChild, setAlternateCollisionTarget, setChild, setCollisionBounds</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.Node">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.Node</h3>
<code>cloneTree, cloneTree, cloneTree, cloneTree, cloneTree, cloneTree, duplicateNode, getBounds, getBoundsAutoCompute, getCollidable, getLocale, getLocalToVworld, getLocalToVworld, getParent, getPickable, setBounds, setBoundsAutoCompute, setCollidable, setPickable</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.SceneGraphObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.SceneGraphObject</h3>
<code>clearCapability, clearCapabilityIsFrequent, duplicateSceneGraphObject, getCapability, getCapabilityIsFrequent, getName, getUserData, isCompiled, isLive, setCapability, setCapabilityIsFrequent, setName, setUserData, toString, updateNodeReferences</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="LINE_WIDTH_SCALE_FACTOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LINE_WIDTH_SCALE_FACTOR</h4>
<pre>protected static final&nbsp;float LINE_WIDTH_SCALE_FACTOR</pre>
</li>
</ul>
<a name="OUTLINE_COLORING_ATTRIBUTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_COLORING_ATTRIBUTES</h4>
<pre>protected static final&nbsp;javax.media.j3d.ColoringAttributes OUTLINE_COLORING_ATTRIBUTES</pre>
</li>
</ul>
<a name="OUTLINE_POLYGON_ATTRIBUTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_POLYGON_ATTRIBUTES</h4>
<pre>protected static final&nbsp;javax.media.j3d.PolygonAttributes OUTLINE_POLYGON_ATTRIBUTES</pre>
</li>
</ul>
<a name="OUTLINE_LINE_ATTRIBUTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OUTLINE_LINE_ATTRIBUTES</h4>
<pre>protected static final&nbsp;javax.media.j3d.LineAttributes OUTLINE_LINE_ATTRIBUTES</pre>
</li>
</ul>
<a name="SELECTION_COLORING_ATTRIBUTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SELECTION_COLORING_ATTRIBUTES</h4>
<pre>protected static final&nbsp;javax.media.j3d.ColoringAttributes SELECTION_COLORING_ATTRIBUTES</pre>
</li>
</ul>
<a name="SELECTION_POLYGON_ATTRIBUTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SELECTION_POLYGON_ATTRIBUTES</h4>
<pre>protected static final&nbsp;javax.media.j3d.PolygonAttributes SELECTION_POLYGON_ATTRIBUTES</pre>
</li>
</ul>
<a name="SELECTION_LINE_ATTRIBUTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SELECTION_LINE_ATTRIBUTES</h4>
<pre>protected static final&nbsp;javax.media.j3d.LineAttributes SELECTION_LINE_ATTRIBUTES</pre>
</li>
</ul>
<a name="SELECTION_TRANSPARENCY_ATTRIBUTES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SELECTION_TRANSPARENCY_ATTRIBUTES</h4>
<pre>protected static final&nbsp;javax.media.j3d.TransparencyAttributes SELECTION_TRANSPARENCY_ATTRIBUTES</pre>
</li>
</ul>
<a name="DEFAULT_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_COLOR</h4>
<pre>protected static final&nbsp;java.lang.Integer DEFAULT_COLOR</pre>
</li>
</ul>
<a name="DEFAULT_AMBIENT_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFAULT_AMBIENT_COLOR</h4>
<pre>protected static final&nbsp;java.lang.Integer DEFAULT_AMBIENT_COLOR</pre>
</li>
</ul>
<a name="DEFAULT_MATERIAL">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DEFAULT_MATERIAL</h4>
<pre>protected static final&nbsp;javax.media.j3d.Material DEFAULT_MATERIAL</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Object3DBranch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Object3DBranch</h4>
<pre>public&nbsp;Object3DBranch()</pre>
</li>
</ul>
<a name="Object3DBranch-java.lang.Object-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-java.lang.Object-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Object3DBranch</h4>
<pre>public&nbsp;Object3DBranch(java.lang.Object&nbsp;item,
                      <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.lang.Object&nbsp;context)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getHome--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHome</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;getHome()</pre>
<div class="block">Returns home instance or <code>null</code>.</div>
</li>
</ul>
<a name="getUserPreferences--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUserPreferences</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;getUserPreferences()</pre>
<div class="block">Returns user preferences.</div>
</li>
</ul>
<a name="getContext--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContext</h4>
<pre>public&nbsp;java.lang.Object&nbsp;getContext()</pre>
<div class="block">Returns the context in which this object is used.</div>
</li>
</ul>
<a name="update--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>update</h4>
<pre>public abstract&nbsp;void&nbsp;update()</pre>
<div class="block">Updates this branch from the home object.</div>
</li>
</ul>
<a name="getHomeTextureClone-javax.media.j3d.Texture-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHomeTextureClone</h4>
<pre>protected&nbsp;javax.media.j3d.Texture&nbsp;getHomeTextureClone(javax.media.j3d.Texture&nbsp;texture,
                                                      <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">Use <a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getContextTexture-javax.media.j3d.Texture-java.lang.Object-"><code>getContextTexture(Texture, Object)</code></a> which context
    parameter may be equal to different contexts for a given home</span></div>
<div class="block">Returns a cloned instance of texture shared per <code>home</code> or
 the texture itself if <code>home</code> is <code>null</code>.
 As sharing textures across universes might cause some problems,
 it's safer to handle a copy of textures for a given home.</div>
</li>
</ul>
<a name="getContextTexture-javax.media.j3d.Texture-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContextTexture</h4>
<pre>protected&nbsp;javax.media.j3d.Texture&nbsp;getContextTexture(javax.media.j3d.Texture&nbsp;texture,
                                                    java.lang.Object&nbsp;context)</pre>
<div class="block">Returns a cloned instance of texture shared per <code>context</code> or
 the texture itself if <code>context</code> is <code>null</code>.
 As sharing textures across universes might cause some problems,
 it's safer to handle a copy of textures for a given context.</div>
</li>
</ul>
<a name="getShape-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShape</h4>
<pre>protected&nbsp;java.awt.Shape&nbsp;getShape(float[][]&nbsp;points)</pre>
<div class="block">Returns the closed shape matching the coordinates in <code>points</code> array.</div>
</li>
</ul>
<a name="getMaterial-java.lang.Integer-java.lang.Integer-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMaterial</h4>
<pre>protected&nbsp;javax.media.j3d.Material&nbsp;getMaterial(java.lang.Integer&nbsp;diffuseColor,
                                               java.lang.Integer&nbsp;ambientColor,
                                               float&nbsp;shininess)</pre>
<div class="block">Returns a shared material instance matching the given color.</div>
</li>
</ul>
<a name="getTextureAttributes-com.eteks.sweethome3d.model.HomeTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextureAttributes</h4>
<pre>protected&nbsp;javax.media.j3d.TextureAttributes&nbsp;getTextureAttributes(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</pre>
<div class="block">Returns shared texture attributes matching transformation applied to the given texture.</div>
</li>
</ul>
<a name="getTextureAttributes-com.eteks.sweethome3d.model.HomeTexture-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextureAttributes</h4>
<pre>protected&nbsp;javax.media.j3d.TextureAttributes&nbsp;getTextureAttributes(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
                                                                 boolean&nbsp;scaled)</pre>
<div class="block">Returns shared texture attributes matching transformation applied to the given texture
 and scaled if required.</div>
</li>
</ul>
<a name="getTextureAttributesFittingArea-com.eteks.sweethome3d.model.HomeTexture-float:A:A-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextureAttributesFittingArea</h4>
<pre>protected&nbsp;javax.media.j3d.TextureAttributes&nbsp;getTextureAttributesFittingArea(<a href="../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
                                                                            float[][]&nbsp;areaPoints,
                                                                            boolean&nbsp;invertY)</pre>
<div class="block">Returns texture attributes with a transformation scaled to fit the surface matching <code>areaPoints</code>.</div>
</li>
</ul>
<a name="getSelectionAppearance--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSelectionAppearance</h4>
<pre>protected&nbsp;javax.media.j3d.Appearance&nbsp;getSelectionAppearance()</pre>
<div class="block">Returns an appearance for selection shapes.</div>
</li>
</ul>
<a name="getAreaPoints-java.awt.geom.Area-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaPoints</h4>
<pre>protected&nbsp;java.util.List&lt;float[][]&gt;&nbsp;getAreaPoints(java.awt.geom.Area&nbsp;area,
                                                  float&nbsp;flatness,
                                                  boolean&nbsp;reversed)</pre>
<div class="block">Returns the list of polygons points matching the given <code>area</code>.</div>
</li>
</ul>
<a name="getAreaPoints-java.awt.geom.Area-java.util.List-java.util.List-float-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getAreaPoints</h4>
<pre>protected&nbsp;java.util.List&lt;float[][]&gt;&nbsp;getAreaPoints(java.awt.geom.Area&nbsp;area,
                                                  java.util.List&lt;float[][]&gt;&nbsp;areaPoints,
                                                  java.util.List&lt;float[][]&gt;&nbsp;areaHoles,
                                                  float&nbsp;flatness,
                                                  boolean&nbsp;reversed)</pre>
<div class="block">Returns the list of polygons points matching the given <code>area</code> with detailed information in
 <code>areaPoints</code> and <code>areaHoles</code>.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Object3DBranch.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/ModelManager.ModelObserver.html" title="interface in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/Object3DBranchFactory.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/Object3DBranch.html" target="_top">Frames</a></li>
<li><a href="Object3DBranch.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
