# ApplicationPlugin_es.properties
# Sweet Home 3D AI Plugin Configuration - Spanish
# Copyright (c) 2025 Samuel Kpassegna

# Plugin identification
name=Análisis IA de Planos
description=Analice planos de planta usando inteligencia artificial para proporcionar perspectivas y sugerencias de mejora
provider=<PERSON>

# Features
features=Análisis IA, Perspectivas de Planos, Múltiples Proveedores IA, Controles de Privacidad

# Requirements
requirements=Conexión a Internet para proveedores IA en la nube (opcional para proveedores locales)

# UI Strings for internationalization
# Action properties
AIAction.Name=Análisis IA
AIAction.ShortDescription=Analizar plano con IA
AIAction.Menu=Herramientas

# Dialog titles
AIChatDialog.title=Análisis IA de Planos
AISettingsDialog.title=Configuración IA

# Button labels
button.send=Enviar
button.newAnalysis=Nuevo Análisis
button.settings=Configuración
button.testConnection=Probar Conexión
button.save=Guardar
button.cancel=Cancelar

# Labels
label.provider=Proveedor:
label.baseUrl=URL Base:
label.apiKey=Clave API:
label.model=Modelo:
label.temperature=Temperatura:
label.maxTokens=Tokens Máx:
label.status=Estado:

# Messages
message.analyzing=Analizando plano...
message.processingQuestion=Procesando pregunta...
message.testingConnection=Probando conexión...
message.connectionSuccessful=¡Conexión exitosa!
message.connectionFailed=Conexión fallida: {0}
message.configurationSaved=Configuración guardada exitosamente
message.validationError=Errores de configuración:\n{0}
message.noConfiguration=Proveedor IA no configurado. Por favor configure primero los ajustes.

# Analysis prompt
analysis.prompt=Por favor analice este plano y proporcione perspectivas integrales incluyendo:\n1. Eficiencia del diseño y utilización del espacio\n2. Flujo de tráfico y patrones de circulación\n3. Oportunidades de iluminación natural y ventilación\n4. Consideraciones de accesibilidad\n5. Relaciones funcionales entre espacios\n6. Sugerencias de mejora\n7. Cumplimiento con estándares de construcción comunes\n8. Consideraciones de eficiencia energética\n\nPor favor proporcione recomendaciones específicas y accionables que mejorarían la funcionalidad, comodidad y atractivo estético de este espacio.

# Error messages
error.analysisError=Error de análisis: {0}
error.configurationError=Error de configuración: {0}
error.connectionError=Error de conexión: {0}
error.invalidConfiguration=Configuración inválida
error.missingApiKey=Se requiere clave API
error.missingBaseUrl=Se requiere URL base
error.missingModel=Se requiere selección de modelo

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Local)
provider.lmstudio=LM Studio (Local)
provider.anythingllm=AnythingLLM (Local)
provider.jan=Jan (Local)
provider.custom=Personalizado
