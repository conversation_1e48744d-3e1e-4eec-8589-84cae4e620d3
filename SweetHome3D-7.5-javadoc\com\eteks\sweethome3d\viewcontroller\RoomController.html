<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>RoomController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RoomController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RoomController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/RoomController.html" target="_top">Frames</a></li>
<li><a href="RoomController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class RoomController" class="title">Class RoomController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.RoomController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">RoomController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for room view.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.Property</a></span></code>
<div class="block">The properties that may be edited by the view associated to this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a></span></code>
<div class="block">The possible values for <a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getFloorPaint--">room paint type</a>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#RoomController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">RoomController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
              <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
              javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of room view with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.RoomController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getAreaVisible--">getAreaVisible</a></span>()</code>
<div class="block">Returns whether room area is visible or not.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getCeilingColor--">getCeilingColor</a></span>()</code>
<div class="block">Returns the edited color of the ceiling.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getCeilingFlat--">getCeilingFlat</a></span>()</code>
<div class="block">Returns whether room ceiling should remain flat whatever its environment or not.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getCeilingPaint--">getCeilingPaint</a></span>()</code>
<div class="block">Returns whether the ceiling is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getCeilingShininess--">getCeilingShininess</a></span>()</code>
<div class="block">Returns the edited shininess of the ceiling.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getCeilingTextureController--">getCeilingTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the room ceiling.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getCeilingVisible--">getCeilingVisible</a></span>()</code>
<div class="block">Returns whether room ceiling is visible or not.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getFloorColor--">getFloorColor</a></span>()</code>
<div class="block">Returns the edited color of the floor.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getFloorPaint--">getFloorPaint</a></span>()</code>
<div class="block">Returns whether the floor is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getFloorShininess--">getFloorShininess</a></span>()</code>
<div class="block">Returns the edited shininess of the floor.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getFloorTextureController--">getFloorTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the room floor.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.lang.Boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getFloorVisible--">getFloorVisible</a></span>()</code>
<div class="block">Returns whether room floor is visible or not.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getName--">getName</a></span>()</code>
<div class="block">Returns the edited name.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getWallSidesBaseboardController--">getWallSidesBaseboardController</a></span>()</code>
<div class="block">Returns the controller of the wall sides baseboard.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getWallSidesColor--">getWallSidesColor</a></span>()</code>
<div class="block">Returns the edited color of the wall sides.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getWallSidesPaint--">getWallSidesPaint</a></span>()</code>
<div class="block">Returns whether the wall sides are colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>java.lang.Float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getWallSidesShininess--">getWallSidesShininess</a></span>()</code>
<div class="block">Returns the edited shininess of the wall sides.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#getWallSidesTextureController--">getWallSidesTextureController</a></span>()</code>
<div class="block">Returns the texture controller of the room wall sides.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#isPropertyEditable-com.eteks.sweethome3d.viewcontroller.RoomController.Property-">isPropertyEditable</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.Property</a>&nbsp;property)</code>
<div class="block">Returns <code>true</code> if the given <code>property</code> is editable.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#isSplitSurroundingWalls--">isSplitSurroundingWalls</a></span>()</code>
<div class="block">Returns <code>true</code> if walls around the edited rooms should be split.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#isSplitSurroundingWallsNeeded--">isSplitSurroundingWallsNeeded</a></span>()</code>
<div class="block">Returns <code>true</code> if walls around the edited rooms need to be split
 to avoid changing the color of wall sides that belong to neighborhood rooms.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#modifyRooms--">modifyRooms</a></span>()</code>
<div class="block">Controls the modification of selected rooms in edited home.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.RoomController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setAreaVisible-java.lang.Boolean-">setAreaVisible</a></span>(java.lang.Boolean&nbsp;areaVisible)</code>
<div class="block">Sets whether room area is visible or not.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setCeilingColor-java.lang.Integer-">setCeilingColor</a></span>(java.lang.Integer&nbsp;ceilingColor)</code>
<div class="block">Sets the edited color of the ceiling.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setCeilingFlat-java.lang.Boolean-">setCeilingFlat</a></span>(java.lang.Boolean&nbsp;ceilingCeilingFlat)</code>
<div class="block">Sets whether room ceiling should remain flat whatever its environment or not.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setCeilingPaint-com.eteks.sweethome3d.viewcontroller.RoomController.RoomPaint-">setCeilingPaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;ceilingPaint)</code>
<div class="block">Sets whether the ceiling is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setCeilingShininess-java.lang.Float-">setCeilingShininess</a></span>(java.lang.Float&nbsp;ceilingShininess)</code>
<div class="block">Sets the edited shininess of the ceiling.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setCeilingVisible-java.lang.Boolean-">setCeilingVisible</a></span>(java.lang.Boolean&nbsp;ceilingCeilingVisible)</code>
<div class="block">Sets whether room ceiling is visible or not.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setFloorColor-java.lang.Integer-">setFloorColor</a></span>(java.lang.Integer&nbsp;floorColor)</code>
<div class="block">Sets the edited color of the floor.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setFloorPaint-com.eteks.sweethome3d.viewcontroller.RoomController.RoomPaint-">setFloorPaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;floorPaint)</code>
<div class="block">Sets whether the floor is colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setFloorShininess-java.lang.Float-">setFloorShininess</a></span>(java.lang.Float&nbsp;floorShininess)</code>
<div class="block">Sets the edited shininess of the floor.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setFloorVisible-java.lang.Boolean-">setFloorVisible</a></span>(java.lang.Boolean&nbsp;floorVisible)</code>
<div class="block">Sets whether room floor is visible or not.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Sets the edited name.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setSplitSurroundingWalls-boolean-">setSplitSurroundingWalls</a></span>(boolean&nbsp;splitSurroundingWalls)</code>
<div class="block">Sets whether walls around the edited rooms should be split or not.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setWallSidesColor-java.lang.Integer-">setWallSidesColor</a></span>(java.lang.Integer&nbsp;wallSidesColor)</code>
<div class="block">Sets the edited color of the wall sides.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setWallSidesPaint-com.eteks.sweethome3d.viewcontroller.RoomController.RoomPaint-">setWallSidesPaint</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;wallSidesPaint)</code>
<div class="block">Sets whether the wall sides are colored, textured or unknown painted.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#setWallSidesShininess-java.lang.Float-">setWallSidesShininess</a></span>(java.lang.Float&nbsp;wallSidesShininess)</code>
<div class="block">Sets the edited shininess of the wall sides.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.html#updateProperties--">updateProperties</a></span>()</code>
<div class="block">Updates edited properties from selected rooms in the home edited by this controller.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RoomController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RoomController</h4>
<pre>public&nbsp;RoomController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                      <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                      <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                      javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of room view with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getFloorTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloorTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getFloorTextureController()</pre>
<div class="block">Returns the texture controller of the room floor.</div>
</li>
</ul>
<a name="getCeilingTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getCeilingTextureController()</pre>
<div class="block">Returns the texture controller of the room ceiling.</div>
</li>
</ul>
<a name="getWallSidesTextureController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallSidesTextureController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">TextureChoiceController</a>&nbsp;getWallSidesTextureController()</pre>
<div class="block">Returns the texture controller of the room wall sides.</div>
</li>
</ul>
<a name="getWallSidesBaseboardController--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallSidesBaseboardController</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/BaseboardChoiceController.html" title="class in com.eteks.sweethome3d.viewcontroller">BaseboardChoiceController</a>&nbsp;getWallSidesBaseboardController()</pre>
<div class="block">Returns the controller of the wall sides baseboard.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.RoomController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.RoomController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="isPropertyEditable-com.eteks.sweethome3d.viewcontroller.RoomController.Property-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPropertyEditable</h4>
<pre>public&nbsp;boolean&nbsp;isPropertyEditable(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.Property</a>&nbsp;property)</pre>
<div class="block">Returns <code>true</code> if the given <code>property</code> is editable.
 Depending on whether a property is editable or not, the view associated to this controller
 may render it differently.
 The implementation of this method always returns <code>true</code> except for <code>WALL</code> properties.</div>
</li>
</ul>
<a name="updateProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateProperties</h4>
<pre>protected&nbsp;void&nbsp;updateProperties()</pre>
<div class="block">Updates edited properties from selected rooms in the home edited by this controller.</div>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the edited name.</div>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the edited name.</div>
</li>
</ul>
<a name="setAreaVisible-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAreaVisible</h4>
<pre>public&nbsp;void&nbsp;setAreaVisible(java.lang.Boolean&nbsp;areaVisible)</pre>
<div class="block">Sets whether room area is visible or not.</div>
</li>
</ul>
<a name="getAreaVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAreaVisible</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getAreaVisible()</pre>
<div class="block">Returns whether room area is visible or not.</div>
</li>
</ul>
<a name="setFloorVisible-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorVisible</h4>
<pre>public&nbsp;void&nbsp;setFloorVisible(java.lang.Boolean&nbsp;floorVisible)</pre>
<div class="block">Sets whether room floor is visible or not.</div>
</li>
</ul>
<a name="getFloorVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloorVisible</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getFloorVisible()</pre>
<div class="block">Returns whether room floor is visible or not.</div>
</li>
</ul>
<a name="setFloorColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorColor</h4>
<pre>public&nbsp;void&nbsp;setFloorColor(java.lang.Integer&nbsp;floorColor)</pre>
<div class="block">Sets the edited color of the floor.</div>
</li>
</ul>
<a name="getFloorColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloorColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getFloorColor()</pre>
<div class="block">Returns the edited color of the floor.</div>
</li>
</ul>
<a name="setFloorPaint-com.eteks.sweethome3d.viewcontroller.RoomController.RoomPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorPaint</h4>
<pre>public&nbsp;void&nbsp;setFloorPaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;floorPaint)</pre>
<div class="block">Sets whether the floor is colored, textured or unknown painted.</div>
</li>
</ul>
<a name="getFloorPaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloorPaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;getFloorPaint()</pre>
<div class="block">Returns whether the floor is colored, textured or unknown painted.</div>
</li>
</ul>
<a name="setFloorShininess-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFloorShininess</h4>
<pre>public&nbsp;void&nbsp;setFloorShininess(java.lang.Float&nbsp;floorShininess)</pre>
<div class="block">Sets the edited shininess of the floor.</div>
</li>
</ul>
<a name="getFloorShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFloorShininess</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getFloorShininess()</pre>
<div class="block">Returns the edited shininess of the floor.</div>
</li>
</ul>
<a name="setCeilingVisible-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingVisible</h4>
<pre>public&nbsp;void&nbsp;setCeilingVisible(java.lang.Boolean&nbsp;ceilingCeilingVisible)</pre>
<div class="block">Sets whether room ceiling is visible or not.</div>
</li>
</ul>
<a name="getCeilingVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingVisible</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getCeilingVisible()</pre>
<div class="block">Returns whether room ceiling is visible or not.</div>
</li>
</ul>
<a name="setCeilingColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingColor</h4>
<pre>public&nbsp;void&nbsp;setCeilingColor(java.lang.Integer&nbsp;ceilingColor)</pre>
<div class="block">Sets the edited color of the ceiling.</div>
</li>
</ul>
<a name="getCeilingColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getCeilingColor()</pre>
<div class="block">Returns the edited color of the ceiling.</div>
</li>
</ul>
<a name="setCeilingPaint-com.eteks.sweethome3d.viewcontroller.RoomController.RoomPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingPaint</h4>
<pre>public&nbsp;void&nbsp;setCeilingPaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;ceilingPaint)</pre>
<div class="block">Sets whether the ceiling is colored, textured or unknown painted.</div>
</li>
</ul>
<a name="getCeilingPaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingPaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;getCeilingPaint()</pre>
<div class="block">Returns whether the ceiling is colored, textured or unknown painted.</div>
</li>
</ul>
<a name="setCeilingShininess-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingShininess</h4>
<pre>public&nbsp;void&nbsp;setCeilingShininess(java.lang.Float&nbsp;ceilingShininess)</pre>
<div class="block">Sets the edited shininess of the ceiling.</div>
</li>
</ul>
<a name="getCeilingShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingShininess</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getCeilingShininess()</pre>
<div class="block">Returns the edited shininess of the ceiling.</div>
</li>
</ul>
<a name="setCeilingFlat-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCeilingFlat</h4>
<pre>public&nbsp;void&nbsp;setCeilingFlat(java.lang.Boolean&nbsp;ceilingCeilingFlat)</pre>
<div class="block">Sets whether room ceiling should remain flat whatever its environment or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getCeilingFlat--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCeilingFlat</h4>
<pre>public&nbsp;java.lang.Boolean&nbsp;getCeilingFlat()</pre>
<div class="block">Returns whether room ceiling should remain flat whatever its environment or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="isSplitSurroundingWalls--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSplitSurroundingWalls</h4>
<pre>public&nbsp;boolean&nbsp;isSplitSurroundingWalls()</pre>
<div class="block">Returns <code>true</code> if walls around the edited rooms should be split.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setSplitSurroundingWalls-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSplitSurroundingWalls</h4>
<pre>public&nbsp;void&nbsp;setSplitSurroundingWalls(boolean&nbsp;splitSurroundingWalls)</pre>
<div class="block">Sets whether walls around the edited rooms should be split or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="isSplitSurroundingWallsNeeded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSplitSurroundingWallsNeeded</h4>
<pre>public&nbsp;boolean&nbsp;isSplitSurroundingWallsNeeded()</pre>
<div class="block">Returns <code>true</code> if walls around the edited rooms need to be split
 to avoid changing the color of wall sides that belong to neighborhood rooms.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setWallSidesColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallSidesColor</h4>
<pre>public&nbsp;void&nbsp;setWallSidesColor(java.lang.Integer&nbsp;wallSidesColor)</pre>
<div class="block">Sets the edited color of the wall sides.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getWallSidesColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallSidesColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getWallSidesColor()</pre>
<div class="block">Returns the edited color of the wall sides.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setWallSidesPaint-com.eteks.sweethome3d.viewcontroller.RoomController.RoomPaint-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallSidesPaint</h4>
<pre>public&nbsp;void&nbsp;setWallSidesPaint(<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;wallSidesPaint)</pre>
<div class="block">Sets whether the wall sides are colored, textured or unknown painted.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getWallSidesPaint--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallSidesPaint</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.RoomPaint.html" title="enum in com.eteks.sweethome3d.viewcontroller">RoomController.RoomPaint</a>&nbsp;getWallSidesPaint()</pre>
<div class="block">Returns whether the wall sides are colored, textured or unknown painted.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="setWallSidesShininess-java.lang.Float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWallSidesShininess</h4>
<pre>public&nbsp;void&nbsp;setWallSidesShininess(java.lang.Float&nbsp;wallSidesShininess)</pre>
<div class="block">Sets the edited shininess of the wall sides.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getWallSidesShininess--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWallSidesShininess</h4>
<pre>public&nbsp;java.lang.Float&nbsp;getWallSidesShininess()</pre>
<div class="block">Returns the edited shininess of the wall sides.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="modifyRooms--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>modifyRooms</h4>
<pre>public&nbsp;void&nbsp;modifyRooms()</pre>
<div class="block">Controls the modification of selected rooms in edited home.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RoomController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/PrintPreviewController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/RoomController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/RoomController.html" target="_top">Frames</a></li>
<li><a href="RoomController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
