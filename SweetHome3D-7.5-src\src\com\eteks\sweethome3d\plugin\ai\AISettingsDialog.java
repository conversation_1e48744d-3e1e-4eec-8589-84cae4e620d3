/*
 * AISettingsDialog.java
 *
 * Sweet Home 3D AI Plugin, Copyright (c) 2025 Samuel <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */
package com.eteks.sweethome3d.plugin.ai;

import com.eteks.sweethome3d.model.UserPreferences;
import com.eteks.sweethome3d.viewcontroller.HomeController;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.lang.ref.WeakReference;
import java.util.List;

/**
 * Dialog for configuring AI provider settings.
 * Allows users to select providers, configure endpoints, and test connections.
 * Uses SweetHome 3D's internationalization system for proper localization.
 *
 * <AUTHOR> Kpassegna
 */
public class AISettingsDialog extends JDialog {
  private final HomeController homeController;
  private final AIIntegration aiIntegration;
  private final UserPreferences preferences;
  private boolean configurationSaved = false;
  
  // UI Components
  private JComboBox<AIProviderPreset> providerComboBox;
  private JTextField baseUrlField;
  private JPasswordField apiKeyField;
  private JComboBox<String> modelComboBox;
  private JSlider temperatureSlider;
  private JSpinner maxTokensSpinner;
  private JButton testConnectionButton;
  private JButton saveButton;
  private JButton cancelButton;
  private JProgressBar progressBar;
  private JLabel statusLabel;
  
  /**
   * Creates a new AI settings dialog.
   */
  public AISettingsDialog(HomeController homeController, AIIntegration aiIntegration) {
    this.homeController = homeController;
    this.aiIntegration = aiIntegration;
    this.preferences = homeController.getUserPreferences();

    // Set localized title
    String title = getLocalizedString("AISettingsDialog.title", "AI Settings");
    setTitle(title);
    setModal(true);

    initializeComponents();
    layoutComponents();
    loadCurrentConfiguration();
    setupEventHandlers();

    setDefaultCloseOperation(DISPOSE_ON_CLOSE);
    pack();
    setLocationRelativeTo(SwingUtilities.getWindowAncestor(homeController.getView()));

    // Listen for language changes to update UI
    this.preferences.addPropertyChangeListener(UserPreferences.Property.LANGUAGE,
        new LanguageChangeListener(this));
  }

  /**
   * Preferences property listener bound to this dialog with a weak reference to avoid
   * strong link between preferences and this dialog.
   */
  private static class LanguageChangeListener implements PropertyChangeListener {
    private final WeakReference<AISettingsDialog> dialog;

    public LanguageChangeListener(AISettingsDialog dialog) {
      this.dialog = new WeakReference<AISettingsDialog>(dialog);
    }

    public void propertyChange(PropertyChangeEvent ev) {
      // If dialog was garbage collected, remove this listener from preferences
      AISettingsDialog dialog = this.dialog.get();
      if (dialog == null) {
        ((UserPreferences)ev.getSource()).removePropertyChangeListener(
            UserPreferences.Property.LANGUAGE, this);
      } else {
        dialog.updateLocalizedStrings();
      }
    }
  }

  /**
   * Returns a localized string for the given key.
   */
  private String getLocalizedString(String key, String defaultValue) {
    try {
      return preferences.getLocalizedString(AISettingsDialog.class, key);
    } catch (IllegalArgumentException ex) {
      return defaultValue;
    }
  }

  /**
   * Updates all localized strings in the UI.
   */
  private void updateLocalizedStrings() {
    setTitle(getLocalizedString("AISettingsDialog.title", "AI Settings"));
    testConnectionButton.setText(getLocalizedString("button.testConnection", "Test Connection"));
    saveButton.setText(getLocalizedString("button.save", "Save"));
    cancelButton.setText(getLocalizedString("button.cancel", "Cancel"));
  }
  
  /**
   * Initializes the UI components.
   */
  private void initializeComponents() {
    // Provider selection
    providerComboBox = new JComboBox<>(AIProviderPreset.values());
    providerComboBox.setRenderer(new DefaultListCellRenderer() {
      @Override
      public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                                                  boolean isSelected, boolean cellHasFocus) {
        super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
        if (value instanceof AIProviderPreset) {
          setText(((AIProviderPreset) value).getDisplayName());
        }
        return this;
      }
    });
    
    // Connection fields
    baseUrlField = new JTextField(30);
    apiKeyField = new JPasswordField(30);
    modelComboBox = new JComboBox<>();
    modelComboBox.setEditable(true);
    
    // Model parameters
    temperatureSlider = new JSlider(0, 200, 70); // 0.0 to 2.0, default 0.7
    temperatureSlider.setMajorTickSpacing(50);
    temperatureSlider.setMinorTickSpacing(10);
    temperatureSlider.setPaintTicks(true);
    temperatureSlider.setPaintLabels(true);
    
    // Create custom labels for temperature slider
    java.util.Hashtable<Integer, JLabel> labelTable = new java.util.Hashtable<>();
    labelTable.put(0, new JLabel("0.0"));
    labelTable.put(50, new JLabel("0.5"));
    labelTable.put(100, new JLabel("1.0"));
    labelTable.put(150, new JLabel("1.5"));
    labelTable.put(200, new JLabel("2.0"));
    temperatureSlider.setLabelTable(labelTable);
    
    maxTokensSpinner = new JSpinner(new SpinnerNumberModel(2048, 1, 8192, 1));
    
    // Action buttons
    testConnectionButton = new JButton(getLocalizedString("button.testConnection", "Test Connection"));
    saveButton = new JButton(getLocalizedString("button.save", "Save"));
    cancelButton = new JButton(getLocalizedString("button.cancel", "Cancel"));

    // Status components
    progressBar = new JProgressBar();
    progressBar.setIndeterminate(true);
    progressBar.setVisible(false);
    statusLabel = new JLabel(" ");
  }
  
  /**
   * Layouts the components in the dialog.
   */
  private void layoutComponents() {
    setLayout(new BorderLayout());
    
    // Main panel
    JPanel mainPanel = new JPanel(new GridBagLayout());
    GridBagConstraints gbc = new GridBagConstraints();
    gbc.insets = new Insets(5, 5, 5, 5);
    
    // Provider selection
    gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
    mainPanel.add(new JLabel(getLocalizedString("label.provider", "Provider:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(providerComboBox, gbc);

    // Base URL
    gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel(getLocalizedString("label.baseUrl", "Base URL:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(baseUrlField, gbc);

    // API Key
    gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel(getLocalizedString("label.apiKey", "API Key:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(apiKeyField, gbc);

    // Model
    gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel(getLocalizedString("label.model", "Model:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(modelComboBox, gbc);

    // Temperature
    gbc.gridx = 0; gbc.gridy = 4; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel(getLocalizedString("label.temperature", "Temperature:")), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(temperatureSlider, gbc);
    
    // Max Tokens
    gbc.gridx = 0; gbc.gridy = 5; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
    mainPanel.add(new JLabel("Max Tokens:"), gbc);
    gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
    mainPanel.add(maxTokensSpinner, gbc);
    
    // Test connection button
    gbc.gridx = 0; gbc.gridy = 6; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.NONE;
    gbc.anchor = GridBagConstraints.CENTER; gbc.weightx = 0;
    mainPanel.add(testConnectionButton, gbc);
    
    // Progress bar
    gbc.gridx = 0; gbc.gridy = 7; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
    gbc.weightx = 1.0;
    mainPanel.add(progressBar, gbc);
    
    // Status label
    gbc.gridx = 0; gbc.gridy = 8; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
    mainPanel.add(statusLabel, gbc);
    
    add(mainPanel, BorderLayout.CENTER);
    
    // Button panel
    JPanel buttonPanel = new JPanel(new FlowLayout());
    buttonPanel.add(saveButton);
    buttonPanel.add(cancelButton);
    add(buttonPanel, BorderLayout.SOUTH);
  }
  
  /**
   * Loads the current configuration into the dialog.
   */
  private void loadCurrentConfiguration() {
    AIProviderConfig config = aiIntegration.getCurrentConfiguration();
    if (config != null) {
      // Find matching preset
      AIProviderPreset matchingPreset = AIProviderPreset.CUSTOM;
      for (AIProviderPreset preset : AIProviderPreset.values()) {
        if (preset.getBaseUrl().equals(config.getBaseUrl())) {
          matchingPreset = preset;
          break;
        }
      }
      
      providerComboBox.setSelectedItem(matchingPreset);
      baseUrlField.setText(config.getBaseUrl());
      apiKeyField.setText(config.getApiKey());
      
      // Update model list and select current model
      updateModelList(matchingPreset);
      if (config.getModel() != null) {
        modelComboBox.setSelectedItem(config.getModel());
      }
      
      // Load model parameters
      AIModelParameters params = config.getModelParams();
      if (params != null) {
        temperatureSlider.setValue((int) (params.getTemperature() * 100));
        maxTokensSpinner.setValue(params.getMaxTokens());
      }
    }
  }
  
  /**
   * Sets up event handlers for the components.
   */
  private void setupEventHandlers() {
    providerComboBox.addActionListener(this::onProviderChanged);
    testConnectionButton.addActionListener(this::onTestConnection);
    saveButton.addActionListener(this::onSave);
    cancelButton.addActionListener(this::onCancel);
  }
  
  /**
   * Handles provider selection changes.
   */
  private void onProviderChanged(ActionEvent e) {
    AIProviderPreset preset = (AIProviderPreset) providerComboBox.getSelectedItem();
    if (preset != null && preset != AIProviderPreset.CUSTOM) {
      baseUrlField.setText(preset.getBaseUrl());
      updateModelList(preset);
      
      // Clear API key field for new provider
      if (!preset.getBaseUrl().equals(baseUrlField.getText())) {
        apiKeyField.setText("");
      }
    } else {
      baseUrlField.setText("");
      modelComboBox.removeAllItems();
    }
  }
  
  /**
   * Updates the model list based on the selected preset.
   */
  private void updateModelList(AIProviderPreset preset) {
    modelComboBox.removeAllItems();
    for (String model : preset.getDefaultModels()) {
      modelComboBox.addItem(model);
    }
  }
  
  /**
   * Handles connection testing.
   */
  private void onTestConnection(ActionEvent e) {
    // Create temporary configuration from UI
    AIProviderConfig tempConfig = createConfigFromUI();

    // Validate basic configuration first
    AIConfigValidator validator = new AIConfigValidator();
    ValidationResult result = validator.validate(tempConfig);

    if (!result.isValid()) {
      statusLabel.setText("Configuration invalid: " + result.getFirstError());
      statusLabel.setForeground(Color.RED);
      return;
    }

    // Start connection test
    testConnectionButton.setEnabled(false);
    progressBar.setVisible(true);
    statusLabel.setText("Testing connection...");
    statusLabel.setForeground(Color.BLACK);

    // Run test in background thread
    SwingWorker<Boolean, Void> worker = new SwingWorker<Boolean, Void>() {
      @Override
      protected Boolean doInBackground() throws Exception {
        try {
          AIClient testClient = AIClientFactory.createClient(tempConfig);
          boolean success = testClient.testConnection();
          testClient.close();
          return success;
        } catch (Exception e) {
          throw e;
        }
      }

      @Override
      protected void done() {
        testConnectionButton.setEnabled(true);
        progressBar.setVisible(false);

        try {
          boolean success = get();
          if (success) {
            statusLabel.setText("Connection successful!");
            statusLabel.setForeground(new Color(0, 128, 0)); // Dark green
          } else {
            statusLabel.setText("Connection failed");
            statusLabel.setForeground(Color.RED);
          }
        } catch (Exception e) {
          AIErrorHandler errorHandler = new AIErrorHandler();
          String errorMessage = errorHandler.getConnectionTestErrorMessage(e);
          statusLabel.setText(errorMessage);
          statusLabel.setForeground(Color.RED);
        }
      }
    };

    worker.execute();
  }
  
  /**
   * Handles save action.
   */
  private void onSave(ActionEvent e) {
    // Create configuration from UI
    AIProviderConfig config = createConfigFromUI();
    
    // Validate configuration
    AIConfigValidator validator = new AIConfigValidator();
    ValidationResult result = validator.validate(config);
    
    if (!result.isValid()) {
      String errors = String.join("\n", result.getErrors());
      JOptionPane.showMessageDialog(this, "Configuration errors:\n" + errors, 
                                  "Validation Error", JOptionPane.ERROR_MESSAGE);
      return;
    }
    
    // Save configuration
    aiIntegration.saveConfiguration(config);
    configurationSaved = true;
    dispose();
  }
  
  /**
   * Handles cancel action.
   */
  private void onCancel(ActionEvent e) {
    dispose();
  }
  
  /**
   * Creates a configuration object from the UI values.
   */
  private AIProviderConfig createConfigFromUI() {
    AIProviderPreset preset = (AIProviderPreset) providerComboBox.getSelectedItem();
    String providerName = preset != null ? preset.getDisplayName() : "Custom";
    
    AIModelParameters params = new AIModelParameters();
    params.setTemperature(temperatureSlider.getValue() / 100.0);
    params.setMaxTokens((Integer) maxTokensSpinner.getValue());
    
    return AIProviderConfig.builder()
        .providerName(providerName)
        .baseUrl(baseUrlField.getText().trim())
        .apiKey(new String(apiKeyField.getPassword()))
        .model(modelComboBox.getSelectedItem() != null ? modelComboBox.getSelectedItem().toString() : "")
        .modelParams(params)
        .build();
  }
  
  /**
   * Returns whether the configuration was saved.
   */
  public boolean isConfigurationSaved() {
    return configurationSaved;
  }
}
