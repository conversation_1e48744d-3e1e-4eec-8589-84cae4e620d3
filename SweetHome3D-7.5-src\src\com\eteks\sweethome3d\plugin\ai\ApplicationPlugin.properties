# ApplicationPlugin.properties
# Sweet Home 3D AI Plugin Configuration
# Copyright (c) 2025 Samuel Kpassegna

# Plugin identification
id=ai-floor-plan-analysis
name=AI Floor Plan Analysis
description=Analyze floor plans using artificial intelligence to provide insights and improvement suggestions
version=1.0.0
license=GPL v2+
provider=<PERSON>

# Plugin class
class=com.eteks.sweethome3d.plugin.ai.AIPlugin

# Java version requirement
javaMinimumVersion=1.8

# Plugin information
author=<PERSON>
website=https://github.com/skpassegna/sweethome3d-ai-plugin
supportEmail=<EMAIL>

# Features
features=AI Analysis, Floor Plan Insights, Multiple AI Providers, Privacy Controls

# Requirements
requirements=Internet connection for cloud AI providers (optional for local providers)

# Compatibility
sweetHome3DMinimumVersion=7.0

# UI Strings for internationalization
# Action properties
AIAction.Name=AI Analysis
AIAction.ShortDescription=Analyze floor plan with AI
AIAction.Menu=Tools

# Dialog titles
AIChatDialog.title=AI Floor Plan Analysis
AISettingsDialog.title=AI Settings

# Button labels
button.send=Send
button.newAnalysis=New Analysis
button.settings=Settings
button.testConnection=Test Connection
button.save=Save
button.cancel=Cancel

# Labels
label.provider=Provider:
label.baseUrl=Base URL:
label.apiKey=API Key:
label.model=Model:
label.temperature=Temperature:
label.maxTokens=Max Tokens:
label.status=Status:

# Messages
message.analyzing=Analyzing floor plan...
message.processingQuestion=Processing question...
message.testingConnection=Testing connection...
message.connectionSuccessful=Connection successful!
message.connectionFailed=Connection failed: {0}
message.configurationSaved=Configuration saved successfully
message.validationError=Configuration errors:\n{0}
message.noConfiguration=AI provider not configured. Please configure settings first.

# Analysis prompt
analysis.prompt=Please analyze this floor plan and provide comprehensive insights including:\n1. Layout efficiency and space utilization\n2. Traffic flow and circulation patterns\n3. Natural lighting and ventilation opportunities\n4. Accessibility considerations\n5. Functional relationships between spaces\n6. Suggestions for improvement\n7. Compliance with common building standards\n8. Energy efficiency considerations\n\nPlease provide specific, actionable recommendations that would enhance the functionality, comfort, and aesthetic appeal of this space.

# Error messages
error.analysisError=Analysis error: {0}
error.configurationError=Configuration error: {0}
error.connectionError=Connection error: {0}
error.invalidConfiguration=Invalid configuration
error.missingApiKey=API key is required
error.missingBaseUrl=Base URL is required
error.missingModel=Model selection is required

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Local)
provider.lmstudio=LM Studio (Local)
provider.anythingllm=AnythingLLM (Local)
provider.jan=Jan (Local)
provider.custom=Custom
