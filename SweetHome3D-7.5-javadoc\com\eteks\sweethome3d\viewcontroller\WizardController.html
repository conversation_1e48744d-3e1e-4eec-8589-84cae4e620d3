<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>WizardController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="WizardController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":6,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/WizardController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallShape.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/WizardController.html" target="_top">Frames</a></li>
<li><a href="WizardController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class WizardController" class="title">Class WizardController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.WizardController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/BackgroundImageWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">BackgroundImageWizardController</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedTextureWizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedTextureWizardController</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">WizardController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">An abstract MVC for a wizard view. Subclasses should create a set of wizard steps
 with subclasses of <code>WizardControllerStepState</code> and
 and choose the first step with a call to <code>setStepState</code>.
 The <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#finish--"><code>finish</code></a> method will be called if user completes the wizard
 steps correctly.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WizardController.Property</a></span></code>
<div class="block">The properties that the view associated to this controller needs.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></span></code>
<div class="block">State of a step in wizard.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#WizardController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">WizardController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WizardController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WizardController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</code>
<div class="block">Displays the view controlled by this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#finish--">finish</a></span>()</code>
<div class="block">Requires the wizard to finish.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.net.URL</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getStepIcon--">getStepIcon</a></span>()</code>
<div class="block">Returns the current step icon.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getStepState--">getStepState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getStepView--">getStepView</a></span>()</code>
<div class="block">Returns the current step view.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getTitle--">getTitle</a></span>()</code>
<div class="block">Returns the wizard title.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#goBackToPreviousStep--">goBackToPreviousStep</a></span>()</code>
<div class="block">Requires to the current step to go back to previous step.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#goToNextStep--">goToNextStep</a></span>()</code>
<div class="block">Requires to the current step to jump to next step.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#isBackStepEnabled--">isBackStepEnabled</a></span>()</code>
<div class="block">Returns whether back step is enabled or not.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#isLastStep--">isLastStep</a></span>()</code>
<div class="block">Returns whether this is the last step or not.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#isNextStepEnabled--">isNextStepEnabled</a></span>()</code>
<div class="block">Returns whether next step is enabled or not.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#isResizable--">isResizable</a></span>()</code>
<div class="block">Returns whether the wizard is resizable or not.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WizardController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WizardController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#setResizable-boolean-">setResizable</a></span>(boolean&nbsp;resizable)</code>
<div class="block">Sets whether the wizard is resizable or not.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#setStepState-com.eteks.sweethome3d.viewcontroller.WizardController.WizardControllerStepState-">setStepState</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a>&nbsp;stepState)</code>
<div class="block">Changes current state of controller.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#setTitle-java.lang.String-">setTitle</a></span>(java.lang.String&nbsp;title)</code>
<div class="block">Sets the wizard title.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="WizardController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>WizardController</h4>
<pre>public&nbsp;WizardController(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                        <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="displayView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>displayView</h4>
<pre>public&nbsp;void&nbsp;displayView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView)</pre>
<div class="block">Displays the view controlled by this controller.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WizardController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WizardController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WizardController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">WizardController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="isBackStepEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackStepEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isBackStepEnabled()</pre>
<div class="block">Returns whether back step is enabled or not.</div>
</li>
</ul>
<a name="isNextStepEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNextStepEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isNextStepEnabled()</pre>
<div class="block">Returns whether next step is enabled or not.</div>
</li>
</ul>
<a name="isLastStep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLastStep</h4>
<pre>public&nbsp;boolean&nbsp;isLastStep()</pre>
<div class="block">Returns whether this is the last step or not.</div>
</li>
</ul>
<a name="getStepView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStepView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getStepView()</pre>
<div class="block">Returns the current step view.</div>
</li>
</ul>
<a name="getStepIcon--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStepIcon</h4>
<pre>public&nbsp;java.net.URL&nbsp;getStepIcon()</pre>
<div class="block">Returns the current step icon.</div>
</li>
</ul>
<a name="setTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTitle</h4>
<pre>public&nbsp;void&nbsp;setTitle(java.lang.String&nbsp;title)</pre>
<div class="block">Sets the wizard title.</div>
</li>
</ul>
<a name="getTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitle</h4>
<pre>public&nbsp;java.lang.String&nbsp;getTitle()</pre>
<div class="block">Returns the wizard title.</div>
</li>
</ul>
<a name="setResizable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResizable</h4>
<pre>public&nbsp;void&nbsp;setResizable(boolean&nbsp;resizable)</pre>
<div class="block">Sets whether the wizard is resizable or not.</div>
</li>
</ul>
<a name="isResizable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isResizable</h4>
<pre>public&nbsp;boolean&nbsp;isResizable()</pre>
<div class="block">Returns whether the wizard is resizable or not.</div>
</li>
</ul>
<a name="setStepState-com.eteks.sweethome3d.viewcontroller.WizardController.WizardControllerStepState-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStepState</h4>
<pre>protected&nbsp;void&nbsp;setStepState(<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a>&nbsp;stepState)</pre>
<div class="block">Changes current state of controller.</div>
</li>
</ul>
<a name="getStepState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStepState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a>&nbsp;getStepState()</pre>
</li>
</ul>
<a name="goToNextStep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>goToNextStep</h4>
<pre>public&nbsp;void&nbsp;goToNextStep()</pre>
<div class="block">Requires to the current step to jump to next step.</div>
</li>
</ul>
<a name="goBackToPreviousStep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>goBackToPreviousStep</h4>
<pre>public&nbsp;void&nbsp;goBackToPreviousStep()</pre>
<div class="block">Requires to the current step to go back to previous step.</div>
</li>
</ul>
<a name="finish--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>finish</h4>
<pre>public abstract&nbsp;void&nbsp;finish()</pre>
<div class="block">Requires the wizard to finish.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/WizardController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WallController.WallShape.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/WizardController.html" target="_top">Frames</a></li>
<li><a href="WizardController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
