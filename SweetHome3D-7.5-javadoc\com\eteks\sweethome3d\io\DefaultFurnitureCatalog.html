<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>DefaultFurnitureCatalog (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DefaultFurnitureCatalog (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultFurnitureCatalog.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DamagedHomeIOException.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html" target="_top">Frames</a></li>
<li><a href="DefaultFurnitureCatalog.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class DefaultFurnitureCatalog" class="title">Class DefaultFurnitureCatalog</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.FurnitureCatalog</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.DefaultFurnitureCatalog</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DefaultFurnitureCatalog</span>
extends <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a></pre>
<div class="block">Furniture default catalog read from resources localized in <code>.properties</code> files.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a></span></code>
<div class="block">The keys of the properties values read in <code>.properties</code> files.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#PLUGIN_FURNITURE_CATALOG_FAMILY">PLUGIN_FURNITURE_CATALOG_FAMILY</a></span></code>
<div class="block">The name of <code>.properties</code> family files in plugin furniture catalog files.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#DefaultFurnitureCatalog--">DefaultFurnitureCatalog</a></span>()</code>
<div class="block">Creates a default furniture catalog read from resources in the package of this class.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#DefaultFurnitureCatalog-java.io.File-">DefaultFurnitureCatalog</a></span>(java.io.File&nbsp;furniturePluginFolder)</code>
<div class="block">Creates a default furniture catalog read from resources and
 furniture plugin folder if <code>furniturePluginFolder</code> isn't <code>null</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#DefaultFurnitureCatalog-java.net.URL:A-">DefaultFurnitureCatalog</a></span>(java.net.URL[]&nbsp;pluginFurnitureCatalogUrls)</code>
<div class="block">Creates a default furniture catalog read only from resources in the given URLs.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#DefaultFurnitureCatalog-java.net.URL:A-java.net.URL-">DefaultFurnitureCatalog</a></span>(java.net.URL[]&nbsp;pluginFurnitureCatalogUrls,
                       java.net.URL&nbsp;furnitureResourcesUrlBase)</code>
<div class="block">Creates a default furniture catalog read only from resources in the given URLs
 or in the classpath if the security manager doesn't allow to create class loaders.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#DefaultFurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File-">DefaultFurnitureCatalog</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       java.io.File&nbsp;furniturePluginFolder)</code>
<div class="block">Creates a default furniture catalog read from resources and
 furniture plugin folder if <code>furniturePluginFolder</code> isn't <code>null</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#DefaultFurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File:A-">DefaultFurnitureCatalog</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       java.io.File[]&nbsp;furniturePluginFolders)</code>
<div class="block">Creates a default furniture catalog read from resources and
 furniture plugin folders if <code>furniturePluginFolders</code> isn't <code>null</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected java.util.Map&lt;java.lang.String,<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#getAdditionalContents-java.util.ResourceBundle-int-java.net.URL-java.net.URL-">getAdditionalContents</a></span>(java.util.ResourceBundle&nbsp;resource,
                     int&nbsp;index,
                     java.net.URL&nbsp;furnitureCatalogUrl,
                     java.net.URL&nbsp;furnitureResourcesUrlBase)</code>
<div class="block">Returns the contents of the piece at the given <code>index</code>
 different from default properties.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected java.util.Map&lt;java.lang.String,java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#getAdditionalProperties-java.util.ResourceBundle-int-">getAdditionalProperties</a></span>(java.util.ResourceBundle&nbsp;resource,
                       int&nbsp;index)</code>
<div class="block">Returns the properties of the piece at the given <code>index</code>
 different from default properties.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Returns the furniture libraries at initialization.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#isDefaultProperty-java.lang.String-">isDefaultProperty</a></span>(java.lang.String&nbsp;keyPrefix)</code>
<div class="block">Returns <code>true</code> if the given parameter is the prefix of a default property
 used as an attribute of a piece of furniture.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#readFurnitureCategory-java.util.ResourceBundle-int-">readFurnitureCategory</a></span>(java.util.ResourceBundle&nbsp;resource,
                     int&nbsp;index)</code>
<div class="block">Returns the furniture category of a piece at the given <code>index</code> of a
 localized <code>resource</code> bundle.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html#readPieceOfFurniture-java.util.ResourceBundle-int-java.net.URL-java.net.URL-">readPieceOfFurniture</a></span>(java.util.ResourceBundle&nbsp;resource,
                    int&nbsp;index,
                    java.net.URL&nbsp;furnitureCatalogUrl,
                    java.net.URL&nbsp;furnitureResourcesUrlBase)</code>
<div class="block">Returns the piece of furniture at the given <code>index</code> of a
 localized <code>resource</code> bundle.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.FurnitureCatalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html" title="class in com.eteks.sweethome3d.model">FurnitureCatalog</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html#add-com.eteks.sweethome3d.model.FurnitureCategory-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-">add</a>, <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html#addFurnitureListener-com.eteks.sweethome3d.model.CollectionListener-">addFurnitureListener</a>, <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html#delete-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-">delete</a>, <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html#getCategories--">getCategories</a>, <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html#getCategoriesCount--">getCategoriesCount</a>, <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html#getCategory-int-">getCategory</a>, <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html#getPieceOfFurnitureWithId-java.lang.String-">getPieceOfFurnitureWithId</a>, <a href="../../../../com/eteks/sweethome3d/model/FurnitureCatalog.html#removeFurnitureListener-com.eteks.sweethome3d.model.CollectionListener-">removeFurnitureListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="PLUGIN_FURNITURE_CATALOG_FAMILY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PLUGIN_FURNITURE_CATALOG_FAMILY</h4>
<pre>public static final&nbsp;java.lang.String PLUGIN_FURNITURE_CATALOG_FAMILY</pre>
<div class="block">The name of <code>.properties</code> family files in plugin furniture catalog files.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.DefaultFurnitureCatalog.PLUGIN_FURNITURE_CATALOG_FAMILY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DefaultFurnitureCatalog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultFurnitureCatalog</h4>
<pre>public&nbsp;DefaultFurnitureCatalog()</pre>
<div class="block">Creates a default furniture catalog read from resources in the package of this class.</div>
</li>
</ul>
<a name="DefaultFurnitureCatalog-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultFurnitureCatalog</h4>
<pre>public&nbsp;DefaultFurnitureCatalog(java.io.File&nbsp;furniturePluginFolder)</pre>
<div class="block">Creates a default furniture catalog read from resources and
 furniture plugin folder if <code>furniturePluginFolder</code> isn't <code>null</code>.</div>
</li>
</ul>
<a name="DefaultFurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultFurnitureCatalog</h4>
<pre>public&nbsp;DefaultFurnitureCatalog(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               java.io.File&nbsp;furniturePluginFolder)</pre>
<div class="block">Creates a default furniture catalog read from resources and
 furniture plugin folder if <code>furniturePluginFolder</code> isn't <code>null</code>.</div>
</li>
</ul>
<a name="DefaultFurnitureCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultFurnitureCatalog</h4>
<pre>public&nbsp;DefaultFurnitureCatalog(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               java.io.File[]&nbsp;furniturePluginFolders)</pre>
<div class="block">Creates a default furniture catalog read from resources and
 furniture plugin folders if <code>furniturePluginFolders</code> isn't <code>null</code>.</div>
</li>
</ul>
<a name="DefaultFurnitureCatalog-java.net.URL:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultFurnitureCatalog</h4>
<pre>public&nbsp;DefaultFurnitureCatalog(java.net.URL[]&nbsp;pluginFurnitureCatalogUrls)</pre>
<div class="block">Creates a default furniture catalog read only from resources in the given URLs.</div>
</li>
</ul>
<a name="DefaultFurnitureCatalog-java.net.URL:A-java.net.URL-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DefaultFurnitureCatalog</h4>
<pre>public&nbsp;DefaultFurnitureCatalog(java.net.URL[]&nbsp;pluginFurnitureCatalogUrls,
                               java.net.URL&nbsp;furnitureResourcesUrlBase)</pre>
<div class="block">Creates a default furniture catalog read only from resources in the given URLs
 or in the classpath if the security manager doesn't allow to create class loaders.
 Model and icon URLs will built from <code>furnitureResourcesUrlBase</code> if it isn't <code>null</code>.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getLibraries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLibraries</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;getLibraries()</pre>
<div class="block">Returns the furniture libraries at initialization.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="getAdditionalProperties-java.util.ResourceBundle-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdditionalProperties</h4>
<pre>protected&nbsp;java.util.Map&lt;java.lang.String,java.lang.String&gt;&nbsp;getAdditionalProperties(java.util.ResourceBundle&nbsp;resource,
                                                                                   int&nbsp;index)</pre>
<div class="block">Returns the properties of the piece at the given <code>index</code>
 different from default properties.</div>
</li>
</ul>
<a name="getAdditionalContents-java.util.ResourceBundle-int-java.net.URL-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAdditionalContents</h4>
<pre>protected&nbsp;java.util.Map&lt;java.lang.String,<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;getAdditionalContents(java.util.ResourceBundle&nbsp;resource,
                                                                        int&nbsp;index,
                                                                        java.net.URL&nbsp;furnitureCatalogUrl,
                                                                        java.net.URL&nbsp;furnitureResourcesUrlBase)</pre>
<div class="block">Returns the contents of the piece at the given <code>index</code>
 different from default properties.</div>
</li>
</ul>
<a name="isDefaultProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDefaultProperty</h4>
<pre>protected&nbsp;boolean&nbsp;isDefaultProperty(java.lang.String&nbsp;keyPrefix)</pre>
<div class="block">Returns <code>true</code> if the given parameter is the prefix of a default property
 used as an attribute of a piece of furniture.</div>
</li>
</ul>
<a name="readPieceOfFurniture-java.util.ResourceBundle-int-java.net.URL-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readPieceOfFurniture</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;readPieceOfFurniture(java.util.ResourceBundle&nbsp;resource,
                                                       int&nbsp;index,
                                                       java.net.URL&nbsp;furnitureCatalogUrl,
                                                       java.net.URL&nbsp;furnitureResourcesUrlBase)</pre>
<div class="block">Returns the piece of furniture at the given <code>index</code> of a
 localized <code>resource</code> bundle.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - a resource bundle</dd>
<dd><code>index</code> - the index of the read piece</dd>
<dd><code>furnitureCatalogUrl</code> - the URL from which piece resources will be loaded
            or <code>null</code> if it's read from current classpath.</dd>
<dd><code>furnitureResourcesUrlBase</code> - the URL used as a base to build the URL to piece resources
            or <code>null</code> if it's read from current classpath or <code>furnitureCatalogUrl</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the read piece of furniture or <code>null</code> if the piece at the given index doesn't exist.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.util.MissingResourceException</code> - if mandatory keys are not defined.</dd>
</dl>
</li>
</ul>
<a name="readFurnitureCategory-java.util.ResourceBundle-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readFurnitureCategory</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a>&nbsp;readFurnitureCategory(java.util.ResourceBundle&nbsp;resource,
                                                  int&nbsp;index)</pre>
<div class="block">Returns the furniture category of a piece at the given <code>index</code> of a
 localized <code>resource</code> bundle.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.util.MissingResourceException</code> - if mandatory keys are not defined.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultFurnitureCatalog.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DamagedHomeIOException.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html" target="_top">Frames</a></li>
<li><a href="DefaultFurnitureCatalog.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
