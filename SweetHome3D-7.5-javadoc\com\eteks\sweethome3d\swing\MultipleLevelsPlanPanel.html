<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:49 CEST 2024 -->
<title>MultipleLevelsPlanPanel (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="MultipleLevelsPlanPanel (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/MultipleLevelsPlanPanel.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/NullableCheckBox.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html" target="_top">Frames</a></li>
<li><a href="MultipleLevelsPlanPanel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JPanel">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class MultipleLevelsPlanPanel" class="title">Class MultipleLevelsPlanPanel</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.awt.Component</li>
<li>
<ul class="inheritance">
<li>java.awt.Container</li>
<li>
<ul class="inheritance">
<li>javax.swing.JComponent</li>
<li>
<ul class="inheritance">
<li>javax.swing.JPanel</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.MultipleLevelsPlanPanel</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>, java.awt.image.ImageObserver, java.awt.MenuContainer, java.awt.print.Printable, java.io.Serializable, javax.accessibility.Accessible</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">MultipleLevelsPlanPanel</span>
extends javax.swing.JPanel
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a>, java.awt.print.Printable</pre>
<div class="block">A panel for multiple levels plans where users can select the displayed level.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.MultipleLevelsPlanPanel">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JPanel">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JPanel</h3>
<code>javax.swing.JPanel.AccessibleJPanel</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.JComponent</h3>
<code>javax.swing.JComponent.AccessibleJComponent</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Container</h3>
<code>java.awt.Container.AccessibleAWTContainer</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.awt.Component</h3>
<code>java.awt.Component.AccessibleAWTComponent, java.awt.Component.BaselineResizeBehavior, java.awt.Component.BltBufferStrategy, java.awt.Component.FlipBufferStrategy</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.PlanView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.TransferableView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.TransferObserver.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView.TransferObserver</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.ExportableView">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.JComponent</h3>
<code>listenerList, TOOL_TIP_TEXT_KEY, ui, UNDEFINED_CONDITION, WHEN_ANCESTOR_OF_FOCUSED_COMPONENT, WHEN_FOCUSED, WHEN_IN_FOCUSED_WINDOW</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.awt.Component</h3>
<code>accessibleContext, BOTTOM_ALIGNMENT, CENTER_ALIGNMENT, LEFT_ALIGNMENT, RIGHT_ALIGNMENT, TOP_ALIGNMENT</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.print.Printable">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.print.Printable</h3>
<code>NO_SUCH_PAGE, PAGE_EXISTS</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.awt.image.ImageObserver">
<!--   -->
</a>
<h3>Fields inherited from interface&nbsp;java.awt.image.ImageObserver</h3>
<code>ABORT, ALLBITS, ERROR, FRAMEBITS, HEIGHT, PROPERTIES, SOMEBITS, WIDTH</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#MultipleLevelsPlanPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">MultipleLevelsPlanPanel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#addFocusListener-java.awt.event.FocusListener-">addFocusListener</a></span>(java.awt.event.FocusListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#addMouseListener-java.awt.event.MouseListener-">addMouseListener</a></span>(java.awt.event.MouseListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#addMouseMotionListener-java.awt.event.MouseMotionListener-">addMouseMotionListener</a></span>(java.awt.event.MouseMotionListener&nbsp;l)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#canImportDraggedItems-java.util.List-int-int-">canImportDraggedItems</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                     int&nbsp;x,
                     int&nbsp;y)</code>
<div class="block">Returns <code>true</code> if the given coordinates belong to the plan displayed by this component.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#convertXModelToScreen-float-">convertXModelToScreen</a></span>(float&nbsp;x)</code>
<div class="block">Returns <code>x</code> converted in screen coordinates space.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#convertXPixelToModel-int-">convertXPixelToModel</a></span>(int&nbsp;x)</code>
<div class="block">Returns <code>x</code> converted in model coordinates space.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#convertYModelToScreen-float-">convertYModelToScreen</a></span>(float&nbsp;y)</code>
<div class="block">Returns <code>y</code> converted in screen coordinates space.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#convertYPixelToModel-int-">convertYPixelToModel</a></span>(int&nbsp;y)</code>
<div class="block">Returns <code>y</code> converted in model coordinates space.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#createPlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">createPlanComponent</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</code>
<div class="block">Creates and returns the main plan component displayed and layout by this component.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">createTransferData</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>&nbsp;dataType)</code>
<div class="block">Returns an image of the plan for transfer purpose.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#deleteFeedback--">deleteFeedback</a></span>()</code>
<div class="block">Deletes all elements shown as feedback.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#deleteToolTipFeedback--">deleteToolTipFeedback</a></span>()</code>
<div class="block">Deletes tool tip text from screen.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">exportData</a></span>(java.io.OutputStream&nbsp;out,
          <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType,
          java.util.Properties&nbsp;settings)</code>
<div class="block">Writes the plan in the given output stream at SVG (Scalable Vector Graphics) format if this is the requested format.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>java.awt.Cursor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getCursor--">getCursor</a></span>()</code>
<div class="block">Returns the cursor of this component.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getHorizontalRuler--">getHorizontalRuler</a></span>()</code>
<div class="block">Returns the component used as an horizontal ruler for the plan displayed by this component.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getPieceOfFurnitureSizeInPlan</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getPixelLength--">getPixelLength</a></span>()</code>
<div class="block">Returns the length in centimeters of a pixel with the current scale.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getPrintPreferredScale-float-float-">getPrintPreferredScale</a></span>(float&nbsp;preferredWidth,
                      float&nbsp;preferredHeight)</code>
<div class="block">Returns the preferred scale to ensure it can be fully printed on the given print zone.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getPrintPreferredScale-java.awt.Graphics-java.awt.print.PageFormat-">getPrintPreferredScale</a></span>(java.awt.Graphics&nbsp;graphics,
                      java.awt.print.PageFormat&nbsp;pageFormat)</code>
<div class="block">Returns the preferred scale to print the plan component.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getScale--">getScale</a></span>()</code>
<div class="block">Returns the scale used to display the plan displayed by this component.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">getTextBounds</a></span>(java.lang.String&nbsp;text,
             <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
             float&nbsp;x,
             float&nbsp;y,
             float&nbsp;angle)</code>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> displayed at
 the point (<code>x</code>,<code>y</code>).</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#getVerticalRuler--">getVerticalRuler</a></span>()</code>
<div class="block">Returns the component used as a vertical ruler for the plan displayed by this component.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">isFormatTypeSupported</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType)</code>
<div class="block">Returns <code>true</code> if the plan component supports the given format type.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#isFurnitureSizeInPlanSupported--">isFurnitureSizeInPlanSupported</a></span>()</code>
<div class="block">Returns <code>true</code> if this component is able to compute the size of horizontally rotated furniture.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#makePointVisible-float-float-">makePointVisible</a></span>(float&nbsp;x,
                float&nbsp;y)</code>
<div class="block">Ensures the point at (<code>x</code>, <code>y</code>) is visible in the plan displayed by this component,
 moving its scroll bars if needed.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#makeSelectionVisible--">makeSelectionVisible</a></span>()</code>
<div class="block">Ensures selected items are visible in the plan displayed by this component and moves
 its scroll bars if needed.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#moveView-float-float-">moveView</a></span>(float&nbsp;dx,
        float&nbsp;dy)</code>
<div class="block">Moves the plan displayed by this component from (dx, dy) unit in the scrolling zone it belongs to.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#print-java.awt.Graphics-java.awt.print.PageFormat-int-">print</a></span>(java.awt.Graphics&nbsp;g,
     java.awt.print.PageFormat&nbsp;pageFormat,
     int&nbsp;pageIndex)</code>
<div class="block">Prints the plan component.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></span>(java.lang.Class&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                    <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                    float&nbsp;x,
                    float&nbsp;y,
                    boolean&nbsp;showPoint)</code>
<div class="block">Sets the location point for alignment feedback.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setAngleFeedback-float-float-float-float-float-float-">setAngleFeedback</a></span>(float&nbsp;xCenter,
                float&nbsp;yCenter,
                float&nbsp;x1,
                float&nbsp;y1,
                float&nbsp;x2,
                float&nbsp;y2)</code>
<div class="block">Sets the points used to draw an angle in the plan displayed by this component.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setComponentPopupMenu-javax.swing.JPopupMenu-">setComponentPopupMenu</a></span>(javax.swing.JPopupMenu&nbsp;popup)</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setCursor-java.awt.Cursor-">setCursor</a></span>(java.awt.Cursor&nbsp;cursor)</code>
<div class="block">Sets the cursor of this component.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setCursor-com.eteks.sweethome3d.viewcontroller.PlanView.CursorType-">setCursor</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a>&nbsp;cursorType)</code>
<div class="block">Sets the cursor of this component.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setDimensionLinesFeedback-java.util.List-">setDimensionLinesFeedback</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;dimensionLines)</code>
<div class="block">Sets the given dimension lines to be drawn as feedback.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setDraggedItemsFeedback-java.util.List-">setDraggedItemsFeedback</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</code>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of the plan displayed by this component.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setRectangleFeedback-float-float-float-float-">setRectangleFeedback</a></span>(float&nbsp;x0,
                    float&nbsp;y0,
                    float&nbsp;x1,
                    float&nbsp;y1)</code>
<div class="block">Sets rectangle selection feedback coordinates.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setResizeIndicatorVisible-boolean-">setResizeIndicatorVisible</a></span>(boolean&nbsp;visible)</code>
<div class="block">Sets whether the resize indicator of selected wall or piece of furniture
 should be visible or not.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setScale-float-">setScale</a></span>(float&nbsp;scale)</code>
<div class="block">Sets the scale used to display the plan displayed by this component.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setToolTipEditedProperties-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty:A-java.lang.Object:A-float-float-">setToolTipEditedProperties</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>[]&nbsp;toolTipEditedProperties,
                          java.lang.Object[]&nbsp;toolTipPropertyValues,
                          float&nbsp;x,
                          float&nbsp;y)</code>
<div class="block">Sets properties edited in tool tip.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setToolTipEditedPropertyValue-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">setToolTipEditedPropertyValue</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;toolTipEditedProperty,
                             java.lang.Object&nbsp;toolTipPropertyValue)</code>
<div class="block">Sets the value of a property edited in tool tip.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setToolTipFeedback-java.lang.String-float-float-">setToolTipFeedback</a></span>(java.lang.String&nbsp;toolTipFeedback,
                  float&nbsp;x,
                  float&nbsp;y)</code>
<div class="block">Sets tool tip text displayed as feedback.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html#setTransferHandler-javax.swing.TransferHandler-">setTransferHandler</a></span>(javax.swing.TransferHandler&nbsp;newHandler)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JPanel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JPanel</h3>
<code>getAccessibleContext, getUI, getUIClassID, paramString, setUI, updateUI</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.JComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.JComponent</h3>
<code>addAncestorListener, addNotify, addVetoableChangeListener, computeVisibleRect, contains, createToolTip, disable, enable, firePropertyChange, firePropertyChange, firePropertyChange, fireVetoableChange, getActionForKeyStroke, getActionMap, getAlignmentX, getAlignmentY, getAncestorListeners, getAutoscrolls, getBaseline, getBaselineResizeBehavior, getBorder, getBounds, getClientProperty, getComponentGraphics, getComponentPopupMenu, getConditionForKeyStroke, getDebugGraphicsOptions, getDefaultLocale, getFontMetrics, getGraphics, getHeight, getInheritsPopupMenu, getInputMap, getInputMap, getInputVerifier, getInsets, getInsets, getListeners, getLocation, getMaximumSize, getMinimumSize, getNextFocusableComponent, getPopupLocation, getPreferredSize, getRegisteredKeyStrokes, getRootPane, getSize, getToolTipLocation, getToolTipText, getToolTipText, getTopLevelAncestor, getTransferHandler, getVerifyInputWhenFocusTarget, getVetoableChangeListeners, getVisibleRect, getWidth, getX, getY, grabFocus, hide, isDoubleBuffered, isLightweightComponent, isManagingFocus, isOpaque, isOptimizedDrawingEnabled, isPaintingForPrint, isPaintingOrigin, isPaintingTile, isRequestFocusEnabled, isValidateRoot, paint, paintBorder, paintChildren, paintComponent, paintImmediately, paintImmediately, print, printAll, printBorder, printChildren, printComponent, processComponentKeyEvent, processKeyBinding, processKeyEvent, processMouseEvent, processMouseMotionEvent, putClientProperty, registerKeyboardAction, registerKeyboardAction, removeAncestorListener, removeNotify, removeVetoableChangeListener, repaint, repaint, requestDefaultFocus, requestFocus, requestFocus, requestFocusInWindow, requestFocusInWindow, resetKeyboardActions, reshape, revalidate, scrollRectToVisible, setActionMap, setAlignmentX, setAlignmentY, setAutoscrolls, setBackground, setBorder, setDebugGraphicsOptions, setDefaultLocale, setDoubleBuffered, setEnabled, setFocusTraversalKeys, setFont, setForeground, setInheritsPopupMenu, setInputMap, setInputVerifier, setMaximumSize, setMinimumSize, setNextFocusableComponent, setOpaque, setPreferredSize, setRequestFocusEnabled, setToolTipText, setUI, setVerifyInputWhenFocusTarget, setVisible, unregisterKeyboardAction, update</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Container">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Container</h3>
<code>add, add, add, add, add, addContainerListener, addImpl, addPropertyChangeListener, addPropertyChangeListener, applyComponentOrientation, areFocusTraversalKeysSet, countComponents, deliverEvent, doLayout, findComponentAt, findComponentAt, getComponent, getComponentAt, getComponentAt, getComponentCount, getComponents, getComponentZOrder, getContainerListeners, getFocusTraversalKeys, getFocusTraversalPolicy, getLayout, getMousePosition, insets, invalidate, isAncestorOf, isFocusCycleRoot, isFocusCycleRoot, isFocusTraversalPolicyProvider, isFocusTraversalPolicySet, layout, list, list, locate, minimumSize, paintComponents, preferredSize, printComponents, processContainerEvent, processEvent, remove, remove, removeAll, removeContainerListener, setComponentZOrder, setFocusCycleRoot, setFocusTraversalPolicy, setFocusTraversalPolicyProvider, setLayout, transferFocusDownCycle, validate, validateTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.awt.Component">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.awt.Component</h3>
<code>action, add, addComponentListener, addHierarchyBoundsListener, addHierarchyListener, addInputMethodListener, addKeyListener, addMouseWheelListener, bounds, checkImage, checkImage, coalesceEvents, contains, createImage, createImage, createVolatileImage, createVolatileImage, disableEvents, dispatchEvent, enable, enableEvents, enableInputMethods, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, firePropertyChange, getBackground, getBounds, getColorModel, getComponentListeners, getComponentOrientation, getDropTarget, getFocusCycleRootAncestor, getFocusListeners, getFocusTraversalKeysEnabled, getFont, getForeground, getGraphicsConfiguration, getHierarchyBoundsListeners, getHierarchyListeners, getIgnoreRepaint, getInputContext, getInputMethodListeners, getInputMethodRequests, getKeyListeners, getLocale, getLocation, getLocationOnScreen, getMouseListeners, getMouseMotionListeners, getMousePosition, getMouseWheelListeners, getName, getParent, getPeer, getPropertyChangeListeners, getPropertyChangeListeners, getSize, getToolkit, getTreeLock, gotFocus, handleEvent, hasFocus, imageUpdate, inside, isBackgroundSet, isCursorSet, isDisplayable, isEnabled, isFocusable, isFocusOwner, isFocusTraversable, isFontSet, isForegroundSet, isLightweight, isMaximumSizeSet, isMinimumSizeSet, isPreferredSizeSet, isShowing, isValid, isVisible, keyDown, keyUp, list, list, list, location, lostFocus, mouseDown, mouseDrag, mouseEnter, mouseExit, mouseMove, mouseUp, move, nextFocus, paintAll, postEvent, prepareImage, prepareImage, processComponentEvent, processFocusEvent, processHierarchyBoundsEvent, processHierarchyEvent, processInputMethodEvent, processMouseWheelEvent, remove, removeComponentListener, removeFocusListener, removeHierarchyBoundsListener, removeHierarchyListener, removeInputMethodListener, removeKeyListener, removeMouseListener, removeMouseMotionListener, removeMouseWheelListener, removePropertyChangeListener, removePropertyChangeListener, repaint, repaint, repaint, resize, resize, setBounds, setBounds, setComponentOrientation, setDropTarget, setFocusable, setFocusTraversalKeysEnabled, setIgnoreRepaint, setLocale, setLocation, setLocation, setName, setSize, setSize, show, show, size, toString, transferFocus, transferFocusBackward, transferFocusUpCycle</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="MultipleLevelsPlanPanel-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MultipleLevelsPlanPanel</h4>
<pre>public&nbsp;MultipleLevelsPlanPanel(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                               <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                               <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createPlanComponent-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.PlanController-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createPlanComponent</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a>&nbsp;createPlanComponent(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                       <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a>&nbsp;controller)</pre>
<div class="block">Creates and returns the main plan component displayed and layout by this component.</div>
</li>
</ul>
<a name="setTransferHandler-javax.swing.TransferHandler-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransferHandler</h4>
<pre>public&nbsp;void&nbsp;setTransferHandler(javax.swing.TransferHandler&nbsp;newHandler)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setTransferHandler</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="setComponentPopupMenu-javax.swing.JPopupMenu-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setComponentPopupMenu</h4>
<pre>public&nbsp;void&nbsp;setComponentPopupMenu(javax.swing.JPopupMenu&nbsp;popup)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setComponentPopupMenu</code>&nbsp;in class&nbsp;<code>javax.swing.JComponent</code></dd>
</dl>
</li>
</ul>
<a name="addMouseMotionListener-java.awt.event.MouseMotionListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMouseMotionListener</h4>
<pre>public&nbsp;void&nbsp;addMouseMotionListener(java.awt.event.MouseMotionListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>addMouseMotionListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="addMouseListener-java.awt.event.MouseListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addMouseListener</h4>
<pre>public&nbsp;void&nbsp;addMouseListener(java.awt.event.MouseListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>addMouseListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="addFocusListener-java.awt.event.FocusListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFocusListener</h4>
<pre>public&nbsp;void&nbsp;addFocusListener(java.awt.event.FocusListener&nbsp;l)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>addFocusListener</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTransferData</h4>
<pre>public&nbsp;java.lang.Object&nbsp;createTransferData(<a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.DataType.html" title="class in com.eteks.sweethome3d.viewcontroller">TransferableView.DataType</a>&nbsp;dataType)</pre>
<div class="block">Returns an image of the plan for transfer purpose.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html#createTransferData-com.eteks.sweethome3d.viewcontroller.TransferableView.DataType-">createTransferData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a></code></dd>
</dl>
</li>
</ul>
<a name="isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFormatTypeSupported</h4>
<pre>public&nbsp;boolean&nbsp;isFormatTypeSupported(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType)</pre>
<div class="block">Returns <code>true</code> if the plan component supports the given format type.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html#isFormatTypeSupported-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-">isFormatTypeSupported</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></code></dd>
</dl>
</li>
</ul>
<a name="exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportData</h4>
<pre>public&nbsp;void&nbsp;exportData(java.io.OutputStream&nbsp;out,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.FormatType.html" title="class in com.eteks.sweethome3d.viewcontroller">ExportableView.FormatType</a>&nbsp;formatType,
                       java.util.Properties&nbsp;settings)
                throws java.io.IOException</pre>
<div class="block">Writes the plan in the given output stream at SVG (Scalable Vector Graphics) format if this is the requested format.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html#exportData-java.io.OutputStream-com.eteks.sweethome3d.viewcontroller.ExportableView.FormatType-java.util.Properties-">exportData</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="setRectangleFeedback-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRectangleFeedback</h4>
<pre>public&nbsp;void&nbsp;setRectangleFeedback(float&nbsp;x0,
                                 float&nbsp;y0,
                                 float&nbsp;x1,
                                 float&nbsp;y1)</pre>
<div class="block">Sets rectangle selection feedback coordinates.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setRectangleFeedback-float-float-float-float-">setRectangleFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="makeSelectionVisible--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makeSelectionVisible</h4>
<pre>public&nbsp;void&nbsp;makeSelectionVisible()</pre>
<div class="block">Ensures selected items are visible in the plan displayed by this component and moves
 its scroll bars if needed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#makeSelectionVisible--">makeSelectionVisible</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="makePointVisible-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>makePointVisible</h4>
<pre>public&nbsp;void&nbsp;makePointVisible(float&nbsp;x,
                             float&nbsp;y)</pre>
<div class="block">Ensures the point at (<code>x</code>, <code>y</code>) is visible in the plan displayed by this component,
 moving its scroll bars if needed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#makePointVisible-float-float-">makePointVisible</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getScale</h4>
<pre>public&nbsp;float&nbsp;getScale()</pre>
<div class="block">Returns the scale used to display the plan displayed by this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getScale--">getScale</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setScale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setScale</h4>
<pre>public&nbsp;void&nbsp;setScale(float&nbsp;scale)</pre>
<div class="block">Sets the scale used to display the plan displayed by this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setScale-float-">setScale</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="moveView-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveView</h4>
<pre>public&nbsp;void&nbsp;moveView(float&nbsp;dx,
                     float&nbsp;dy)</pre>
<div class="block">Moves the plan displayed by this component from (dx, dy) unit in the scrolling zone it belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#moveView-float-float-">moveView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="convertXPixelToModel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertXPixelToModel</h4>
<pre>public&nbsp;float&nbsp;convertXPixelToModel(int&nbsp;x)</pre>
<div class="block">Returns <code>x</code> converted in model coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertXPixelToModel-int-">convertXPixelToModel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="convertYPixelToModel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertYPixelToModel</h4>
<pre>public&nbsp;float&nbsp;convertYPixelToModel(int&nbsp;y)</pre>
<div class="block">Returns <code>y</code> converted in model coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertYPixelToModel-int-">convertYPixelToModel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="convertXModelToScreen-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertXModelToScreen</h4>
<pre>public&nbsp;int&nbsp;convertXModelToScreen(float&nbsp;x)</pre>
<div class="block">Returns <code>x</code> converted in screen coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertXModelToScreen-float-">convertXModelToScreen</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="convertYModelToScreen-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertYModelToScreen</h4>
<pre>public&nbsp;int&nbsp;convertYModelToScreen(float&nbsp;y)</pre>
<div class="block">Returns <code>y</code> converted in screen coordinates space.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#convertYModelToScreen-float-">convertYModelToScreen</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getPixelLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPixelLength</h4>
<pre>public&nbsp;float&nbsp;getPixelLength()</pre>
<div class="block">Returns the length in centimeters of a pixel with the current scale.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPixelLength--">getPixelLength</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTextBounds</h4>
<pre>public&nbsp;float[][]&nbsp;getTextBounds(java.lang.String&nbsp;text,
                               <a href="../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;style,
                               float&nbsp;x,
                               float&nbsp;y,
                               float&nbsp;angle)</pre>
<div class="block">Returns the coordinates of the bounding rectangle of the <code>text</code> displayed at
 the point (<code>x</code>,<code>y</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getTextBounds-java.lang.String-com.eteks.sweethome3d.model.TextStyle-float-float-float-">getTextBounds</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setCursor-com.eteks.sweethome3d.viewcontroller.PlanView.CursorType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCursor</h4>
<pre>public&nbsp;void&nbsp;setCursor(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.CursorType.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanView.CursorType</a>&nbsp;cursorType)</pre>
<div class="block">Sets the cursor of this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setCursor-com.eteks.sweethome3d.viewcontroller.PlanView.CursorType-">setCursor</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setCursor-java.awt.Cursor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCursor</h4>
<pre>public&nbsp;void&nbsp;setCursor(java.awt.Cursor&nbsp;cursor)</pre>
<div class="block">Sets the cursor of this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setCursor</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="getCursor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCursor</h4>
<pre>public&nbsp;java.awt.Cursor&nbsp;getCursor()</pre>
<div class="block">Returns the cursor of this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getCursor</code>&nbsp;in class&nbsp;<code>java.awt.Component</code></dd>
</dl>
</li>
</ul>
<a name="setToolTipFeedback-java.lang.String-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipFeedback</h4>
<pre>public&nbsp;void&nbsp;setToolTipFeedback(java.lang.String&nbsp;toolTipFeedback,
                               float&nbsp;x,
                               float&nbsp;y)</pre>
<div class="block">Sets tool tip text displayed as feedback.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipFeedback-java.lang.String-float-float-">setToolTipFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>toolTipFeedback</code> - the text displayed in the tool tip
                    or <code>null</code> to make tool tip disappear.</dd>
</dl>
</li>
</ul>
<a name="setToolTipEditedProperties-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty:A-java.lang.Object:A-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipEditedProperties</h4>
<pre>public&nbsp;void&nbsp;setToolTipEditedProperties(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>[]&nbsp;toolTipEditedProperties,
                                       java.lang.Object[]&nbsp;toolTipPropertyValues,
                                       float&nbsp;x,
                                       float&nbsp;y)</pre>
<div class="block">Sets properties edited in tool tip.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipEditedProperties-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty:A-java.lang.Object:A-float-float-">setToolTipEditedProperties</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setToolTipEditedPropertyValue-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setToolTipEditedPropertyValue</h4>
<pre>public&nbsp;void&nbsp;setToolTipEditedPropertyValue(<a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.EditableProperty.html" title="enum in com.eteks.sweethome3d.viewcontroller">PlanController.EditableProperty</a>&nbsp;toolTipEditedProperty,
                                          java.lang.Object&nbsp;toolTipPropertyValue)</pre>
<div class="block">Sets the value of a property edited in tool tip.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setToolTipEditedPropertyValue-com.eteks.sweethome3d.viewcontroller.PlanController.EditableProperty-java.lang.Object-">setToolTipEditedPropertyValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="deleteToolTipFeedback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteToolTipFeedback</h4>
<pre>public&nbsp;void&nbsp;deleteToolTipFeedback()</pre>
<div class="block">Deletes tool tip text from screen.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#deleteToolTipFeedback--">deleteToolTipFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setResizeIndicatorVisible-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResizeIndicatorVisible</h4>
<pre>public&nbsp;void&nbsp;setResizeIndicatorVisible(boolean&nbsp;visible)</pre>
<div class="block">Sets whether the resize indicator of selected wall or piece of furniture
 should be visible or not.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setResizeIndicatorVisible-boolean-">setResizeIndicatorVisible</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignmentFeedback</h4>
<pre>public&nbsp;void&nbsp;setAlignmentFeedback(java.lang.Class&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;alignedObjectClass,
                                 <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&nbsp;alignedObject,
                                 float&nbsp;x,
                                 float&nbsp;y,
                                 boolean&nbsp;showPoint)</pre>
<div class="block">Sets the location point for alignment feedback.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setAlignmentFeedback-java.lang.Class-com.eteks.sweethome3d.model.Selectable-float-float-boolean-">setAlignmentFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setAngleFeedback-float-float-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAngleFeedback</h4>
<pre>public&nbsp;void&nbsp;setAngleFeedback(float&nbsp;xCenter,
                             float&nbsp;yCenter,
                             float&nbsp;x1,
                             float&nbsp;y1,
                             float&nbsp;x2,
                             float&nbsp;y2)</pre>
<div class="block">Sets the points used to draw an angle in the plan displayed by this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setAngleFeedback-float-float-float-float-float-float-">setAngleFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setDraggedItemsFeedback-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDraggedItemsFeedback</h4>
<pre>public&nbsp;void&nbsp;setDraggedItemsFeedback(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;draggedItems)</pre>
<div class="block">Sets the feedback of dragged items drawn during a drag and drop operation,
 initiated from outside of the plan displayed by this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setDraggedItemsFeedback-java.util.List-">setDraggedItemsFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="setDimensionLinesFeedback-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDimensionLinesFeedback</h4>
<pre>public&nbsp;void&nbsp;setDimensionLinesFeedback(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&gt;&nbsp;dimensionLines)</pre>
<div class="block">Sets the given dimension lines to be drawn as feedback.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#setDimensionLinesFeedback-java.util.List-">setDimensionLinesFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="deleteFeedback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteFeedback</h4>
<pre>public&nbsp;void&nbsp;deleteFeedback()</pre>
<div class="block">Deletes all elements shown as feedback.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#deleteFeedback--">deleteFeedback</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="canImportDraggedItems-java.util.List-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>canImportDraggedItems</h4>
<pre>public&nbsp;boolean&nbsp;canImportDraggedItems(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;items,
                                     int&nbsp;x,
                                     int&nbsp;y)</pre>
<div class="block">Returns <code>true</code> if the given coordinates belong to the plan displayed by this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#canImportDraggedItems-java.util.List-int-int-">canImportDraggedItems</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPieceOfFurnitureSizeInPlan</h4>
<pre>public&nbsp;float[]&nbsp;getPieceOfFurnitureSizeInPlan(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns the size of the given piece of furniture in the horizontal plan.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPieceOfFurnitureSizeInPlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getPieceOfFurnitureSizeInPlan</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="isFurnitureSizeInPlanSupported--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isFurnitureSizeInPlanSupported</h4>
<pre>public&nbsp;boolean&nbsp;isFurnitureSizeInPlanSupported()</pre>
<div class="block">Returns <code>true</code> if this component is able to compute the size of horizontally rotated furniture.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#isFurnitureSizeInPlanSupported--">isFurnitureSizeInPlanSupported</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getHorizontalRuler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHorizontalRuler</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getHorizontalRuler()</pre>
<div class="block">Returns the component used as an horizontal ruler for the plan displayed by this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getHorizontalRuler--">getHorizontalRuler</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="getVerticalRuler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVerticalRuler</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getVerticalRuler()</pre>
<div class="block">Returns the component used as a vertical ruler for the plan displayed by this component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getVerticalRuler--">getVerticalRuler</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
</dl>
</li>
</ul>
<a name="print-java.awt.Graphics-java.awt.print.PageFormat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>print</h4>
<pre>public&nbsp;int&nbsp;print(java.awt.Graphics&nbsp;g,
                 java.awt.print.PageFormat&nbsp;pageFormat,
                 int&nbsp;pageIndex)
          throws java.awt.print.PrinterException</pre>
<div class="block">Prints the plan component.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>print</code>&nbsp;in interface&nbsp;<code>java.awt.print.Printable</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.awt.print.PrinterException</code></dd>
</dl>
</li>
</ul>
<a name="getPrintPreferredScale-java.awt.Graphics-java.awt.print.PageFormat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPrintPreferredScale</h4>
<pre>public&nbsp;float&nbsp;getPrintPreferredScale(java.awt.Graphics&nbsp;graphics,
                                    java.awt.print.PageFormat&nbsp;pageFormat)</pre>
<div class="block">Returns the preferred scale to print the plan component.</div>
</li>
</ul>
<a name="getPrintPreferredScale-float-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPrintPreferredScale</h4>
<pre>public&nbsp;float&nbsp;getPrintPreferredScale(float&nbsp;preferredWidth,
                                    float&nbsp;preferredHeight)</pre>
<div class="block">Returns the preferred scale to ensure it can be fully printed on the given print zone.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html#getPrintPreferredScale-float-float-">getPrintPreferredScale</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>preferredWidth</code> - width of the zone in cm</dd>
<dd><code>preferredHeight</code> - height of the zone in cm</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/MultipleLevelsPlanPanel.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/ModelPreviewComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/NullableCheckBox.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html" target="_top">Frames</a></li>
<li><a href="MultipleLevelsPlanPanel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.JPanel">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.JComponent">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
