<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.model.HomeTexture (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.model.HomeTexture (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/HomeTexture.html" target="_top">Frames</a></li>
<li><a href="HomeTexture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.model.HomeTexture" class="title">Uses of Class<br>com.eteks.sweethome3d.model.HomeTexture</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d</a></td>
<td class="colLast">
<div class="block">Contains various tool 3D classes and 3D home objects useful in 
<a href="../../../../../com/eteks/sweethome3d/swing/package-summary.html">Swing package</a>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.model">com.eteks.sweethome3d.model</a></td>
<td class="colLast">
<div class="block">Describes model classes of Sweet Home 3D.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeTexture-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeTexture-java.lang.String-">writeTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
            java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>texture</code> object with the given <code>writer</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.j3d">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a> in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/j3d/package-summary.html">com.eteks.sweethome3d.j3d</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><span class="typeNameLabel">TextureManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.html#getRotatedTextureHeight-com.eteks.sweethome3d.model.HomeTexture-">getRotatedTextureHeight</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Returns the height of the given texture once its rotation angle is applied.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><span class="typeNameLabel">TextureManager.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/TextureManager.html#getRotatedTextureWidth-com.eteks.sweethome3d.model.HomeTexture-">getRotatedTextureWidth</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Returns the width of the given texture once its rotation angle is applied.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected javax.media.j3d.TextureAttributes</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DBranch.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributes-com.eteks.sweethome3d.model.HomeTexture-">getTextureAttributes</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Returns shared texture attributes matching transformation applied to the given texture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected javax.media.j3d.TextureAttributes</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DBranch.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributes-com.eteks.sweethome3d.model.HomeTexture-boolean-">getTextureAttributes</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
                    boolean&nbsp;scaled)</code>
<div class="block">Returns shared texture attributes matching transformation applied to the given texture
 and scaled if required.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected javax.media.j3d.TextureAttributes</code></td>
<td class="colLast"><span class="typeNameLabel">Object3DBranch.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/j3d/Object3DBranch.html#getTextureAttributesFittingArea-com.eteks.sweethome3d.model.HomeTexture-float:A:A-boolean-">getTextureAttributesFittingArea</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
                               float[][]&nbsp;areaPoints,
                               boolean&nbsp;invertY)</code>
<div class="block">Returns texture attributes with a transformation scaled to fit the surface matching <code>areaPoints</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.model">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a> in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> that return <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">Room.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html#getCeilingTexture--">getCeilingTexture</a></span>()</code>
<div class="block">Returns the ceiling texture of this room.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">Room.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html#getFloorTexture--">getFloorTexture</a></span>()</code>
<div class="block">Returns the floor texture of this room.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeEnvironment.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getGroundTexture--">getGroundTexture</a></span>()</code>
<div class="block">Returns the ground texture of this environment.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">Wall.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html#getLeftSideTexture--">getLeftSideTexture</a></span>()</code>
<div class="block">Returns the left side texture of this wall.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">Wall.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html#getRightSideTexture--">getRightSideTexture</a></span>()</code>
<div class="block">Returns the right side texture of this wall.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeEnvironment.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#getSkyTexture--">getSkyTexture</a></span>()</code>
<div class="block">Returns the sky texture of this environment.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeMaterial.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeMaterial.html#getTexture--">getTexture</a></span>()</code>
<div class="block">Returns the texture of this material.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#getTexture--">getTexture</a></span>()</code>
<div class="block">Returns <code>null</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#getTexture--">getTexture</a></span>()</code>
<div class="block">Returns the texture of this piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">Baseboard.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Baseboard.html#getTexture--">getTexture</a></span>()</code>
<div class="block">Returns the texture of this baseboard.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a></code></td>
<td class="colLast"><span class="typeNameLabel">Baseboard.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Baseboard.html#getInstance-float-float-java.lang.Integer-com.eteks.sweethome3d.model.HomeTexture-">getInstance</a></span>(float&nbsp;thickness,
           float&nbsp;height,
           java.lang.Integer&nbsp;color,
           <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Returns an instance of this class matching the given parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Room.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html#setCeilingTexture-com.eteks.sweethome3d.model.HomeTexture-">setCeilingTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;ceilingTexture)</code>
<div class="block">Sets the ceiling texture of this room.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Room.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Room.html#setFloorTexture-com.eteks.sweethome3d.model.HomeTexture-">setFloorTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;floorTexture)</code>
<div class="block">Sets the floor texture of this room.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeEnvironment.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setGroundTexture-com.eteks.sweethome3d.model.HomeTexture-">setGroundTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture)</code>
<div class="block">Sets the ground texture of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Wall.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html#setLeftSideTexture-com.eteks.sweethome3d.model.HomeTexture-">setLeftSideTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;leftSideTexture)</code>
<div class="block">Sets the left side texture of this wall.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Wall.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Wall.html#setRightSideTexture-com.eteks.sweethome3d.model.HomeTexture-">setRightSideTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;rightSideTexture)</code>
<div class="block">Sets the right side texture of this wall.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeEnvironment.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#setSkyTexture-com.eteks.sweethome3d.model.HomeTexture-">setSkyTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture)</code>
<div class="block">Sets the sky texture of this environment and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeFurnitureGroup.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html#setTexture-com.eteks.sweethome3d.model.HomeTexture-">setTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Sets the <code>texture</code> of the furniture of this group.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomePieceOfFurniture.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html#setTexture-com.eteks.sweethome3d.model.HomeTexture-">setTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Sets the texture of this piece of furniture.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">com.eteks.sweethome3d.model</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/Baseboard.html#Baseboard-float-float-java.lang.Integer-com.eteks.sweethome3d.model.HomeTexture-">Baseboard</a></span>(float&nbsp;thickness,
         float&nbsp;height,
         java.lang.Integer&nbsp;color,
         <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Creates a baseboard.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#HomeEnvironment-int-com.eteks.sweethome3d.model.HomeTexture-int-com.eteks.sweethome3d.model.HomeTexture-int-float-">HomeEnvironment</a></span>(int&nbsp;groundColor,
               <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
               int&nbsp;skyColor,
               <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture,
               int&nbsp;lightColor,
               float&nbsp;wallsAlpha)</code>
<div class="block">Creates home environment from parameters.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#HomeEnvironment-int-com.eteks.sweethome3d.model.HomeTexture-int-int-float-">HomeEnvironment</a></span>(int&nbsp;groundColor,
               <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
               int&nbsp;skyColor,
               int&nbsp;lightColor,
               float&nbsp;wallsAlpha)</code>
<div class="block">Creates home environment from parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeEnvironment.html#HomeEnvironment-java.lang.String-int-com.eteks.sweethome3d.model.HomeTexture-int-com.eteks.sweethome3d.model.HomeTexture-int-float-">HomeEnvironment</a></span>(java.lang.String&nbsp;id,
               int&nbsp;groundColor,
               <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;groundTexture,
               int&nbsp;skyColor,
               <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;skyTexture,
               int&nbsp;lightColor,
               float&nbsp;wallsAlpha)</code>
<div class="block">Creates home environment from parameters.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeMaterial.html#HomeMaterial-java.lang.String-java.lang.Integer-com.eteks.sweethome3d.model.HomeTexture-java.lang.Float-">HomeMaterial</a></span>(java.lang.String&nbsp;name,
            java.lang.Integer&nbsp;color,
            <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
            java.lang.Float&nbsp;shininess)</code>
<div class="block">Creates a material instance from parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/model/HomeMaterial.html#HomeMaterial-java.lang.String-java.lang.String-java.lang.Integer-com.eteks.sweethome3d.model.HomeTexture-java.lang.Float-">HomeMaterial</a></span>(java.lang.String&nbsp;name,
            java.lang.String&nbsp;key,
            java.lang.Integer&nbsp;color,
            <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
            java.lang.Float&nbsp;shininess)</code>
<div class="block">Creates a material instance from parameters.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></code></td>
<td class="colLast"><span class="typeNameLabel">TextureChoiceController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html#getTexture--">getTexture</a></span>()</code>
<div class="block">Returns the texture displayed by view.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">TextureChoiceController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceController.html#setTexture-com.eteks.sweethome3d.model.HomeTexture-">setTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture)</code>
<div class="block">Sets the texture displayed by view and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/model/class-use/HomeTexture.html" target="_top">Frames</a></li>
<li><a href="HomeTexture.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
