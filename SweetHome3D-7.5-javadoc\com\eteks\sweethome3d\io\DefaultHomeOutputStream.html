<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>DefaultHomeOutputStream (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DefaultHomeOutputStream (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultHomeOutputStream.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" target="_top">Frames</a></li>
<li><a href="DefaultHomeOutputStream.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.FilterOutputStream">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class DefaultHomeOutputStream" class="title">Class DefaultHomeOutputStream</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.io.OutputStream</li>
<li>
<ul class="inheritance">
<li>java.io.FilterOutputStream</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.DefaultHomeOutputStream</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Closeable, java.io.Flushable, java.lang.AutoCloseable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DefaultHomeOutputStream</span>
extends java.io.FilterOutputStream</pre>
<div class="block">An <code>OutputStream</code> filter that writes a home in a stream
 at .sh3d file format.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io"><code>DefaultHomeInputStream</code></a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.io.FilterOutputStream">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.io.FilterOutputStream</h3>
<code>out</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html#DefaultHomeOutputStream-java.io.OutputStream-">DefaultHomeOutputStream</a></span>(java.io.OutputStream&nbsp;out)</code>
<div class="block">Creates a stream that will save a home and all the contents it references
 in an uncompressed zip stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html#DefaultHomeOutputStream-java.io.OutputStream-int-boolean-">DefaultHomeOutputStream</a></span>(java.io.OutputStream&nbsp;out,
                       int&nbsp;compressionLevel,
                       boolean&nbsp;includeTemporaryContent)</code>
<div class="block">Creates a stream that will save a home in a zip stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html#DefaultHomeOutputStream-java.io.OutputStream-int-com.eteks.sweethome3d.io.ContentRecording-">DefaultHomeOutputStream</a></span>(java.io.OutputStream&nbsp;out,
                       int&nbsp;compressionLevel,
                       <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording)</code>
<div class="block">Creates a stream that will save a home in a zip stream.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html#DefaultHomeOutputStream-java.io.OutputStream-int-com.eteks.sweethome3d.io.ContentRecording-boolean-com.eteks.sweethome3d.io.HomeXMLExporter-">DefaultHomeOutputStream</a></span>(java.io.OutputStream&nbsp;out,
                       int&nbsp;compressionLevel,
                       <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                       boolean&nbsp;serializedHome,
                       <a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html" title="class in com.eteks.sweethome3d.io">HomeXMLExporter</a>&nbsp;homeXmlExporter)</code>
<div class="block">Creates a stream that will serialize a home in a zip stream.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html#writeHome-com.eteks.sweethome3d.model.Home-">writeHome</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Writes home in a zipped stream followed by <code>Content</code> objects
 it points to.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.FilterOutputStream">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.io.FilterOutputStream</h3>
<code>close, flush, write, write, write</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DefaultHomeOutputStream-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultHomeOutputStream</h4>
<pre>public&nbsp;DefaultHomeOutputStream(java.io.OutputStream&nbsp;out)
                        throws java.io.IOException</pre>
<div class="block">Creates a stream that will save a home and all the contents it references
 in an uncompressed zip stream. Home data will be serialized in an entry named
 <code>Home</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="DefaultHomeOutputStream-java.io.OutputStream-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultHomeOutputStream</h4>
<pre>public&nbsp;DefaultHomeOutputStream(java.io.OutputStream&nbsp;out,
                               int&nbsp;compressionLevel,
                               boolean&nbsp;includeTemporaryContent)
                        throws java.io.IOException</pre>
<div class="block">Creates a stream that will save a home in a zip stream. Home data will be serialized
 in an entry named <code>Home</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>compressionLevel</code> - 0-9</dd>
<dd><code>includeTemporaryContent</code> - if <code>true</code>, content instances of
            <code>TemporaryURLContent</code> and <code>SimpleURLContent</code> classes
            referenced by the saved home as well as the content previously saved with it
            will be written. If <code>false</code>, all the content instances
            referenced by the saved home will be written in the zip stream.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="DefaultHomeOutputStream-java.io.OutputStream-int-com.eteks.sweethome3d.io.ContentRecording-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultHomeOutputStream</h4>
<pre>public&nbsp;DefaultHomeOutputStream(java.io.OutputStream&nbsp;out,
                               int&nbsp;compressionLevel,
                               <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording)
                        throws java.io.IOException</pre>
<div class="block">Creates a stream that will save a home in a zip stream. Home data will be serialized
 in an entry named <code>Home</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>compressionLevel</code> - 0-9</dd>
<dd><code>contentRecording</code> - how content should be recorded with home.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="DefaultHomeOutputStream-java.io.OutputStream-int-com.eteks.sweethome3d.io.ContentRecording-boolean-com.eteks.sweethome3d.io.HomeXMLExporter-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DefaultHomeOutputStream</h4>
<pre>public&nbsp;DefaultHomeOutputStream(java.io.OutputStream&nbsp;out,
                               int&nbsp;compressionLevel,
                               <a href="../../../../com/eteks/sweethome3d/io/ContentRecording.html" title="enum in com.eteks.sweethome3d.io">ContentRecording</a>&nbsp;contentRecording,
                               boolean&nbsp;serializedHome,
                               <a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html" title="class in com.eteks.sweethome3d.io">HomeXMLExporter</a>&nbsp;homeXmlExporter)
                        throws java.io.IOException</pre>
<div class="block">Creates a stream that will serialize a home in a zip stream. Home data will be serialized
 in an entry named <code>Home</code> if <code>serializedHome</code> is <code>true</code>,
 and saved in <code>Home.xml</code> entry at XML format if <code>homeXmlExporter</code> is not <code>null</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>compressionLevel</code> - 0-9</dd>
<dd><code>contentRecording</code> - specifies how content should be recorded with home</dd>
<dd><code>serializedHome</code> - if <code>true</code>, zip stream will include a <code>Home</code>
            entry containing the serialized home</dd>
<dd><code>homeXmlExporter</code> - if not <code>null</code>, sets how a home will be saved
            in an additional <code>Home.xml</code> entry</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="writeHome-com.eteks.sweethome3d.model.Home-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeHome</h4>
<pre>public&nbsp;void&nbsp;writeHome(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)
               throws java.io.IOException</pre>
<div class="block">Writes home in a zipped stream followed by <code>Content</code> objects
 it points to.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultHomeOutputStream.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" target="_top">Frames</a></li>
<li><a href="DefaultHomeOutputStream.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.FilterOutputStream">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
