<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>ResourceURLContent (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ResourceURLContent (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResourceURLContent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/tools/SimpleURLContent.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/tools/ResourceURLContent.html" target="_top">Frames</a></li>
<li><a href="ResourceURLContent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.tools</div>
<h2 title="Class ResourceURLContent" class="title">Class ResourceURLContent</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools">com.eteks.sweethome3d.tools.URLContent</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.tools.ResourceURLContent</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>, java.io.Serializable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ResourceURLContent</span>
extends <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools">URLContent</a></pre>
<div class="block">URL content read from a class resource or a SH3F file.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.tools.ResourceURLContent">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ResourceURLContent.html#ResourceURLContent-java.lang.Class-java.lang.String-">ResourceURLContent</a></span>(java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                  java.lang.String&nbsp;resourceName)</code>
<div class="block">Creates a content for <code>resourceName</code> relative to <code>resourceClass</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ResourceURLContent.html#ResourceURLContent-java.lang.Class-java.lang.String-boolean-">ResourceURLContent</a></span>(java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                  java.lang.String&nbsp;resourceName,
                  boolean&nbsp;multiPartResource)</code>
<div class="block">Creates a content for <code>resourceName</code> relative to <code>resourceClass</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ResourceURLContent.html#ResourceURLContent-java.lang.ClassLoader-java.lang.String-">ResourceURLContent</a></span>(java.lang.ClassLoader&nbsp;resourceClassLoader,
                  java.lang.String&nbsp;resourceName)</code>
<div class="block">Creates a content for <code>resourceName</code> relative to <code>resourceClassLoader</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ResourceURLContent.html#ResourceURLContent-java.net.URL-boolean-">ResourceURLContent</a></span>(java.net.URL&nbsp;url,
                  boolean&nbsp;multiPartResource)</code>
<div class="block">Creates a content for <code>resourceUrl</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/ResourceURLContent.html#isMultiPartResource--">isMultiPartResource</a></span>()</code>
<div class="block">Returns <code>true</code> if the resource is a multi part resource stored
 in a folder with other required resources.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.tools.URLContent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.tools.<a href="../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools">URLContent</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/tools/URLContent.html#equals-java.lang.Object-">equals</a>, <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html#getJAREntryName--">getJAREntryName</a>, <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html#getJAREntryURL--">getJAREntryURL</a>, <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html#getSize--">getSize</a>, <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html#getURL--">getURL</a>, <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html#hashCode--">hashCode</a>, <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html#isJAREntry--">isJAREntry</a>, <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html#openStream--">openStream</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, finalize, getClass, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ResourceURLContent-java.lang.Class-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ResourceURLContent</h4>
<pre>public&nbsp;ResourceURLContent(java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                          java.lang.String&nbsp;resourceName)</pre>
<div class="block">Creates a content for <code>resourceName</code> relative to <code>resourceClass</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourceClass</code> - the class relative to the resource name to load</dd>
<dd><code>resourceName</code> - the name of the resource</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if the resource doesn't match a valid resource.</dd>
</dl>
</li>
</ul>
<a name="ResourceURLContent-java.lang.Class-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ResourceURLContent</h4>
<pre>public&nbsp;ResourceURLContent(java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                          java.lang.String&nbsp;resourceName,
                          boolean&nbsp;multiPartResource)</pre>
<div class="block">Creates a content for <code>resourceName</code> relative to <code>resourceClass</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourceClass</code> - the class relative to the resource name to load</dd>
<dd><code>resourceName</code> - the name of the resource</dd>
<dd><code>multiPartResource</code> - if <code>true</code> then the resource is a multi part resource
           stored in a folder with other required resources</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if the resource doesn't match a valid resource.</dd>
</dl>
</li>
</ul>
<a name="ResourceURLContent-java.lang.ClassLoader-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ResourceURLContent</h4>
<pre>public&nbsp;ResourceURLContent(java.lang.ClassLoader&nbsp;resourceClassLoader,
                          java.lang.String&nbsp;resourceName)</pre>
<div class="block">Creates a content for <code>resourceName</code> relative to <code>resourceClassLoader</code>.
 <code>resourceName</code> is absolute and shouldn't start with a slash.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourceClassLoader</code> - the class loader used to load the given resource name</dd>
<dd><code>resourceName</code> - the name of the resource</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if the resource doesn't match a valid resource.</dd>
</dl>
</li>
</ul>
<a name="ResourceURLContent-java.net.URL-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ResourceURLContent</h4>
<pre>public&nbsp;ResourceURLContent(java.net.URL&nbsp;url,
                          boolean&nbsp;multiPartResource)</pre>
<div class="block">Creates a content for <code>resourceUrl</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>url</code> - the URL of the resource</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isMultiPartResource--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isMultiPartResource</h4>
<pre>public&nbsp;boolean&nbsp;isMultiPartResource()</pre>
<div class="block">Returns <code>true</code> if the resource is a multi part resource stored
 in a folder with other required resources.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResourceURLContent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/tools/SimpleURLContent.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/tools/ResourceURLContent.html" target="_top">Frames</a></li>
<li><a href="ResourceURLContent.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
