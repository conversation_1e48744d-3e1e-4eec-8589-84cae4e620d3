<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title>PluginAction (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="PluginAction (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":6,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PluginAction.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/plugin/PluginAction.html" target="_top">Frames</a></li>
<li><a href="PluginAction.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.plugin</div>
<h2 title="Class PluginAction" class="title">Class PluginAction</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.plugin.PluginAction</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">PluginAction</span>
extends java.lang.Object</pre>
<div class="block">An action made available to application users through a plugin.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a></span></code>
<div class="block">Enumeration of the various properties this action may define.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#PluginAction--">PluginAction</a></span>()</code>
<div class="block">Creates a disabled plug-in action.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#PluginAction-java.lang.String-java.lang.String-java.lang.ClassLoader-">PluginAction</a></span>(java.lang.String&nbsp;resourceBaseName,
            java.lang.String&nbsp;actionPrefix,
            java.lang.ClassLoader&nbsp;pluginClassLoader)</code>
<div class="block">Creates a disabled plug-in action with properties retrieved from a resource bundle 
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#PluginAction-java.lang.String-java.lang.String-java.lang.ClassLoader-boolean-">PluginAction</a></span>(java.lang.String&nbsp;resourceBaseName,
            java.lang.String&nbsp;actionPrefix,
            java.lang.ClassLoader&nbsp;pluginClassLoader,
            boolean&nbsp;enabled)</code>
<div class="block">Creates an action with properties retrieved from a resource bundle 
 in which key starts with <code>actionPrefix</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this plugin action.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>abstract void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#execute--">execute</a></span>()</code>
<div class="block">Executes this action.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#getPropertyValue-com.eteks.sweethome3d.plugin.PluginAction.Property-">getPropertyValue</a></span>(<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>&nbsp;property)</code>
<div class="block">Returns a property value of this action.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#isEnabled--">isEnabled</a></span>()</code>
<div class="block">Returns the enabled state of this action.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#putPropertyValue-com.eteks.sweethome3d.plugin.PluginAction.Property-java.lang.Object-">putPropertyValue</a></span>(<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>&nbsp;property,
                java.lang.Object&nbsp;value)</code>
<div class="block">Sets a property value of this action, and fires a <code>PropertyChangeEvent</code>
 if the value changed.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this plugin action.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.html#setEnabled-boolean-">setEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">Sets the enabled state of this action.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="PluginAction--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PluginAction</h4>
<pre>public&nbsp;PluginAction()</pre>
<div class="block">Creates a disabled plug-in action.</div>
</li>
</ul>
<a name="PluginAction-java.lang.String-java.lang.String-java.lang.ClassLoader-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PluginAction</h4>
<pre>public&nbsp;PluginAction(java.lang.String&nbsp;resourceBaseName,
                    java.lang.String&nbsp;actionPrefix,
                    java.lang.ClassLoader&nbsp;pluginClassLoader)</pre>
<div class="block">Creates a disabled plug-in action with properties retrieved from a resource bundle 
 in which key starts with <code>actionPrefix</code>.
 <br>For example, a plug-in action created by the call
 <code>new PluginAction("com.mycompany.mypackage.MyResources", "EXPORT", plugin.getPluginClassLoader())</code> 
 will retrieve its property values from the <code>/com/mycompany/mypackage/MyResources.properties</code> file
 bundled with the plug-in class, and this file may describe the action <code>EXPORT</code> 
 with the following keys: 
 <pre> EXPORT.NAME=Export
 EXPORT.SHORT_DESCRIPTION=Export home data
 EXPORT.SMALL_ICON=/com/mycompany/mypackage/resources/export.png
 EXPORT.MNEMONIC=X
 EXPORT.TOOL_BAR=true
 EXPORT.MENU=File</pre></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourceBaseName</code> - the base name of a resource bundle</dd>
<dd><code>actionPrefix</code> - prefix used in resource bundle to search action properties</dd>
<dd><code>pluginClassLoader</code> - the class loader that will be used to search the resource bundle</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.util.MissingResourceException</code> - if no resource bundle could be found from 
    <code>resourceBaseName</code>.</dd>
</dl>
</li>
</ul>
<a name="PluginAction-java.lang.String-java.lang.String-java.lang.ClassLoader-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PluginAction</h4>
<pre>public&nbsp;PluginAction(java.lang.String&nbsp;resourceBaseName,
                    java.lang.String&nbsp;actionPrefix,
                    java.lang.ClassLoader&nbsp;pluginClassLoader,
                    boolean&nbsp;enabled)</pre>
<div class="block">Creates an action with properties retrieved from a resource bundle 
 in which key starts with <code>actionPrefix</code>.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourceBaseName</code> - the base name of a resource bundle</dd>
<dd><code>actionPrefix</code> - prefix used in resource bundle to search action properties</dd>
<dd><code>pluginClassLoader</code> - the class loader that will be used to search the resource bundle</dd>
<dd><code>enabled</code> - <code>true</code> if the action should be enabled at creation.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.util.MissingResourceException</code> - if no resource bundle could be found from 
    <code>resourceBaseName</code>.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addPropertyChangeListener-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this plugin action.</div>
</li>
</ul>
<a name="removePropertyChangeListener-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this plugin action.</div>
</li>
</ul>
<a name="getPropertyValue-com.eteks.sweethome3d.plugin.PluginAction.Property-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPropertyValue</h4>
<pre>public&nbsp;java.lang.Object&nbsp;getPropertyValue(<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>&nbsp;property)</pre>
<div class="block">Returns a property value of this action.</div>
</li>
</ul>
<a name="putPropertyValue-com.eteks.sweethome3d.plugin.PluginAction.Property-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>putPropertyValue</h4>
<pre>public&nbsp;void&nbsp;putPropertyValue(<a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin">PluginAction.Property</a>&nbsp;property,
                             java.lang.Object&nbsp;value)</pre>
<div class="block">Sets a property value of this action, and fires a <code>PropertyChangeEvent</code>
 if the value changed.</div>
</li>
</ul>
<a name="setEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnabled</h4>
<pre>public&nbsp;void&nbsp;setEnabled(boolean&nbsp;enabled)</pre>
<div class="block">Sets the enabled state of this action. When enabled, any menu item or tool bar button 
 associated with this object is enabled and able to call the <code>execute</code> method.
 If the value has changed, a <code>PropertyChangeEvent</code> is sent
 to listeners. By default, an action is disabled.</div>
</li>
</ul>
<a name="isEnabled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEnabled</h4>
<pre>public&nbsp;boolean&nbsp;isEnabled()</pre>
<div class="block">Returns the enabled state of this action.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this action is enabled.</dd>
</dl>
</li>
</ul>
<a name="execute--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>execute</h4>
<pre>public abstract&nbsp;void&nbsp;execute()</pre>
<div class="block">Executes this action. This method will be called by application when the user
 wants to execute this action.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/PluginAction.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/plugin/Plugin.html" title="class in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/plugin/PluginAction.Property.html" title="enum in com.eteks.sweethome3d.plugin"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/plugin/PluginAction.html" target="_top">Frames</a></li>
<li><a href="PluginAction.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
