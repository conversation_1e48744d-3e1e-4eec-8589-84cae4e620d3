<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:47 CEST 2024 -->
<title>FileContentManager (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileContentManager (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FileContentManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/FontNameComboBox.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/FileContentManager.html" target="_top">Frames</a></li>
<li><a href="FileContentManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class FileContentManager" class="title">Class FileContentManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.FileContentManager</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/applet/AppletContentManager.html" title="class in com.eteks.sweethome3d.applet">AppletContentManager</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">FileContentManager</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></pre>
<div class="block">Content manager for files with Swing file choosers.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.ContentManager">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#FileContentManager-com.eteks.sweethome3d.model.UserPreferences-">FileContentManager</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#confirmOverwrite-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-">confirmOverwrite</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
                java.lang.String&nbsp;path)</code>
<div class="block">Displays a dialog that let user choose whether he wants to overwrite
 file <code>path</code> or not.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getContent-java.lang.String-">getContent</a></span>(java.lang.String&nbsp;contentPath)</code>
<div class="block">Returns a <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools"><code>URL content</code></a> object that references
 the given file path.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getDefaultFileExtension-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getDefaultFileExtension</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the default file extension of a given content type.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getFileDialogTitle-boolean-">getFileDialogTitle</a></span>(boolean&nbsp;save)</code>
<div class="block">Returns default file dialog title.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected java.lang.String[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getFileExtensions-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getFileExtensions</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the supported file extensions for a given content type.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected javax.swing.filechooser.FileFilter[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getFileFilter-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getFileFilter</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the file filters available for a given content type.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected java.io.File</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getLastDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getLastDirectory</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the last directory used for the given content type.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#getPresentationName-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getPresentationName</a></span>(java.lang.String&nbsp;contentPath,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the file name of the file path in parameter.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#isAcceptable-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">isAcceptable</a></span>(java.lang.String&nbsp;contentPath,
            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns <code>true</code> if the file path in parameter is accepted
 for <code>contentType</code>.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#isDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">isDirectory</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns <code>true</code> if the given content type is for directories.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#setLastDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.io.File-">setLastDirectory</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
                java.io.File&nbsp;directory)</code>
<div class="block">Stores the last directory for the given content type.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">showOpenDialog</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</code>
<div class="block">Returns the file path chosen by user with an open file dialog.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/FileContentManager.html#showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">showSaveDialog</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
              java.lang.String&nbsp;dialogTitle,
              <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
              java.lang.String&nbsp;path)</code>
<div class="block">Returns the file path chosen by user with a save file dialog.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="FileContentManager-com.eteks.sweethome3d.model.UserPreferences-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FileContentManager</h4>
<pre>public&nbsp;FileContentManager(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getContent-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContent</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getContent(java.lang.String&nbsp;contentPath)
                   throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Returns a <a href="../../../../com/eteks/sweethome3d/tools/URLContent.html" title="class in com.eteks.sweethome3d.tools"><code>URL content</code></a> object that references
 the given file path.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#getContent-java.lang.String-">getContent</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="getPresentationName-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPresentationName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getPresentationName(java.lang.String&nbsp;contentPath,
                                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns the file name of the file path in parameter.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#getPresentationName-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">getPresentationName</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
</dl>
</li>
</ul>
<a name="getFileFilter-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileFilter</h4>
<pre>protected&nbsp;javax.swing.filechooser.FileFilter[]&nbsp;getFileFilter(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns the file filters available for a given content type.
 This method may be overridden to add some file filters to existing content types
 or to define the filters of a user defined content type.</div>
</li>
</ul>
<a name="getDefaultFileExtension-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultFileExtension</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDefaultFileExtension(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns the default file extension of a given content type.
 If not <code>null</code> this extension will be appended automatically
 to the file name chosen by user in save dialog.
 This method may be overridden to change the default file extension of an existing content type
 or to define the default file extension of a user defined content type.</div>
</li>
</ul>
<a name="getFileExtensions-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileExtensions</h4>
<pre>protected&nbsp;java.lang.String[]&nbsp;getFileExtensions(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns the supported file extensions for a given content type.
 This method may be overridden to change the file extensions of an existing content type
 or to define the file extensions of a user defined content type.</div>
</li>
</ul>
<a name="isAcceptable-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAcceptable</h4>
<pre>public&nbsp;boolean&nbsp;isAcceptable(java.lang.String&nbsp;contentPath,
                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns <code>true</code> if the file path in parameter is accepted
 for <code>contentType</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#isAcceptable-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">isAcceptable</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
</dl>
</li>
</ul>
<a name="isDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDirectory</h4>
<pre>protected&nbsp;boolean&nbsp;isDirectory(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns <code>true</code> if the given content type is for directories.</div>
</li>
</ul>
<a name="showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showOpenDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showOpenDialog(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
                                       java.lang.String&nbsp;dialogTitle,
                                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns the file path chosen by user with an open file dialog.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#showOpenDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">showOpenDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the file path or <code>null</code> if user canceled its choice.</dd>
</dl>
</li>
</ul>
<a name="showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showSaveDialog</h4>
<pre>public&nbsp;java.lang.String&nbsp;showSaveDialog(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
                                       java.lang.String&nbsp;dialogTitle,
                                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
                                       java.lang.String&nbsp;path)</pre>
<div class="block">Returns the file path chosen by user with a save file dialog.
 If this file already exists, the user will be prompted whether
 he wants to overwrite this existing file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html#showSaveDialog-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.lang.String-">showSaveDialog</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the chosen file path or <code>null</code> if user canceled its choice.</dd>
</dl>
</li>
</ul>
<a name="getLastDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastDirectory</h4>
<pre>protected&nbsp;java.io.File&nbsp;getLastDirectory(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType)</pre>
<div class="block">Returns the last directory used for the given content type.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the last directory for <code>contentType</code> or the default last directory
         if it's not set. If <code>contentType</code> is <code>null</code>, the
         returned directory will be the default last one or <code>null</code> if it's not set yet.</dd>
</dl>
</li>
</ul>
<a name="setLastDirectory-com.eteks.sweethome3d.viewcontroller.ContentManager.ContentType-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastDirectory</h4>
<pre>protected&nbsp;void&nbsp;setLastDirectory(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.ContentType.html" title="enum in com.eteks.sweethome3d.viewcontroller">ContentManager.ContentType</a>&nbsp;contentType,
                                java.io.File&nbsp;directory)</pre>
<div class="block">Stores the last directory for the given content type.</div>
</li>
</ul>
<a name="getFileDialogTitle-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileDialogTitle</h4>
<pre>protected&nbsp;java.lang.String&nbsp;getFileDialogTitle(boolean&nbsp;save)</pre>
<div class="block">Returns default file dialog title.</div>
</li>
</ul>
<a name="confirmOverwrite-com.eteks.sweethome3d.viewcontroller.View-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>confirmOverwrite</h4>
<pre>protected&nbsp;boolean&nbsp;confirmOverwrite(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;parentView,
                                   java.lang.String&nbsp;path)</pre>
<div class="block">Displays a dialog that let user choose whether he wants to overwrite
 file <code>path</code> or not.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if user confirmed to overwrite.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FileContentManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/FontNameComboBox.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/FileContentManager.html" target="_top">Frames</a></li>
<li><a href="FileContentManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
