# ApplicationPlugin_fr.properties
# Sweet Home 3D AI Plugin Configuration - French
# Copyright (c) 2025 Samuel <PERSON>

# Plugin identification
name=Analyse IA de Plan d'Étage
description=Analysez les plans d'étage en utilisant l'intelligence artificielle pour fournir des insights et des suggestions d'amélioration
provider=<PERSON> Kpassegna

# Features
features=Analyse IA, Insights de Plan d'Étage, Multiples Fournisseurs IA, Contrôles de Confidentialité

# Requirements
requirements=Connexion Internet pour les fournisseurs IA cloud (optionnel pour les fournisseurs locaux)

# UI Strings for internationalization
# Action properties
AIAction.Name=Analyse IA
AIAction.ShortDescription=Analyser le plan d'étage avec l'IA
AIAction.Menu=Outils

# Dialog titles
AIChatDialog.title=Analyse IA de Plan d'Étage
AISettingsDialog.title=Paramètres IA

# Button labels
button.send=Envoyer
button.newAnalysis=Nouvelle Analyse
button.settings=Paramètres
button.testConnection=Tester la Connexion
button.save=Enregistrer
button.cancel=Annuler

# Labels
label.provider=Fournisseur :
label.baseUrl=URL de Base :
label.apiKey=Clé API :
label.model=Modèle :
label.temperature=Température :
label.maxTokens=Tokens Max :
label.status=Statut :

# Messages
message.analyzing=Analyse du plan d'étage...
message.processingQuestion=Traitement de la question...
message.testingConnection=Test de la connexion...
message.connectionSuccessful=Connexion réussie !
message.connectionFailed=Échec de la connexion : {0}
message.configurationSaved=Configuration enregistrée avec succès
message.validationError=Erreurs de configuration :\n{0}
message.noConfiguration=Fournisseur IA non configuré. Veuillez d'abord configurer les paramètres.

# Analysis prompt
analysis.prompt=Veuillez analyser ce plan d'étage et fournir des insights complets incluant :\n1. Efficacité de l'aménagement et utilisation de l'espace\n2. Flux de circulation et modèles de déplacement\n3. Opportunités d'éclairage naturel et de ventilation\n4. Considérations d'accessibilité\n5. Relations fonctionnelles entre les espaces\n6. Suggestions d'amélioration\n7. Conformité aux normes de construction communes\n8. Considérations d'efficacité énergétique\n\nVeuillez fournir des recommandations spécifiques et exploitables qui amélioreraient la fonctionnalité, le confort et l'attrait esthétique de cet espace.

# Error messages
error.analysisError=Erreur d'analyse : {0}
error.configurationError=Erreur de configuration : {0}
error.connectionError=Erreur de connexion : {0}
error.invalidConfiguration=Configuration invalide
error.missingApiKey=La clé API est requise
error.missingBaseUrl=L'URL de base est requise
error.missingModel=La sélection du modèle est requise

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Local)
provider.lmstudio=LM Studio (Local)
provider.anythingllm=AnythingLLM (Local)
provider.jan=Jan (Local)
provider.custom=Personnalisé
