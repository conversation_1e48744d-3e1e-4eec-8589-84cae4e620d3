<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>OperatingSystem (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OperatingSystem (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/OperatingSystem.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/tools/ResourceURLContent.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/tools/OperatingSystem.html" target="_top">Frames</a></li>
<li><a href="OperatingSystem.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.tools</div>
<h2 title="Class OperatingSystem" class="title">Class OperatingSystem</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.tools.OperatingSystem</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">OperatingSystem</span>
extends java.lang.Object</pre>
<div class="block">Tools used to test current user operating system.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#compareVersions-java.lang.String-java.lang.String-">compareVersions</a></span>(java.lang.String&nbsp;version1,
               java.lang.String&nbsp;version2)</code>
<div class="block">Returns a negative number if <code>version1</code> &lt; <code>version2</code>,
 0 if <code>version1</code> = <code>version2</code>
 and a positive number if <code>version1</code> &gt; <code>version2</code>.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.io.File</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#createTemporaryFile-java.lang.String-java.lang.String-">createTemporaryFile</a></span>(java.lang.String&nbsp;prefix,
                   java.lang.String&nbsp;suffix)</code>
<div class="block">Returns a temporary file that will be deleted when JVM will exit.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#deleteTemporaryFiles--">deleteTemporaryFiles</a></span>()</code>
<div class="block">Deletes all the temporary files created with <a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#createTemporaryFile-java.lang.String-java.lang.String-"><code>createTemporaryFile</code></a>.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.io.File</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#getDefaultApplicationFolder--">getDefaultApplicationFolder</a></span>()</code>
<div class="block">Returns default application folder.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.util.Comparator&lt;java.io.File&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#getFileVersionComparator--">getFileVersionComparator</a></span>()</code>
<div class="block">Returns a file comparator that sorts file names according to their version number (excluding their extension when they are the same).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isJavaVersionBetween-java.lang.String-java.lang.String-">isJavaVersionBetween</a></span>(java.lang.String&nbsp;javaMinimumVersion,
                    java.lang.String&nbsp;javaMaximumVersion)</code>
<div class="block">Returns <code>true</code> if the version of the current JVM is greater or equal to the
 <code>javaMinimumVersion</code> and smaller than <code>javaMaximumVersion</code>.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isJavaVersionGreaterOrEqual-java.lang.String-">isJavaVersionGreaterOrEqual</a></span>(java.lang.String&nbsp;javaMinimumVersion)</code>
<div class="block">Returns <code>true</code> if the given version is greater than or equal to the version
 of the current JVM.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isLinux--">isLinux</a></span>()</code>
<div class="block">Returns <code>true</code> if current operating is Linux.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isMacOSX--">isMacOSX</a></span>()</code>
<div class="block">Returns <code>true</code> if current operating is Mac OS X.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isMacOSXBigSurOrSuperior--">isMacOSXBigSurOrSuperior</a></span>()</code>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.16 or superior.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isMacOSXHighSierraOrSuperior--">isMacOSXHighSierraOrSuperior</a></span>()</code>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.13 or superior.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isMacOSXLeopardOrSuperior--">isMacOSXLeopardOrSuperior</a></span>()</code>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.5 or superior.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isMacOSXLionOrSuperior--">isMacOSXLionOrSuperior</a></span>()</code>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.7 or superior.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isMacOSXYosemiteOrSuperior--">isMacOSXYosemiteOrSuperior</a></span>()</code>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.10 or superior.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#isWindows--">isWindows</a></span>()</code>
<div class="block">Returns <code>true</code> if current operating is Windows.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isLinux--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isLinux</h4>
<pre>public static&nbsp;boolean&nbsp;isLinux()</pre>
<div class="block">Returns <code>true</code> if current operating is Linux.</div>
</li>
</ul>
<a name="isWindows--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isWindows</h4>
<pre>public static&nbsp;boolean&nbsp;isWindows()</pre>
<div class="block">Returns <code>true</code> if current operating is Windows.</div>
</li>
</ul>
<a name="isMacOSX--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMacOSX</h4>
<pre>public static&nbsp;boolean&nbsp;isMacOSX()</pre>
<div class="block">Returns <code>true</code> if current operating is Mac OS X.</div>
</li>
</ul>
<a name="isMacOSXLeopardOrSuperior--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMacOSXLeopardOrSuperior</h4>
<pre>public static&nbsp;boolean&nbsp;isMacOSXLeopardOrSuperior()</pre>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.5 or superior.</div>
</li>
</ul>
<a name="isMacOSXLionOrSuperior--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMacOSXLionOrSuperior</h4>
<pre>public static&nbsp;boolean&nbsp;isMacOSXLionOrSuperior()</pre>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.7 or superior.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.1</dd>
</dl>
</li>
</ul>
<a name="isMacOSXYosemiteOrSuperior--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMacOSXYosemiteOrSuperior</h4>
<pre>public static&nbsp;boolean&nbsp;isMacOSXYosemiteOrSuperior()</pre>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.10 or superior.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="isMacOSXHighSierraOrSuperior--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMacOSXHighSierraOrSuperior</h4>
<pre>public static&nbsp;boolean&nbsp;isMacOSXHighSierraOrSuperior()</pre>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.13 or superior.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.7</dd>
</dl>
</li>
</ul>
<a name="isMacOSXBigSurOrSuperior--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMacOSXBigSurOrSuperior</h4>
<pre>public static&nbsp;boolean&nbsp;isMacOSXBigSurOrSuperior()</pre>
<div class="block">Returns <code>true</code> if current operating is Mac OS X 10.16 or superior.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.5</dd>
</dl>
</li>
</ul>
<a name="isJavaVersionGreaterOrEqual-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isJavaVersionGreaterOrEqual</h4>
<pre>public static&nbsp;boolean&nbsp;isJavaVersionGreaterOrEqual(java.lang.String&nbsp;javaMinimumVersion)</pre>
<div class="block">Returns <code>true</code> if the given version is greater than or equal to the version
 of the current JVM.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="isJavaVersionBetween-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isJavaVersionBetween</h4>
<pre>public static&nbsp;boolean&nbsp;isJavaVersionBetween(java.lang.String&nbsp;javaMinimumVersion,
                                           java.lang.String&nbsp;javaMaximumVersion)</pre>
<div class="block">Returns <code>true</code> if the version of the current JVM is greater or equal to the
 <code>javaMinimumVersion</code> and smaller than <code>javaMaximumVersion</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.2</dd>
</dl>
</li>
</ul>
<a name="compareVersions-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compareVersions</h4>
<pre>public static&nbsp;int&nbsp;compareVersions(java.lang.String&nbsp;version1,
                                  java.lang.String&nbsp;version2)</pre>
<div class="block">Returns a negative number if <code>version1</code> &lt; <code>version2</code>,
 0 if <code>version1</code> = <code>version2</code>
 and a positive number if <code>version1</code> &gt; <code>version2</code>.
 Version strings are first split into parts, each subpart ending at each punctuation, space
 or when a character of a different type is encountered (letter vs digit). Then each numeric
 or string subparts are compared to each other, strings being considered greater than null numbers
 and pre release strings (i.e. alpha, beta, rc). Examples:<pre>
 "" < "1"
 "0" < "1.0"
 "1.2beta" < "1.2"
 "1.2beta" < "1.2beta2"
 "1.2beta" < "1.2.0"
 "1.2beta4" < "1.2beta10"
 "1.2beta4" < "1.2"
 "1.2beta4" < "1.2rc"
 "1.2alpha" < "1.2beta"
 "1.2beta" < "1.2rc"
 "1.2rc" < "1.2"
 "1.2rc" < "1.2a"
 "1.2" < "1.2a"
 "1.2.0" < "1.2a"
 "1.2a" < "1.2b"
 "1.2a" < "1.2.1"
 "1.7.0_11" < "1.7.0_12"
 "1.7.0_11rc1" < "1.7.0_11rc2"
 "1.7.0_11rc" < "1.7.0_11"
 "1.7.0_9" < "1.7.0_11rc"
 "1.2" < "1.2.1"
 "1.2" < "1.2.0.1"

 "1.2" = "1.2.0.0" (missing information is considered as 0)
 "1.2beta4" = "1.2 beta-4" (punctuation, space or missing punctuation doesn't influence result)
 "1.2beta4" = "1,2,beta,4"
 </pre></div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="createTemporaryFile-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTemporaryFile</h4>
<pre>public static&nbsp;java.io.File&nbsp;createTemporaryFile(java.lang.String&nbsp;prefix,
                                               java.lang.String&nbsp;suffix)
                                        throws java.io.IOException</pre>
<div class="block">Returns a temporary file that will be deleted when JVM will exit.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if the file couldn't be created</dd>
</dl>
</li>
</ul>
<a name="getFileVersionComparator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFileVersionComparator</h4>
<pre>public static&nbsp;java.util.Comparator&lt;java.io.File&gt;&nbsp;getFileVersionComparator()</pre>
<div class="block">Returns a file comparator that sorts file names according to their version number (excluding their extension when they are the same).</div>
</li>
</ul>
<a name="deleteTemporaryFiles--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteTemporaryFiles</h4>
<pre>public static&nbsp;void&nbsp;deleteTemporaryFiles()</pre>
<div class="block">Deletes all the temporary files created with <a href="../../../../com/eteks/sweethome3d/tools/OperatingSystem.html#createTemporaryFile-java.lang.String-java.lang.String-"><code>createTemporaryFile</code></a>.</div>
</li>
</ul>
<a name="getDefaultApplicationFolder--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getDefaultApplicationFolder</h4>
<pre>public static&nbsp;java.io.File&nbsp;getDefaultApplicationFolder()
                                                throws java.io.IOException</pre>
<div class="block">Returns default application folder.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/OperatingSystem.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/tools/ExtensionsClassLoader.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/tools/ResourceURLContent.html" title="class in com.eteks.sweethome3d.tools"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/tools/OperatingSystem.html" target="_top">Frames</a></li>
<li><a href="OperatingSystem.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
