<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>ObjectXMLExporter (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ObjectXMLExporter (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObjectXMLExporter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/ObjectXMLExporter.html" target="_top">Frames</a></li>
<li><a href="ObjectXMLExporter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class ObjectXMLExporter" class="title">Class ObjectXMLExporter&lt;T&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.ObjectXMLExporter&lt;T&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html" title="class in com.eteks.sweethome3d.io">HomeXMLExporter</a>, <a href="../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html" title="class in com.eteks.sweethome3d.io">HomeXMLExporter.PieceOfFurnitureExporter</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">ObjectXMLExporter&lt;T&gt;</span>
extends java.lang.Object</pre>
<div class="block">Base class used to write objects to XML.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#ObjectXMLExporter--">ObjectXMLExporter</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#getTag-T-">getTag</a></span>(<a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)</code>
<div class="block">Returns the element tag matching the object in parameter.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-T-">writeAttributes</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
               <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)</code>
<div class="block">Writes the attributes of the object in parameter.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-T-">writeChildren</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)</code>
<div class="block">Writes the children of the object in parameter.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeElement-com.eteks.sweethome3d.io.XMLWriter-T-">writeElement</a></span>(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)</code>
<div class="block">Writes in XML the given <code>object</code> in the element returned by the
 <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#getTag-T-"><code>getTag</code></a>, then writes its attributes and children
 calling <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-T-"><code>writeAttributes</code></a>
 and <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-T-"><code>writeChildren</code></a> methods.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ObjectXMLExporter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ObjectXMLExporter</h4>
<pre>public&nbsp;ObjectXMLExporter()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="writeElement-com.eteks.sweethome3d.io.XMLWriter-java.lang.Object-">
<!--   -->
</a><a name="writeElement-com.eteks.sweethome3d.io.XMLWriter-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeElement</h4>
<pre>public&nbsp;void&nbsp;writeElement(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                         <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)
                  throws java.io.IOException</pre>
<div class="block">Writes in XML the given <code>object</code> in the element returned by the
 <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#getTag-T-"><code>getTag</code></a>, then writes its attributes and children
 calling <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-T-"><code>writeAttributes</code></a>
 and <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-T-"><code>writeChildren</code></a> methods.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getTag-java.lang.Object-">
<!--   -->
</a><a name="getTag-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTag</h4>
<pre>protected&nbsp;java.lang.String&nbsp;getTag(<a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)</pre>
<div class="block">Returns the element tag matching the object in parameter.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the simple class name of the exported object with its first letter at lower case,
    without <code>Home</code> prefix if it's the case.</dd>
</dl>
</li>
</ul>
<a name="writeAttributes-com.eteks.sweethome3d.io.XMLWriter-java.lang.Object-">
<!--   -->
</a><a name="writeAttributes-com.eteks.sweethome3d.io.XMLWriter-T-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeAttributes</h4>
<pre>protected&nbsp;void&nbsp;writeAttributes(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                               <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)
                        throws java.io.IOException</pre>
<div class="block">Writes the attributes of the object in parameter.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeChildren-com.eteks.sweethome3d.io.XMLWriter-java.lang.Object-">
<!--   -->
</a><a name="writeChildren-com.eteks.sweethome3d.io.XMLWriter-T-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeChildren</h4>
<pre>protected&nbsp;void&nbsp;writeChildren(<a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                             <a href="../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)
                      throws java.io.IOException</pre>
<div class="block">Writes the children of the object in parameter.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObjectXMLExporter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/HomeXMLHandler.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/ObjectXMLExporter.html" target="_top">Frames</a></li>
<li><a href="ObjectXMLExporter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
