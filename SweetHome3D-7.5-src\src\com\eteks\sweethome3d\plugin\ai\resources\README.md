# AI Plugin Resources

This directory contains resources for the Sweet Home 3D AI Plugin.

## Required Icons

The following icon files should be placed in this directory:

### ai-icon-16.png
- Size: 16x16 pixels
- Format: PNG
- Usage: Toolbar button icon
- Description: Small AI icon for the toolbar

### ai-icon-24.png
- Size: 24x24 pixels
- Format: PNG
- Usage: Menu icon
- Description: Medium AI icon for menus

### ai-icon-32.png
- Size: 32x32 pixels
- Format: PNG
- Usage: Dialog icon
- Description: Large AI icon for dialogs

## Icon Design Guidelines

- Use a simple, recognizable AI symbol (e.g., brain, circuit, robot head)
- Ensure good contrast and visibility at small sizes
- Follow Sweet Home 3D's visual style
- Use consistent colors across all sizes

## Localization Files

The plugin includes full internationalization support for all languages supported by Sweet Home 3D:

### Available Languages:
- **ApplicationPlugin.properties** (English - Default)
- **ApplicationPlugin_fr.properties** (French)
- **ApplicationPlugin_de.properties** (German)
- **ApplicationPlugin_es.properties** (Spanish)
- **ApplicationPlugin_it.properties** (Italian)
- **ApplicationPlugin_nl.properties** (Dutch)
- **ApplicationPlugin_pt.properties** (Portuguese)
- **ApplicationPlugin_pt_BR.properties** (Brazilian Portuguese)
- **ApplicationPlugin_ru.properties** (Russian)
- **ApplicationPlugin_ja.properties** (Japanese)
- **ApplicationPlugin_zh_CN.properties** (Chinese Simplified)
- **ApplicationPlugin_zh_TW.properties** (Chinese Traditional)
- **ApplicationPlugin_cs.properties** (Czech)
- **ApplicationPlugin_pl.properties** (Polish)
- **ApplicationPlugin_hu.properties** (Hungarian)
- **ApplicationPlugin_bg.properties** (Bulgarian)
- **ApplicationPlugin_el.properties** (Greek)
- **ApplicationPlugin_sv.properties** (Swedish)
- **ApplicationPlugin_vi.properties** (Vietnamese)

### Internationalization Features:
- Plugin metadata (name, description, provider)
- UI strings (button labels, dialog titles, menu items)
- Status messages and progress indicators
- Error messages with parameter substitution
- AI provider names and descriptions
- Analysis prompts and instructions
- Validation messages

### Usage in Code:
Use the `AIResourceBundle` utility class to access localized strings:

```java
// Simple string lookup
String title = AIResourceBundle.getChatDialogTitle();
String buttonText = AIResourceBundle.getButtonText("save");

// Formatted messages with parameters
String errorMsg = AIResourceBundle.getErrorMessage("connectionFailed", exception.getMessage());

// Direct key access
String customText = AIResourceBundle.getString("custom.key", "Default Value");
```

### Adding New Languages:
1. Create a new properties file: `ApplicationPlugin_[locale].properties`
2. Copy all keys from the English version
3. Translate all values while preserving parameter placeholders like `{0}`, `{1}`
4. Ensure proper Unicode encoding for non-Latin scripts
5. Test with the target locale in Sweet Home 3D

## Example Icon Creation

You can create simple icons using any image editor or online icon generators.
Recommended tools:
- GIMP (free)
- Inkscape (free, vector-based)
- Online icon generators
- AI-generated icons

## Installation

When packaging the plugin as a JAR file, ensure all resources are included
in the correct directory structure.
