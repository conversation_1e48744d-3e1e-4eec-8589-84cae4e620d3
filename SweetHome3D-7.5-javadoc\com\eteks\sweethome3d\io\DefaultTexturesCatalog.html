<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>DefaultTexturesCatalog (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DefaultTexturesCatalog (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultTexturesCatalog.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultTexturesCatalog.html" target="_top">Frames</a></li>
<li><a href="DefaultTexturesCatalog.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class DefaultTexturesCatalog" class="title">Class DefaultTexturesCatalog</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.TexturesCatalog</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.DefaultTexturesCatalog</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DefaultTexturesCatalog</span>
extends <a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a></pre>
<div class="block">Textures default catalog read from localized resources.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultTexturesCatalog.PropertyKey</a></span></code>
<div class="block">The keys of the properties values read in <code>.properties</code> files.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#PLUGIN_TEXTURES_CATALOG_FAMILY">PLUGIN_TEXTURES_CATALOG_FAMILY</a></span></code>
<div class="block">The name of <code>.properties</code> family files in plugin textures catalog files.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#DefaultTexturesCatalog--">DefaultTexturesCatalog</a></span>()</code>
<div class="block">Creates a default textures catalog read from resources.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#DefaultTexturesCatalog-java.io.File-">DefaultTexturesCatalog</a></span>(java.io.File&nbsp;texturesPluginFolder)</code>
<div class="block">Creates a default textures catalog read from resources and
 textures plugin folder if <code>texturesPluginFolder</code> isn't <code>null</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#DefaultTexturesCatalog-java.net.URL:A-">DefaultTexturesCatalog</a></span>(java.net.URL[]&nbsp;pluginTexturesCatalogUrls)</code>
<div class="block">Creates a default textures catalog read only from resources in the given URLs.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#DefaultTexturesCatalog-java.net.URL:A-java.net.URL-">DefaultTexturesCatalog</a></span>(java.net.URL[]&nbsp;pluginTexturesCatalogUrls,
                      java.net.URL&nbsp;texturesResourcesUrlBase)</code>
<div class="block">Creates a default textures catalog read only from resources in the given URLs.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#DefaultTexturesCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File-">DefaultTexturesCatalog</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.io.File&nbsp;texturesPluginFolder)</code>
<div class="block">Creates a default textures catalog read from resources and
 textures plugin folder if <code>texturesPluginFolder</code> isn't <code>null</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#DefaultTexturesCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File:A-">DefaultTexturesCatalog</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                      java.io.File[]&nbsp;texturesPluginFolders)</code>
<div class="block">Creates a default textures catalog read from resources and
 textures plugin folders if <code>texturesPluginFolders</code> isn't <code>null</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#getLibraries--">getLibraries</a></span>()</code>
<div class="block">Returns the furniture libraries at initialization.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#readTexture-java.util.ResourceBundle-int-java.net.URL-java.net.URL-">readTexture</a></span>(java.util.ResourceBundle&nbsp;resource,
           int&nbsp;index,
           java.net.URL&nbsp;texturesUrl,
           java.net.URL&nbsp;texturesResourcesUrlBase)</code>
<div class="block">Reads each texture described in <code>resource</code> bundle.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.html#readTexturesCategory-java.util.ResourceBundle-int-">readTexturesCategory</a></span>(java.util.ResourceBundle&nbsp;resource,
                    int&nbsp;index)</code>
<div class="block">Returns the category of a texture at the given <code>index</code> of a
 localized <code>resource</code> bundle.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.TexturesCatalog">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html" title="class in com.eteks.sweethome3d.model">TexturesCatalog</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#add-com.eteks.sweethome3d.model.TexturesCategory-com.eteks.sweethome3d.model.CatalogTexture-">add</a>, <a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#addTexturesListener-com.eteks.sweethome3d.model.CollectionListener-">addTexturesListener</a>, <a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#delete-com.eteks.sweethome3d.model.CatalogTexture-">delete</a>, <a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#getCategories--">getCategories</a>, <a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#getCategoriesCount--">getCategoriesCount</a>, <a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#getCategory-int-">getCategory</a>, <a href="../../../../com/eteks/sweethome3d/model/TexturesCatalog.html#removeTexturesListener-com.eteks.sweethome3d.model.CollectionListener-">removeTexturesListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="PLUGIN_TEXTURES_CATALOG_FAMILY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>PLUGIN_TEXTURES_CATALOG_FAMILY</h4>
<pre>public static final&nbsp;java.lang.String PLUGIN_TEXTURES_CATALOG_FAMILY</pre>
<div class="block">The name of <code>.properties</code> family files in plugin textures catalog files.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.DefaultTexturesCatalog.PLUGIN_TEXTURES_CATALOG_FAMILY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DefaultTexturesCatalog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultTexturesCatalog</h4>
<pre>public&nbsp;DefaultTexturesCatalog()</pre>
<div class="block">Creates a default textures catalog read from resources.</div>
</li>
</ul>
<a name="DefaultTexturesCatalog-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultTexturesCatalog</h4>
<pre>public&nbsp;DefaultTexturesCatalog(java.io.File&nbsp;texturesPluginFolder)</pre>
<div class="block">Creates a default textures catalog read from resources and
 textures plugin folder if <code>texturesPluginFolder</code> isn't <code>null</code>.</div>
</li>
</ul>
<a name="DefaultTexturesCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultTexturesCatalog</h4>
<pre>public&nbsp;DefaultTexturesCatalog(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                              java.io.File&nbsp;texturesPluginFolder)</pre>
<div class="block">Creates a default textures catalog read from resources and
 textures plugin folder if <code>texturesPluginFolder</code> isn't <code>null</code>.</div>
</li>
</ul>
<a name="DefaultTexturesCatalog-com.eteks.sweethome3d.model.UserPreferences-java.io.File:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultTexturesCatalog</h4>
<pre>public&nbsp;DefaultTexturesCatalog(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                              java.io.File[]&nbsp;texturesPluginFolders)</pre>
<div class="block">Creates a default textures catalog read from resources and
 textures plugin folders if <code>texturesPluginFolders</code> isn't <code>null</code>.</div>
</li>
</ul>
<a name="DefaultTexturesCatalog-java.net.URL:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DefaultTexturesCatalog</h4>
<pre>public&nbsp;DefaultTexturesCatalog(java.net.URL[]&nbsp;pluginTexturesCatalogUrls)</pre>
<div class="block">Creates a default textures catalog read only from resources in the given URLs.</div>
</li>
</ul>
<a name="DefaultTexturesCatalog-java.net.URL:A-java.net.URL-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DefaultTexturesCatalog</h4>
<pre>public&nbsp;DefaultTexturesCatalog(java.net.URL[]&nbsp;pluginTexturesCatalogUrls,
                              java.net.URL&nbsp;texturesResourcesUrlBase)</pre>
<div class="block">Creates a default textures catalog read only from resources in the given URLs.
 Texture image URLs will built from <code>texturesResourcesUrlBase</code> if it isn't <code>null</code>.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getLibraries--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLibraries</h4>
<pre>public&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Library.html" title="interface in com.eteks.sweethome3d.model">Library</a>&gt;&nbsp;getLibraries()</pre>
<div class="block">Returns the furniture libraries at initialization.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.0</dd>
</dl>
</li>
</ul>
<a name="readTexture-java.util.ResourceBundle-int-java.net.URL-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readTexture</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/CatalogTexture.html" title="class in com.eteks.sweethome3d.model">CatalogTexture</a>&nbsp;readTexture(java.util.ResourceBundle&nbsp;resource,
                                     int&nbsp;index,
                                     java.net.URL&nbsp;texturesUrl,
                                     java.net.URL&nbsp;texturesResourcesUrlBase)</pre>
<div class="block">Reads each texture described in <code>resource</code> bundle.
 Resources described in texture properties will be loaded from <code>texturesUrl</code>
 if it isn't <code>null</code>.</div>
</li>
</ul>
<a name="readTexturesCategory-java.util.ResourceBundle-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readTexturesCategory</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/TexturesCategory.html" title="class in com.eteks.sweethome3d.model">TexturesCategory</a>&nbsp;readTexturesCategory(java.util.ResourceBundle&nbsp;resource,
                                                int&nbsp;index)</pre>
<div class="block">Returns the category of a texture at the given <code>index</code> of a
 localized <code>resource</code> bundle.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.util.MissingResourceException</code> - if mandatory keys are not defined.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultTexturesCatalog.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeOutputStream.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultTexturesCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultTexturesCatalog.html" target="_top">Frames</a></li>
<li><a href="DefaultTexturesCatalog.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
