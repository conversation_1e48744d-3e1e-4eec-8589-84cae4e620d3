<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Ya<PERSON><PERSON><PERSON><PERSON><PERSON> (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/YafarayRenderer.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Wall3D.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/YafarayRenderer.html" target="_top">Frames</a></li>
<li><a href="YafarayRenderer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.j3d.AbstractPhotoRenderer">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class YafarayRenderer" class="title">Class YafarayRenderer</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">com.eteks.sweethome3d.j3d.AbstractPhotoRenderer</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.YafarayRenderer</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">YafarayRenderer</span>
extends <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></pre>
<div class="block">A renderer implemented with YafaRay rendering engine called with JNI.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.j3d.AbstractPhotoRenderer">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.eteks.sweethome3d.j3d.<a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer.Quality</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html#YafarayRenderer-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.j3d.AbstractPhotoRenderer.Quality-">YafarayRenderer</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
               <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
               <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer.Quality</a>&nbsp;quality)</code>
<div class="block">Creates an instance ready to render the scene matching the given <code>home</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html#dispose--">dispose</a></span>()</code>
<div class="block">Disposes temporary data that may be required to run this renderer.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html#finalize--">finalize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html#getName--">getName</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html#isAvailable--">isAvailable</a></span>()</code>
<div class="block">Returns <code>true</code> if this render is able to run in the current environment.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html#render-java.awt.image.BufferedImage-com.eteks.sweethome3d.model.Camera-java.util.List-java.awt.image.ImageObserver-">render</a></span>(java.awt.image.BufferedImage&nbsp;image,
      <a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
      java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;updatedItems,
      java.awt.image.ImageObserver&nbsp;observer)</code>
<div class="block">Renders home in <code>image</code> at the given <code>camera</code> location and image size.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/YafarayRenderer.html#stop--">stop</a></span>()</code>
<div class="block">Stops the rendering process.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.j3d.AbstractPhotoRenderer">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.j3d.<a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#createInstance-java.lang.String-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.j3d.AbstractPhotoRenderer.Quality-">createInstance</a>, <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#getAvailableRenderers--">getAvailableRenderers</a>, <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#getHome--">getHome</a>, <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#getQuality--">getQuality</a>, <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#getRenderingParameterValue-java.lang.String-">getRenderingParameterValue</a>, <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#render-java.awt.image.BufferedImage-com.eteks.sweethome3d.model.Camera-java.awt.image.ImageObserver-">render</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="YafarayRenderer-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.viewcontroller.Object3DFactory-com.eteks.sweethome3d.j3d.AbstractPhotoRenderer.Quality-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>YafarayRenderer</h4>
<pre>public&nbsp;YafarayRenderer(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                       <a href="../../../../com/eteks/sweethome3d/viewcontroller/Object3DFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">Object3DFactory</a>&nbsp;object3dFactory,
                       <a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.Quality.html" title="enum in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer.Quality</a>&nbsp;quality)
                throws java.io.IOException</pre>
<div class="block">Creates an instance ready to render the scene matching the given <code>home</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="isAvailable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAvailable</h4>
<pre>public&nbsp;boolean&nbsp;isAvailable()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#isAvailable--">AbstractPhotoRenderer</a></code></span></div>
<div class="block">Returns <code>true</code> if this render is able to run in the current environment.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#isAvailable--">isAvailable</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></code></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#getName--">getName</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></code></dd>
</dl>
</li>
</ul>
<a name="render-java.awt.image.BufferedImage-com.eteks.sweethome3d.model.Camera-java.util.List-java.awt.image.ImageObserver-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>render</h4>
<pre>public&nbsp;void&nbsp;render(java.awt.image.BufferedImage&nbsp;image,
                   <a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
                   java.util.List&lt;? extends <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;updatedItems,
                   java.awt.image.ImageObserver&nbsp;observer)
            throws java.io.IOException</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#render-java.awt.image.BufferedImage-com.eteks.sweethome3d.model.Camera-java.util.List-java.awt.image.ImageObserver-">AbstractPhotoRenderer</a></code></span></div>
<div class="block">Renders home in <code>image</code> at the given <code>camera</code> location and image size.
 The home objects listed in <code>updatedItems</code> will be updated in the renderer,
 allowing animations or modifications of their appearance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#render-java.awt.image.BufferedImage-com.eteks.sweethome3d.model.Camera-java.util.List-java.awt.image.ImageObserver-">render</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="stop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stop</h4>
<pre>public&nbsp;void&nbsp;stop()</pre>
<div class="block">Stops the rendering process.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#stop--">stop</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></code></dd>
</dl>
</li>
</ul>
<a name="dispose--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dispose</h4>
<pre>public&nbsp;void&nbsp;dispose()</pre>
<div class="block">Disposes temporary data that may be required to run this renderer.
 Trying to use this renderer after a call to this method may lead to errors.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html#dispose--">dispose</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/j3d/AbstractPhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d">AbstractPhotoRenderer</a></code></dd>
</dl>
</li>
</ul>
<a name="finalize--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>finalize</h4>
<pre>protected&nbsp;void&nbsp;finalize()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>finalize</code>&nbsp;in class&nbsp;<code>java.lang.Object</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/YafarayRenderer.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/Wall3D.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/YafarayRenderer.html" target="_top">Frames</a></li>
<li><a href="YafarayRenderer.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.com.eteks.sweethome3d.j3d.AbstractPhotoRenderer">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
