<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>Base64 (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Base64 (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Base64.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/AutoRecoveryManager.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/ContentDigestManager.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/Base64.html" target="_top">Frames</a></li>
<li><a href="Base64.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Class Base64" class="title">Class Base64</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.Base64</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Base64</span>
extends java.lang.Object</pre>
<div class="block"><p>Encodes and decodes to and from Base64 notation.</p>
 <p>Homepage: <a href="http://iharder.net/base64">http://iharder.net/base64</a>.</p>
 
 <p>Example:</p>
 
 <code>String encoded = Base64.encode( myByteArray );</code>
 <br>
 <code>byte[] myByteArray = Base64.decode( encoded );</code>

 <p>The <tt>options</tt> parameter, which appears in a few places, is used to pass 
 several pieces of information to the encoder. In the "higher level" methods such as 
 encodeBytes( bytes, options ) the options parameter can be used to indicate such 
 things as first gzipping the bytes before encoding them, not inserting linefeeds,
 and encoding using the URL-safe and Ordered dialects.</p>

 <p>Note, according to <a href="http://www.faqs.org/rfcs/rfc3548.html">RFC3548</a>,
 Section 2.1, implementations should not add line feeds unless explicitly told
 to do so. I've got Base64 set to this behavior now, although earlier versions
 broke lines by default.</p>

 <p>The constants defined in Base64 can be OR-ed together to combine options, so you 
 might make a call like this:</p>

 <code>String encoded = Base64.encodeBytes( mybytes, Base64.GZIP | Base64.DO_BREAK_LINES );</code>
 <p>to compress the data before encoding it and then making the output have newline characters.</p>
 <p>Also...</p>
 <code>String encoded = Base64.encodeBytes( crazyString.getBytes() );</code>

 <p>
 Change Log:
 </p>
 <ul>
  <li>v2.3.7 - Fixed subtle bug when base 64 input stream contained the
   value 01111111, which is an invalid base 64 character but should not
   throw an ArrayIndexOutOfBoundsException either. Led to discovery of
   mishandling (or potential for better handling) of other bad input
   characters. You should now get an IOException if you try decoding
   something that has bad characters in it.</li>
  <li>v2.3.6 - Fixed bug when breaking lines and the final byte of the encoded
   string ended in the last column; the buffer was not properly shrunk and
   contained an extra (null) byte that made it into the string.</li>
  <li>v2.3.5 - Fixed bug in <code>encodeFromFile</code> where estimated buffer size
   was wrong for files of size 31, 34, and 37 bytes.</li>
  <li>v2.3.4 - Fixed bug when working with gzipped streams whereby flushing
   the Base64.OutputStream closed the Base64 encoding (by padding with equals
   signs) too soon. Also added an option to suppress the automatic decoding
   of gzipped streams. Also added experimental support for specifying a
   class loader when using the <code>decodeToObject</code> method.</li>
  <li>v2.3.3 - Changed default char encoding to US-ASCII which reduces the internal Java
   footprint with its CharEncoders and so forth. Fixed some javadocs that were
   inconsistent. Removed imports and specified things like java.io.IOException
   explicitly inline.</li>
  <li>v2.3.2 - Reduced memory footprint! Finally refined the "guessing" of how big the
   final encoded data will be so that the code doesn't have to create two output
   arrays: an oversized initial one and then a final, exact-sized one. Big win
   when using the <code>encodeBytesToBytes(byte[])</code> family of methods (and not
   using the gzip options which uses a different mechanism with streams and stuff).</li>
  <li>v2.3.1 - Added <a href="../../../../com/eteks/sweethome3d/io/Base64.html#encodeBytesToBytes-byte:A-int-int-int-"><code>encodeBytesToBytes(byte[], int, int, int)</code></a> and some
   similar helper methods to be more efficient with memory by not returning a
   String but just a byte array.</li>
  <li>v2.3 - <strong>This is not a drop-in replacement!</strong> This is two years of comments
   and bug fixes queued up and finally executed. Thanks to everyone who sent
   me stuff, and I'm sorry I wasn't able to distribute your fixes to everyone else.
   Much bad coding was cleaned up including throwing exceptions where necessary 
   instead of returning null values or something similar. Here are some changes
   that may affect you:
   <ul>
    <li><em>Does not break lines, by default.</em> This is to keep in compliance with
      <a href="http://www.faqs.org/rfcs/rfc3548.html">RFC3548</a>.</li>
    <li><em>Throws exceptions instead of returning null values.</em> Because some operations
      (especially those that may permit the GZIP option) use IO streams, there
      is a possiblity of an java.io.IOException being thrown. After some discussion and
      thought, I've changed the behavior of the methods to throw java.io.IOExceptions
      rather than return null if ever there's an error. I think this is more
      appropriate, though it will require some changes to your code. Sorry,
      it should have been done this way to begin with.</li>
    <li><em>Removed all references to System.out, System.err, and the like.</em>
      Shame on me. All I can say is sorry they were ever there.</li>
    <li><em>Throws NullPointerExceptions and IllegalArgumentExceptions</em> as needed
      such as when passed arrays are null or offsets are invalid.</li>
    <li>Cleaned up as much javadoc as I could to avoid any javadoc warnings.
      This was especially annoying before for people who were thorough in their
      own projects and then had gobs of javadoc warnings on this file.</li>
   </ul>
  <li>v2.2.1 - Fixed bug using URL_SAFE and ORDERED encodings. Fixed bug
   when using very small files (~&lt; 40 bytes).</li>
  <li>v2.2 - Added some helper methods for encoding/decoding directly from
   one file to the next. Also added a main() method to support command line
   encoding/decoding from one file to the next. Also added these Base64 dialects:
   <ol>
   <li>The default is RFC3548 format.</li>
   <li>Calling Base64.setFormat(Base64.BASE64_FORMAT.URLSAFE_FORMAT) generates
   URL and file name friendly format as described in Section 4 of RFC3548.
   http://www.faqs.org/rfcs/rfc3548.html</li>
   <li>Calling Base64.setFormat(Base64.BASE64_FORMAT.ORDERED_FORMAT) generates
   URL and file name friendly format that preserves lexical ordering as described
   in http://www.faqs.org/qa/rfcc-1940.html</li>
   </ol>
   Special thanks to Jim Kellerman at <a href="http://www.powerset.com/">http://www.powerset.com/</a>
   for contributing the new Base64 dialects.
  </li>
 
  <li>v2.1 - Cleaned up javadoc comments and unused variables and methods. Added
   some convenience methods for reading and writing to and from files.</li>
  <li>v2.0.2 - Now specifies UTF-8 encoding in places where the code fails on systems
   with other encodings (like EBCDIC).</li>
  <li>v2.0.1 - Fixed an error when decoding a single byte, that is, when the
   encoded data was a single byte.</li>
  <li>v2.0 - I got rid of methods that used booleans to set options. 
   Now everything is more consolidated and cleaner. The code now detects
   when data that's being decoded is gzip-compressed and will decompress it
   automatically. Generally things are cleaner. You'll probably have to
   change some method calls that you were making to support the new
   options format (<tt>int</tt>s that you "OR" together).</li>
  <li>v1.5.1 - Fixed bug when decompressing and decoding to a             
   byte[] using <tt>decode( String s, boolean gzipCompressed )</tt>.      
   Added the ability to "suspend" encoding in the Output Stream so        
   you can turn on and off the encoding if you need to embed base64       
   data in an otherwise "normal" stream (like an XML file).</li>  
  <li>v1.5 - Output stream pases on flush() command but doesn't do anything itself.
      This helps when using GZIP streams.
      Added the ability to GZip-compress objects before encoding them.</li>
  <li>v1.4 - Added helper methods to read/write files.</li>
  <li>v1.3.6 - Fixed OutputStream.flush() so that 'position' is reset.</li>
  <li>v1.3.5 - Added flag to turn on and off line breaks. Fixed bug in input stream
      where last buffer being read, if not completely full, was not returned.</li>
  <li>v1.3.4 - Fixed when "improperly padded stream" error was thrown at the wrong time.</li>
  <li>v1.3.3 - Fixed I/O streams which were totally messed up.</li>
 </ul>

 <p>
 I am placing this code in the Public Domain. Do with it as you will.
 This software comes with no guarantees or warranties but with
 plenty of well-wishing instead!
 Please visit <a href="http://iharder.net/base64">http://iharder.net/base64</a>
 periodically to check for updates or to contribute improvements.
 </p></div>
<dl>
<dt><span class="simpleTagLabel">Version:</span></dt>
<dd>2.3.7</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Robert Harder, <EMAIL></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#DECODE">DECODE</a></span></code>
<div class="block">Specify decoding in first bit.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#DO_BREAK_LINES">DO_BREAK_LINES</a></span></code>
<div class="block">Do break lines when encoding.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#DONT_GUNZIP">DONT_GUNZIP</a></span></code>
<div class="block">Specify that gzipped data should <em>not</em> be automatically gunzipped.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#ENCODE">ENCODE</a></span></code>
<div class="block">Specify encoding in first bit.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#GZIP">GZIP</a></span></code>
<div class="block">Specify that data should be gzip-compressed in second bit.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#NO_OPTIONS">NO_OPTIONS</a></span></code>
<div class="block">No options specified.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#ORDERED">ORDERED</a></span></code>
<div class="block">Encode using the special "ordered" dialect of Base64 described here:
 <a href="http://www.faqs.org/qa/rfcc-1940.html">http://www.faqs.org/qa/rfcc-1940.html</a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#URL_SAFE">URL_SAFE</a></span></code>
<div class="block">Encode using Base64-like encoding that is URL- and Filename-safe as described
 in Section 4 of RFC3548: 
 <a href="http://www.faqs.org/rfcs/rfc3548.html">http://www.faqs.org/rfcs/rfc3548.html</a>.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#decode-java.lang.String-">decode</a></span>(java.lang.String&nbsp;s)</code>
<div class="block">Decodes data from Base64 notation, automatically
 detecting gzip-compressed data and decompressing it.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#decode-java.lang.String-int-">decode</a></span>(java.lang.String&nbsp;s,
      int&nbsp;options)</code>
<div class="block">Decodes data from Base64 notation, automatically
 detecting gzip-compressed data and decompressing it.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#encodeBytes-byte:A-">encodeBytes</a></span>(byte[]&nbsp;source)</code>
<div class="block">Encodes a byte array into Base64 notation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#encodeBytes-byte:A-int-">encodeBytes</a></span>(byte[]&nbsp;source,
           int&nbsp;options)</code>
<div class="block">Encodes a byte array into Base64 notation.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#encodeBytes-byte:A-int-int-">encodeBytes</a></span>(byte[]&nbsp;source,
           int&nbsp;off,
           int&nbsp;len)</code>
<div class="block">Encodes a byte array into Base64 notation.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/Base64.html#encodeBytes-byte:A-int-int-int-">encodeBytes</a></span>(byte[]&nbsp;source,
           int&nbsp;off,
           int&nbsp;len,
           int&nbsp;options)</code>
<div class="block">Encodes a byte array into Base64 notation.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="NO_OPTIONS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NO_OPTIONS</h4>
<pre>public static final&nbsp;int NO_OPTIONS</pre>
<div class="block">No options specified. Value is zero.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.Base64.NO_OPTIONS">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ENCODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ENCODE</h4>
<pre>public static final&nbsp;int ENCODE</pre>
<div class="block">Specify encoding in first bit. Value is one.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.Base64.ENCODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DECODE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DECODE</h4>
<pre>public static final&nbsp;int DECODE</pre>
<div class="block">Specify decoding in first bit. Value is zero.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.Base64.DECODE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="GZIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GZIP</h4>
<pre>public static final&nbsp;int GZIP</pre>
<div class="block">Specify that data should be gzip-compressed in second bit. Value is two.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.Base64.GZIP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DONT_GUNZIP">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DONT_GUNZIP</h4>
<pre>public static final&nbsp;int DONT_GUNZIP</pre>
<div class="block">Specify that gzipped data should <em>not</em> be automatically gunzipped.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.Base64.DONT_GUNZIP">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DO_BREAK_LINES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DO_BREAK_LINES</h4>
<pre>public static final&nbsp;int DO_BREAK_LINES</pre>
<div class="block">Do break lines when encoding. Value is 8.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.Base64.DO_BREAK_LINES">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="URL_SAFE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>URL_SAFE</h4>
<pre>public static final&nbsp;int URL_SAFE</pre>
<div class="block">Encode using Base64-like encoding that is URL- and Filename-safe as described
 in Section 4 of RFC3548: 
 <a href="http://www.faqs.org/rfcs/rfc3548.html">http://www.faqs.org/rfcs/rfc3548.html</a>.
 It is important to note that data encoded this way is <em>not</em> officially valid Base64, 
 or at the very least should not be called Base64 without also specifying that is
 was encoded using the URL- and Filename-safe dialect.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.Base64.URL_SAFE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ORDERED">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ORDERED</h4>
<pre>public static final&nbsp;int ORDERED</pre>
<div class="block">Encode using the special "ordered" dialect of Base64 described here:
 <a href="http://www.faqs.org/qa/rfcc-1940.html">http://www.faqs.org/qa/rfcc-1940.html</a>.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.io.Base64.ORDERED">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="encodeBytes-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeBytes</h4>
<pre>public static&nbsp;java.lang.String&nbsp;encodeBytes(byte[]&nbsp;source)</pre>
<div class="block">Encodes a byte array into Base64 notation.
 Does not GZip-compress data.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The data to convert</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The data in Base64-encoded form</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.NullPointerException</code> - if source array is null</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.4</dd>
</dl>
</li>
</ul>
<a name="encodeBytes-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeBytes</h4>
<pre>public static&nbsp;java.lang.String&nbsp;encodeBytes(byte[]&nbsp;source,
                                           int&nbsp;options)
                                    throws java.io.IOException</pre>
<div class="block">Encodes a byte array into Base64 notation.
 <p>
 Example options:<pre>
   GZIP: gzip-compresses object before encoding it.
   DO_BREAK_LINES: break lines at 76 characters
     <i>Note: Technically, this makes your encoding non-compliant.</i>
 </pre>
 <p>
 Example: <code>encodeBytes( myData, Base64.GZIP )</code> or
 <p>
 Example: <code>encodeBytes( myData, Base64.GZIP | Base64.DO_BREAK_LINES )</code>

  
 <p>As of v 2.3, if there is an error with the GZIP stream,
 the method will throw an java.io.IOException. <b>This is new to v2.3!</b>
 In earlier versions, it just returned a null value, but
 in retrospect that's a pretty poor way to handle it.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The data to convert</dd>
<dd><code>options</code> - Specified options</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Base64-encoded data as a String</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if there is an error</dd>
<dd><code>java.lang.NullPointerException</code> - if source array is null</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/io/Base64.html#GZIP"><code>GZIP</code></a>, 
<a href="../../../../com/eteks/sweethome3d/io/Base64.html#DO_BREAK_LINES"><code>DO_BREAK_LINES</code></a></dd>
</dl>
</li>
</ul>
<a name="encodeBytes-byte:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeBytes</h4>
<pre>public static&nbsp;java.lang.String&nbsp;encodeBytes(byte[]&nbsp;source,
                                           int&nbsp;off,
                                           int&nbsp;len)</pre>
<div class="block">Encodes a byte array into Base64 notation.
 Does not GZip-compress data.
  
 <p>As of v 2.3, if there is an error,
 the method will throw an java.io.IOException. <b>This is new to v2.3!</b>
 In earlier versions, it just returned a null value, but
 in retrospect that's a pretty poor way to handle it.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The data to convert</dd>
<dd><code>off</code> - Offset in array where conversion should begin</dd>
<dd><code>len</code> - Length of data to convert</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Base64-encoded data as a String</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.NullPointerException</code> - if source array is null</dd>
<dd><code>java.lang.IllegalArgumentException</code> - if source array, offset, or length are invalid</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.4</dd>
</dl>
</li>
</ul>
<a name="encodeBytes-byte:A-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encodeBytes</h4>
<pre>public static&nbsp;java.lang.String&nbsp;encodeBytes(byte[]&nbsp;source,
                                           int&nbsp;off,
                                           int&nbsp;len,
                                           int&nbsp;options)
                                    throws java.io.IOException</pre>
<div class="block">Encodes a byte array into Base64 notation.
 <p>
 Example options:<pre>
   GZIP: gzip-compresses object before encoding it.
   DO_BREAK_LINES: break lines at 76 characters
     <i>Note: Technically, this makes your encoding non-compliant.</i>
 </pre>
 <p>
 Example: <code>encodeBytes( myData, Base64.GZIP )</code> or
 <p>
 Example: <code>encodeBytes( myData, Base64.GZIP | Base64.DO_BREAK_LINES )</code>

  
 <p>As of v 2.3, if there is an error with the GZIP stream,
 the method will throw an java.io.IOException. <b>This is new to v2.3!</b>
 In earlier versions, it just returned a null value, but
 in retrospect that's a pretty poor way to handle it.</p></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>source</code> - The data to convert</dd>
<dd><code>off</code> - Offset in array where conversion should begin</dd>
<dd><code>len</code> - Length of data to convert</dd>
<dd><code>options</code> - Specified options</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Base64-encoded data as a String</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if there is an error</dd>
<dd><code>java.lang.NullPointerException</code> - if source array is null</dd>
<dd><code>java.lang.IllegalArgumentException</code> - if source array, offset, or length are invalid</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>2.0</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/io/Base64.html#GZIP"><code>GZIP</code></a>, 
<a href="../../../../com/eteks/sweethome3d/io/Base64.html#DO_BREAK_LINES"><code>DO_BREAK_LINES</code></a></dd>
</dl>
</li>
</ul>
<a name="decode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decode</h4>
<pre>public static&nbsp;byte[]&nbsp;decode(java.lang.String&nbsp;s)
                     throws java.io.IOException</pre>
<div class="block">Decodes data from Base64 notation, automatically
 detecting gzip-compressed data and decompressing it.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>s</code> - the string to decode</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the decoded data</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - If there is a problem</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.4</dd>
</dl>
</li>
</ul>
<a name="decode-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>decode</h4>
<pre>public static&nbsp;byte[]&nbsp;decode(java.lang.String&nbsp;s,
                            int&nbsp;options)
                     throws java.io.IOException</pre>
<div class="block">Decodes data from Base64 notation, automatically
 detecting gzip-compressed data and decompressing it.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>s</code> - the string to decode</dd>
<dd><code>options</code> - encode options such as URL_SAFE</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the decoded data</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if there is an error</dd>
<dd><code>java.lang.NullPointerException</code> - if <tt>s</tt> is null</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>1.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Base64.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/AutoRecoveryManager.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/ContentDigestManager.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/Base64.html" target="_top">Frames</a></li>
<li><a href="Base64.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
