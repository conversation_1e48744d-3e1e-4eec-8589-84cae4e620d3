<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>OBJMaterial (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OBJMaterial (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/OBJMaterial.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/OBJLoader.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/OBJMaterial.html" target="_top">Frames</a></li>
<li><a href="OBJMaterial.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.media.j3d.Material">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class OBJMaterial" class="title">Class OBJMaterial</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.SceneGraphObject</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.NodeComponent</li>
<li>
<ul class="inheritance">
<li>javax.media.j3d.Material</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.OBJMaterial</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">OBJMaterial</span>
extends javax.media.j3d.Material</pre>
<div class="block">A material with additional parameters useful for raytracing rendering.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.media.j3d.Material">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.media.j3d.Material</h3>
<code>ALLOW_COMPONENT_READ, ALLOW_COMPONENT_WRITE, AMBIENT, AMBIENT_AND_DIFFUSE, DIFFUSE, EMISSIVE, SPECULAR</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#OBJMaterial--">OBJMaterial</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>javax.media.j3d.NodeComponent</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#cloneNodeComponent-boolean-">cloneNodeComponent</a></span>(boolean&nbsp;forceDuplicate)</code>
<div class="block">Returns a clone of this material.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#getIlluminationModel--">getIlluminationModel</a></span>()</code>
<div class="block">Returns the illumination model of this material as defined in MTL format.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#getOpticalDensity--">getOpticalDensity</a></span>()</code>
<div class="block">Returns the optical density of this material.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#getSharpness--">getSharpness</a></span>()</code>
<div class="block">Returns the sharpness of this material.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#isIlluminationModelSet--">isIlluminationModelSet</a></span>()</code>
<div class="block">Returns <code>true</code> if illumination model was set on this material.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#isOpticalDensitySet--">isOpticalDensitySet</a></span>()</code>
<div class="block">Returns <code>true</code> if optical density was set on this material.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#isSharpnessSet--">isSharpnessSet</a></span>()</code>
<div class="block">Returns <code>true</code> if sharpness was set on this material.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#setIlluminationModel-int-">setIlluminationModel</a></span>(int&nbsp;illuminationModel)</code>
<div class="block">Sets the illumination model of this material.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#setOpticalDensity-float-">setOpticalDensity</a></span>(float&nbsp;opticalDensity)</code>
<div class="block">Sets the optical density of this material.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html#setSharpness-float-">setSharpness</a></span>(float&nbsp;sharpness)</code>
<div class="block">Sets the sharpness of this material.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.Material">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.Material</h3>
<code>cloneNodeComponent, getAmbientColor, getColorTarget, getDiffuseColor, getEmissiveColor, getLightingEnable, getShininess, getSpecularColor, setAmbientColor, setAmbientColor, setColorTarget, setDiffuseColor, setDiffuseColor, setDiffuseColor, setEmissiveColor, setEmissiveColor, setLightingEnable, setShininess, setSpecularColor, setSpecularColor, toString</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.NodeComponent">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.NodeComponent</h3>
<code>duplicateNodeComponent, duplicateNodeComponent, getDuplicateOnCloneTree, setDuplicateOnCloneTree</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.media.j3d.SceneGraphObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.media.j3d.SceneGraphObject</h3>
<code>clearCapability, clearCapabilityIsFrequent, duplicateSceneGraphObject, getCapability, getCapabilityIsFrequent, getName, getUserData, isCompiled, isLive, setCapability, setCapabilityIsFrequent, setName, setUserData, updateNodeReferences</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="OBJMaterial--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>OBJMaterial</h4>
<pre>public&nbsp;OBJMaterial()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setOpticalDensity-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOpticalDensity</h4>
<pre>public&nbsp;void&nbsp;setOpticalDensity(float&nbsp;opticalDensity)</pre>
<div class="block">Sets the optical density of this material.</div>
</li>
</ul>
<a name="getOpticalDensity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOpticalDensity</h4>
<pre>public&nbsp;float&nbsp;getOpticalDensity()</pre>
<div class="block">Returns the optical density of this material.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if optical density wasn't set.</dd>
</dl>
</li>
</ul>
<a name="isOpticalDensitySet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOpticalDensitySet</h4>
<pre>public&nbsp;boolean&nbsp;isOpticalDensitySet()</pre>
<div class="block">Returns <code>true</code> if optical density was set on this material.</div>
</li>
</ul>
<a name="setIlluminationModel-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIlluminationModel</h4>
<pre>public&nbsp;void&nbsp;setIlluminationModel(int&nbsp;illuminationModel)</pre>
<div class="block">Sets the illumination model of this material.</div>
</li>
</ul>
<a name="getIlluminationModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIlluminationModel</h4>
<pre>public&nbsp;int&nbsp;getIlluminationModel()</pre>
<div class="block">Returns the illumination model of this material as defined in MTL format.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if illumination model wasn't set.</dd>
</dl>
</li>
</ul>
<a name="isIlluminationModelSet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIlluminationModelSet</h4>
<pre>public&nbsp;boolean&nbsp;isIlluminationModelSet()</pre>
<div class="block">Returns <code>true</code> if illumination model was set on this material.</div>
</li>
</ul>
<a name="setSharpness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSharpness</h4>
<pre>public&nbsp;void&nbsp;setSharpness(float&nbsp;sharpness)</pre>
<div class="block">Sets the sharpness of this material.</div>
</li>
</ul>
<a name="getSharpness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSharpness</h4>
<pre>public&nbsp;float&nbsp;getSharpness()</pre>
<div class="block">Returns the sharpness of this material.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if sharpness wasn't set.</dd>
</dl>
</li>
</ul>
<a name="isSharpnessSet--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isSharpnessSet</h4>
<pre>public&nbsp;boolean&nbsp;isSharpnessSet()</pre>
<div class="block">Returns <code>true</code> if sharpness was set on this material.</div>
</li>
</ul>
<a name="cloneNodeComponent-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>cloneNodeComponent</h4>
<pre>public&nbsp;javax.media.j3d.NodeComponent&nbsp;cloneNodeComponent(boolean&nbsp;forceDuplicate)</pre>
<div class="block">Returns a clone of this material.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>cloneNodeComponent</code>&nbsp;in class&nbsp;<code>javax.media.j3d.NodeComponent</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/OBJMaterial.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/OBJLoader.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/OBJMaterial.html" target="_top">Frames</a></li>
<li><a href="OBJMaterial.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.media.j3d.Material">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
