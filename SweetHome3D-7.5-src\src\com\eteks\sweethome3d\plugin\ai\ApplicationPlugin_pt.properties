# ApplicationPlugin_pt.properties
# Sweet Home 3D AI Plugin Configuration - Portuguese
# Copyright (c) 2025 Samuel <PERSON>

# Plugin identification
name=Análise IA de Plantas
description=Analise plantas baixas usando inteligência artificial para fornecer insights e sugestões de melhoria
provider=<PERSON>

# Features
features=Análise IA, Insights de Plantas, Múltiplos Provedores IA, Controles de Privacidade

# Requirements
requirements=Conexão com a Internet para provedores IA na nuvem (opcional para provedores locais)

# UI Strings for internationalization
# Action properties
AIAction.Name=Análise IA
AIAction.ShortDescription=Analisar planta com IA
AIAction.Menu=Ferramentas

# Dialog titles
AIChatDialog.title=Análise IA de Plantas
AISettingsDialog.title=Configurações IA

# Button labels
button.send=Enviar
button.newAnalysis=Nova Análise
button.settings=Configurações
button.testConnection=Testar Conexão
button.save=Salvar
button.cancel=Cancelar

# Labels
label.provider=Provedor:
label.baseUrl=URL Base:
label.apiKey=Chave API:
label.model=Modelo:
label.temperature=Temperatura:
label.maxTokens=Tokens Máx:
label.status=Status:

# Messages
message.analyzing=Analisando planta...
message.processingQuestion=Processando pergunta...
message.testingConnection=Testando conexão...
message.connectionSuccessful=Conexão bem-sucedida!
message.connectionFailed=Conexão falhou: {0}
message.configurationSaved=Configuração salva com sucesso
message.validationError=Erros de configuração:\n{0}
message.noConfiguration=Provedor IA não configurado. Por favor configure as definições primeiro.

# Analysis prompt
analysis.prompt=Por favor analise esta planta e forneça insights abrangentes incluindo:\n1. Eficiência do layout e utilização do espaço\n2. Fluxo de tráfego e padrões de circulação\n3. Oportunidades de iluminação natural e ventilação\n4. Considerações de acessibilidade\n5. Relações funcionais entre espaços\n6. Sugestões de melhoria\n7. Conformidade com padrões de construção comuns\n8. Considerações de eficiência energética\n\nPor favor forneça recomendações específicas e acionáveis que melhorariam a funcionalidade, conforto e apelo estético deste espaço.

# Error messages
error.analysisError=Erro de análise: {0}
error.configurationError=Erro de configuração: {0}
error.connectionError=Erro de conexão: {0}
error.invalidConfiguration=Configuração inválida
error.missingApiKey=Chave API é obrigatória
error.missingBaseUrl=URL base é obrigatória
error.missingModel=Seleção de modelo é obrigatória

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (Local)
provider.lmstudio=LM Studio (Local)
provider.anythingllm=AnythingLLM (Local)
provider.jan=Jan (Local)
provider.custom=Personalizado
