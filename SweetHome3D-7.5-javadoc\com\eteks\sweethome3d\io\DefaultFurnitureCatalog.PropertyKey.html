<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>DefaultFurnitureCatalog.PropertyKey (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DefaultFurnitureCatalog.PropertyKey (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":9,"i4":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultFurnitureCatalog.PropertyKey.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" target="_top">Frames</a></li>
<li><a href="DefaultFurnitureCatalog.PropertyKey.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.io</div>
<h2 title="Enum DefaultFurnitureCatalog.PropertyKey" class="title">Enum DefaultFurnitureCatalog.PropertyKey</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a>&gt;</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.io.DefaultFurnitureCatalog.PropertyKey</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, java.lang.Comparable&lt;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a>&gt;</dd>
</dl>
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html" title="class in com.eteks.sweethome3d.io">DefaultFurnitureCatalog</a></dd>
</dl>
<hr>
<br>
<pre>public static enum <span class="typeNameLabel">DefaultFurnitureCatalog.PropertyKey</span>
extends java.lang.Enum&lt;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a>&gt;</pre>
<div class="block">The keys of the properties values read in <code>.properties</code> files.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== ENUM CONSTANT SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.summary">
<!--   -->
</a>
<h3>Enum Constant Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Constant Summary table, listing enum constants, and an explanation">
<caption><span>Enum Constants</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Enum Constant and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#CATEGORY">CATEGORY</a></span></code>
<div class="block">The key for the category's name of a piece of furniture (mandatory).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#CREATION_DATE">CREATION_DATE</a></span></code>
<div class="block">The key for the creation or publication date of a piece of furniture at
 <code>yyyy-MM-dd</code> format (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#CREATOR">CREATOR</a></span></code>
<div class="block">The key for the creator of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#CURRENCY">CURRENCY</a></span></code>
<div class="block">The key for the currency ISO 4217 code of the price of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DEFORMABLE">DEFORMABLE</a></span></code>
<div class="block">The key for the deformability of a piece of furniture (optional, <code>true</code> by default).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DEPTH">DEPTH</a></span></code>
<div class="block">The key for the depth in centimeters of a piece of furniture (mandatory).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DESCRIPTION">DESCRIPTION</a></span></code>
<div class="block">The key for the description of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW">DOOR_OR_WINDOW</a></span></code>
<div class="block">The key for the door or window type of a piece of furniture (mandatory).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_CUT_OUT_SHAPE">DOOR_OR_WINDOW_CUT_OUT_SHAPE</a></span></code>
<div class="block">The key for the shape of a door or window used to cut out walls when they intersect it (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_SASH_END_ANGLE">DOOR_OR_WINDOW_SASH_END_ANGLE</a></span></code>
<div class="block">The key for the sash end angle(s) of a door or a window
 (mandatory if sash axis distance along X axis is defined).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_SASH_START_ANGLE">DOOR_OR_WINDOW_SASH_START_ANGLE</a></span></code>
<div class="block">The key for the sash start angle(s) of a door or a window
 (mandatory if sash axis distance along X axis is defined).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_SASH_WIDTH">DOOR_OR_WINDOW_SASH_WIDTH</a></span></code>
<div class="block">The key for the sash width(s) of a door or a window
 (mandatory if sash axis distance along X axis is defined).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_SASH_X_AXIS">DOOR_OR_WINDOW_SASH_X_AXIS</a></span></code>
<div class="block">The key for the sash axis distance(s) of a door or a window along X axis (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_SASH_Y_AXIS">DOOR_OR_WINDOW_SASH_Y_AXIS</a></span></code>
<div class="block">The key for the sash axis distance(s) of a door or a window along Y axis
 (mandatory if sash axis distance along X axis is defined).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_WALL_CUT_OUT_ON_BOTH_SIDES">DOOR_OR_WINDOW_WALL_CUT_OUT_ON_BOTH_SIDES</a></span></code>
<div class="block">The key for the wall cut out rule of a door or a window (optional, <code>true</code> by default).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_WALL_DISTANCE">DOOR_OR_WINDOW_WALL_DISTANCE</a></span></code>
<div class="block">The key for the distance in centimeters of a door or a window to its wall (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_WALL_THICKNESS">DOOR_OR_WINDOW_WALL_THICKNESS</a></span></code>
<div class="block">The key for the wall thickness in centimeters of a door or a window (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DOOR_OR_WINDOW_WIDTH_DEPTH_DEFORMABLE">DOOR_OR_WINDOW_WIDTH_DEPTH_DEFORMABLE</a></span></code>
<div class="block">The key for the width/depth deformability of a door or a window (optional, <code>true</code> by default).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#DROP_ON_TOP_ELEVATION">DROP_ON_TOP_ELEVATION</a></span></code>
<div class="block">The key for the preferred elevation (from the bottom of a piece) at which should be placed
 an object dropped on a piece (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#ELEVATION">ELEVATION</a></span></code>
<div class="block">The key for the elevation in centimeters of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#GRADE">GRADE</a></span></code>
<div class="block">The key for the grade of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#HEIGHT">HEIGHT</a></span></code>
<div class="block">The key for the height in centimeters of a piece of furniture (mandatory).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#HORIZONTALLY_ROTATABLE">HORIZONTALLY_ROTATABLE</a></span></code>
<div class="block">The key for the ability of a piece of furniture to rotate around a horizontal axis (optional, <code>true</code> by default).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#ICON">ICON</a></span></code>
<div class="block">The key for the icon file of a piece of furniture (mandatory).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#ICON_DIGEST">ICON_DIGEST</a></span></code>
<div class="block">The key for the SHA-1 digest of the icon file of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#ID">ID</a></span></code>
<div class="block">The key for the ID of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#INFORMATION">INFORMATION</a></span></code>
<div class="block">The key for some additional information associated to a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#LICENSE">LICENSE</a></span></code>
<div class="block">The key for the license associated to a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#LIGHT_SOURCE_COLOR">LIGHT_SOURCE_COLOR</a></span></code>
<div class="block">The key for the color(s) of light sources in a light (mandatory if light source abscissa is defined).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#LIGHT_SOURCE_DIAMETER">LIGHT_SOURCE_DIAMETER</a></span></code>
<div class="block">The key for the diameter(s) of light sources in a light (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#LIGHT_SOURCE_MATERIAL_NAME">LIGHT_SOURCE_MATERIAL_NAME</a></span></code>
<div class="block">The key for the material name(s) of light source shapes in the 3D model of a light (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#LIGHT_SOURCE_X">LIGHT_SOURCE_X</a></span></code>
<div class="block">The key for the abscissa(s) of light sources in a light (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#LIGHT_SOURCE_Y">LIGHT_SOURCE_Y</a></span></code>
<div class="block">The key for the ordinate(s) of light sources in a light (mandatory if light source abscissa is defined).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#LIGHT_SOURCE_Z">LIGHT_SOURCE_Z</a></span></code>
<div class="block">The key for the elevation(s) of light sources in a light (mandatory if light source abscissa is defined).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#MODEL">MODEL</a></span></code>
<div class="block">The key for the 3D model file of a piece of furniture (mandatory).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#MODEL_DIGEST">MODEL_DIGEST</a></span></code>
<div class="block">The key for the SHA-1 digest of the 3D model file of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#MODEL_FLAGS">MODEL_FLAGS</a></span></code>
<div class="block">The key for the model flags applied to a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#MODEL_ROTATION">MODEL_ROTATION</a></span></code>
<div class="block">The key for the transformation matrix values applied to a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#MODEL_SIZE">MODEL_SIZE</a></span></code>
<div class="block">The key for the size of the 3D model of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#MOVABLE">MOVABLE</a></span></code>
<div class="block">The key for the movability of a piece of furniture (mandatory).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#MULTI_PART_MODEL">MULTI_PART_MODEL</a></span></code>
<div class="block">The key for a piece of furniture with multiple parts (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#NAME">NAME</a></span></code>
<div class="block">The key for the name of a piece of furniture (mandatory).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#PLAN_ICON">PLAN_ICON</a></span></code>
<div class="block">The key for the plan icon file of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#PLAN_ICON_DIGEST">PLAN_ICON_DIGEST</a></span></code>
<div class="block">The key for the SHA-1 digest of the plan icon file of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#PRICE">PRICE</a></span></code>
<div class="block">The key for the price of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#RESIZABLE">RESIZABLE</a></span></code>
<div class="block">The key for the resizability of a piece of furniture (optional, <code>true</code> by default).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#SHELF_BOXES">SHELF_BOXES</a></span></code>
<div class="block">The key for the shelf box(es) in which other objects can be placed in a piece of furniture (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#SHELF_ELEVATIONS">SHELF_ELEVATIONS</a></span></code>
<div class="block">The key for the shelf elevation(s) at which other objects can be placed on a piece of furniture
 from its bottom (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#STAIRCASE_CUT_OUT_SHAPE">STAIRCASE_CUT_OUT_SHAPE</a></span></code>
<div class="block">The key for the shape used to cut out upper levels when they intersect with a piece
 like a staircase (optional).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#TAGS">TAGS</a></span></code>
<div class="block">The key for the tags or keywords associated to a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#TEXTURABLE">TEXTURABLE</a></span></code>
<div class="block">The key for the texturable capability of a piece of furniture (optional, <code>true</code> by default).</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#VALUE_ADDED_TAX_PERCENTAGE">VALUE_ADDED_TAX_PERCENTAGE</a></span></code>
<div class="block">The key for the VAT percentage of a piece of furniture (optional).</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#WIDTH">WIDTH</a></span></code>
<div class="block">The key for the width in centimeters of a piece of furniture (mandatory).</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#fromPrefix-java.lang.String-">fromPrefix</a></span>(java.lang.String&nbsp;keyPrefix)</code>
<div class="block">Returns the <code>PropertyKey</code> instance matching the given key prefix.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#getKey-int-">getKey</a></span>(int&nbsp;pieceIndex)</code>
<div class="block">Returns the key for the piece property of the given index.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#getKeyPrefix--">getKeyPrefix</a></span>()</code>
<div class="block">Returns the key prefix for the piece property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#valueOf-java.lang.String-">valueOf</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html#values--">values</a></span>()</code>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Enum">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Enum</h3>
<code>clone, compareTo, equals, finalize, getDeclaringClass, hashCode, name, ordinal, toString, valueOf</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>getClass, notify, notifyAll, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ ENUM CONSTANT DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="enum.constant.detail">
<!--   -->
</a>
<h3>Enum Constant Detail</h3>
<a name="ID">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ID</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> ID</pre>
<div class="block">The key for the ID of a piece of furniture (optional).
 Two pieces of furniture read in a furniture catalog can't have the same ID
 and the second one will be ignored.</div>
</li>
</ul>
<a name="NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NAME</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> NAME</pre>
<div class="block">The key for the name of a piece of furniture (mandatory).</div>
</li>
</ul>
<a name="DESCRIPTION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DESCRIPTION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DESCRIPTION</pre>
<div class="block">The key for the description of a piece of furniture (optional).
 This may give detailed information about a piece of furniture.</div>
</li>
</ul>
<a name="INFORMATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>INFORMATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> INFORMATION</pre>
<div class="block">The key for some additional information associated to a piece of furniture (optional).
 This information may contain some HTML code or a link to an external web site.</div>
</li>
</ul>
<a name="LICENSE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LICENSE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> LICENSE</pre>
<div class="block">The key for the license associated to a piece of furniture (optional).</div>
</li>
</ul>
<a name="TAGS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TAGS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> TAGS</pre>
<div class="block">The key for the tags or keywords associated to a piece of furniture (optional).
 Tags are separated by commas with possible heading or trailing spaces.</div>
</li>
</ul>
<a name="CREATION_DATE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATION_DATE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> CREATION_DATE</pre>
<div class="block">The key for the creation or publication date of a piece of furniture at
 <code>yyyy-MM-dd</code> format (optional).</div>
</li>
</ul>
<a name="GRADE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>GRADE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> GRADE</pre>
<div class="block">The key for the grade of a piece of furniture (optional).</div>
</li>
</ul>
<a name="CATEGORY">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CATEGORY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> CATEGORY</pre>
<div class="block">The key for the category's name of a piece of furniture (mandatory).
 A new category with this name will be created if it doesn't exist.</div>
</li>
</ul>
<a name="ICON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ICON</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> ICON</pre>
<div class="block">The key for the icon file of a piece of furniture (mandatory).
 This icon file can be either the path to an image relative to classpath
 or an absolute URL. It should be encoded in application/x-www-form-urlencoded
 format if needed.</div>
</li>
</ul>
<a name="ICON_DIGEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ICON_DIGEST</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> ICON_DIGEST</pre>
<div class="block">The key for the SHA-1 digest of the icon file of a piece of furniture (optional).
 This property is used to compare faster catalog resources with the ones of a read home,
 and should be encoded in Base64.</div>
</li>
</ul>
<a name="PLAN_ICON">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLAN_ICON</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> PLAN_ICON</pre>
<div class="block">The key for the plan icon file of a piece of furniture (optional).
 This icon file can be either the path to an image relative to classpath
 or an absolute URL. It should be encoded in application/x-www-form-urlencoded
 format if needed.</div>
</li>
</ul>
<a name="PLAN_ICON_DIGEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PLAN_ICON_DIGEST</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> PLAN_ICON_DIGEST</pre>
<div class="block">The key for the SHA-1 digest of the plan icon file of a piece of furniture (optional).
 This property is used to compare faster catalog resources with the ones of a read home,
 and should be encoded in Base64.</div>
</li>
</ul>
<a name="MODEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> MODEL</pre>
<div class="block">The key for the 3D model file of a piece of furniture (mandatory).
 The 3D model file can be either a path relative to classpath
 or an absolute URL.  It should be encoded in application/x-www-form-urlencoded
 format if needed.</div>
</li>
</ul>
<a name="MODEL_SIZE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_SIZE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> MODEL_SIZE</pre>
<div class="block">The key for the size of the 3D model of a piece of furniture (optional).
 If model content is a file this should contain the file size.</div>
</li>
</ul>
<a name="MODEL_DIGEST">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_DIGEST</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> MODEL_DIGEST</pre>
<div class="block">The key for the SHA-1 digest of the 3D model file of a piece of furniture (optional).
 This property is used to compare faster catalog resources with the ones of a read home,
 and should be encoded in Base64.</div>
</li>
</ul>
<a name="MULTI_PART_MODEL">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MULTI_PART_MODEL</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> MULTI_PART_MODEL</pre>
<div class="block">The key for a piece of furniture with multiple parts (optional).
 If the value of this key is <code>true</code>, all the files
 stored in the same folder as the 3D model file (MTL, texture files...)
 will be considered as being necessary to view correctly the 3D model.</div>
</li>
</ul>
<a name="WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>WIDTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> WIDTH</pre>
<div class="block">The key for the width in centimeters of a piece of furniture (mandatory).</div>
</li>
</ul>
<a name="DEPTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEPTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DEPTH</pre>
<div class="block">The key for the depth in centimeters of a piece of furniture (mandatory).</div>
</li>
</ul>
<a name="HEIGHT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HEIGHT</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> HEIGHT</pre>
<div class="block">The key for the height in centimeters of a piece of furniture (mandatory).</div>
</li>
</ul>
<a name="MOVABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MOVABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> MOVABLE</pre>
<div class="block">The key for the movability of a piece of furniture (mandatory).
 If the value of this key is <code>true</code>, the piece of furniture
 will be considered as a movable piece.</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW</pre>
<div class="block">The key for the door or window type of a piece of furniture (mandatory).
 If the value of this key is <code>true</code>, the piece of furniture
 will be considered as a door or a window.</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_CUT_OUT_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_CUT_OUT_SHAPE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_CUT_OUT_SHAPE</pre>
<div class="block">The key for the shape of a door or window used to cut out walls when they intersect it (optional).
 This shape should be defined with the syntax of the d attribute of a
 <a href="http://www.w3.org/TR/SVG/paths.html">SVG path element</a>
 and should fit in a square spreading from (0, 0) to (1, 1) which will be
 scaled afterwards to the real size of the piece.
 If not specified, then this shape will be automatically computed from the actual shape of the model.</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_WALL_THICKNESS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_WALL_THICKNESS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_WALL_THICKNESS</pre>
<div class="block">The key for the wall thickness in centimeters of a door or a window (optional).
 By default, a door or a window has the same depth as the wall it belongs to.</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_WALL_DISTANCE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_WALL_DISTANCE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_WALL_DISTANCE</pre>
<div class="block">The key for the distance in centimeters of a door or a window to its wall (optional).
 By default, this distance is zero.</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_WALL_CUT_OUT_ON_BOTH_SIDES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_WALL_CUT_OUT_ON_BOTH_SIDES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_WALL_CUT_OUT_ON_BOTH_SIDES</pre>
<div class="block">The key for the wall cut out rule of a door or a window (optional, <code>true</code> by default).
 By default, a door or a window placed on a wall and parallel to it will cut out the both sides of that wall
 even if its depth is smaller than the wall thickness or if it intersects only one side of the wall.
 If the value of this key is <code>false</code>, a door or a window will only dig the wall
 at its intersection, and will cut the both sides of a wall only if it intersects both of them.</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_WIDTH_DEPTH_DEFORMABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_WIDTH_DEPTH_DEFORMABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_WIDTH_DEPTH_DEFORMABLE</pre>
<div class="block">The key for the width/depth deformability of a door or a window (optional, <code>true</code> by default).
 By default, the depth of a door or a window can be changed and adapted to
 the wall thickness where it's placed regardless of its width. To avoid this deformation
 in the case of open doors, the value of this key can be set to <code>false</code>.
 Doors and windows with their width/depth deformability set to <code>false</code>
 and their <a href="../../../../com/eteks/sweethome3d/model/HomeDoorOrWindow.html#isBoundToWall--"><code>bouldToWall</code></a> flag set to <code>true</code>
 will also make a hole in the wall when they are placed whatever their depth.</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_SASH_X_AXIS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_SASH_X_AXIS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_SASH_X_AXIS</pre>
<div class="block">The key for the sash axis distance(s) of a door or a window along X axis (optional).
 If a door or a window has more than one sash, the values of each sash should be
 separated by spaces.</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_SASH_Y_AXIS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_SASH_Y_AXIS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_SASH_Y_AXIS</pre>
<div class="block">The key for the sash axis distance(s) of a door or a window along Y axis
 (mandatory if sash axis distance along X axis is defined).</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_SASH_WIDTH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_SASH_WIDTH</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_SASH_WIDTH</pre>
<div class="block">The key for the sash width(s) of a door or a window
 (mandatory if sash axis distance along X axis is defined).</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_SASH_START_ANGLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_SASH_START_ANGLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_SASH_START_ANGLE</pre>
<div class="block">The key for the sash start angle(s) of a door or a window
 (mandatory if sash axis distance along X axis is defined).</div>
</li>
</ul>
<a name="DOOR_OR_WINDOW_SASH_END_ANGLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DOOR_OR_WINDOW_SASH_END_ANGLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DOOR_OR_WINDOW_SASH_END_ANGLE</pre>
<div class="block">The key for the sash end angle(s) of a door or a window
 (mandatory if sash axis distance along X axis is defined).</div>
</li>
</ul>
<a name="LIGHT_SOURCE_X">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LIGHT_SOURCE_X</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> LIGHT_SOURCE_X</pre>
<div class="block">The key for the abscissa(s) of light sources in a light (optional).
 If a light has more than one light source, the values of each light source should
 be separated by spaces.</div>
</li>
</ul>
<a name="LIGHT_SOURCE_Y">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LIGHT_SOURCE_Y</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> LIGHT_SOURCE_Y</pre>
<div class="block">The key for the ordinate(s) of light sources in a light (mandatory if light source abscissa is defined).</div>
</li>
</ul>
<a name="LIGHT_SOURCE_Z">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LIGHT_SOURCE_Z</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> LIGHT_SOURCE_Z</pre>
<div class="block">The key for the elevation(s) of light sources in a light (mandatory if light source abscissa is defined).</div>
</li>
</ul>
<a name="LIGHT_SOURCE_COLOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LIGHT_SOURCE_COLOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> LIGHT_SOURCE_COLOR</pre>
<div class="block">The key for the color(s) of light sources in a light (mandatory if light source abscissa is defined).</div>
</li>
</ul>
<a name="LIGHT_SOURCE_DIAMETER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LIGHT_SOURCE_DIAMETER</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> LIGHT_SOURCE_DIAMETER</pre>
<div class="block">The key for the diameter(s) of light sources in a light (optional).</div>
</li>
</ul>
<a name="LIGHT_SOURCE_MATERIAL_NAME">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>LIGHT_SOURCE_MATERIAL_NAME</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> LIGHT_SOURCE_MATERIAL_NAME</pre>
<div class="block">The key for the material name(s) of light source shapes in the 3D model of a light (optional).</div>
</li>
</ul>
<a name="STAIRCASE_CUT_OUT_SHAPE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>STAIRCASE_CUT_OUT_SHAPE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> STAIRCASE_CUT_OUT_SHAPE</pre>
<div class="block">The key for the shape used to cut out upper levels when they intersect with a piece
 like a staircase (optional). This shape should be defined with the syntax of
 the d attribute of a <a href="http://www.w3.org/TR/SVG/paths.html">SVG path element</a>
 and should fit in a square spreading from (0, 0) to (1, 1) which will be scaled afterwards
 to the real size of the piece.</div>
</li>
</ul>
<a name="ELEVATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ELEVATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> ELEVATION</pre>
<div class="block">The key for the elevation in centimeters of a piece of furniture (optional).</div>
</li>
</ul>
<a name="DROP_ON_TOP_ELEVATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DROP_ON_TOP_ELEVATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DROP_ON_TOP_ELEVATION</pre>
<div class="block">The key for the preferred elevation (from the bottom of a piece) at which should be placed
 an object dropped on a piece (optional). A negative value means that the piece should be ignored
 when an object is dropped on it. By default, this elevation is equal to its height.</div>
</li>
</ul>
<a name="SHELF_ELEVATIONS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHELF_ELEVATIONS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> SHELF_ELEVATIONS</pre>
<div class="block">The key for the shelf elevation(s) at which other objects can be placed on a piece of furniture
 from its bottom (optional).</div>
</li>
</ul>
<a name="SHELF_BOXES">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SHELF_BOXES</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> SHELF_BOXES</pre>
<div class="block">The key for the shelf box(es) in which other objects can be placed in a piece of furniture (optional).
 Each box is defined by the 6 values of the x, y, z coordinates of its left front bottom corner and
 its right back top corner.</div>
</li>
</ul>
<a name="MODEL_ROTATION">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_ROTATION</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> MODEL_ROTATION</pre>
<div class="block">The key for the transformation matrix values applied to a piece of furniture (optional).
 If the 3D model of a piece of furniture isn't correctly oriented,
 the value of this key should give the 9 values of the transformation matrix
 that will orient it correctly.</div>
</li>
</ul>
<a name="MODEL_FLAGS">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODEL_FLAGS</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> MODEL_FLAGS</pre>
<div class="block">The key for the model flags applied to a piece of furniture (optional).
 May be a combination of <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#SHOW_BACK_FACE"><code>PieceOfFurniture.SHOW_BACK_FACE</code></a> and <a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html#HIDE_EDGE_COLOR_MATERIAL"><code>PieceOfFurniture.HIDE_EDGE_COLOR_MATERIAL</code></a> flags.</div>
</li>
</ul>
<a name="CREATOR">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>CREATOR</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> CREATOR</pre>
<div class="block">The key for the creator of a piece of furniture (optional).</div>
</li>
</ul>
<a name="RESIZABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RESIZABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> RESIZABLE</pre>
<div class="block">The key for the resizability of a piece of furniture (optional, <code>true</code> by default).
 If the value of this key is <code>false</code>, the piece of furniture
 will be considered as a piece with a fixed size.</div>
</li>
</ul>
<a name="DEFORMABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DEFORMABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> DEFORMABLE</pre>
<div class="block">The key for the deformability of a piece of furniture (optional, <code>true</code> by default).
 If the value of this key is <code>false</code>, the piece of furniture
 will be considered as a piece that should always keep its proportions when resized.</div>
</li>
</ul>
<a name="TEXTURABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TEXTURABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> TEXTURABLE</pre>
<div class="block">The key for the texturable capability of a piece of furniture (optional, <code>true</code> by default).
 If the value of this key is <code>false</code>, the piece of furniture
 will be considered as a piece that will always keep the same color or texture.</div>
</li>
</ul>
<a name="HORIZONTALLY_ROTATABLE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>HORIZONTALLY_ROTATABLE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> HORIZONTALLY_ROTATABLE</pre>
<div class="block">The key for the ability of a piece of furniture to rotate around a horizontal axis (optional, <code>true</code> by default).
 If the value of this key is <code>false</code>, the piece of furniture
 will be considered as a piece that can't be horizontally rotated.</div>
</li>
</ul>
<a name="PRICE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>PRICE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> PRICE</pre>
<div class="block">The key for the price of a piece of furniture (optional).</div>
</li>
</ul>
<a name="VALUE_ADDED_TAX_PERCENTAGE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>VALUE_ADDED_TAX_PERCENTAGE</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> VALUE_ADDED_TAX_PERCENTAGE</pre>
<div class="block">The key for the VAT percentage of a piece of furniture (optional).</div>
</li>
</ul>
<a name="CURRENCY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CURRENCY</h4>
<pre>public static final&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a> CURRENCY</pre>
<div class="block">The key for the currency ISO 4217 code of the price of a piece of furniture (optional).</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="values--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>values</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a>[]&nbsp;values()</pre>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.  This method may be used to iterate
over the constants as follows:
<pre>
for (DefaultFurnitureCatalog.PropertyKey c : DefaultFurnitureCatalog.PropertyKey.values())
&nbsp;   System.out.println(c);
</pre></div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the constants of this enum type, in the order they are declared</dd>
</dl>
</li>
</ul>
<a name="valueOf-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>valueOf</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a>&nbsp;valueOf(java.lang.String&nbsp;name)</pre>
<div class="block">Returns the enum constant of this type with the specified name.
The string must match <i>exactly</i> an identifier used to declare an
enum constant in this type.  (Extraneous whitespace characters are 
not permitted.)</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>name</code> - the name of the enum constant to be returned.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the enum constant with the specified name</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalArgumentException</code> - if this enum type has no constant with the specified name</dd>
<dd><code>java.lang.NullPointerException</code> - if the argument is null</dd>
</dl>
</li>
</ul>
<a name="getKeyPrefix--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKeyPrefix</h4>
<pre>public&nbsp;java.lang.String&nbsp;getKeyPrefix()</pre>
<div class="block">Returns the key prefix for the piece property.</div>
</li>
</ul>
<a name="getKey-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKey</h4>
<pre>public&nbsp;java.lang.String&nbsp;getKey(int&nbsp;pieceIndex)</pre>
<div class="block">Returns the key for the piece property of the given index.</div>
</li>
</ul>
<a name="fromPrefix-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>fromPrefix</h4>
<pre>public static&nbsp;<a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" title="enum in com.eteks.sweethome3d.io">DefaultFurnitureCatalog.PropertyKey</a>&nbsp;fromPrefix(java.lang.String&nbsp;keyPrefix)</pre>
<div class="block">Returns the <code>PropertyKey</code> instance matching the given key prefix.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultFurnitureCatalog.PropertyKey.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultFurnitureCatalog.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/io/DefaultHomeInputStream.html" title="class in com.eteks.sweethome3d.io"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/io/DefaultFurnitureCatalog.PropertyKey.html" target="_top">Frames</a></li>
<li><a href="DefaultFurnitureCatalog.PropertyKey.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#enum.constant.summary">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#enum.constant.detail">Enum Constants</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
