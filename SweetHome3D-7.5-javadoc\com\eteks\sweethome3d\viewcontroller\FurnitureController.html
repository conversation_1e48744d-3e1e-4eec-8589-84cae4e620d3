<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>FurnitureController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FurnitureController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":42,"i36":10,"i37":10,"i38":10,"i39":10,"i40":42,"i41":42,"i42":10,"i43":42,"i44":10,"i45":10,"i46":42,"i47":10,"i48":10,"i49":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FurnitureController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/FurnitureController.html" target="_top">Frames</a></li>
<li><a href="FurnitureController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class FurnitureController" class="title">Class FurnitureController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.FurnitureController</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">FurnitureController</span>
extends java.lang.Object
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">A MVC controller for the home furniture table.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#FurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">FurnitureController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</code>
<div class="block">Creates the controller of home furniture view.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#FurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">FurnitureController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                   <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                   <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                   javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurniture-java.util.List-">addFurniture</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</code>
<div class="block">Controls new furniture added to home.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurniture-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">addFurniture</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
            <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;beforePiece)</code>
<div class="block">Controls new furniture added to home.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#addFurnitureToGroup-java.util.List-com.eteks.sweethome3d.model.HomeFurnitureGroup-">addFurnitureToGroup</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                   <a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;group)</code>
<div class="block">Controls new furniture added to the given group.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnBackSide--">alignSelectedFurnitureOnBackSide</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on the back side of the first selected piece.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnBottom--">alignSelectedFurnitureOnBottom</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on bottom of the first selected piece.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnFrontSide--">alignSelectedFurnitureOnFrontSide</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on the front side of the first selected piece.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnLeft--">alignSelectedFurnitureOnLeft</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on left of the first selected piece.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnLeftSide--">alignSelectedFurnitureOnLeftSide</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on the left side of the first selected piece.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnRight--">alignSelectedFurnitureOnRight</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on right of the first selected piece.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnRightSide--">alignSelectedFurnitureOnRightSide</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on the right side of the first selected piece.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureOnTop--">alignSelectedFurnitureOnTop</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on top of the first selected piece.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#alignSelectedFurnitureSideBySide--">alignSelectedFurnitureSideBySide</a></span>()</code>
<div class="block">Controls the alignment of selected furniture on the sides of the first selected piece.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomeFurnitureGroup-java.util.List-">createHomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</code>
<div class="block">Returns a new furniture group for the given furniture list.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">createHomeFurnitureGroup</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                        <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;leadingPiece)</code>
<div class="block">Returns a new furniture group for the given furniture list.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#createHomePieceOfFurniture-com.eteks.sweethome3d.model.PieceOfFurniture-">createHomePieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns a new home piece of furniture created from an other given <code>piece</code> of furniture.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#deleteFurniture-java.util.List-">deleteFurniture</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;deletedFurniture)</code>
<div class="block">Deletes the furniture of <code>deletedFurniture</code> from home.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#deleteSelection--">deleteSelection</a></span>()</code>
<div class="block">Controls the deletion of the current selected furniture in home.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#distributeSelectedFurniture-boolean-">distributeSelectedFurniture</a></span>(boolean&nbsp;horizontal)</code>
<div class="block">Controls the distribution of the selected furniture along the axis orthogonal to the given one.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#distributeSelectedFurnitureHorizontally--">distributeSelectedFurnitureHorizontally</a></span>()</code>
<div class="block">Controls the distribution of the selected furniture along horizontal axis.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#distributeSelectedFurnitureVertically--">distributeSelectedFurnitureVertically</a></span>()</code>
<div class="block">Controls the distribution of the selected furniture along vertical axis.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getFurnitureInSameGroup-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getFurnitureInSameGroup</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the furniture list of the given <code>piece</code> which belongs to same group
 or home furniture if it doesn't belong to home furniture.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getHighestSurroundingPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getHighestSurroundingPieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the highest piece of furniture that includes the given <code>piece</code>
 with a margin error of 5% of the smallest side length.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getSurroundingFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">getSurroundingFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns the shelf units which include the given <code>piece</code>
 with a margin error of 20% of the smallest side length.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#getView--">getView</a></span>()</code>
<div class="block">Returns the view associated with this controller.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#groupSelectedFurniture--">groupSelectedFurniture</a></span>()</code>
<div class="block">Groups the selected furniture as one piece of furniture.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#importFurniture--">importFurniture</a></span>()</code>
<div class="block">Displays the wizard that helps to import furniture to home.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#importFurniture-java.lang.String-">importFurniture</a></span>(java.lang.String&nbsp;modelName)</code>
<div class="block">Displays the wizard that helps to import furniture to home with a
 given model name.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureDeletable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureDeletable</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given <code>piece</code> may be deleted.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureMovable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureMovable</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given <code>piece</code> may be moved.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurniturePartOfBasePlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurniturePartOfBasePlan</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given <code>piece</code> isn't movable.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#isPieceOfFurnitureVisibleAtSelectedLevel-com.eteks.sweethome3d.model.HomePieceOfFurniture-">isPieceOfFurnitureVisibleAtSelectedLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Returns <code>true</code> if the given piece is viewable and
 its height and elevation make it viewable at the selected level in home.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#modifySelectedFurniture--">modifySelectedFurniture</a></span>()</code>
<div class="block">Controls the modification of selected furniture.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#moveSelectedFurnitureBefore-com.eteks.sweethome3d.model.HomePieceOfFurniture-">moveSelectedFurnitureBefore</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;beforePiece)</code>
<div class="block">Reorders the selected furniture in home to place it before the given piece.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#resetFurnitureElevation--">resetFurnitureElevation</a></span>()</code>
<div class="block">Resets the elevation of the selected furniture to its default elevation.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#selectAll--">selectAll</a></span>()</code>
<div class="block">Selects all furniture in home.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setFurnitureVisibleProperties-java.util.List-">setFurnitureVisibleProperties</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;&nbsp;furnitureVisibleProperties)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <code>#setFurnitureVisiblePropertyNames(List<String>)</code>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setFurnitureVisiblePropertyNames-java.util.List-">setFurnitureVisiblePropertyNames</a></span>(java.util.List&lt;java.lang.String&gt;&nbsp;furnitureVisiblePropertyNames)</code>
<div class="block">Updates the furniture visible properties in home.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setHomeProperty-java.lang.String-java.lang.String-">setHomeProperty</a></span>(java.lang.String&nbsp;propertyName,
               java.lang.String&nbsp;propertyValue)</code>
<div class="block">Controls the change of value of a property in home.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setSelectedFurniture-java.util.List-">setSelectedFurniture</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;selectedFurniture)</code>
<div class="block">Updates the selected furniture in home.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setSelectedFurniture-java.util.List-boolean-">setSelectedFurniture</a></span>(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;selectedFurniture,
                    boolean&nbsp;resetSelection)</code>
<div class="block">Updates the selected furniture in home, unselecting all other kinds of selected objects
 when <code>resetSelection</code> is <code>true</code>.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setVisualProperty-java.lang.String-java.lang.Object-">setVisualProperty</a></span>(java.lang.String&nbsp;propertyName,
                 java.lang.Object&nbsp;propertyValue)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty</code></a> should be replaced by a call to
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setHomeProperty-java.lang.String-java.lang.String-"><code>setHomeProperty(String, String)</code></a> to ensure the property can be easily saved and read.</span></div>
</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">sortFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>sortFurniture(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-java.lang.String-"><code>sortFurniture(String)</code></a>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-java.lang.String-">sortFurniture</a></span>(java.lang.String&nbsp;furniturePropertyName)</code>
<div class="block">Controls the sort of the furniture in home.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">toggleFurnitureSort</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>toggleFurnitureSort(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-java.lang.String-"><code>toggleFurnitureSort(String)</code></a>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-java.lang.String-">toggleFurnitureSort</a></span>(java.lang.String&nbsp;furniturePropertyName)</code>
<div class="block">Uses <code>furniturePropertyName</code> to sort home furniture
 or cancels home furniture sort if home is already sorted on <code>furnitureProperty</code></div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSortOrder--">toggleFurnitureSortOrder</a></span>()</code>
<div class="block">Toggles home furniture sort order.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">toggleFurnitureVisibleProperty</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>toggleFurnitureVisibleProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-java.lang.String-"><code>toggleFurnitureVisibleProperty(String)</code></a>
     to allow displaying additional properties.</span></div>
</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-java.lang.String-">toggleFurnitureVisibleProperty</a></span>(java.lang.String&nbsp;furniturePropertyName)</code>
<div class="block">Toggles furniture property visibility in home.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleSelectedFurnitureVisibility--">toggleSelectedFurnitureVisibility</a></span>()</code>
<div class="block">Controls the modification of the visibility of the selected piece of furniture.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#ungroupSelectedFurniture--">ungroupSelectedFurniture</a></span>()</code>
<div class="block">Ungroups the selected groups of furniture.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="FurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>FurnitureController</h4>
<pre>public&nbsp;FurnitureController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                           <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                           <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory)</pre>
<div class="block">Creates the controller of home furniture view.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>home</code> - the home edited by this controller and its view</dd>
<dd><code>preferences</code> - the preferences of the application</dd>
<dd><code>viewFactory</code> - a factory able to create the furniture view managed by this controller</dd>
</dl>
</li>
</ul>
<a name="FurnitureController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>FurnitureController</h4>
<pre>public&nbsp;FurnitureController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                           <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                           <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                           <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                           javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates the controller of home furniture view with undo support.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getView</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;getView()</pre>
<div class="block">Returns the view associated with this controller.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></code></dd>
</dl>
</li>
</ul>
<a name="addFurniture-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurniture</h4>
<pre>public&nbsp;void&nbsp;addFurniture(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</pre>
<div class="block">Controls new furniture added to home.
 Once added the furniture will be selected in view
 and undo support will receive a new undoable edit.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furniture</code> - the furniture to add.</dd>
</dl>
</li>
</ul>
<a name="addFurniture-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurniture</h4>
<pre>public&nbsp;void&nbsp;addFurniture(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                         <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;beforePiece)</pre>
<div class="block">Controls new furniture added to home.
 Once added the furniture will be selected in view
 and undo support will receive a new undoable edit.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furniture</code> - the furniture to add.</dd>
<dd><code>beforePiece</code> - the piece before which the furniture will be added</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.3</dd>
</dl>
</li>
</ul>
<a name="addFurnitureToGroup-java.util.List-com.eteks.sweethome3d.model.HomeFurnitureGroup-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addFurnitureToGroup</h4>
<pre>public&nbsp;void&nbsp;addFurnitureToGroup(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                                <a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;group)</pre>
<div class="block">Controls new furniture added to the given group.
 Once added the furniture will be selected in view
 and undo support will receive a new undoable edit.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furniture</code> - the furniture to add.</dd>
<dd><code>group</code> - the group to which furniture will be added.</dd>
</dl>
</li>
</ul>
<a name="deleteSelection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteSelection</h4>
<pre>public&nbsp;void&nbsp;deleteSelection()</pre>
<div class="block">Controls the deletion of the current selected furniture in home.
 Once the selected furniture is deleted, undo support will receive a new undoable edit.</div>
</li>
</ul>
<a name="deleteFurniture-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteFurniture</h4>
<pre>public&nbsp;void&nbsp;deleteFurniture(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;deletedFurniture)</pre>
<div class="block">Deletes the furniture of <code>deletedFurniture</code> from home.
 Once the selected furniture is deleted, undo support will receive a new undoable edit.</div>
</li>
</ul>
<a name="moveSelectedFurnitureBefore-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>moveSelectedFurnitureBefore</h4>
<pre>public&nbsp;void&nbsp;moveSelectedFurnitureBefore(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;beforePiece)</pre>
<div class="block">Reorders the selected furniture in home to place it before the given piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.3</dd>
</dl>
</li>
</ul>
<a name="setSelectedFurniture-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectedFurniture</h4>
<pre>public&nbsp;void&nbsp;setSelectedFurniture(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;selectedFurniture)</pre>
<div class="block">Updates the selected furniture in home.</div>
</li>
</ul>
<a name="setSelectedFurniture-java.util.List-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSelectedFurniture</h4>
<pre>public&nbsp;void&nbsp;setSelectedFurniture(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;selectedFurniture,
                                 boolean&nbsp;resetSelection)</pre>
<div class="block">Updates the selected furniture in home, unselecting all other kinds of selected objects
 when <code>resetSelection</code> is <code>true</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.1</dd>
</dl>
</li>
</ul>
<a name="selectAll--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>selectAll</h4>
<pre>public&nbsp;void&nbsp;selectAll()</pre>
<div class="block">Selects all furniture in home.</div>
</li>
</ul>
<a name="isPieceOfFurniturePartOfBasePlan-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPieceOfFurniturePartOfBasePlan</h4>
<pre>protected&nbsp;boolean&nbsp;isPieceOfFurniturePartOfBasePlan(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns <code>true</code> if the given <code>piece</code> isn't movable.</div>
</li>
</ul>
<a name="isPieceOfFurnitureMovable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPieceOfFurnitureMovable</h4>
<pre>protected&nbsp;boolean&nbsp;isPieceOfFurnitureMovable(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns <code>true</code> if the given <code>piece</code> may be moved.
 Default implementation always returns <code>true</code>.</div>
</li>
</ul>
<a name="isPieceOfFurnitureDeletable-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPieceOfFurnitureDeletable</h4>
<pre>protected&nbsp;boolean&nbsp;isPieceOfFurnitureDeletable(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns <code>true</code> if the given <code>piece</code> may be deleted.
 Default implementation always returns <code>true</code>.</div>
</li>
</ul>
<a name="createHomePieceOfFurniture-com.eteks.sweethome3d.model.PieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHomePieceOfFurniture</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;createHomePieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model">PieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns a new home piece of furniture created from an other given <code>piece</code> of furniture.</div>
</li>
</ul>
<a name="toggleFurnitureSort-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleFurnitureSort</h4>
<pre>public&nbsp;void&nbsp;toggleFurnitureSort(java.lang.String&nbsp;furniturePropertyName)</pre>
<div class="block">Uses <code>furniturePropertyName</code> to sort home furniture
 or cancels home furniture sort if home is already sorted on <code>furnitureProperty</code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furniturePropertyName</code> - a property of <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model"><code>HomePieceOfFurniture</code></a> class.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="toggleFurnitureSort-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleFurnitureSort</h4>
<pre>public&nbsp;void&nbsp;toggleFurnitureSort(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>toggleFurnitureSort(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureSort-java.lang.String-"><code>toggleFurnitureSort(String)</code></a>
     to allow displaying additional properties.</span></div>
<div class="block">Uses <code>furnitureProperty</code> to sort home furniture
 or cancels home furniture sort if home is already sorted on <code>furnitureProperty</code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureProperty</code> - a property of <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model"><code>HomePieceOfFurniture</code></a> class.</dd>
</dl>
</li>
</ul>
<a name="toggleFurnitureSortOrder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleFurnitureSortOrder</h4>
<pre>public&nbsp;void&nbsp;toggleFurnitureSortOrder()</pre>
<div class="block">Toggles home furniture sort order.</div>
</li>
</ul>
<a name="sortFurniture-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sortFurniture</h4>
<pre>public&nbsp;void&nbsp;sortFurniture(java.lang.String&nbsp;furniturePropertyName)</pre>
<div class="block">Controls the sort of the furniture in home. If home furniture isn't sorted
 or is sorted on an other property, it will be sorted on the given
 <code>furnitureProperty</code> in ascending order. If home furniture is already
 sorted on the given <code>furnitureProperty</code>, it will be sorted in descending
 order, if the sort is in ascending order, otherwise it won't be sorted at all
 and home furniture will be listed in insertion order.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furniturePropertyName</code> - the furniture property on which the view wants
          to sort the furniture it displays.</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="sortFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>sortFurniture</h4>
<pre>public&nbsp;void&nbsp;sortFurniture(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>sortFurniture(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#sortFurniture-java.lang.String-"><code>sortFurniture(String)</code></a>
     to allow displaying additional properties.</span></div>
<div class="block">Controls the sort of the furniture in home.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>furnitureProperty</code> - the furniture property on which the view wants
          to sort the furniture it displays.</dd>
</dl>
</li>
</ul>
<a name="setFurnitureVisiblePropertyNames-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureVisiblePropertyNames</h4>
<pre>public&nbsp;void&nbsp;setFurnitureVisiblePropertyNames(java.util.List&lt;java.lang.String&gt;&nbsp;furnitureVisiblePropertyNames)</pre>
<div class="block">Updates the furniture visible properties in home.</div>
</li>
</ul>
<a name="setFurnitureVisibleProperties-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFurnitureVisibleProperties</h4>
<pre>public&nbsp;void&nbsp;setFurnitureVisibleProperties(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&gt;&nbsp;furnitureVisibleProperties)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><code>#setFurnitureVisibleProperties(List<HomePieceOfFurniture.SortableProperty>)</code>
     should be replaced by calls to <code>#setFurnitureVisiblePropertyNames(List<String>)</code>
     to allow displaying additional properties.</span></div>
<div class="block">Updates the furniture visible properties in home.</div>
</li>
</ul>
<a name="toggleFurnitureVisibleProperty-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleFurnitureVisibleProperty</h4>
<pre>public&nbsp;void&nbsp;toggleFurnitureVisibleProperty(java.lang.String&nbsp;furniturePropertyName)</pre>
<div class="block">Toggles furniture property visibility in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="toggleFurnitureVisibleProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleFurnitureVisibleProperty</h4>
<pre>public&nbsp;void&nbsp;toggleFurnitureVisibleProperty(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.SortableProperty.html" title="enum in com.eteks.sweethome3d.model">HomePieceOfFurniture.SortableProperty</a>&nbsp;furnitureProperty)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-com.eteks.sweethome3d.model.HomePieceOfFurniture.SortableProperty-"><code>toggleFurnitureVisibleProperty(HomePieceOfFurniture.SortableProperty)</code></a>
     should be replaced by calls to <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#toggleFurnitureVisibleProperty-java.lang.String-"><code>toggleFurnitureVisibleProperty(String)</code></a>
     to allow displaying additional properties.</span></div>
<div class="block">Toggles furniture property visibility in home.</div>
</li>
</ul>
<a name="modifySelectedFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>modifySelectedFurniture</h4>
<pre>public&nbsp;void&nbsp;modifySelectedFurniture()</pre>
<div class="block">Controls the modification of selected furniture.</div>
</li>
</ul>
<a name="toggleSelectedFurnitureVisibility--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toggleSelectedFurnitureVisibility</h4>
<pre>public&nbsp;void&nbsp;toggleSelectedFurnitureVisibility()</pre>
<div class="block">Controls the modification of the visibility of the selected piece of furniture.</div>
</li>
</ul>
<a name="groupSelectedFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>groupSelectedFurniture</h4>
<pre>public&nbsp;void&nbsp;groupSelectedFurniture()</pre>
<div class="block">Groups the selected furniture as one piece of furniture.</div>
</li>
</ul>
<a name="createHomeFurnitureGroup-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHomeFurnitureGroup</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;createHomeFurnitureGroup(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture)</pre>
<div class="block">Returns a new furniture group for the given furniture list.</div>
</li>
</ul>
<a name="createHomeFurnitureGroup-java.util.List-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createHomeFurnitureGroup</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomeFurnitureGroup.html" title="class in com.eteks.sweethome3d.model">HomeFurnitureGroup</a>&nbsp;createHomeFurnitureGroup(java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;furniture,
                                                      <a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;leadingPiece)</pre>
<div class="block">Returns a new furniture group for the given furniture list.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.5</dd>
</dl>
</li>
</ul>
<a name="ungroupSelectedFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ungroupSelectedFurniture</h4>
<pre>public&nbsp;void&nbsp;ungroupSelectedFurniture()</pre>
<div class="block">Ungroups the selected groups of furniture.</div>
</li>
</ul>
<a name="importFurniture--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importFurniture</h4>
<pre>public&nbsp;void&nbsp;importFurniture()</pre>
<div class="block">Displays the wizard that helps to import furniture to home.</div>
</li>
</ul>
<a name="importFurniture-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>importFurniture</h4>
<pre>public&nbsp;void&nbsp;importFurniture(java.lang.String&nbsp;modelName)</pre>
<div class="block">Displays the wizard that helps to import furniture to home with a
 given model name.</div>
</li>
</ul>
<a name="alignSelectedFurnitureOnTop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureOnTop</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureOnTop()</pre>
<div class="block">Controls the alignment of selected furniture on top of the first selected piece.</div>
</li>
</ul>
<a name="alignSelectedFurnitureOnBottom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureOnBottom</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureOnBottom()</pre>
<div class="block">Controls the alignment of selected furniture on bottom of the first selected piece.</div>
</li>
</ul>
<a name="alignSelectedFurnitureOnLeft--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureOnLeft</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureOnLeft()</pre>
<div class="block">Controls the alignment of selected furniture on left of the first selected piece.</div>
</li>
</ul>
<a name="alignSelectedFurnitureOnRight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureOnRight</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureOnRight()</pre>
<div class="block">Controls the alignment of selected furniture on right of the first selected piece.</div>
</li>
</ul>
<a name="alignSelectedFurnitureOnFrontSide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureOnFrontSide</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureOnFrontSide()</pre>
<div class="block">Controls the alignment of selected furniture on the front side of the first selected piece.</div>
</li>
</ul>
<a name="alignSelectedFurnitureOnBackSide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureOnBackSide</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureOnBackSide()</pre>
<div class="block">Controls the alignment of selected furniture on the back side of the first selected piece.</div>
</li>
</ul>
<a name="alignSelectedFurnitureOnLeftSide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureOnLeftSide</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureOnLeftSide()</pre>
<div class="block">Controls the alignment of selected furniture on the left side of the first selected piece.</div>
</li>
</ul>
<a name="alignSelectedFurnitureOnRightSide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureOnRightSide</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureOnRightSide()</pre>
<div class="block">Controls the alignment of selected furniture on the right side of the first selected piece.</div>
</li>
</ul>
<a name="alignSelectedFurnitureSideBySide--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>alignSelectedFurnitureSideBySide</h4>
<pre>public&nbsp;void&nbsp;alignSelectedFurnitureSideBySide()</pre>
<div class="block">Controls the alignment of selected furniture on the sides of the first selected piece.</div>
</li>
</ul>
<a name="distributeSelectedFurnitureHorizontally--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>distributeSelectedFurnitureHorizontally</h4>
<pre>public&nbsp;void&nbsp;distributeSelectedFurnitureHorizontally()</pre>
<div class="block">Controls the distribution of the selected furniture along horizontal axis.</div>
</li>
</ul>
<a name="distributeSelectedFurnitureVertically--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>distributeSelectedFurnitureVertically</h4>
<pre>public&nbsp;void&nbsp;distributeSelectedFurnitureVertically()</pre>
<div class="block">Controls the distribution of the selected furniture along vertical axis.</div>
</li>
</ul>
<a name="distributeSelectedFurniture-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>distributeSelectedFurniture</h4>
<pre>public&nbsp;void&nbsp;distributeSelectedFurniture(boolean&nbsp;horizontal)</pre>
<div class="block">Controls the distribution of the selected furniture along the axis orthogonal to the given one.</div>
</li>
</ul>
<a name="resetFurnitureElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetFurnitureElevation</h4>
<pre>public&nbsp;void&nbsp;resetFurnitureElevation()</pre>
<div class="block">Resets the elevation of the selected furniture to its default elevation.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getHighestSurroundingPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHighestSurroundingPieceOfFurniture</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;getHighestSurroundingPieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns the highest piece of furniture that includes the given <code>piece</code>
 with a margin error of 5% of the smallest side length.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="getSurroundingFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSurroundingFurniture</h4>
<pre>protected&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;getSurroundingFurniture(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns the shelf units which include the given <code>piece</code>
 with a margin error of 20% of the smallest side length.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="getFurnitureInSameGroup-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureInSameGroup</h4>
<pre>protected&nbsp;java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&gt;&nbsp;getFurnitureInSameGroup(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns the furniture list of the given <code>piece</code> which belongs to same group
 or home furniture if it doesn't belong to home furniture.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="isPieceOfFurnitureVisibleAtSelectedLevel-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPieceOfFurnitureVisibleAtSelectedLevel</h4>
<pre>protected&nbsp;boolean&nbsp;isPieceOfFurnitureVisibleAtSelectedLevel(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Returns <code>true</code> if the given piece is viewable and
 its height and elevation make it viewable at the selected level in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="setVisualProperty-java.lang.String-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisualProperty</h4>
<pre>public&nbsp;void&nbsp;setVisualProperty(java.lang.String&nbsp;propertyName,
                              java.lang.Object&nbsp;propertyValue)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment"><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setVisualProperty-java.lang.String-java.lang.Object-"><code>setVisualProperty</code></a> should be replaced by a call to
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html#setHomeProperty-java.lang.String-java.lang.String-"><code>setHomeProperty(String, String)</code></a> to ensure the property can be easily saved and read.</span></div>
<div class="block">Controls the change of value of a visual property in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="setHomeProperty-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setHomeProperty</h4>
<pre>public&nbsp;void&nbsp;setHomeProperty(java.lang.String&nbsp;propertyName,
                            java.lang.String&nbsp;propertyValue)</pre>
<div class="block">Controls the change of value of a property in home.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.2</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/FurnitureController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureCatalogController.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/FurnitureController.html" target="_top">Frames</a></li>
<li><a href="FurnitureController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
