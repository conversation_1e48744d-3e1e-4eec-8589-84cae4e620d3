<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:46 CEST 2024 -->
<title><PERSON><PERSON><PERSON> (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="<PERSON><PERSON><PERSON> (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Polyline.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Polyline.html" target="_top">Frames</a></li>
<li><a href="Polyline.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.model</div>
<h2 title="Class Polyline" class="title">Class Polyline</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">com.eteks.sweethome3d.model.HomeObject</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.model.Polyline</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a>, <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, java.io.Serializable, java.lang.Cloneable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Polyline</span>
extends <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a>
implements <a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>, <a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></pre>
<div class="block">A polyline or a polygon in a home plan.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.model.Polyline">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.Property.html" title="enum in com.eteks.sweethome3d.model">Polyline.Property</a></span></code>
<div class="block">The properties of a polyline that may change.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#Polyline-float:A:A-">Polyline</a></span>(float[][]&nbsp;points)</code>
<div class="block">Creates a polyline from the given coordinates.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#Polyline-float:A:A-float-com.eteks.sweethome3d.model.Polyline.CapStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-float-com.eteks.sweethome3d.model.Polyline.ArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-boolean-int-">Polyline</a></span>(float[][]&nbsp;points,
        float&nbsp;thickness,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle,
        float&nbsp;dashOffset,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;startArrowStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;endArrowStyle,
        boolean&nbsp;closedPath,
        int&nbsp;color)</code>
<div class="block">Creates a polyline from the given coordinates.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#Polyline-float:A:A-float-com.eteks.sweethome3d.model.Polyline.CapStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-boolean-int-">Polyline</a></span>(float[][]&nbsp;points,
        float&nbsp;thickness,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;startArrowStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;endArrowStyle,
        boolean&nbsp;closedPath,
        int&nbsp;color)</code>
<div class="block">Creates a polyline from the given coordinates.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#Polyline-java.lang.String-float:A:A-">Polyline</a></span>(java.lang.String&nbsp;id,
        float[][]&nbsp;points)</code>
<div class="block">Creates a polyline from the given coordinates.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#Polyline-java.lang.String-float:A:A-float-com.eteks.sweethome3d.model.Polyline.CapStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-float-com.eteks.sweethome3d.model.Polyline.ArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-boolean-int-">Polyline</a></span>(java.lang.String&nbsp;id,
        float[][]&nbsp;points,
        float&nbsp;thickness,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle,
        float&nbsp;dashOffset,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;startArrowStyle,
        <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;endArrowStyle,
        boolean&nbsp;closedPath,
        int&nbsp;color)</code>
<div class="block">Creates a polyline from the given coordinates.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#addPoint-float-float-">addPoint</a></span>(float&nbsp;x,
        float&nbsp;y)</code>
<div class="block">Adds a point at the end of polyline points.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#addPoint-float-float-int-">addPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        int&nbsp;index)</code>
<div class="block">Adds a point at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#clone--">clone</a></span>()</code>
<div class="block">Returns a clone of this polyline.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#containsPoint-float-float-float-">containsPoint</a></span>(float&nbsp;x,
             float&nbsp;y,
             float&nbsp;margin)</code>
<div class="block">Returns <code>true</code> if this polyline contains
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getCapStyle--">getCapStyle</a></span>()</code>
<div class="block">Returns the cap style of this polyline.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the color of this polyline.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getDashOffset--">getDashOffset</a></span>()</code>
<div class="block">Returns the offset from which the dash of this polyline should start.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getDashPattern--">getDashPattern</a></span>()</code>
<div class="block">Returns the dash pattern of this polyline in percentage of its thickness.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getDashStyle--">getDashStyle</a></span>()</code>
<div class="block">Returns the dash style of this polyline.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the elevation of this polyline in 3D.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getEndArrowStyle--">getEndArrowStyle</a></span>()</code>
<div class="block">Returns the arrow style at the end of this polyline.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getGroundElevation--">getGroundElevation</a></span>()</code>
<div class="block">Returns the elevation of this polyline
 from the ground according to the elevation of its level.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getJoinStyle--">getJoinStyle</a></span>()</code>
<div class="block">Returns the join style of this polyline.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getLength--">getLength</a></span>()</code>
<div class="block">Returns an approximate length of this polyline.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getLevel--">getLevel</a></span>()</code>
<div class="block">Returns the level which this polyline belongs to.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getPointCount--">getPointCount</a></span>()</code>
<div class="block">Returns the number of points of the polygon matching this polyline.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getPointIndexAt-float-float-float-">getPointIndexAt</a></span>(float&nbsp;x,
               float&nbsp;y,
               float&nbsp;margin)</code>
<div class="block">Returns the index of the point of this polyline equal to
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getPoints--">getPoints</a></span>()</code>
<div class="block">Returns the points of the polygon matching this polyline.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getStartArrowStyle--">getStartArrowStyle</a></span>()</code>
<div class="block">Returns the arrow style at the start of this polyline.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getThickness--">getThickness</a></span>()</code>
<div class="block">Returns the thickness of this polyline.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></span>(float&nbsp;x0,
                   float&nbsp;y0,
                   float&nbsp;x1,
                   float&nbsp;y1)</code>
<div class="block">Returns <code>true</code> if this polyline intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Returns <code>true</code> if this polyline is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#isClosedPath--">isClosedPath</a></span>()</code>
<div class="block">Returns <code>true</code> if the first and last points of this polyline should be joined to form a polygon.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#isVisibleIn3D--">isVisibleIn3D</a></span>()</code>
<div class="block">Returns <code>true</code> if this polyline should be displayed in 3D.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#move-float-float-">move</a></span>(float&nbsp;dx,
    float&nbsp;dy)</code>
<div class="block">Moves this polyline of (<code>dx</code>, <code>dy</code>) units.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#removePoint-int-">removePoint</a></span>(int&nbsp;index)</code>
<div class="block">Removes the point at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setCapStyle-com.eteks.sweethome3d.model.Polyline.CapStyle-">setCapStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle)</code>
<div class="block">Sets the cap style of this polyline.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setClosedPath-boolean-">setClosedPath</a></span>(boolean&nbsp;closedPath)</code>
<div class="block">Sets whether the first and last points of this polyline should be joined.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setColor-int-">setColor</a></span>(int&nbsp;color)</code>
<div class="block">Sets the color of this polyline.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setDashOffset-float-">setDashOffset</a></span>(float&nbsp;dashOffset)</code>
<div class="block">Sets the offset from which the dash of this polyline should start.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setDashPattern-float:A-">setDashPattern</a></span>(float[]&nbsp;dashPattern)</code>
<div class="block">Sets the dash pattern of this polyline in percentage of its thickness.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setDashStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-">setDashStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle)</code>
<div class="block">Sets the dash style of this polyline.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setElevation-float-">setElevation</a></span>(float&nbsp;elevation)</code>
<div class="block">Sets the elevation of this polyline in 3D.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setEndArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-">setEndArrowStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;endArrowStyle)</code>
<div class="block">Sets the arrow style at the end of this polyline.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setJoinStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-">setJoinStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle)</code>
<div class="block">Sets the join style of this polyline.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setLevel-com.eteks.sweethome3d.model.Level-">setLevel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Sets the level of this polyline.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setPoint-float-float-int-">setPoint</a></span>(float&nbsp;x,
        float&nbsp;y,
        int&nbsp;index)</code>
<div class="block">Sets the point at the given <code>index</code>.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setPoints-float:A:A-">setPoints</a></span>(float[][]&nbsp;points)</code>
<div class="block">Sets the points of the polygon matching this polyline.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setStartArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-">setStartArrowStyle</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;startArrowStyle)</code>
<div class="block">Sets the arrow style at the start of this polyline.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setThickness-float-">setThickness</a></span>(float&nbsp;thickness)</code>
<div class="block">Sets the thickness of this polyline.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/model/Polyline.html#setVisibleIn3D-boolean-">setVisibleIn3D</a></span>(boolean&nbsp;visibleIn3D)</code>
<div class="block">Sets whether this polyline should be displayed in 3D and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.model.HomeObject">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.model.<a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#addPropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#createId-java.lang.String-">createId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#duplicate--">duplicate</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#firePropertyChange-java.lang.String-java.lang.Object-java.lang.Object-">firePropertyChange</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getContentProperty-java.lang.String-">getContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getId--">getId</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getProperty-java.lang.String-">getProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#getPropertyNames--">getPropertyNames</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#isContentProperty-java.lang.String-">isContentProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#removePropertyChangeListener-java.lang.String-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.Object-">setProperty</a>, <a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#setProperty-java.lang.String-java.lang.String-">setProperty</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Polyline-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Polyline</h4>
<pre>public&nbsp;Polyline(float[][]&nbsp;points)</pre>
<div class="block">Creates a polyline from the given coordinates.</div>
</li>
</ul>
<a name="Polyline-java.lang.String-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Polyline</h4>
<pre>public&nbsp;Polyline(java.lang.String&nbsp;id,
                float[][]&nbsp;points)</pre>
<div class="block">Creates a polyline from the given coordinates.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
<a name="Polyline-float:A:A-float-com.eteks.sweethome3d.model.Polyline.CapStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Polyline</h4>
<pre>public&nbsp;Polyline(float[][]&nbsp;points,
                float&nbsp;thickness,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;startArrowStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;endArrowStyle,
                boolean&nbsp;closedPath,
                int&nbsp;color)</pre>
<div class="block">Creates a polyline from the given coordinates.</div>
</li>
</ul>
<a name="Polyline-float:A:A-float-com.eteks.sweethome3d.model.Polyline.CapStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-float-com.eteks.sweethome3d.model.Polyline.ArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-boolean-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Polyline</h4>
<pre>public&nbsp;Polyline(float[][]&nbsp;points,
                float&nbsp;thickness,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle,
                float&nbsp;dashOffset,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;startArrowStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;endArrowStyle,
                boolean&nbsp;closedPath,
                int&nbsp;color)</pre>
<div class="block">Creates a polyline from the given coordinates.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="Polyline-java.lang.String-float:A:A-float-com.eteks.sweethome3d.model.Polyline.CapStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-float-com.eteks.sweethome3d.model.Polyline.ArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-boolean-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Polyline</h4>
<pre>public&nbsp;Polyline(java.lang.String&nbsp;id,
                float[][]&nbsp;points,
                float&nbsp;thickness,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle,
                float&nbsp;dashOffset,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;startArrowStyle,
                <a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;endArrowStyle,
                boolean&nbsp;closedPath,
                int&nbsp;color)</pre>
<div class="block">Creates a polyline from the given coordinates.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.4</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getPoints--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPoints</h4>
<pre>public&nbsp;float[][]&nbsp;getPoints()</pre>
<div class="block">Returns the points of the polygon matching this polyline.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#getPoints--">getPoints</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array of the (x,y) coordinates of the polyline points.</dd>
</dl>
</li>
</ul>
<a name="getPointCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPointCount</h4>
<pre>public&nbsp;int&nbsp;getPointCount()</pre>
<div class="block">Returns the number of points of the polygon matching this polyline.</div>
</li>
</ul>
<a name="setPoints-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoints</h4>
<pre>public&nbsp;void&nbsp;setPoints(float[][]&nbsp;points)</pre>
<div class="block">Sets the points of the polygon matching this polyline. Once this polyline
 is updated, listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="addPoint-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPoint</h4>
<pre>public&nbsp;void&nbsp;addPoint(float&nbsp;x,
                     float&nbsp;y)</pre>
<div class="block">Adds a point at the end of polyline points.</div>
</li>
</ul>
<a name="addPoint-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPoint</h4>
<pre>public&nbsp;void&nbsp;addPoint(float&nbsp;x,
                     float&nbsp;y,
                     int&nbsp;index)</pre>
<div class="block">Adds a point at the given <code>index</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IndexOutOfBoundsException</code> - if <code>index</code> is negative or > <code>getPointCount()</code></dd>
</dl>
</li>
</ul>
<a name="setPoint-float-float-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPoint</h4>
<pre>public&nbsp;void&nbsp;setPoint(float&nbsp;x,
                     float&nbsp;y,
                     int&nbsp;index)</pre>
<div class="block">Sets the point at the given <code>index</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IndexOutOfBoundsException</code> - if <code>index</code> is negative or >= <code>getPointCount()</code></dd>
</dl>
</li>
</ul>
<a name="removePoint-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePoint</h4>
<pre>public&nbsp;void&nbsp;removePoint(int&nbsp;index)</pre>
<div class="block">Removes the point at the given <code>index</code>.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IndexOutOfBoundsException</code> - if <code>index</code> is negative or >= <code>getPointCount()</code></dd>
</dl>
</li>
</ul>
<a name="getThickness--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getThickness</h4>
<pre>public&nbsp;float&nbsp;getThickness()</pre>
<div class="block">Returns the thickness of this polyline.</div>
</li>
</ul>
<a name="setThickness-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setThickness</h4>
<pre>public&nbsp;void&nbsp;setThickness(float&nbsp;thickness)</pre>
<div class="block">Sets the thickness of this polyline.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="getCapStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCapStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;getCapStyle()</pre>
<div class="block">Returns the cap style of this polyline.</div>
</li>
</ul>
<a name="setCapStyle-com.eteks.sweethome3d.model.Polyline.CapStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCapStyle</h4>
<pre>public&nbsp;void&nbsp;setCapStyle(<a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle)</pre>
<div class="block">Sets the cap style of this polyline.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="getJoinStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getJoinStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;getJoinStyle()</pre>
<div class="block">Returns the join style of this polyline.</div>
</li>
</ul>
<a name="setJoinStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setJoinStyle</h4>
<pre>public&nbsp;void&nbsp;setJoinStyle(<a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle)</pre>
<div class="block">Sets the join style of this polyline.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="getDashStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDashStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;getDashStyle()</pre>
<div class="block">Returns the dash style of this polyline. If <code>DashStyle.CUSTOMIZED</code> is returned,
 the actual dash pattern will be returned by <a href="../../../../com/eteks/sweethome3d/model/Polyline.html#getDashPattern--"><code>getDashPattern()</code></a>.</div>
</li>
</ul>
<a name="setDashStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDashStyle</h4>
<pre>public&nbsp;void&nbsp;setDashStyle(<a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle)</pre>
<div class="block">Sets the dash style of this polyline.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="getDashPattern--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDashPattern</h4>
<pre>public&nbsp;float[]&nbsp;getDashPattern()</pre>
<div class="block">Returns the dash pattern of this polyline in percentage of its thickness.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setDashPattern-float:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDashPattern</h4>
<pre>public&nbsp;void&nbsp;setDashPattern(float[]&nbsp;dashPattern)</pre>
<div class="block">Sets the dash pattern of this polyline in percentage of its thickness.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getDashOffset--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDashOffset</h4>
<pre>public&nbsp;float&nbsp;getDashOffset()</pre>
<div class="block">Returns the offset from which the dash of this polyline should start.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the offset in percentage of the dash pattern</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setDashOffset-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDashOffset</h4>
<pre>public&nbsp;void&nbsp;setDashOffset(float&nbsp;dashOffset)</pre>
<div class="block">Sets the offset from which the dash of this polyline should start.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dashOffset</code> - the offset in percentage of the dash pattern</dd>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getStartArrowStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartArrowStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;getStartArrowStyle()</pre>
<div class="block">Returns the arrow style at the start of this polyline.</div>
</li>
</ul>
<a name="setStartArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartArrowStyle</h4>
<pre>public&nbsp;void&nbsp;setStartArrowStyle(<a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;startArrowStyle)</pre>
<div class="block">Sets the arrow style at the start of this polyline.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="getEndArrowStyle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndArrowStyle</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;getEndArrowStyle()</pre>
<div class="block">Returns the arrow style at the end of this polyline.</div>
</li>
</ul>
<a name="setEndArrowStyle-com.eteks.sweethome3d.model.Polyline.ArrowStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEndArrowStyle</h4>
<pre>public&nbsp;void&nbsp;setEndArrowStyle(<a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.ArrowStyle</a>&nbsp;endArrowStyle)</pre>
<div class="block">Sets the arrow style at the end of this polyline.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="isClosedPath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isClosedPath</h4>
<pre>public&nbsp;boolean&nbsp;isClosedPath()</pre>
<div class="block">Returns <code>true</code> if the first and last points of this polyline should be joined to form a polygon.</div>
</li>
</ul>
<a name="setClosedPath-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setClosedPath</h4>
<pre>public&nbsp;void&nbsp;setClosedPath(boolean&nbsp;closedPath)</pre>
<div class="block">Sets whether the first and last points of this polyline should be joined.
 Once this polyline is updated, listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;int&nbsp;getColor()</pre>
<div class="block">Returns the color of this polyline.</div>
</li>
</ul>
<a name="setColor-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(int&nbsp;color)</pre>
<div class="block">Sets the color of this polyline. Once this polyline is updated,
 listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="getGroundElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGroundElevation</h4>
<pre>public&nbsp;float&nbsp;getGroundElevation()</pre>
<div class="block">Returns the elevation of this polyline
 from the ground according to the elevation of its level.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>public&nbsp;float&nbsp;getElevation()</pre>
<div class="block">Returns the elevation of this polyline in 3D.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setElevation-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevation</h4>
<pre>public&nbsp;void&nbsp;setElevation(float&nbsp;elevation)</pre>
<div class="block">Sets the elevation of this polyline in 3D. Once this polyline is updated,
 listeners added to this polyline will receive a change notification.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="isVisibleIn3D--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isVisibleIn3D</h4>
<pre>public&nbsp;boolean&nbsp;isVisibleIn3D()</pre>
<div class="block">Returns <code>true</code> if this polyline should be displayed in 3D.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="setVisibleIn3D-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setVisibleIn3D</h4>
<pre>public&nbsp;void&nbsp;setVisibleIn3D(boolean&nbsp;visibleIn3D)</pre>
<div class="block">Sets whether this polyline should be displayed in 3D and fires a <code>PropertyChangeEvent</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>6.0</dd>
</dl>
</li>
</ul>
<a name="getLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLevel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;getLevel()</pre>
<div class="block">Returns the level which this polyline belongs to.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#getLevel--">getLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
</dl>
</li>
</ul>
<a name="setLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLevel</h4>
<pre>public&nbsp;void&nbsp;setLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Sets the level of this polyline. Once this polyline is updated,
 listeners added to this polyline will receive a change notification.</div>
</li>
</ul>
<a name="isAtLevel-com.eteks.sweethome3d.model.Level-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAtLevel</h4>
<pre>public&nbsp;boolean&nbsp;isAtLevel(<a href="../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</pre>
<div class="block">Returns <code>true</code> if this polyline is at the given <code>level</code>
 or at a level with the same elevation and a smaller elevation index.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html#isAtLevel-com.eteks.sweethome3d.model.Level-">isAtLevel</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Elevatable.html" title="interface in com.eteks.sweethome3d.model">Elevatable</a></code></dd>
</dl>
</li>
</ul>
<a name="getLength--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLength</h4>
<pre>public&nbsp;float&nbsp;getLength()</pre>
<div class="block">Returns an approximate length of this polyline.</div>
</li>
</ul>
<a name="intersectsRectangle-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>intersectsRectangle</h4>
<pre>public&nbsp;boolean&nbsp;intersectsRectangle(float&nbsp;x0,
                                   float&nbsp;y0,
                                   float&nbsp;x1,
                                   float&nbsp;y1)</pre>
<div class="block">Returns <code>true</code> if this polyline intersects
 with the horizontal rectangle which opposite corners are at points
 (<code>x0</code>, <code>y0</code>) and (<code>x1</code>, <code>y1</code>).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#intersectsRectangle-float-float-float-float-">intersectsRectangle</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="containsPoint-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsPoint</h4>
<pre>public&nbsp;boolean&nbsp;containsPoint(float&nbsp;x,
                             float&nbsp;y,
                             float&nbsp;margin)</pre>
<div class="block">Returns <code>true</code> if this polyline contains
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#containsPoint-float-float-float-">containsPoint</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="getPointIndexAt-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPointIndexAt</h4>
<pre>public&nbsp;int&nbsp;getPointIndexAt(float&nbsp;x,
                           float&nbsp;y,
                           float&nbsp;margin)</pre>
<div class="block">Returns the index of the point of this polyline equal to
 the point at (<code>x</code>, <code>y</code>) with a given <code>margin</code>.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the index of the first found point or -1.</dd>
</dl>
</li>
</ul>
<a name="move-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>move</h4>
<pre>public&nbsp;void&nbsp;move(float&nbsp;dx,
                 float&nbsp;dy)</pre>
<div class="block">Moves this polyline of (<code>dx</code>, <code>dy</code>) units.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#move-float-float-">move</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
</dl>
</li>
</ul>
<a name="clone--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clone</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;clone()</pre>
<div class="block">Returns a clone of this polyline.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html#clone--">clone</a></code>&nbsp;in interface&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html#clone--">clone</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/model/HomeObject.html" title="class in com.eteks.sweethome3d.model">HomeObject</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Polyline.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/model/PieceOfFurniture.html" title="interface in com.eteks.sweethome3d.model"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/model/Polyline.ArrowStyle.html" title="enum in com.eteks.sweethome3d.model"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/model/Polyline.html" target="_top">Frames</a></li>
<li><a href="Polyline.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
