<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.io.XMLWriter (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.io.XMLWriter (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/io/class-use/XMLWriter.html" target="_top">Frames</a></li>
<li><a href="XMLWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.io.XMLWriter" class="title">Uses of Class<br>com.eteks.sweethome3d.io.XMLWriter</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.io">com.eteks.sweethome3d.io</a></td>
<td class="colLast">
<div class="block">Implements how to read and write 
<a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">homes</a> and 
<a href="../../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">user preferences</a> created in 
<a href="../../../../../com/eteks/sweethome3d/model/package-summary.html">model</a> classes of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.io">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a> in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/io/package-summary.html">com.eteks.sweethome3d.io</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">writeAttributes</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
               <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Writes as XML attributes some data of <code>home</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.PieceOfFurnitureExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePieceOfFurniture-">writeAttributes</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
               <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">ObjectXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-T-">writeAttributes</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
               <a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)</code>
<div class="block">Writes the attributes of the object in parameter.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeBackgroundImage-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.BackgroundImage-">writeBackgroundImage</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                    <a href="../../../../../com/eteks/sweethome3d/model/BackgroundImage.html" title="class in com.eteks.sweethome3d.model">BackgroundImage</a>&nbsp;backgroundImage)</code>
<div class="block">Writes in XML the <code>background</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeBaseboard-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Baseboard-java.lang.String-">writeBaseboard</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
              <a href="../../../../../com/eteks/sweethome3d/model/Baseboard.html" title="class in com.eteks.sweethome3d.model">Baseboard</a>&nbsp;baseboard,
              java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>baseboard</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeCamera-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Camera-java.lang.String-">writeCamera</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
           <a href="../../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&nbsp;camera,
           java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>camera</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">writeChildren</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Writes as XML elements some objects that depends on of <code>home</code> with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.PieceOfFurnitureExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.PieceOfFurnitureExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePieceOfFurniture-">writeChildren</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">ObjectXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-T-">writeChildren</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)</code>
<div class="block">Writes the children of the object in parameter.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeCompass-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Compass-">writeCompass</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../../com/eteks/sweethome3d/model/Compass.html" title="class in com.eteks.sweethome3d.model">Compass</a>&nbsp;compass)</code>
<div class="block">Writes in XML the <code>compass</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeDimensionLine-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.DimensionLine-">writeDimensionLine</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                  <a href="../../../../../com/eteks/sweethome3d/model/DimensionLine.html" title="class in com.eteks.sweethome3d.model">DimensionLine</a>&nbsp;dimensionLine)</code>
<div class="block">Writes in XML the <code>dimensionLine</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeElement-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Home-">writeElement</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home)</code>
<div class="block">Writes in XML the <code>home</code> object and the objects that depends on it with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ObjectXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeElement-com.eteks.sweethome3d.io.XMLWriter-T-">writeElement</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html" title="type parameter in ObjectXMLExporter">T</a>&nbsp;object)</code>
<div class="block">Writes in XML the given <code>object</code> in the element returned by the
 <a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#getTag-T-"><code>getTag</code></a>, then writes its attributes and children
 calling <a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeAttributes-com.eteks.sweethome3d.io.XMLWriter-T-"><code>writeAttributes</code></a>
 and <a href="../../../../../com/eteks/sweethome3d/io/ObjectXMLExporter.html#writeChildren-com.eteks.sweethome3d.io.XMLWriter-T-"><code>writeChildren</code></a> methods.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeEnvironment-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeEnvironment-">writeEnvironment</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                <a href="../../../../../com/eteks/sweethome3d/model/HomeEnvironment.html" title="class in com.eteks.sweethome3d.model">HomeEnvironment</a>&nbsp;environment)</code>
<div class="block">Writes in XML the <code>environment</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeLabel-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Label-">writeLabel</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
          <a href="../../../../../com/eteks/sweethome3d/model/Label.html" title="class in com.eteks.sweethome3d.model">Label</a>&nbsp;label)</code>
<div class="block">Writes in XML the <code>label</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeLevel-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Level-">writeLevel</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
          <a href="../../../../../com/eteks/sweethome3d/model/Level.html" title="class in com.eteks.sweethome3d.model">Level</a>&nbsp;level)</code>
<div class="block">Writes in XML the <code>level</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeMaterial-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeMaterial-com.eteks.sweethome3d.model.Content-">writeMaterial</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../../com/eteks/sweethome3d/model/HomeMaterial.html" title="class in com.eteks.sweethome3d.model">HomeMaterial</a>&nbsp;material,
             <a href="../../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Writes in XML the <code>material</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writePieceOfFurniture-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePieceOfFurniture-">writePieceOfFurniture</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
                     <a href="../../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Writes in XML the <code>piece</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writePolyline-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Polyline-">writePolyline</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
             <a href="../../../../../com/eteks/sweethome3d/model/Polyline.html" title="class in com.eteks.sweethome3d.model">Polyline</a>&nbsp;polyline)</code>
<div class="block">Writes in XML the <code>polyline</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writePrint-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomePrint-">writePrint</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
          <a href="../../../../../com/eteks/sweethome3d/model/HomePrint.html" title="class in com.eteks.sweethome3d.model">HomePrint</a>&nbsp;print)</code>
<div class="block">Writes in XML the <code>print</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeRoom-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Room-">writeRoom</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
         <a href="../../../../../com/eteks/sweethome3d/model/Room.html" title="class in com.eteks.sweethome3d.model">Room</a>&nbsp;room)</code>
<div class="block">Writes in XML the <code>room</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeTextStyle-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.TextStyle-java.lang.String-">writeTextStyle</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
              <a href="../../../../../com/eteks/sweethome3d/model/TextStyle.html" title="class in com.eteks.sweethome3d.model">TextStyle</a>&nbsp;textStyle,
              java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>textStyle</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeTexture-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.HomeTexture-java.lang.String-">writeTexture</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
            <a href="../../../../../com/eteks/sweethome3d/model/HomeTexture.html" title="class in com.eteks.sweethome3d.model">HomeTexture</a>&nbsp;texture,
            java.lang.String&nbsp;attributeName)</code>
<div class="block">Writes in XML the <code>texture</code> object with the given <code>writer</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeXMLExporter.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/io/HomeXMLExporter.html#writeWall-com.eteks.sweethome3d.io.XMLWriter-com.eteks.sweethome3d.model.Wall-">writeWall</a></span>(<a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">XMLWriter</a>&nbsp;writer,
         <a href="../../../../../com/eteks/sweethome3d/model/Wall.html" title="class in com.eteks.sweethome3d.model">Wall</a>&nbsp;wall)</code>
<div class="block">Writes in XML the <code>wall</code> object with the given <code>writer</code>.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/io/XMLWriter.html" title="class in com.eteks.sweethome3d.io">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/io/class-use/XMLWriter.html" target="_top">Frames</a></li>
<li><a href="XMLWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
