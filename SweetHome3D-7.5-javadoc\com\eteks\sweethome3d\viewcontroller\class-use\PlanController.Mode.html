<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:52 CEST 2024 -->
<title>Uses of Class com.eteks.sweethome3d.viewcontroller.PlanController.Mode (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class com.eteks.sweethome3d.viewcontroller.PlanController.Mode (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/viewcontroller/class-use/PlanController.Mode.html" target="_top">Frames</a></li>
<li><a href="PlanController.Mode.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class com.eteks.sweethome3d.viewcontroller.PlanController.Mode" class="title">Uses of Class<br>com.eteks.sweethome3d.viewcontroller.PlanController.Mode</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller</a></td>
<td class="colLast">
<div class="block">Describes controller classes and view interfaces of Sweet Home 3D.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="com.eteks.sweethome3d.viewcontroller">
<!--   -->
</a>
<h3>Uses of <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a> in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> declared as <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.Mode.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html#DIMENSION_LINE_CREATION">DIMENSION_LINE_CREATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.Mode.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html#LABEL_CREATION">LABEL_CREATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.Mode.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html#PANNING">PANNING</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.Mode.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html#POLYLINE_CREATION">POLYLINE_CREATION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.Mode.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html#ROOM_CREATION">ROOM_CREATION</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.Mode.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html#SELECTION">SELECTION</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.Mode.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html#WALL_CREATION">WALL_CREATION</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> that return <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#getMode--">getMode</a></span>()</code>
<div class="block">Returns the active mode of this controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>abstract <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.ControllerState.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#getMode--">getMode</a></span>()</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.ControllerStateDecorator.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#getMode--">getMode</a></span>()</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../../com/eteks/sweethome3d/viewcontroller/package-summary.html">com.eteks.sweethome3d.viewcontroller</a> with parameters of type <a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">HomeController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/HomeController.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</code>
<div class="block">Displays a tip message dialog depending on the given mode and
 sets the active mode of the plan controller.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</code>
<div class="block">Sets the active mode of this controller and fires a <code>PropertyChangeEvent</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.ControllerState.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerState.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">PlanController.ControllerStateDecorator.</span><code><span class="memberNameLink"><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.ControllerStateDecorator.html#setMode-com.eteks.sweethome3d.viewcontroller.PlanController.Mode-">setMode</a></span>(<a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">PlanController.Mode</a>&nbsp;mode)</code>&nbsp;</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../../com/eteks/sweethome3d/viewcontroller/PlanController.Mode.html" title="class in com.eteks.sweethome3d.viewcontroller">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/eteks/sweethome3d/viewcontroller/class-use/PlanController.Mode.html" target="_top">Frames</a></li>
<li><a href="PlanController.Mode.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
