# ApplicationPlugin_zh_TW.properties
# Sweet Home 3D AI Plugin Configuration - Chinese Traditional
# Copyright (c) 2025 Samuel Kpassegna

# Plugin identification
name=AI平面圖分析
description=使用人工智慧分析平面圖，提供洞察和改進建議
provider=Samuel Kpassegna

# Features
features=AI分析，平面圖洞察，多AI提供商，隱私控制

# Requirements
requirements=雲端AI提供商需要網路連接（本地提供商可選）

# UI Strings for internationalization
# Action properties
AIAction.Name=AI分析
AIAction.ShortDescription=用AI分析平面圖
AIAction.Menu=工具

# Dialog titles
AIChatDialog.title=AI平面圖分析
AISettingsDialog.title=AI設定

# Button labels
button.send=發送
button.newAnalysis=新分析
button.settings=設定
button.testConnection=測試連接
button.save=儲存
button.cancel=取消

# Labels
label.provider=提供商:
label.baseUrl=基礎URL:
label.apiKey=API金鑰:
label.model=模型:
label.temperature=溫度:
label.maxTokens=最大令牌:
label.status=狀態:

# Messages
message.analyzing=正在分析平面圖...
message.processingQuestion=正在處理問題...
message.testingConnection=正在測試連接...
message.connectionSuccessful=連接成功！
message.connectionFailed=連接失敗: {0}
message.configurationSaved=配置儲存成功
message.validationError=配置錯誤:\n{0}
message.noConfiguration=AI提供商未配置。請先配置設定。

# Analysis prompt
analysis.prompt=請分析此平面圖並提供全面的洞察，包括:\n1. 佈局效率和空間利用\n2. 交通流線和循環模式\n3. 自然採光和通風機會\n4. 無障礙考慮\n5. 空間間的功能關係\n6. 改進建議\n7. 符合常見建築標準\n8. 能效考慮\n\n請提供具體、可操作的建議，以提高此空間的功能性、舒適性和美觀性。

# Error messages
error.analysisError=分析錯誤: {0}
error.configurationError=配置錯誤: {0}
error.connectionError=連接錯誤: {0}
error.invalidConfiguration=無效配置
error.missingApiKey=需要API金鑰
error.missingBaseUrl=需要基礎URL
error.missingModel=需要選擇模型

# Provider names
provider.openai=OpenAI
provider.anthropic=Anthropic
provider.google=Google AI
provider.xai=xAI
provider.together=Together AI
provider.fireworks=Fireworks AI
provider.openrouter=OpenRouter
provider.groq=Groq
provider.deepinfra=DeepInfra
provider.ollama=Ollama (本地)
provider.lmstudio=LM Studio (本地)
provider.anythingllm=AnythingLLM (本地)
provider.jan=Jan (本地)
provider.custom=自訂
