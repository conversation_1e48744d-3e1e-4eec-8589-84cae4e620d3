<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>View (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="View (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/View.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/View.html" target="_top">Frames</a></li>
<li><a href="View.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Interface View" class="title">Interface View</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Subinterfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/DialogView.html" title="interface in com.eteks.sweethome3d.viewcontroller">DialogView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/ExportableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ExportableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureView.html" title="interface in com.eteks.sweethome3d.viewcontroller">FurnitureView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HelpView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HelpView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html" title="interface in com.eteks.sweethome3d.viewcontroller">HomeView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/PlanView.html" title="interface in com.eteks.sweethome3d.viewcontroller">PlanView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TextureChoiceView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TextureChoiceView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/ThreadedTaskView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ThreadedTaskView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/TransferableView.html" title="interface in com.eteks.sweethome3d.viewcontroller">TransferableView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/View3D.html" title="interface in com.eteks.sweethome3d.viewcontroller">View3D</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/swing/BackgroundImageWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">BackgroundImageWizardStepsPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/BaseboardChoiceComponent.html" title="class in com.eteks.sweethome3d.swing">BaseboardChoiceComponent</a>, <a href="../../../../com/eteks/sweethome3d/swing/CompassPanel.html" title="class in com.eteks.sweethome3d.swing">CompassPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/DimensionLinePanel.html" title="class in com.eteks.sweethome3d.swing">DimensionLinePanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogListPanel.html" title="class in com.eteks.sweethome3d.swing">FurnitureCatalogListPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/FurnitureCatalogTree.html" title="class in com.eteks.sweethome3d.swing">FurnitureCatalogTree</a>, <a href="../../../../com/eteks/sweethome3d/swing/FurnitureTable.html" title="class in com.eteks.sweethome3d.swing">FurnitureTable</a>, <a href="../../../../com/eteks/sweethome3d/swing/FurnitureTablePanel.html" title="class in com.eteks.sweethome3d.swing">FurnitureTablePanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/HelpPane.html" title="class in com.eteks.sweethome3d.swing">HelpPane</a>, <a href="../../../../com/eteks/sweethome3d/swing/Home3DAttributesPanel.html" title="class in com.eteks.sweethome3d.swing">Home3DAttributesPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/HomeComponent3D.html" title="class in com.eteks.sweethome3d.swing">HomeComponent3D</a>, <a href="../../../../com/eteks/sweethome3d/HomeFramePane.html" title="class in com.eteks.sweethome3d">HomeFramePane</a>, <a href="../../../../com/eteks/sweethome3d/swing/HomeFurniturePanel.html" title="class in com.eteks.sweethome3d.swing">HomeFurniturePanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/HomePane.html" title="class in com.eteks.sweethome3d.swing">HomePane</a>, <a href="../../../../com/eteks/sweethome3d/swing/ImportedFurnitureWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">ImportedFurnitureWizardStepsPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/ImportedTextureWizardStepsPanel.html" title="class in com.eteks.sweethome3d.swing">ImportedTextureWizardStepsPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/LabelPanel.html" title="class in com.eteks.sweethome3d.swing">LabelPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/LevelPanel.html" title="class in com.eteks.sweethome3d.swing">LevelPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/ModelMaterialsComponent.html" title="class in com.eteks.sweethome3d.swing">ModelMaterialsComponent</a>, <a href="../../../../com/eteks/sweethome3d/swing/MultipleLevelsPlanPanel.html" title="class in com.eteks.sweethome3d.swing">MultipleLevelsPlanPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/ObserverCameraPanel.html" title="class in com.eteks.sweethome3d.swing">ObserverCameraPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/PageSetupPanel.html" title="class in com.eteks.sweethome3d.swing">PageSetupPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/PhotoPanel.html" title="class in com.eteks.sweethome3d.swing">PhotoPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/PhotosPanel.html" title="class in com.eteks.sweethome3d.swing">PhotosPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.html" title="class in com.eteks.sweethome3d.swing">PlanComponent</a>, <a href="../../../../com/eteks/sweethome3d/swing/PlanComponent.PlanRulerComponent.html" title="class in com.eteks.sweethome3d.swing">PlanComponent.PlanRulerComponent</a>, <a href="../../../../com/eteks/sweethome3d/swing/PolylinePanel.html" title="class in com.eteks.sweethome3d.swing">PolylinePanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/PrintPreviewPanel.html" title="class in com.eteks.sweethome3d.swing">PrintPreviewPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/RoomPanel.html" title="class in com.eteks.sweethome3d.swing">RoomPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/TextureChoiceComponent.html" title="class in com.eteks.sweethome3d.swing">TextureChoiceComponent</a>, <a href="../../../../com/eteks/sweethome3d/swing/ThreadedTaskPanel.html" title="class in com.eteks.sweethome3d.swing">ThreadedTaskPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/UserPreferencesPanel.html" title="class in com.eteks.sweethome3d.swing">UserPreferencesPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/VideoPanel.html" title="class in com.eteks.sweethome3d.swing">VideoPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/WallPanel.html" title="class in com.eteks.sweethome3d.swing">WallPanel</a>, <a href="../../../../com/eteks/sweethome3d/swing/WizardPane.html" title="class in com.eteks.sweethome3d.swing">WizardPane</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">View</span></pre>
<div class="block">An MVC view created and controlled by a controller.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Interface and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></span></code>
<div class="block">The pointer types that the user may use to interact with the plan</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/View.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/VideoController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/View.html" target="_top">Frames</a></li>
<li><a href="View.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
