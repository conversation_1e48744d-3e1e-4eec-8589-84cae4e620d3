<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:49 CEST 2024 -->
<title>LocatedTransferHandler (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LocatedTransferHand<PERSON> (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":6,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LocatedTransferHandler.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/LevelPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ModelMaterialsComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/LocatedTransferHandler.html" target="_top">Frames</a></li>
<li><a href="LocatedTransferHandler.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.TransferHandler">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.TransferHandler">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class LocatedTransferHandler" class="title">Class LocatedTransferHandler</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>javax.swing.TransferHandler</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.LocatedTransferHandler</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/swing/Component3DTransferHandler.html" title="class in com.eteks.sweethome3d.swing">Component3DTransferHandler</a>, <a href="../../../../com/eteks/sweethome3d/swing/FurnitureTransferHandler.html" title="class in com.eteks.sweethome3d.swing">FurnitureTransferHandler</a>, <a href="../../../../com/eteks/sweethome3d/swing/PlanTransferHandler.html" title="class in com.eteks.sweethome3d.swing">PlanTransferHandler</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">LocatedTransferHandler</span>
extends javax.swing.TransferHandler</pre>
<div class="block">Transfer handler that stores the dropped location of mouse pointer.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.LocatedTransferHandler">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.javax.swing.TransferHandler">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;javax.swing.TransferHandler</h3>
<code>javax.swing.TransferHandler.DropLocation, javax.swing.TransferHandler.TransferSupport</code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.TransferHandler">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.TransferHandler</h3>
<code>COPY, COPY_OR_MOVE, LINK, MOVE, NONE</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#LocatedTransferHandler--">LocatedTransferHandler</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#canImport-javax.swing.JComponent-java.awt.datatransfer.DataFlavor:A-">canImport</a></span>(javax.swing.JComponent&nbsp;destination,
         java.awt.datatransfer.DataFlavor[]&nbsp;flavors)</code>
<div class="block">Adds a listener to the drop target of <code>destination</code> that
 will keep track of the current drop location and returns the value returned
 by <a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#canImportFlavor-java.awt.datatransfer.DataFlavor:A-"><code>canImportFlavor</code></a> method.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected abstract boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#canImportFlavor-java.awt.datatransfer.DataFlavor:A-">canImportFlavor</a></span>(java.awt.datatransfer.DataFlavor[]&nbsp;flavors)</code>
<div class="block">Returns <code>true</code> if <code>flavors</code> contains a flavor that 
 can be imported by this transfer handler.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#dragEntered-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">dragEntered</a></span>(javax.swing.JComponent&nbsp;destination,
           java.awt.datatransfer.Transferable&nbsp;transferable,
           int&nbsp;dragAction)</code>
<div class="block">Called once <code>transferable</code> data entered in <code>destination</code> component
 during a drag and drop operation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#dragExited-javax.swing.JComponent-">dragExited</a></span>(javax.swing.JComponent&nbsp;destination)</code>
<div class="block">Called once the cursor left <code>destination</code> component during a drag and drop operation.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#dragMoved-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">dragMoved</a></span>(javax.swing.JComponent&nbsp;destination,
         java.awt.datatransfer.Transferable&nbsp;transferable,
         int&nbsp;dragAction)</code>
<div class="block">Called when <code>transferable</code> data moved in <code>destination</code> component
 during a drag and drop operation.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>protected java.awt.Point</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#getDropLocation--">getDropLocation</a></span>()</code>
<div class="block">Returns the location where mouse pointer was dropped in screen coordinates.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>protected java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#getModelContents-java.util.List-com.eteks.sweethome3d.viewcontroller.ContentManager-">getModelContents</a></span>(java.util.List&lt;java.io.File&gt;&nbsp;files,
                <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Returns the model contents among files.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#isDrop--">isDrop</a></span>()</code>
<div class="block">Returns <code>true</code> if current operation is a drag and drop.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.TransferHandler">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.TransferHandler</h3>
<code>canImport, createTransferable, exportAsDrag, exportDone, exportToClipboard, getCopyAction, getCutAction, getDragImage, getDragImageOffset, getPasteAction, getSourceActions, getVisualRepresentation, importData, importData, setDragImage, setDragImageOffset</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="LocatedTransferHandler--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LocatedTransferHandler</h4>
<pre>public&nbsp;LocatedTransferHandler()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="canImport-javax.swing.JComponent-java.awt.datatransfer.DataFlavor:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>canImport</h4>
<pre>public final&nbsp;boolean&nbsp;canImport(javax.swing.JComponent&nbsp;destination,
                               java.awt.datatransfer.DataFlavor[]&nbsp;flavors)</pre>
<div class="block">Adds a listener to the drop target of <code>destination</code> that
 will keep track of the current drop location and returns the value returned
 by <a href="../../../../com/eteks/sweethome3d/swing/LocatedTransferHandler.html#canImportFlavor-java.awt.datatransfer.DataFlavor:A-"><code>canImportFlavor</code></a> method.
 Once drop is finished the listener will be removed.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>canImport</code>&nbsp;in class&nbsp;<code>javax.swing.TransferHandler</code></dd>
</dl>
</li>
</ul>
<a name="dragEntered-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dragEntered</h4>
<pre>protected&nbsp;void&nbsp;dragEntered(javax.swing.JComponent&nbsp;destination,
                           java.awt.datatransfer.Transferable&nbsp;transferable,
                           int&nbsp;dragAction)</pre>
<div class="block">Called once <code>transferable</code> data entered in <code>destination</code> component
 during a drag and drop operation. Subclasses should override this method if they are
 interested by this event.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dragAction</code> - the current drag action (<code>TransferHandler.COPY</code>, <code>TransferHandler.MOVE</code>
    or <code>TransferHandler.LINK</code>)</dd>
</dl>
</li>
</ul>
<a name="dragMoved-javax.swing.JComponent-java.awt.datatransfer.Transferable-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dragMoved</h4>
<pre>protected&nbsp;void&nbsp;dragMoved(javax.swing.JComponent&nbsp;destination,
                         java.awt.datatransfer.Transferable&nbsp;transferable,
                         int&nbsp;dragAction)</pre>
<div class="block">Called when <code>transferable</code> data moved in <code>destination</code> component
 during a drag and drop operation. Subclasses should override this method if they are
 interested by this event.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dragAction</code> - the current drag action (<code>TransferHandler.COPY</code>, <code>TransferHandler.MOVE</code>
    or <code>TransferHandler.LINK</code>)</dd>
</dl>
</li>
</ul>
<a name="dragExited-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>dragExited</h4>
<pre>protected&nbsp;void&nbsp;dragExited(javax.swing.JComponent&nbsp;destination)</pre>
<div class="block">Called once the cursor left <code>destination</code> component during a drag and drop operation. 
 Subclasses should override this method if they are interested by this event.</div>
</li>
</ul>
<a name="canImportFlavor-java.awt.datatransfer.DataFlavor:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>canImportFlavor</h4>
<pre>protected abstract&nbsp;boolean&nbsp;canImportFlavor(java.awt.datatransfer.DataFlavor[]&nbsp;flavors)</pre>
<div class="block">Returns <code>true</code> if <code>flavors</code> contains a flavor that 
 can be imported by this transfer handler. Subclasses should override this
 method to return which flavors it supports.</div>
</li>
</ul>
<a name="getDropLocation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDropLocation</h4>
<pre>protected&nbsp;java.awt.Point&nbsp;getDropLocation()</pre>
<div class="block">Returns the location where mouse pointer was dropped in screen coordinates.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.lang.IllegalStateException</code> - if current operation isn't a drag and drop.</dd>
</dl>
</li>
</ul>
<a name="isDrop--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDrop</h4>
<pre>protected&nbsp;boolean&nbsp;isDrop()</pre>
<div class="block">Returns <code>true</code> if current operation is a drag and drop.</div>
</li>
</ul>
<a name="getModelContents-java.util.List-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getModelContents</h4>
<pre>protected&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getModelContents(java.util.List&lt;java.io.File&gt;&nbsp;files,
                                                            <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
<div class="block">Returns the model contents among files.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LocatedTransferHandler.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/LevelPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ModelMaterialsComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/LocatedTransferHandler.html" target="_top">Frames</a></li>
<li><a href="LocatedTransferHandler.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.javax.swing.TransferHandler">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.TransferHandler">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
