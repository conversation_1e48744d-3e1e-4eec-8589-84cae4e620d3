<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:45 CEST 2024 -->
<title>OBJWriter (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="OBJWriter (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":9,"i8":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/OBJWriter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/PhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/OBJWriter.html" target="_top">Frames</a></li>
<li><a href="OBJWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.FilterWriter">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.j3d</div>
<h2 title="Class OBJWriter" class="title">Class OBJWriter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>java.io.Writer</li>
<li>
<ul class="inheritance">
<li>java.io.FilterWriter</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.j3d.OBJWriter</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Closeable, java.io.Flushable, java.lang.Appendable, java.lang.AutoCloseable</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">OBJWriter</span>
extends java.io.FilterWriter</pre>
<div class="block">An output stream that writes Java 3D nodes at OBJ + MTL format.
 <p>Once you wrote nodes, call <code>close</code> method to create the MTL file
 and the texture images in the same folder as OBJ file. This feature applies
 only to constructor that takes a file as parameter.<br>
 Note: this class is compatible with Java 3D 1.3.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.io.FilterWriter">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.io.FilterWriter</h3>
<code>out</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.io.Writer">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.io.Writer</h3>
<code>lock</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#OBJWriter-java.io.File-">OBJWriter</a></span>(java.io.File&nbsp;objFile)</code>
<div class="block">Create an OBJ writer for the given file, with no header and default precision.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#OBJWriter-java.io.File-java.lang.String-int-">OBJWriter</a></span>(java.io.File&nbsp;objFile,
         java.lang.String&nbsp;header,
         int&nbsp;maximumFractionDigits)</code>
<div class="block">Create an OBJ writer for the given file.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#OBJWriter-java.io.OutputStream-">OBJWriter</a></span>(java.io.OutputStream&nbsp;out)</code>
<div class="block">Create an OBJ writer that will writes in <code>out</code> stream,
 with no header and default precision.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#OBJWriter-java.io.OutputStream-java.lang.String-int-">OBJWriter</a></span>(java.io.OutputStream&nbsp;out,
         java.lang.String&nbsp;header,
         int&nbsp;maximumFractionDigits)</code>
<div class="block">Create an OBJ writer that will writes in <code>out</code> stream.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#OBJWriter-java.lang.String-">OBJWriter</a></span>(java.lang.String&nbsp;objFileName)</code>
<div class="block">Create an OBJ writer for the given file name, with no header and default precision.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#OBJWriter-java.lang.String-java.lang.String-int-">OBJWriter</a></span>(java.lang.String&nbsp;objFileName,
         java.lang.String&nbsp;header,
         int&nbsp;maximumFractionDigits)</code>
<div class="block">Create an OBJ writer for the given file name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#OBJWriter-java.io.Writer-">OBJWriter</a></span>(java.io.Writer&nbsp;out)</code>
<div class="block">Create an OBJ writer that will writes in <code>out</code> stream,
 with no header and default precision.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#OBJWriter-java.io.Writer-java.lang.String-int-">OBJWriter</a></span>(java.io.Writer&nbsp;out,
         java.lang.String&nbsp;header,
         int&nbsp;maximumFractionDigits)</code>
<div class="block">Create an OBJ writer that will writes in <code>out</code> stream.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#close--">close</a></span>()</code>
<div class="block">Closes this writer and writes MTL file and its texture images,
 if this writer was created from a file.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#write-char:A-int-int-">write</a></span>(char[]&nbsp;cbuf,
     int&nbsp;off,
     int&nbsp;len)</code>
<div class="block">Write a portion of an array of characters in a comment at OBJ format.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#write-int-">write</a></span>(int&nbsp;c)</code>
<div class="block">Write a single character in a comment at OBJ format.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#write-java.lang.String-">write</a></span>(java.lang.String&nbsp;str)</code>
<div class="block">Write a string in a comment at OBJ format.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#write-java.lang.String-int-int-">write</a></span>(java.lang.String&nbsp;str,
     int&nbsp;off,
     int&nbsp;len)</code>
<div class="block">Write a portion of a string in a comment at OBJ format.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#writeNode-javax.media.j3d.Node-">writeNode</a></span>(javax.media.j3d.Node&nbsp;node)</code>
<div class="block">Writes all the 3D shapes children of <code>node</code> at OBJ format.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#writeNode-javax.media.j3d.Node-java.lang.String-">writeNode</a></span>(javax.media.j3d.Node&nbsp;node,
         java.lang.String&nbsp;nodeName)</code>
<div class="block">Writes all the 3D shapes children of <code>node</code> at OBJ format.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#writeNodeInZIPFile-javax.media.j3d.Node-java.io.File-int-java.lang.String-java.lang.String-">writeNodeInZIPFile</a></span>(javax.media.j3d.Node&nbsp;node,
                  java.io.File&nbsp;zipFile,
                  int&nbsp;compressionLevel,
                  java.lang.String&nbsp;entryName,
                  java.lang.String&nbsp;header)</code>
<div class="block">Writes <code>node</code> in an entry at OBJ format of the given zip file
 along with its MTL file and texture images.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/j3d/OBJWriter.html#writeNodeInZIPFile-javax.media.j3d.Node-java.util.Map-java.io.File-int-java.lang.String-java.lang.String-">writeNodeInZIPFile</a></span>(javax.media.j3d.Node&nbsp;node,
                  java.util.Map&lt;java.lang.String,javax.media.j3d.Appearance&gt;&nbsp;materialAppearances,
                  java.io.File&nbsp;zipFile,
                  int&nbsp;compressionLevel,
                  java.lang.String&nbsp;entryName,
                  java.lang.String&nbsp;header)</code>
<div class="block">Writes <code>node</code> in an entry at OBJ format of the given zip file
 along with its MTL file and texture images.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.FilterWriter">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.io.FilterWriter</h3>
<code>flush</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.io.Writer">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.io.Writer</h3>
<code>append, append, append, write</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="OBJWriter-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBJWriter</h4>
<pre>public&nbsp;OBJWriter(java.io.File&nbsp;objFile)
          throws java.io.FileNotFoundException,
                 java.io.IOException</pre>
<div class="block">Create an OBJ writer for the given file, with no header and default precision.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code></dd>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="OBJWriter-java.io.File-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBJWriter</h4>
<pre>public&nbsp;OBJWriter(java.io.File&nbsp;objFile,
                 java.lang.String&nbsp;header,
                 int&nbsp;maximumFractionDigits)
          throws java.io.FileNotFoundException,
                 java.io.IOException</pre>
<div class="block">Create an OBJ writer for the given file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>objFile</code> - the file into which 3D nodes will be written at OBJ format</dd>
<dd><code>header</code> - a header written as a comment at start of the OBJ file and its MTL counterpart</dd>
<dd><code>maximumFractionDigits</code> - the maximum digits count used in fraction part of numbers,
                or -1 for default value. Using -1 may cause writing nodes to be twice faster.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code></dd>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="OBJWriter-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBJWriter</h4>
<pre>public&nbsp;OBJWriter(java.lang.String&nbsp;objFileName)
          throws java.io.FileNotFoundException,
                 java.io.IOException</pre>
<div class="block">Create an OBJ writer for the given file name, with no header and default precision.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code></dd>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="OBJWriter-java.lang.String-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBJWriter</h4>
<pre>public&nbsp;OBJWriter(java.lang.String&nbsp;objFileName,
                 java.lang.String&nbsp;header,
                 int&nbsp;maximumFractionDigits)
          throws java.io.FileNotFoundException,
                 java.io.IOException</pre>
<div class="block">Create an OBJ writer for the given file name.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>objFileName</code> - the name of the file into which 3D nodes will be written at OBJ format</dd>
<dd><code>header</code> - a header written as a comment at start of the OBJ file and its MTL counterpart</dd>
<dd><code>maximumFractionDigits</code> - the maximum digits count used in fraction part of numbers,
                or -1 for default value. Using -1 may cause writing nodes to be twice faster.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.FileNotFoundException</code></dd>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="OBJWriter-java.io.OutputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBJWriter</h4>
<pre>public&nbsp;OBJWriter(java.io.OutputStream&nbsp;out)
          throws java.io.IOException</pre>
<div class="block">Create an OBJ writer that will writes in <code>out</code> stream,
 with no header and default precision.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="OBJWriter-java.io.OutputStream-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBJWriter</h4>
<pre>public&nbsp;OBJWriter(java.io.OutputStream&nbsp;out,
                 java.lang.String&nbsp;header,
                 int&nbsp;maximumFractionDigits)
          throws java.io.IOException</pre>
<div class="block">Create an OBJ writer that will writes in <code>out</code> stream.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>out</code> - the stream into which 3D nodes will be written at OBJ format</dd>
<dd><code>header</code> - a header written as a comment at start of the stream</dd>
<dd><code>maximumFractionDigits</code> - the maximum digits count used in fraction part of numbers,
                or -1 for default value. Using -1 may cause writing nodes to be twice faster.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="OBJWriter-java.io.Writer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>OBJWriter</h4>
<pre>public&nbsp;OBJWriter(java.io.Writer&nbsp;out)
          throws java.io.IOException</pre>
<div class="block">Create an OBJ writer that will writes in <code>out</code> stream,
 with no header and default precision.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="OBJWriter-java.io.Writer-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>OBJWriter</h4>
<pre>public&nbsp;OBJWriter(java.io.Writer&nbsp;out,
                 java.lang.String&nbsp;header,
                 int&nbsp;maximumFractionDigits)
          throws java.io.IOException</pre>
<div class="block">Create an OBJ writer that will writes in <code>out</code> stream.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>out</code> - the stream into which 3D nodes will be written at OBJ format</dd>
<dd><code>header</code> - a header written as a comment at start of the stream</dd>
<dd><code>maximumFractionDigits</code> - the maximum digits count used in fraction part of numbers,
                or -1 for default value. Using -1 may cause writing nodes to be twice faster.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="write-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(int&nbsp;c)
           throws java.io.IOException</pre>
<div class="block">Write a single character in a comment at OBJ format.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>write</code>&nbsp;in class&nbsp;<code>java.io.FilterWriter</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="write-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(char[]&nbsp;cbuf,
                  int&nbsp;off,
                  int&nbsp;len)
           throws java.io.IOException</pre>
<div class="block">Write a portion of an array of characters in a comment at OBJ format.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>write</code>&nbsp;in class&nbsp;<code>java.io.FilterWriter</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="write-java.lang.String-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(java.lang.String&nbsp;str,
                  int&nbsp;off,
                  int&nbsp;len)
           throws java.io.IOException</pre>
<div class="block">Write a portion of a string in a comment at OBJ format.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>write</code>&nbsp;in class&nbsp;<code>java.io.FilterWriter</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="write-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public&nbsp;void&nbsp;write(java.lang.String&nbsp;str)
           throws java.io.IOException</pre>
<div class="block">Write a string in a comment at OBJ format.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>write</code>&nbsp;in class&nbsp;<code>java.io.Writer</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeNode-javax.media.j3d.Node-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeNode</h4>
<pre>public&nbsp;void&nbsp;writeNode(javax.media.j3d.Node&nbsp;node)
               throws java.io.IOException,
                      java.io.InterruptedIOException</pre>
<div class="block">Writes all the 3D shapes children of <code>node</code> at OBJ format.
 If there are transformation groups on the path from <code>node</code> to its shapes,
 they'll be applied to the coordinates written on output.
 The <code>node</code> shouldn't be alive or if it's alive it should have the
 capabilities to read its children, the geometries and the appearance of its shapes.
 Only geometries which are instances of <code>GeometryArray</code> will be written.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - a Java 3D node</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if the operation failed</dd>
<dd><code>java.io.InterruptedIOException</code> - if the current thread was interrupted during this operation.
         The interrupted status of the current thread is cleared when this exception is thrown.</dd>
</dl>
</li>
</ul>
<a name="writeNode-javax.media.j3d.Node-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeNode</h4>
<pre>public&nbsp;void&nbsp;writeNode(javax.media.j3d.Node&nbsp;node,
                      java.lang.String&nbsp;nodeName)
               throws java.io.IOException,
                      java.io.InterruptedIOException</pre>
<div class="block">Writes all the 3D shapes children of <code>node</code> at OBJ format.
 If there are transformation groups on the path from <code>node</code> to its shapes,
 they'll be applied to the coordinates written on output.
 The <code>node</code> shouldn't be alive or if it's alive, it should have the
 capabilities to read its children, the geometries and the appearance of its shapes.
 Only geometries which are instances of <code>GeometryArray</code> will be written.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>node</code> - a Java 3D node</dd>
<dd><code>nodeName</code> - the name of the node. This is useful to distinguish the objects
                 names in output. If this name is <code>null</code> or isn't built
                 with A-Z, a-z, 0-9 and underscores, it will be ignored.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if the operation failed</dd>
<dd><code>java.io.InterruptedIOException</code> - if the current thread was interrupted during this operation
         The interrupted status of the current thread is cleared when this exception is thrown.</dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close()
           throws java.io.IOException,
                  java.io.InterruptedIOException</pre>
<div class="block">Closes this writer and writes MTL file and its texture images,
 if this writer was created from a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>close</code>&nbsp;in interface&nbsp;<code>java.io.Closeable</code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>close</code>&nbsp;in interface&nbsp;<code>java.lang.AutoCloseable</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>close</code>&nbsp;in class&nbsp;<code>java.io.FilterWriter</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code> - if this writer couldn't be closed
                     or couldn't write MTL and texture files couldn't be written</dd>
<dd><code>java.io.InterruptedIOException</code> - if the current thread was interrupted during this operation
         The interrupted status of the current thread is cleared when this exception is thrown.</dd>
</dl>
</li>
</ul>
<a name="writeNodeInZIPFile-javax.media.j3d.Node-java.io.File-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>writeNodeInZIPFile</h4>
<pre>public static&nbsp;void&nbsp;writeNodeInZIPFile(javax.media.j3d.Node&nbsp;node,
                                      java.io.File&nbsp;zipFile,
                                      int&nbsp;compressionLevel,
                                      java.lang.String&nbsp;entryName,
                                      java.lang.String&nbsp;header)
                               throws java.io.IOException</pre>
<div class="block">Writes <code>node</code> in an entry at OBJ format of the given zip file
 along with its MTL file and texture images.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="writeNodeInZIPFile-javax.media.j3d.Node-java.util.Map-java.io.File-int-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>writeNodeInZIPFile</h4>
<pre>public static&nbsp;void&nbsp;writeNodeInZIPFile(javax.media.j3d.Node&nbsp;node,
                                      java.util.Map&lt;java.lang.String,javax.media.j3d.Appearance&gt;&nbsp;materialAppearances,
                                      java.io.File&nbsp;zipFile,
                                      int&nbsp;compressionLevel,
                                      java.lang.String&nbsp;entryName,
                                      java.lang.String&nbsp;header)
                               throws java.io.IOException</pre>
<div class="block">Writes <code>node</code> in an entry at OBJ format of the given zip file
 along with its MTL file and texture images.
 Once saved, <code>materialAppearances</code> will contain the appearances matching
 each material saved in the MTL file. Material names used as keys maybe be different
 of the appearance names to respect MTL specifications.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/OBJWriter.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/j3d/OBJMaterial.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/j3d/PhotoRenderer.html" title="class in com.eteks.sweethome3d.j3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/j3d/OBJWriter.html" target="_top">Frames</a></li>
<li><a href="OBJWriter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.io.FilterWriter">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
