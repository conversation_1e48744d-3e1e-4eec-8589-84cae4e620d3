<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:50 CEST 2024 -->
<title>SwingTools (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SwingTools (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SwingTools.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/SwingTools.html" target="_top">Frames</a></li>
<li><a href="SwingTools.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class SwingTools" class="title">Class SwingTools</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.SwingTools</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SwingTools</span>
extends java.lang.Object</pre>
<div class="block">Gathers some useful tools for Swing.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#addAutoSelectionOnFocusGain-javax.swing.text.JTextComponent-">addAutoSelectionOnFocusGain</a></span>(javax.swing.text.JTextComponent&nbsp;textComponent)</code>
<div class="block">Adds focus and mouse listeners to the given <code>textComponent</code> that will
 select all its text when it gains focus by transfer.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static java.awt.event.AdjustmentListener</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#createAdjustmentListenerUpdatingScrollPaneViewToolTip-javax.swing.JScrollPane-">createAdjustmentListenerUpdatingScrollPaneViewToolTip</a></span>(javax.swing.JScrollPane&nbsp;scrollPane)</code>
<div class="block">Returns a scroll bar adjustment listener bound to the given <code>scrollPane</code> view
 that updates view tool tip when its vertical scroll bar is adjusted.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static java.awt.Cursor</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#createCustomCursor-java.net.URL-java.net.URL-float-float-java.lang.String-java.awt.Cursor-">createCustomCursor</a></span>(java.net.URL&nbsp;smallCursorImageUrl,
                  java.net.URL&nbsp;largeCursorImageUrl,
                  float&nbsp;xCursorHotSpot,
                  float&nbsp;yCursorHotSpot,
                  java.lang.String&nbsp;cursorName,
                  java.awt.Cursor&nbsp;defaultCursor)</code>
<div class="block">Returns a new custom cursor.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static javax.swing.JScrollPane</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#createScrollPane-javax.swing.JComponent-">createScrollPane</a></span>(javax.swing.JComponent&nbsp;component)</code>
<div class="block">Returns a scroll pane containing the given <code>component</code>
 that always displays scroll bars under Mac OS X.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static javax.swing.JPanel</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#createTitledPanel-java.lang.String-">createTitledPanel</a></span>(java.lang.String&nbsp;title)</code>
<div class="block">Returns a new panel with a border and the given <code>title</code></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#deselectAllRadioButtons-javax.swing.JRadioButton...-">deselectAllRadioButtons</a></span>(javax.swing.JRadioButton...&nbsp;radioButtons)</code>
<div class="block">Forces radio buttons to be deselected even if they belong to a button group.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static &lt;T extends java.awt.Component&gt;<br>java.util.List&lt;T&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#findChildren-javax.swing.JComponent-java.lang.Class-">findChildren</a></span>(javax.swing.JComponent&nbsp;parent,
            java.lang.Class&lt;T&gt;&nbsp;childrenClass)</code>
<div class="block">Returns the children of a component of the given class.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static javax.swing.border.Border</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getDropableComponentBorder--">getDropableComponentBorder</a></span>()</code>
<div class="block">Returns the border of a component where a user may drop objects.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static java.awt.Dimension</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getImageSizeInPixels-com.eteks.sweethome3d.model.Content-">getImageSizeInPixels</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image)</code>
<div class="block">Returns <code>image</code> size in pixels.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getLocalizedLabelText-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-java.lang.Object...-">getLocalizedLabelText</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                     java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                     java.lang.String&nbsp;resourceKey,
                     java.lang.Object...&nbsp;resourceParameters)</code>
<div class="block">Returns a localized text for menus items and labels depending on the system.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static java.awt.image.BufferedImage</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getPatternImage-com.eteks.sweethome3d.model.TextureImage-java.awt.Color-java.awt.Color-">getPatternImage</a></span>(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern,
               java.awt.Color&nbsp;backgroundColor,
               java.awt.Color&nbsp;foregroundColor)</code>
<div class="block">Returns the image matching a given pattern.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getResolutionScale--">getResolutionScale</a></span>()</code>
<div class="block">Returns a scale factor used to adapt user interface items to screen resolution.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static javax.swing.ImageIcon</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getScaledImageIcon-java.net.URL-">getScaledImageIcon</a></span>(java.net.URL&nbsp;imageUrl)</code>
<div class="block">Returns an image icon scaled according to the value returned by <a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getResolutionScale--"><code>getResolutionScale()</code></a>.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static java.awt.Stroke</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getStroke-float-com.eteks.sweethome3d.model.Polyline.CapStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-">getStroke</a></span>(float&nbsp;thickness,
         <a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle,
         <a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle,
         <a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle)</code>
<div class="block">Returns the line stroke matching the given line styles.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#hideDisabledMenuItems-javax.swing.JPopupMenu-">hideDisabledMenuItems</a></span>(javax.swing.JPopupMenu&nbsp;popupMenu)</code>
<div class="block">Adds a listener that will update the given popup menu to hide disabled menu items.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#installFocusBorder-javax.swing.JComponent-">installFocusBorder</a></span>(javax.swing.JComponent&nbsp;component)</code>
<div class="block">Updates the border of <code>component</code> with an empty border
 changed to a colored border when it will gain focus.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#isRectangleVisibleAtScreen-java.awt.Rectangle-">isRectangleVisibleAtScreen</a></span>(java.awt.Rectangle&nbsp;rectangle)</code>
<div class="block">Returns <code>true</code> if the given rectangle is fully visible at screen.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#isToolTipShowing--">isToolTipShowing</a></span>()</code>
<div class="block">Returns <code>true</code> if a tool tip is showing.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#requestFocusInWindow-javax.swing.JComponent-">requestFocusInWindow</a></span>(javax.swing.JComponent&nbsp;focusedComponent)</code>
<div class="block">Requests the focus for the given component.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#showConfirmDialog-javax.swing.JComponent-javax.swing.JComponent-java.lang.String-javax.swing.JComponent-">showConfirmDialog</a></span>(javax.swing.JComponent&nbsp;parentComponent,
                 javax.swing.JComponent&nbsp;messageComponent,
                 java.lang.String&nbsp;title,
                 javax.swing.JComponent&nbsp;focusedComponent)</code>
<div class="block">Displays <code>messageComponent</code> in a modal dialog box, giving focus to one of its components.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#showDocumentInBrowser-java.net.URL-">showDocumentInBrowser</a></span>(java.net.URL&nbsp;url)</code>
<div class="block">Attempts to display the given <code>url</code> in a browser and returns <code>true</code>
 if it was done successfully.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#showMessageDialog-javax.swing.JComponent-javax.swing.JComponent-java.lang.String-int-javax.swing.JComponent-">showMessageDialog</a></span>(javax.swing.JComponent&nbsp;parentComponent,
                 javax.swing.JComponent&nbsp;messageComponent,
                 java.lang.String&nbsp;title,
                 int&nbsp;messageType,
                 javax.swing.JComponent&nbsp;focusedComponent)</code>
<div class="block">Displays <code>messageComponent</code> in a modal dialog box, giving focus to one of its components.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#showMessageDialog-javax.swing.JComponent-java.lang.Object-java.lang.String-int-">showMessageDialog</a></span>(javax.swing.JComponent&nbsp;parentComponent,
                 java.lang.Object&nbsp;message,
                 java.lang.String&nbsp;title,
                 int&nbsp;messageType)</code>
<div class="block">Displays <code>message</code> in a modal dialog box.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#showOptionDialog-java.awt.Component-java.lang.String-java.lang.String-int-int-java.lang.Object:A-java.lang.Object-">showOptionDialog</a></span>(java.awt.Component&nbsp;parentComponent,
                java.lang.String&nbsp;message,
                java.lang.String&nbsp;title,
                int&nbsp;optionType,
                int&nbsp;messageType,
                java.lang.Object[]&nbsp;options,
                java.lang.Object&nbsp;initialValue)</code>
<div class="block">Displays message in a dialog box, possibly adjusting font size if required.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#showSplashScreenWindow-java.net.URL-">showSplashScreenWindow</a></span>(java.net.URL&nbsp;imageUrl)</code>
<div class="block">Displays the image referenced by <code>imageUrl</code> in an AWT window
 disposed once an instance of <code>JFrame</code> or <code>JDialog</code> is displayed.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#updateSwingResourceLanguage--">updateSwingResourceLanguage</a></span>()</code>
<div class="block">Updates the Swing resource bundles in use from the default Locale and class loader.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#updateSwingResourceLanguage-com.eteks.sweethome3d.model.UserPreferences-">updateSwingResourceLanguage</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</code>
<div class="block">Updates the Swing resource bundles in use from the preferences Locale and the class loaders of preferences.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="installFocusBorder-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>installFocusBorder</h4>
<pre>public static&nbsp;void&nbsp;installFocusBorder(javax.swing.JComponent&nbsp;component)</pre>
<div class="block">Updates the border of <code>component</code> with an empty border
 changed to a colored border when it will gain focus.
 If the <code>component</code> component is the child of a <code>JViewPort</code>
 instance this border will be installed on its scroll pane parent.</div>
</li>
</ul>
<a name="updateSwingResourceLanguage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateSwingResourceLanguage</h4>
<pre>public static&nbsp;void&nbsp;updateSwingResourceLanguage()</pre>
<div class="block">Updates the Swing resource bundles in use from the default Locale and class loader.</div>
</li>
</ul>
<a name="updateSwingResourceLanguage-com.eteks.sweethome3d.model.UserPreferences-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateSwingResourceLanguage</h4>
<pre>public static&nbsp;void&nbsp;updateSwingResourceLanguage(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences)</pre>
<div class="block">Updates the Swing resource bundles in use from the preferences Locale and the class loaders of preferences.</div>
</li>
</ul>
<a name="getLocalizedLabelText-com.eteks.sweethome3d.model.UserPreferences-java.lang.Class-java.lang.String-java.lang.Object...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLocalizedLabelText</h4>
<pre>public static&nbsp;java.lang.String&nbsp;getLocalizedLabelText(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                                     java.lang.Class&lt;?&gt;&nbsp;resourceClass,
                                                     java.lang.String&nbsp;resourceKey,
                                                     java.lang.Object...&nbsp;resourceParameters)</pre>
<div class="block">Returns a localized text for menus items and labels depending on the system.</div>
</li>
</ul>
<a name="addAutoSelectionOnFocusGain-javax.swing.text.JTextComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAutoSelectionOnFocusGain</h4>
<pre>public static&nbsp;void&nbsp;addAutoSelectionOnFocusGain(javax.swing.text.JTextComponent&nbsp;textComponent)</pre>
<div class="block">Adds focus and mouse listeners to the given <code>textComponent</code> that will
 select all its text when it gains focus by transfer.</div>
</li>
</ul>
<a name="deselectAllRadioButtons-javax.swing.JRadioButton...-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deselectAllRadioButtons</h4>
<pre>public static&nbsp;void&nbsp;deselectAllRadioButtons(javax.swing.JRadioButton...&nbsp;radioButtons)</pre>
<div class="block">Forces radio buttons to be deselected even if they belong to a button group.</div>
</li>
</ul>
<a name="showConfirmDialog-javax.swing.JComponent-javax.swing.JComponent-java.lang.String-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showConfirmDialog</h4>
<pre>public static&nbsp;int&nbsp;showConfirmDialog(javax.swing.JComponent&nbsp;parentComponent,
                                    javax.swing.JComponent&nbsp;messageComponent,
                                    java.lang.String&nbsp;title,
                                    javax.swing.JComponent&nbsp;focusedComponent)</pre>
<div class="block">Displays <code>messageComponent</code> in a modal dialog box, giving focus to one of its components.</div>
</li>
</ul>
<a name="showMessageDialog-javax.swing.JComponent-java.lang.Object-java.lang.String-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showMessageDialog</h4>
<pre>public static&nbsp;void&nbsp;showMessageDialog(javax.swing.JComponent&nbsp;parentComponent,
                                     java.lang.Object&nbsp;message,
                                     java.lang.String&nbsp;title,
                                     int&nbsp;messageType)</pre>
<div class="block">Displays <code>message</code> in a modal dialog box.</div>
</li>
</ul>
<a name="showMessageDialog-javax.swing.JComponent-javax.swing.JComponent-java.lang.String-int-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showMessageDialog</h4>
<pre>public static&nbsp;void&nbsp;showMessageDialog(javax.swing.JComponent&nbsp;parentComponent,
                                     javax.swing.JComponent&nbsp;messageComponent,
                                     java.lang.String&nbsp;title,
                                     int&nbsp;messageType,
                                     javax.swing.JComponent&nbsp;focusedComponent)</pre>
<div class="block">Displays <code>messageComponent</code> in a modal dialog box, giving focus to one of its components.</div>
</li>
</ul>
<a name="showOptionDialog-java.awt.Component-java.lang.String-java.lang.String-int-int-java.lang.Object:A-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showOptionDialog</h4>
<pre>public static&nbsp;int&nbsp;showOptionDialog(java.awt.Component&nbsp;parentComponent,
                                   java.lang.String&nbsp;message,
                                   java.lang.String&nbsp;title,
                                   int&nbsp;optionType,
                                   int&nbsp;messageType,
                                   java.lang.Object[]&nbsp;options,
                                   java.lang.Object&nbsp;initialValue)</pre>
<div class="block">Displays message in a dialog box, possibly adjusting font size if required.</div>
</li>
</ul>
<a name="requestFocusInWindow-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>requestFocusInWindow</h4>
<pre>public static&nbsp;void&nbsp;requestFocusInWindow(javax.swing.JComponent&nbsp;focusedComponent)</pre>
<div class="block">Requests the focus for the given component.</div>
</li>
</ul>
<a name="getPatternImage-com.eteks.sweethome3d.model.TextureImage-java.awt.Color-java.awt.Color-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPatternImage</h4>
<pre>public static&nbsp;java.awt.image.BufferedImage&nbsp;getPatternImage(<a href="../../../../com/eteks/sweethome3d/model/TextureImage.html" title="interface in com.eteks.sweethome3d.model">TextureImage</a>&nbsp;pattern,
                                                           java.awt.Color&nbsp;backgroundColor,
                                                           java.awt.Color&nbsp;foregroundColor)</pre>
<div class="block">Returns the image matching a given pattern.</div>
</li>
</ul>
<a name="getDropableComponentBorder--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDropableComponentBorder</h4>
<pre>public static&nbsp;javax.swing.border.Border&nbsp;getDropableComponentBorder()</pre>
<div class="block">Returns the border of a component where a user may drop objects.</div>
</li>
</ul>
<a name="showSplashScreenWindow-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showSplashScreenWindow</h4>
<pre>public static&nbsp;void&nbsp;showSplashScreenWindow(java.net.URL&nbsp;imageUrl)</pre>
<div class="block">Displays the image referenced by <code>imageUrl</code> in an AWT window
 disposed once an instance of <code>JFrame</code> or <code>JDialog</code> is displayed.
 If the <code>imageUrl</code> is incorrect, nothing happens.</div>
</li>
</ul>
<a name="createTitledPanel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTitledPanel</h4>
<pre>public static&nbsp;javax.swing.JPanel&nbsp;createTitledPanel(java.lang.String&nbsp;title)</pre>
<div class="block">Returns a new panel with a border and the given <code>title</code></div>
</li>
</ul>
<a name="createScrollPane-javax.swing.JComponent-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createScrollPane</h4>
<pre>public static&nbsp;javax.swing.JScrollPane&nbsp;createScrollPane(javax.swing.JComponent&nbsp;component)</pre>
<div class="block">Returns a scroll pane containing the given <code>component</code>
 that always displays scroll bars under Mac OS X.</div>
</li>
</ul>
<a name="createAdjustmentListenerUpdatingScrollPaneViewToolTip-javax.swing.JScrollPane-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAdjustmentListenerUpdatingScrollPaneViewToolTip</h4>
<pre>public static&nbsp;java.awt.event.AdjustmentListener&nbsp;createAdjustmentListenerUpdatingScrollPaneViewToolTip(javax.swing.JScrollPane&nbsp;scrollPane)</pre>
<div class="block">Returns a scroll bar adjustment listener bound to the given <code>scrollPane</code> view
 that updates view tool tip when its vertical scroll bar is adjusted.</div>
</li>
</ul>
<a name="isToolTipShowing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isToolTipShowing</h4>
<pre>public static&nbsp;boolean&nbsp;isToolTipShowing()</pre>
<div class="block">Returns <code>true</code> if a tool tip is showing.</div>
</li>
</ul>
<a name="hideDisabledMenuItems-javax.swing.JPopupMenu-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hideDisabledMenuItems</h4>
<pre>public static&nbsp;void&nbsp;hideDisabledMenuItems(javax.swing.JPopupMenu&nbsp;popupMenu)</pre>
<div class="block">Adds a listener that will update the given popup menu to hide disabled menu items.</div>
</li>
</ul>
<a name="showDocumentInBrowser-java.net.URL-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showDocumentInBrowser</h4>
<pre>public static&nbsp;boolean&nbsp;showDocumentInBrowser(java.net.URL&nbsp;url)</pre>
<div class="block">Attempts to display the given <code>url</code> in a browser and returns <code>true</code>
 if it was done successfully.</div>
</li>
</ul>
<a name="findChildren-javax.swing.JComponent-java.lang.Class-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>findChildren</h4>
<pre>public static&nbsp;&lt;T extends java.awt.Component&gt;&nbsp;java.util.List&lt;T&gt;&nbsp;findChildren(javax.swing.JComponent&nbsp;parent,
                                                                            java.lang.Class&lt;T&gt;&nbsp;childrenClass)</pre>
<div class="block">Returns the children of a component of the given class.</div>
</li>
</ul>
<a name="isRectangleVisibleAtScreen-java.awt.Rectangle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isRectangleVisibleAtScreen</h4>
<pre>public static&nbsp;boolean&nbsp;isRectangleVisibleAtScreen(java.awt.Rectangle&nbsp;rectangle)</pre>
<div class="block">Returns <code>true</code> if the given rectangle is fully visible at screen.</div>
</li>
</ul>
<a name="createCustomCursor-java.net.URL-java.net.URL-float-float-java.lang.String-java.awt.Cursor-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createCustomCursor</h4>
<pre>public static&nbsp;java.awt.Cursor&nbsp;createCustomCursor(java.net.URL&nbsp;smallCursorImageUrl,
                                                 java.net.URL&nbsp;largeCursorImageUrl,
                                                 float&nbsp;xCursorHotSpot,
                                                 float&nbsp;yCursorHotSpot,
                                                 java.lang.String&nbsp;cursorName,
                                                 java.awt.Cursor&nbsp;defaultCursor)</pre>
<div class="block">Returns a new custom cursor.</div>
</li>
</ul>
<a name="getImageSizeInPixels-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImageSizeInPixels</h4>
<pre>public static&nbsp;java.awt.Dimension&nbsp;getImageSizeInPixels(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;image)
                                               throws java.io.IOException</pre>
<div class="block">Returns <code>image</code> size in pixels.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the size or <code>null</code> if the information isn't given in the meta data of the image</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>java.io.IOException</code></dd>
</dl>
</li>
</ul>
<a name="getStroke-float-com.eteks.sweethome3d.model.Polyline.CapStyle-com.eteks.sweethome3d.model.Polyline.JoinStyle-com.eteks.sweethome3d.model.Polyline.DashStyle-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStroke</h4>
<pre>public static&nbsp;java.awt.Stroke&nbsp;getStroke(float&nbsp;thickness,
                                        <a href="../../../../com/eteks/sweethome3d/model/Polyline.CapStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.CapStyle</a>&nbsp;capStyle,
                                        <a href="../../../../com/eteks/sweethome3d/model/Polyline.JoinStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.JoinStyle</a>&nbsp;joinStyle,
                                        <a href="../../../../com/eteks/sweethome3d/model/Polyline.DashStyle.html" title="enum in com.eteks.sweethome3d.model">Polyline.DashStyle</a>&nbsp;dashStyle)</pre>
<div class="block">Returns the line stroke matching the given line styles.</div>
</li>
</ul>
<a name="getResolutionScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResolutionScale</h4>
<pre>public static&nbsp;float&nbsp;getResolutionScale()</pre>
<div class="block">Returns a scale factor used to adapt user interface items to screen resolution.</div>
</li>
</ul>
<a name="getScaledImageIcon-java.net.URL-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getScaledImageIcon</h4>
<pre>public static&nbsp;javax.swing.ImageIcon&nbsp;getScaledImageIcon(java.net.URL&nbsp;imageUrl)</pre>
<div class="block">Returns an image icon scaled according to the value returned by <a href="../../../../com/eteks/sweethome3d/swing/SwingTools.html#getResolutionScale--"><code>getResolutionScale()</code></a>.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/SwingTools.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/ScaledImageComponent.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/SwingViewFactory.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/SwingTools.html" target="_top">Frames</a></li>
<li><a href="SwingTools.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
