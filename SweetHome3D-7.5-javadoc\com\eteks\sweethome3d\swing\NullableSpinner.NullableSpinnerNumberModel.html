<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:49 CEST 2024 -->
<title>NullableSpinner.NullableSpinnerNumberModel (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="NullableSpinner.NullableSpinnerNumberModel (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/NullableSpinner.NullableSpinnerNumberModel.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerModuloNumberModel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ObserverCameraPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html" target="_top">Frames</a></li>
<li><a href="NullableSpinner.NullableSpinnerNumberModel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.AbstractSpinnerModel">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.swing</div>
<h2 title="Class NullableSpinner.NullableSpinnerNumberModel" class="title">Class NullableSpinner.NullableSpinnerNumberModel</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>javax.swing.AbstractSpinnerModel</li>
<li>
<ul class="inheritance">
<li>javax.swing.SpinnerNumberModel</li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.swing.NullableSpinner.NullableSpinnerNumberModel</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>java.io.Serializable, javax.swing.SpinnerModel</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerLengthModel.html" title="class in com.eteks.sweethome3d.swing">NullableSpinner.NullableSpinnerLengthModel</a>, <a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerModuloNumberModel.html" title="class in com.eteks.sweethome3d.swing">NullableSpinner.NullableSpinnerModuloNumberModel</a></dd>
</dl>
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.html" title="class in com.eteks.sweethome3d.swing">NullableSpinner</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">NullableSpinner.NullableSpinnerNumberModel</span>
extends javax.swing.SpinnerNumberModel</pre>
<div class="block">Spinner number model that accepts <code>null</code> values.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../serialized-form.html#com.eteks.sweethome3d.swing.NullableSpinner.NullableSpinnerNumberModel">Serialized Form</a></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.javax.swing.AbstractSpinnerModel">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;javax.swing.AbstractSpinnerModel</h3>
<code>listenerList</code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#NullableSpinnerNumberModel-java.math.BigDecimal-java.math.BigDecimal-java.math.BigDecimal-java.math.BigDecimal-">NullableSpinnerNumberModel</a></span>(java.math.BigDecimal&nbsp;value,
                          java.math.BigDecimal&nbsp;minimum,
                          java.math.BigDecimal&nbsp;maximum,
                          java.math.BigDecimal&nbsp;stepSize)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#NullableSpinnerNumberModel-float-float-float-float-">NullableSpinnerNumberModel</a></span>(float&nbsp;value,
                          float&nbsp;minimum,
                          float&nbsp;maximum,
                          float&nbsp;stepSize)</code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#NullableSpinnerNumberModel-int-int-int-int-">NullableSpinnerNumberModel</a></span>(int&nbsp;value,
                          int&nbsp;minimum,
                          int&nbsp;maximum,
                          int&nbsp;stepSize)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#getNextValue--">getNextValue</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.Number</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#getNumber--">getNumber</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#getPreviousValue--">getPreviousValue</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.Object</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#getValue--">getValue</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#isNullable--">isNullable</a></span>()</code>
<div class="block">Returns <code>true</code> if this spinner model is nullable.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#setNullable-boolean-">setNullable</a></span>(boolean&nbsp;nullable)</code>
<div class="block">Sets whether this spinner model is nullable.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html#setValue-java.lang.Object-">setValue</a></span>(java.lang.Object&nbsp;value)</code>
<div class="block">Sets model value.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.SpinnerNumberModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.SpinnerNumberModel</h3>
<code>getMaximum, getMinimum, getStepSize, setMaximum, setMinimum, setStepSize</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.javax.swing.AbstractSpinnerModel">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;javax.swing.AbstractSpinnerModel</h3>
<code>addChangeListener, fireStateChanged, getChangeListeners, getListeners, removeChangeListener</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="NullableSpinnerNumberModel-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NullableSpinnerNumberModel</h4>
<pre>public&nbsp;NullableSpinnerNumberModel(int&nbsp;value,
                                  int&nbsp;minimum,
                                  int&nbsp;maximum,
                                  int&nbsp;stepSize)</pre>
</li>
</ul>
<a name="NullableSpinnerNumberModel-float-float-float-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>NullableSpinnerNumberModel</h4>
<pre>public&nbsp;NullableSpinnerNumberModel(float&nbsp;value,
                                  float&nbsp;minimum,
                                  float&nbsp;maximum,
                                  float&nbsp;stepSize)</pre>
</li>
</ul>
<a name="NullableSpinnerNumberModel-java.math.BigDecimal-java.math.BigDecimal-java.math.BigDecimal-java.math.BigDecimal-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>NullableSpinnerNumberModel</h4>
<pre>public&nbsp;NullableSpinnerNumberModel(java.math.BigDecimal&nbsp;value,
                                  java.math.BigDecimal&nbsp;minimum,
                                  java.math.BigDecimal&nbsp;maximum,
                                  java.math.BigDecimal&nbsp;stepSize)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getNextValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNextValue</h4>
<pre>public&nbsp;java.lang.Object&nbsp;getNextValue()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getNextValue</code>&nbsp;in interface&nbsp;<code>javax.swing.SpinnerModel</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getNextValue</code>&nbsp;in class&nbsp;<code>javax.swing.SpinnerNumberModel</code></dd>
</dl>
</li>
</ul>
<a name="getPreviousValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreviousValue</h4>
<pre>public&nbsp;java.lang.Object&nbsp;getPreviousValue()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getPreviousValue</code>&nbsp;in interface&nbsp;<code>javax.swing.SpinnerModel</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getPreviousValue</code>&nbsp;in class&nbsp;<code>javax.swing.SpinnerNumberModel</code></dd>
</dl>
</li>
</ul>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;java.lang.Object&nbsp;getValue()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>getValue</code>&nbsp;in interface&nbsp;<code>javax.swing.SpinnerModel</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getValue</code>&nbsp;in class&nbsp;<code>javax.swing.SpinnerNumberModel</code></dd>
</dl>
</li>
</ul>
<a name="setValue-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setValue</h4>
<pre>public&nbsp;void&nbsp;setValue(java.lang.Object&nbsp;value)</pre>
<div class="block">Sets model value. This method is overridden to store whether current value is <code>null</code>
 or not (super class <code>setValue</code> doesn't accept <code>null</code> value).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>setValue</code>&nbsp;in interface&nbsp;<code>javax.swing.SpinnerModel</code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>setValue</code>&nbsp;in class&nbsp;<code>javax.swing.SpinnerNumberModel</code></dd>
</dl>
</li>
</ul>
<a name="getNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumber</h4>
<pre>public&nbsp;java.lang.Number&nbsp;getNumber()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code>getNumber</code>&nbsp;in class&nbsp;<code>javax.swing.SpinnerNumberModel</code></dd>
</dl>
</li>
</ul>
<a name="isNullable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isNullable</h4>
<pre>public&nbsp;boolean&nbsp;isNullable()</pre>
<div class="block">Returns <code>true</code> if this spinner model is nullable.</div>
</li>
</ul>
<a name="setNullable-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setNullable</h4>
<pre>public&nbsp;void&nbsp;setNullable(boolean&nbsp;nullable)</pre>
<div class="block">Sets whether this spinner model is nullable.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/NullableSpinner.NullableSpinnerNumberModel.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerModuloNumberModel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/swing/ObserverCameraPanel.html" title="class in com.eteks.sweethome3d.swing"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/swing/NullableSpinner.NullableSpinnerNumberModel.html" target="_top">Frames</a></li>
<li><a href="NullableSpinner.NullableSpinnerNumberModel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.javax.swing.AbstractSpinnerModel">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
