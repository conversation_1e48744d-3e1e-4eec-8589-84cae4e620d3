<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>HomeView (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="HomeView (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6,"i7":6,"i8":6,"i9":6,"i10":6,"i11":6,"i12":6,"i13":6,"i14":6,"i15":6,"i16":6,"i17":6,"i18":6,"i19":6,"i20":6,"i21":6,"i22":6,"i23":6,"i24":6,"i25":6,"i26":6,"i27":6,"i28":6,"i29":6,"i30":6,"i31":6,"i32":6,"i33":6,"i34":6,"i35":6,"i36":6,"i37":6,"i38":6,"i39":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeView.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeView.html" target="_top">Frames</a></li>
<li><a href="HomeView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Interface HomeView" class="title">Interface HomeView</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></dd>
</dl>
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/swing/HomePane.html" title="class in com.eteks.sweethome3d.swing">HomePane</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">HomeView</span>
extends <a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></pre>
<div class="block">The main view that displays a home.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Interface and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a></span></code>
<div class="block">The actions proposed by the view to user.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.SaveAnswer</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.View">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/View.PointerType.html" title="enum in com.eteks.sweethome3d.viewcontroller">View.PointerType</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#DISPLAY_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX">DISPLAY_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#SORT_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX">SORT_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#attachView-com.eteks.sweethome3d.viewcontroller.View-">attachView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Attaches the given <code>view</code> to home view.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmDeleteCatalogSelection--">confirmDeleteCatalogSelection</a></span>()</code>
<div class="block">Displays a dialog that let user choose whether he wants to delete
 the selected furniture from catalog or not.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmExit--">confirmExit</a></span>()</code>
<div class="block">Displays a dialog that let user choose whether he wants to exit
 application or not.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmOpenDamagedHome-java.lang.String-com.eteks.sweethome3d.model.Home-java.util.List-">confirmOpenDamagedHome</a></span>(java.lang.String&nbsp;homeName,
                      <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                      java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent)</code>
<div class="block">Displays a dialog that lets user choose what he wants
 to do with a damaged home he tries to open it.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmReplaceFurnitureLibrary-java.lang.String-">confirmReplaceFurnitureLibrary</a></span>(java.lang.String&nbsp;furnitureLibraryName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing furniture library or not.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmReplaceLanguageLibrary-java.lang.String-">confirmReplaceLanguageLibrary</a></span>(java.lang.String&nbsp;languageLibraryName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing language library or not.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmReplacePlugin-java.lang.String-">confirmReplacePlugin</a></span>(java.lang.String&nbsp;pluginName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing plug-in or not.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmReplaceTexturesLibrary-java.lang.String-">confirmReplaceTexturesLibrary</a></span>(java.lang.String&nbsp;texturesLibraryName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing textures library or not.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.SaveAnswer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmSave-java.lang.String-">confirmSave</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Displays a dialog that lets user choose whether he wants to save
 the current home or not.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#confirmSaveNewerHome-java.lang.String-">confirmSaveNewerHome</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Displays a dialog that let user choose whether he wants to save
 a home that was created with a newer version of Sweet Home 3D.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#detachView-com.eteks.sweethome3d.viewcontroller.View-">detachView</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</code>
<div class="block">Detaches the given <code>view</code> from home view.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToCSV-java.lang.String-">exportToCSV</a></span>(java.lang.String&nbsp;csvName)</code>
<div class="block">Exports furniture list to a given SVG file.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToOBJ-java.lang.String-">exportToOBJ</a></span>(java.lang.String&nbsp;objFile)</code>
<div class="block">Exports the 3D home objects to a given OBJ file.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#exportToSVG-java.lang.String-">exportToSVG</a></span>(java.lang.String&nbsp;svgName)</code>
<div class="block">Exports the plan objects to a given SVG file.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#getClipboardItems--">getClipboardItems</a></span>()</code>
<div class="block">Returns the list of selectable items that are currently in clipboard
 or <code>null</code> if clipboard doesn't contain any selectable item.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#invokeLater-java.lang.Runnable-">invokeLater</a></span>(java.lang.Runnable&nbsp;runnable)</code>
<div class="block">Execute <code>runnable</code> asynchronously in the thread
 that manages toolkit events.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#isClipboardEmpty--">isClipboardEmpty</a></span>()</code>
<div class="block">Returns <code>true</code> if clipboard contains data that
 components are able to handle.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#printToPDF-java.lang.String-">printToPDF</a></span>(java.lang.String&nbsp;pdfFile)</code>
<div class="block">Prints a home to a given PDF file.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#setActionEnabled-java.lang.String-boolean-">setActionEnabled</a></span>(java.lang.String&nbsp;actionKey,
                boolean&nbsp;enabled)</code>
<div class="block">Enables or disables the action matching <code>actionKey</code>.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#setEnabled-com.eteks.sweethome3d.viewcontroller.HomeView.ActionType-boolean-">setEnabled</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>&nbsp;actionType,
          boolean&nbsp;enabled)</code>
<div class="block">Enables or disables the action matching <code>actionType</code>.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#setTransferEnabled-boolean-">setTransferEnabled</a></span>(boolean&nbsp;enabled)</code>
<div class="block">Enables or disables transfer between components.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#setUndoRedoName-java.lang.String-java.lang.String-">setUndoRedoName</a></span>(java.lang.String&nbsp;undoText,
               java.lang.String&nbsp;redoText)</code>
<div class="block">Sets the name and tool tip of undo and redo actions.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showAboutDialog--">showAboutDialog</a></span>()</code>
<div class="block">Displays an about dialog.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showActionTipMessage-java.lang.String-">showActionTipMessage</a></span>(java.lang.String&nbsp;actionTipKey)</code>
<div class="block">Displays the tip matching <code>actionTipKey</code> and
 returns <code>true</code> if the user chose not to display again the tip.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showDeletedCamerasDialog--">showDeletedCamerasDialog</a></span>()</code>
<div class="block">Displays a dialog showing the list of cameras stored in home
 and returns the ones selected by the user to be deleted.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showError-java.lang.String-">showError</a></span>(java.lang.String&nbsp;message)</code>
<div class="block">Displays <code>message</code> in an error message box.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showExportToCSVDialog-java.lang.String-">showExportToCSVDialog</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Shows a content chooser save dialog to export furniture list in a CSV file.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showExportToOBJDialog-java.lang.String-">showExportToOBJDialog</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Shows a content chooser save dialog to export a 3D home in a OBJ file.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showExportToSVGDialog-java.lang.String-">showExportToSVGDialog</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Shows a content chooser save dialog to export a home plan in a SVG file.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showImportFurnitureLibraryDialog--">showImportFurnitureLibraryDialog</a></span>()</code>
<div class="block">Displays a content chooser open dialog to choose a furniture library.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showImportLanguageLibraryDialog--">showImportLanguageLibraryDialog</a></span>()</code>
<div class="block">Displays a content chooser open dialog to choose a language library.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showImportTexturesLibraryDialog--">showImportTexturesLibraryDialog</a></span>()</code>
<div class="block">Displays a content chooser open dialog to choose a textures library.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showMessage-java.lang.String-">showMessage</a></span>(java.lang.String&nbsp;message)</code>
<div class="block">Displays <code>message</code> in a message box.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showNewHomeFromExampleDialog--">showNewHomeFromExampleDialog</a></span>()</code>
<div class="block">Displays a dialog to let the user choose a home example.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showOpenDialog--">showOpenDialog</a></span>()</code>
<div class="block">Displays a content chooser open dialog to choose the name of a home.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>java.util.concurrent.Callable&lt;java.lang.Void&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showPrintDialog--">showPrintDialog</a></span>()</code>
<div class="block">Shows a print dialog to print the home displayed by this pane.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showPrintToPDFDialog-java.lang.String-">showPrintToPDFDialog</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Shows a content chooser save dialog to print a home in a PDF file.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showSaveDialog-java.lang.String-">showSaveDialog</a></span>(java.lang.String&nbsp;homeName)</code>
<div class="block">Displays a content chooser save dialog to choose the name of a home.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showStoreCameraDialog-java.lang.String-">showStoreCameraDialog</a></span>(java.lang.String&nbsp;cameraName)</code>
<div class="block">Displays a dialog that lets the user choose a name for the current camera.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.html#showUpdatesMessage-java.lang.String-boolean-">showUpdatesMessage</a></span>(java.lang.String&nbsp;updatesMessage,
                  boolean&nbsp;showOnlyMessage)</code>
<div class="block">Displays the given message and returns <code>false</code> if the user
 doesn't want to be informed of the displayed updates and <code>showOnlyMessage</code> is <code>false</code>.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="SORT_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SORT_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX</h4>
<pre>static final&nbsp;java.lang.String SORT_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.viewcontroller.HomeView.SORT_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DISPLAY_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DISPLAY_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX</h4>
<pre>static final&nbsp;java.lang.String DISPLAY_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../constant-values.html#com.eteks.sweethome3d.viewcontroller.HomeView.DISPLAY_HOME_FURNITURE_ADDITIONAL_PROPERTY_ACTION_PREFIX">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setEnabled-com.eteks.sweethome3d.viewcontroller.HomeView.ActionType-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEnabled</h4>
<pre>void&nbsp;setEnabled(<a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.ActionType</a>&nbsp;actionType,
                boolean&nbsp;enabled)</pre>
<div class="block">Enables or disables the action matching <code>actionType</code>.</div>
</li>
</ul>
<a name="setActionEnabled-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActionEnabled</h4>
<pre>void&nbsp;setActionEnabled(java.lang.String&nbsp;actionKey,
                      boolean&nbsp;enabled)</pre>
<div class="block">Enables or disables the action matching <code>actionKey</code>.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.2</dd>
</dl>
</li>
</ul>
<a name="setUndoRedoName-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUndoRedoName</h4>
<pre>void&nbsp;setUndoRedoName(java.lang.String&nbsp;undoText,
                     java.lang.String&nbsp;redoText)</pre>
<div class="block">Sets the name and tool tip of undo and redo actions. If a parameter is <code>null</code>,
 the properties will be reset to their initial values.</div>
</li>
</ul>
<a name="setTransferEnabled-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTransferEnabled</h4>
<pre>void&nbsp;setTransferEnabled(boolean&nbsp;enabled)</pre>
<div class="block">Enables or disables transfer between components.</div>
</li>
</ul>
<a name="detachView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>detachView</h4>
<pre>void&nbsp;detachView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</pre>
<div class="block">Detaches the given <code>view</code> from home view.</div>
</li>
</ul>
<a name="attachView-com.eteks.sweethome3d.viewcontroller.View-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>attachView</h4>
<pre>void&nbsp;attachView(<a href="../../../../com/eteks/sweethome3d/viewcontroller/View.html" title="interface in com.eteks.sweethome3d.viewcontroller">View</a>&nbsp;view)</pre>
<div class="block">Attaches the given <code>view</code> to home view.</div>
</li>
</ul>
<a name="showOpenDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showOpenDialog</h4>
<pre>java.lang.String&nbsp;showOpenDialog()</pre>
<div class="block">Displays a content chooser open dialog to choose the name of a home.</div>
</li>
</ul>
<a name="confirmOpenDamagedHome-java.lang.String-com.eteks.sweethome3d.model.Home-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmOpenDamagedHome</h4>
<pre><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.OpenDamagedHomeAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.OpenDamagedHomeAnswer</a>&nbsp;confirmOpenDamagedHome(java.lang.String&nbsp;homeName,
                                                      <a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;damagedHome,
                                                      java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&gt;&nbsp;invalidContent)</pre>
<div class="block">Displays a dialog that lets user choose what he wants
 to do with a damaged home he tries to open it.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>4.4</dd>
</dl>
</li>
</ul>
<a name="showNewHomeFromExampleDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showNewHomeFromExampleDialog</h4>
<pre>java.lang.String&nbsp;showNewHomeFromExampleDialog()</pre>
<div class="block">Displays a dialog to let the user choose a home example.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="showImportLanguageLibraryDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImportLanguageLibraryDialog</h4>
<pre>java.lang.String&nbsp;showImportLanguageLibraryDialog()</pre>
<div class="block">Displays a content chooser open dialog to choose a language library.</div>
</li>
</ul>
<a name="confirmReplaceLanguageLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReplaceLanguageLibrary</h4>
<pre>boolean&nbsp;confirmReplaceLanguageLibrary(java.lang.String&nbsp;languageLibraryName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing language library or not.</div>
</li>
</ul>
<a name="showImportFurnitureLibraryDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImportFurnitureLibraryDialog</h4>
<pre>java.lang.String&nbsp;showImportFurnitureLibraryDialog()</pre>
<div class="block">Displays a content chooser open dialog to choose a furniture library.</div>
</li>
</ul>
<a name="confirmReplaceFurnitureLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReplaceFurnitureLibrary</h4>
<pre>boolean&nbsp;confirmReplaceFurnitureLibrary(java.lang.String&nbsp;furnitureLibraryName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing furniture library or not.</div>
</li>
</ul>
<a name="showImportTexturesLibraryDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showImportTexturesLibraryDialog</h4>
<pre>java.lang.String&nbsp;showImportTexturesLibraryDialog()</pre>
<div class="block">Displays a content chooser open dialog to choose a textures library.</div>
</li>
</ul>
<a name="confirmReplaceTexturesLibrary-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReplaceTexturesLibrary</h4>
<pre>boolean&nbsp;confirmReplaceTexturesLibrary(java.lang.String&nbsp;texturesLibraryName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing textures library or not.</div>
</li>
</ul>
<a name="confirmReplacePlugin-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmReplacePlugin</h4>
<pre>boolean&nbsp;confirmReplacePlugin(java.lang.String&nbsp;pluginName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to overwrite
 an existing plug-in or not.</div>
</li>
</ul>
<a name="showSaveDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showSaveDialog</h4>
<pre>java.lang.String&nbsp;showSaveDialog(java.lang.String&nbsp;homeName)</pre>
<div class="block">Displays a content chooser save dialog to choose the name of a home.</div>
</li>
</ul>
<a name="confirmSave-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmSave</h4>
<pre><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller">HomeView.SaveAnswer</a>&nbsp;confirmSave(java.lang.String&nbsp;homeName)</pre>
<div class="block">Displays a dialog that lets user choose whether he wants to save
 the current home or not.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html#SAVE"><code>HomeView.SaveAnswer.SAVE</code></a> if user chose to save home,
 <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html#DO_NOT_SAVE"><code>HomeView.SaveAnswer.DO_NOT_SAVE</code></a> if user don't want to save home,
 or <a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html#CANCEL"><code>HomeView.SaveAnswer.CANCEL</code></a> if doesn't want to continue current operation.</dd>
</dl>
</li>
</ul>
<a name="confirmSaveNewerHome-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmSaveNewerHome</h4>
<pre>boolean&nbsp;confirmSaveNewerHome(java.lang.String&nbsp;homeName)</pre>
<div class="block">Displays a dialog that let user choose whether he wants to save
 a home that was created with a newer version of Sweet Home 3D.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if user confirmed to save.</dd>
</dl>
</li>
</ul>
<a name="confirmDeleteCatalogSelection--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmDeleteCatalogSelection</h4>
<pre>boolean&nbsp;confirmDeleteCatalogSelection()</pre>
<div class="block">Displays a dialog that let user choose whether he wants to delete
 the selected furniture from catalog or not.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if user confirmed to delete.</dd>
</dl>
</li>
</ul>
<a name="confirmExit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>confirmExit</h4>
<pre>boolean&nbsp;confirmExit()</pre>
<div class="block">Displays a dialog that let user choose whether he wants to exit
 application or not.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if user confirmed to exit.</dd>
</dl>
</li>
</ul>
<a name="showError-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showError</h4>
<pre>void&nbsp;showError(java.lang.String&nbsp;message)</pre>
<div class="block">Displays <code>message</code> in an error message box.</div>
</li>
</ul>
<a name="showMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showMessage</h4>
<pre>void&nbsp;showMessage(java.lang.String&nbsp;message)</pre>
<div class="block">Displays <code>message</code> in a message box.</div>
</li>
</ul>
<a name="showActionTipMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showActionTipMessage</h4>
<pre>boolean&nbsp;showActionTipMessage(java.lang.String&nbsp;actionTipKey)</pre>
<div class="block">Displays the tip matching <code>actionTipKey</code> and
 returns <code>true</code> if the user chose not to display again the tip.</div>
</li>
</ul>
<a name="showAboutDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showAboutDialog</h4>
<pre>void&nbsp;showAboutDialog()</pre>
<div class="block">Displays an about dialog.</div>
</li>
</ul>
<a name="showPrintDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showPrintDialog</h4>
<pre>java.util.concurrent.Callable&lt;java.lang.Void&gt;&nbsp;showPrintDialog()</pre>
<div class="block">Shows a print dialog to print the home displayed by this pane.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a print task to execute or <code>null</code> if the user canceled print.
    The <code>call</code> method of the returned task may throw a
    <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model"><code>RecorderException</code></a> exception if print failed
    or an <a href="../../../../com/eteks/sweethome3d/model/InterruptedRecorderException.html" title="class in com.eteks.sweethome3d.model"><code>InterruptedRecorderException</code></a>
    exception if it was interrupted.</dd>
</dl>
</li>
</ul>
<a name="showPrintToPDFDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showPrintToPDFDialog</h4>
<pre>java.lang.String&nbsp;showPrintToPDFDialog(java.lang.String&nbsp;homeName)</pre>
<div class="block">Shows a content chooser save dialog to print a home in a PDF file.</div>
</li>
</ul>
<a name="printToPDF-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>printToPDF</h4>
<pre>void&nbsp;printToPDF(java.lang.String&nbsp;pdfFile)
         throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Prints a home to a given PDF file. This method may be overridden
 to write to another kind of output stream.
 Caution !!! This method may be called from a threaded task.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="showExportToCSVDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportToCSVDialog</h4>
<pre>java.lang.String&nbsp;showExportToCSVDialog(java.lang.String&nbsp;name)</pre>
<div class="block">Shows a content chooser save dialog to export furniture list in a CSV file.</div>
</li>
</ul>
<a name="exportToCSV-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToCSV</h4>
<pre>void&nbsp;exportToCSV(java.lang.String&nbsp;csvName)
          throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Exports furniture list to a given SVG file.
 Caution !!! This method may be called from a threaded task.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="showExportToSVGDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportToSVGDialog</h4>
<pre>java.lang.String&nbsp;showExportToSVGDialog(java.lang.String&nbsp;name)</pre>
<div class="block">Shows a content chooser save dialog to export a home plan in a SVG file.</div>
</li>
</ul>
<a name="exportToSVG-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToSVG</h4>
<pre>void&nbsp;exportToSVG(java.lang.String&nbsp;svgName)
          throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Exports the plan objects to a given SVG file.
 Caution !!! This method may be called from a threaded task.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="showExportToOBJDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showExportToOBJDialog</h4>
<pre>java.lang.String&nbsp;showExportToOBJDialog(java.lang.String&nbsp;homeName)</pre>
<div class="block">Shows a content chooser save dialog to export a 3D home in a OBJ file.</div>
</li>
</ul>
<a name="exportToOBJ-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>exportToOBJ</h4>
<pre>void&nbsp;exportToOBJ(java.lang.String&nbsp;objFile)
          throws <a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></pre>
<div class="block">Exports the 3D home objects to a given OBJ file.
 Caution !!! This method may be called from a threaded task.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/model/RecorderException.html" title="class in com.eteks.sweethome3d.model">RecorderException</a></code></dd>
</dl>
</li>
</ul>
<a name="showStoreCameraDialog-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showStoreCameraDialog</h4>
<pre>java.lang.String&nbsp;showStoreCameraDialog(java.lang.String&nbsp;cameraName)</pre>
<div class="block">Displays a dialog that lets the user choose a name for the current camera.</div>
</li>
</ul>
<a name="showDeletedCamerasDialog--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showDeletedCamerasDialog</h4>
<pre>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Camera.html" title="class in com.eteks.sweethome3d.model">Camera</a>&gt;&nbsp;showDeletedCamerasDialog()</pre>
<div class="block">Displays a dialog showing the list of cameras stored in home
 and returns the ones selected by the user to be deleted.</div>
</li>
</ul>
<a name="isClipboardEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isClipboardEmpty</h4>
<pre>boolean&nbsp;isClipboardEmpty()</pre>
<div class="block">Returns <code>true</code> if clipboard contains data that
 components are able to handle.</div>
</li>
</ul>
<a name="getClipboardItems--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClipboardItems</h4>
<pre>java.util.List&lt;<a href="../../../../com/eteks/sweethome3d/model/Selectable.html" title="interface in com.eteks.sweethome3d.model">Selectable</a>&gt;&nbsp;getClipboardItems()</pre>
<div class="block">Returns the list of selectable items that are currently in clipboard
 or <code>null</code> if clipboard doesn't contain any selectable item.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.0</dd>
</dl>
</li>
</ul>
<a name="showUpdatesMessage-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>showUpdatesMessage</h4>
<pre>boolean&nbsp;showUpdatesMessage(java.lang.String&nbsp;updatesMessage,
                           boolean&nbsp;showOnlyMessage)</pre>
<div class="block">Displays the given message and returns <code>false</code> if the user
 doesn't want to be informed of the displayed updates and <code>showOnlyMessage</code> is <code>false</code>.</div>
</li>
</ul>
<a name="invokeLater-java.lang.Runnable-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>invokeLater</h4>
<pre>void&nbsp;invokeLater(java.lang.Runnable&nbsp;runnable)</pre>
<div class="block">Execute <code>runnable</code> asynchronously in the thread
 that manages toolkit events.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/HomeView.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeFurnitureController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.ActionType.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/HomeView.html" target="_top">Frames</a></li>
<li><a href="HomeView.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
