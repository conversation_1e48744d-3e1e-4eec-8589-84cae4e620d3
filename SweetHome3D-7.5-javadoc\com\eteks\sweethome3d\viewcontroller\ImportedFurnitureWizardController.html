<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="fr">
<head>
<!-- Generated by javadoc (1.8.0_202) on Wed Aug 21 10:39:51 CEST 2024 -->
<title>ImportedFurnitureWizardController (Sweet Home 3D 7.5 API)</title>
<meta name="date" content="2024-08-21">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ImportedFurnitureWizardController (Sweet Home 3D 7.5 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ImportedFurnitureWizardController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" target="_top">Frames</a></li>
<li><a href="ImportedFurnitureWizardController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.eteks.sweethome3d.viewcontroller</div>
<h2 title="Class ImportedFurnitureWizardController" class="title">Class ImportedFurnitureWizardController</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">com.eteks.sweethome3d.viewcontroller.WizardController</a></li>
<li>
<ul class="inheritance">
<li>com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ImportedFurnitureWizardController</span>
extends <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a>
implements <a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></pre>
<div class="block">Wizard controller to manage furniture importation.</div>
<dl>
<dt><span class="simpleTagLabel">Author:</span></dt>
<dd>Emmanuel Puybaret</dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a></span></code>
<div class="block">Step state superclass.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Property</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Step</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.com.eteks.sweethome3d.viewcontroller.WizardController">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.WizardControllerStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController.WizardControllerStepState</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedFurnitureWizardController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                 <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits <code>piece</code> values.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-com.eteks.sweethome3d.model.Home-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">ImportedFurnitureWizardController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                 java.lang.String&nbsp;modelName,
                                 <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                                 javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates a controller that edits a new imported home piece of furniture
 with a given <code>modelName</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">ImportedFurnitureWizardController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                 <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                                 javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</code>
<div class="block">Creates a controller that edits a new imported home piece of furniture.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedFurnitureWizardController</a></span>(java.lang.String&nbsp;modelName,
                                 <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits a new catalog piece of furniture with a given
 <code>modelName</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#ImportedFurnitureWizardController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">ImportedFurnitureWizardController</a></span>(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                 <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</code>
<div class="block">Creates a controller that edits a new catalog piece of furniture.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">addPieceOfFurniture</a></span>(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</code>
<div class="block">Controls new piece added to home.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Property</a>&nbsp;property,
                         java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#finish--">finish</a></span>()</code>
<div class="block">Imports piece in catalog and/or home and posts an undoable operation.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getCategory--">getCategory</a></span>()</code>
<div class="block">Returns the category of the imported piece.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>java.lang.Integer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getColor--">getColor</a></span>()</code>
<div class="block">Returns the color of the imported piece.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getContentManager--">getContentManager</a></span>()</code>
<div class="block">Returns the content manager of this controller.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getCreator--">getCreator</a></span>()</code>
<div class="block">Returns the creator of the imported piece.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getDepth--">getDepth</a></span>()</code>
<div class="block">Returns the depth of the imported piece.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getElevation--">getElevation</a></span>()</code>
<div class="block">Returns the elevation of the imported piece.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getFurnitureAttributesStepState--">getFurnitureAttributesStepState</a></span>()</code>
<div class="block">Returns the furniture attributes step state.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getFurnitureIconStepState--">getFurnitureIconStepState</a></span>()</code>
<div class="block">Returns the furniture icon step state.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getFurnitureModelStepState--">getFurnitureModelStepState</a></span>()</code>
<div class="block">Returns the furniture choice step state.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getFurnitureOrientationStepState--">getFurnitureOrientationStepState</a></span>()</code>
<div class="block">Returns the furniture orientation step state.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getHeight--">getHeight</a></span>()</code>
<div class="block">Returns the height.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getIconPitch--">getIconPitch</a></span>()</code>
<div class="block">Returns the pitch angle of the piece icon.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getIconScale--">getIconScale</a></span>()</code>
<div class="block">Returns the scale of the piece icon.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getIconYaw--">getIconYaw</a></span>()</code>
<div class="block">Returns the yaw angle of the piece icon.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getModel--">getModel</a></span>()</code>
<div class="block">Returns the model content of the imported piece.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>float[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getModelRotation--">getModelRotation</a></span>()</code>
<div class="block">Returns the pitch angle of the imported piece model.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getModelSize--">getModelSize</a></span>()</code>
<div class="block">Returns the model size of the imported piece.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getName--">getName</a></span>()</code>
<div class="block">Returns the name of the imported piece.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getStaircaseCutOutShape--">getStaircaseCutOutShape</a></span>()</code>
<div class="block">Returns the shape used to cut out upper levels at its intersection with a staircase.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Step</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getStep--">getStep</a></span>()</code>
<div class="block">Returns the current step in wizard view.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getStepState--">getStepState</a></span>()</code>
<div class="block">Returns the current step state.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>protected <a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getStepsView--">getStepsView</a></span>()</code>
<div class="block">Returns the unique wizard view used for all steps.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Returns the width.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#isBackFaceShown--">isBackFaceShown</a></span>()</code>
<div class="block">Returns <code>true</code> if imported piece back face should be shown.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#isDoorOrWindow--">isDoorOrWindow</a></span>()</code>
<div class="block">Returns <code>true</code> if imported piece is a door or a window.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#isEdgeColorMaterialHidden--">isEdgeColorMaterialHidden</a></span>()</code>
<div class="block">Returns <code>true</code> if edge color materials should be hidden.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#isMovable--">isMovable</a></span>()</code>
<div class="block">Returns <code>true</code> if imported piece is movable.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#isPieceOfFurnitureNameValid--">isPieceOfFurnitureNameValid</a></span>()</code>
<div class="block">Returns <code>true</code> if piece name is valid.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#isProportional--">isProportional</a></span>()</code>
<div class="block">Returns <code>true</code> if piece proportions should be kept.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Property</a>&nbsp;property,
                            java.beans.PropertyChangeListener&nbsp;listener)</code>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setBackFaceShown-boolean-">setBackFaceShown</a></span>(boolean&nbsp;backFaceShown)</code>
<div class="block">Sets whether imported piece back face should be shown.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setCategory-com.eteks.sweethome3d.model.FurnitureCategory-">setCategory</a></span>(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a>&nbsp;category)</code>
<div class="block">Sets the category of the imported piece.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setColor-java.lang.Integer-">setColor</a></span>(java.lang.Integer&nbsp;color)</code>
<div class="block">Sets the color of the imported piece.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setCreator-java.lang.String-">setCreator</a></span>(java.lang.String&nbsp;creator)</code>
<div class="block">Sets the creator of the imported piece.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setDepth-float-">setDepth</a></span>(float&nbsp;depth)</code>
<div class="block">Sets the depth of the imported piece.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setDoorOrWindow-boolean-">setDoorOrWindow</a></span>(boolean&nbsp;doorOrWindow)</code>
<div class="block">Sets whether imported piece is a door or a window.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setEdgeColorMaterialHidden-boolean-">setEdgeColorMaterialHidden</a></span>(boolean&nbsp;edgeColorMaterialHidden)</code>
<div class="block">Sets whether edge color materials should be hidden or not.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setElevation-float-">setElevation</a></span>(float&nbsp;elevation)</code>
<div class="block">Sets the elevation of the imported piece.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setHeight-float-">setHeight</a></span>(float&nbsp;height)</code>
<div class="block">Sets the size of the imported piece.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setIconPitch-float-">setIconPitch</a></span>(float&nbsp;iconPitch)</code>
<div class="block">Sets the pitch angle of the piece icon.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setIconScale-float-">setIconScale</a></span>(float&nbsp;iconScale)</code>
<div class="block">Sets the scale of the piece icon.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setIconYaw-float-">setIconYaw</a></span>(float&nbsp;iconYaw)</code>
<div class="block">Sets the yaw angle of the piece icon.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setModel-com.eteks.sweethome3d.model.Content-">setModel</a></span>(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</code>
<div class="block">Sets the model content of the imported piece.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setModelRotation-float:A:A-">setModelRotation</a></span>(float[][]&nbsp;modelRotation)</code>
<div class="block">Sets the orientation pitch angle of the imported piece model.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setModelSize-long-">setModelSize</a></span>(long&nbsp;modelSize)</code>
<div class="block">Sets the model size of the content of the imported piece.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setMovable-boolean-">setMovable</a></span>(boolean&nbsp;movable)</code>
<div class="block">Sets whether imported piece is movable.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setName-java.lang.String-">setName</a></span>(java.lang.String&nbsp;name)</code>
<div class="block">Sets the name of the imported piece.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setProportional-boolean-">setProportional</a></span>(boolean&nbsp;proportional)</code>
<div class="block">Sets whether piece proportions should be kept or not.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setStaircaseCutOutShape-java.lang.String-">setStaircaseCutOutShape</a></span>(java.lang.String&nbsp;staircaseCutOutShape)</code>
<div class="block">Sets the shape used to cut out upper levels at its intersection with a staircase.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setStep-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController.Step-">setStep</a></span>(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Step</a>&nbsp;step)</code>
<div class="block">Switch in the wizard view to the given <code>step</code>.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html#setWidth-float-">setWidth</a></span>(float&nbsp;width)</code>
<div class="block">Sets the width of the imported piece.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.WizardController">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WizardController.Property-java.beans.PropertyChangeListener-">addPropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#displayView-com.eteks.sweethome3d.viewcontroller.View-">displayView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getStepIcon--">getStepIcon</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getStepView--">getStepView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getTitle--">getTitle</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getView--">getView</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#goBackToPreviousStep--">goBackToPreviousStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#goToNextStep--">goToNextStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#isBackStepEnabled--">isBackStepEnabled</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#isLastStep--">isLastStep</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#isNextStepEnabled--">isNextStepEnabled</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#isResizable--">isResizable</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.WizardController.Property-java.beans.PropertyChangeListener-">removePropertyChangeListener</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#setResizable-boolean-">setResizable</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#setStepState-com.eteks.sweethome3d.viewcontroller.WizardController.WizardControllerStepState-">setStepState</a>, <a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#setTitle-java.lang.String-">setTitle</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>clone, equals, finalize, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.com.eteks.sweethome3d.viewcontroller.Controller">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;com.eteks.sweethome3d.viewcontroller.<a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html" title="interface in com.eteks.sweethome3d.viewcontroller">Controller</a></h3>
<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/Controller.html#getView--">getView</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ImportedFurnitureWizardController-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ImportedFurnitureWizardController</h4>
<pre>public&nbsp;ImportedFurnitureWizardController(<a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
<div class="block">Creates a controller that edits a new catalog piece of furniture.</div>
</li>
</ul>
<a name="ImportedFurnitureWizardController-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ImportedFurnitureWizardController</h4>
<pre>public&nbsp;ImportedFurnitureWizardController(java.lang.String&nbsp;modelName,
                                         <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
<div class="block">Creates a controller that edits a new catalog piece of furniture with a given
 <code>modelName</code>.</div>
</li>
</ul>
<a name="ImportedFurnitureWizardController-com.eteks.sweethome3d.model.CatalogPieceOfFurniture-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ImportedFurnitureWizardController</h4>
<pre>public&nbsp;ImportedFurnitureWizardController(<a href="../../../../com/eteks/sweethome3d/model/CatalogPieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">CatalogPieceOfFurniture</a>&nbsp;piece,
                                         <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager)</pre>
<div class="block">Creates a controller that edits <code>piece</code> values.</div>
</li>
</ul>
<a name="ImportedFurnitureWizardController-com.eteks.sweethome3d.model.Home-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ImportedFurnitureWizardController</h4>
<pre>public&nbsp;ImportedFurnitureWizardController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                         <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                                         javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates a controller that edits a new imported home piece of furniture.</div>
</li>
</ul>
<a name="ImportedFurnitureWizardController-com.eteks.sweethome3d.model.Home-java.lang.String-com.eteks.sweethome3d.model.UserPreferences-com.eteks.sweethome3d.viewcontroller.FurnitureController-com.eteks.sweethome3d.viewcontroller.ViewFactory-com.eteks.sweethome3d.viewcontroller.ContentManager-javax.swing.undo.UndoableEditSupport-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ImportedFurnitureWizardController</h4>
<pre>public&nbsp;ImportedFurnitureWizardController(<a href="../../../../com/eteks/sweethome3d/model/Home.html" title="class in com.eteks.sweethome3d.model">Home</a>&nbsp;home,
                                         java.lang.String&nbsp;modelName,
                                         <a href="../../../../com/eteks/sweethome3d/model/UserPreferences.html" title="class in com.eteks.sweethome3d.model">UserPreferences</a>&nbsp;preferences,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/FurnitureController.html" title="class in com.eteks.sweethome3d.viewcontroller">FurnitureController</a>&nbsp;furnitureController,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ViewFactory.html" title="interface in com.eteks.sweethome3d.viewcontroller">ViewFactory</a>&nbsp;viewFactory,
                                         <a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;contentManager,
                                         javax.swing.undo.UndoableEditSupport&nbsp;undoSupport)</pre>
<div class="block">Creates a controller that edits a new imported home piece of furniture
 with a given <code>modelName</code>.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="finish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finish</h4>
<pre>public&nbsp;void&nbsp;finish()</pre>
<div class="block">Imports piece in catalog and/or home and posts an undoable operation.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#finish--">finish</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a></code></dd>
</dl>
</li>
</ul>
<a name="addPieceOfFurniture-com.eteks.sweethome3d.model.HomePieceOfFurniture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPieceOfFurniture</h4>
<pre>public&nbsp;void&nbsp;addPieceOfFurniture(<a href="../../../../com/eteks/sweethome3d/model/HomePieceOfFurniture.html" title="class in com.eteks.sweethome3d.model">HomePieceOfFurniture</a>&nbsp;piece)</pre>
<div class="block">Controls new piece added to home.
 Once added the furniture will be selected in view
 and undo support will receive a new undoable edit.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>piece</code> - the piece of furniture to add.</dd>
</dl>
</li>
</ul>
<a name="getContentManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContentManager</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ContentManager.html" title="interface in com.eteks.sweethome3d.viewcontroller">ContentManager</a>&nbsp;getContentManager()</pre>
<div class="block">Returns the content manager of this controller.</div>
</li>
</ul>
<a name="getStepState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStepState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a>&nbsp;getStepState()</pre>
<div class="block">Returns the current step state.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html#getStepState--">getStepState</a></code>&nbsp;in class&nbsp;<code><a href="../../../../com/eteks/sweethome3d/viewcontroller/WizardController.html" title="class in com.eteks.sweethome3d.viewcontroller">WizardController</a></code></dd>
</dl>
</li>
</ul>
<a name="getFurnitureModelStepState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureModelStepState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a>&nbsp;getFurnitureModelStepState()</pre>
<div class="block">Returns the furniture choice step state.</div>
</li>
</ul>
<a name="getFurnitureOrientationStepState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureOrientationStepState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a>&nbsp;getFurnitureOrientationStepState()</pre>
<div class="block">Returns the furniture orientation step state.</div>
</li>
</ul>
<a name="getFurnitureAttributesStepState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureAttributesStepState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a>&nbsp;getFurnitureAttributesStepState()</pre>
<div class="block">Returns the furniture attributes step state.</div>
</li>
</ul>
<a name="getFurnitureIconStepState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFurnitureIconStepState</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.ImportedFurnitureWizardStepState</a>&nbsp;getFurnitureIconStepState()</pre>
<div class="block">Returns the furniture icon step state.</div>
</li>
</ul>
<a name="getStepsView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStepsView</h4>
<pre>protected&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardStepsView.html" title="interface in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardStepsView</a>&nbsp;getStepsView()</pre>
<div class="block">Returns the unique wizard view used for all steps.</div>
</li>
</ul>
<a name="setStep-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController.Step-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStep</h4>
<pre>protected&nbsp;void&nbsp;setStep(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Step</a>&nbsp;step)</pre>
<div class="block">Switch in the wizard view to the given <code>step</code>.</div>
</li>
</ul>
<a name="getStep--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStep</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Step.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Step</a>&nbsp;getStep()</pre>
<div class="block">Returns the current step in wizard view.</div>
</li>
</ul>
<a name="addPropertyChangeListener-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addPropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;addPropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Property</a>&nbsp;property,
                                      java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Adds the property change <code>listener</code> in parameter to this controller.</div>
</li>
</ul>
<a name="removePropertyChangeListener-com.eteks.sweethome3d.viewcontroller.ImportedFurnitureWizardController.Property-java.beans.PropertyChangeListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removePropertyChangeListener</h4>
<pre>public&nbsp;void&nbsp;removePropertyChangeListener(<a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.Property.html" title="enum in com.eteks.sweethome3d.viewcontroller">ImportedFurnitureWizardController.Property</a>&nbsp;property,
                                         java.beans.PropertyChangeListener&nbsp;listener)</pre>
<div class="block">Removes the property change <code>listener</code> in parameter from this controller.</div>
</li>
</ul>
<a name="getModel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModel</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;getModel()</pre>
<div class="block">Returns the model content of the imported piece.</div>
</li>
</ul>
<a name="setModel-com.eteks.sweethome3d.model.Content-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModel</h4>
<pre>public&nbsp;void&nbsp;setModel(<a href="../../../../com/eteks/sweethome3d/model/Content.html" title="interface in com.eteks.sweethome3d.model">Content</a>&nbsp;model)</pre>
<div class="block">Sets the model content of the imported piece.</div>
</li>
</ul>
<a name="isBackFaceShown--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBackFaceShown</h4>
<pre>public&nbsp;boolean&nbsp;isBackFaceShown()</pre>
<div class="block">Returns <code>true</code> if imported piece back face should be shown.</div>
</li>
</ul>
<a name="setBackFaceShown-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBackFaceShown</h4>
<pre>public&nbsp;void&nbsp;setBackFaceShown(boolean&nbsp;backFaceShown)</pre>
<div class="block">Sets whether imported piece back face should be shown.</div>
</li>
</ul>
<a name="getModelSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelSize</h4>
<pre>public&nbsp;long&nbsp;getModelSize()</pre>
<div class="block">Returns the model size of the imported piece.</div>
</li>
</ul>
<a name="setModelSize-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelSize</h4>
<pre>public&nbsp;void&nbsp;setModelSize(long&nbsp;modelSize)</pre>
<div class="block">Sets the model size of the content of the imported piece.</div>
</li>
</ul>
<a name="getModelRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getModelRotation</h4>
<pre>public&nbsp;float[][]&nbsp;getModelRotation()</pre>
<div class="block">Returns the pitch angle of the imported piece model.</div>
</li>
</ul>
<a name="setModelRotation-float:A:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setModelRotation</h4>
<pre>public&nbsp;void&nbsp;setModelRotation(float[][]&nbsp;modelRotation)</pre>
<div class="block">Sets the orientation pitch angle of the imported piece model.</div>
</li>
</ul>
<a name="isEdgeColorMaterialHidden--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEdgeColorMaterialHidden</h4>
<pre>public&nbsp;boolean&nbsp;isEdgeColorMaterialHidden()</pre>
<div class="block">Returns <code>true</code> if edge color materials should be hidden.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setEdgeColorMaterialHidden-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEdgeColorMaterialHidden</h4>
<pre>public&nbsp;void&nbsp;setEdgeColorMaterialHidden(boolean&nbsp;edgeColorMaterialHidden)</pre>
<div class="block">Sets whether edge color materials should be hidden or not.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;java.lang.String&nbsp;getName()</pre>
<div class="block">Returns the name of the imported piece.</div>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(java.lang.String&nbsp;name)</pre>
<div class="block">Sets the name of the imported piece.</div>
</li>
</ul>
<a name="getCreator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreator</h4>
<pre>public&nbsp;java.lang.String&nbsp;getCreator()</pre>
<div class="block">Returns the creator of the imported piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="setCreator-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreator</h4>
<pre>public&nbsp;void&nbsp;setCreator(java.lang.String&nbsp;creator)</pre>
<div class="block">Sets the creator of the imported piece.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>5.5</dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;float&nbsp;getWidth()</pre>
<div class="block">Returns the width.</div>
</li>
</ul>
<a name="setWidth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidth</h4>
<pre>public&nbsp;void&nbsp;setWidth(float&nbsp;width)</pre>
<div class="block">Sets the width of the imported piece.</div>
</li>
</ul>
<a name="getDepth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDepth</h4>
<pre>public&nbsp;float&nbsp;getDepth()</pre>
<div class="block">Returns the depth of the imported piece.</div>
</li>
</ul>
<a name="setDepth-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDepth</h4>
<pre>public&nbsp;void&nbsp;setDepth(float&nbsp;depth)</pre>
<div class="block">Sets the depth of the imported piece.</div>
</li>
</ul>
<a name="getHeight--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHeight</h4>
<pre>public&nbsp;float&nbsp;getHeight()</pre>
<div class="block">Returns the height.</div>
</li>
</ul>
<a name="setHeight-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHeight</h4>
<pre>public&nbsp;void&nbsp;setHeight(float&nbsp;height)</pre>
<div class="block">Sets the size of the imported piece.</div>
</li>
</ul>
<a name="getElevation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getElevation</h4>
<pre>public&nbsp;float&nbsp;getElevation()</pre>
<div class="block">Returns the elevation of the imported piece.</div>
</li>
</ul>
<a name="setElevation-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setElevation</h4>
<pre>public&nbsp;void&nbsp;setElevation(float&nbsp;elevation)</pre>
<div class="block">Sets the elevation of the imported piece.</div>
</li>
</ul>
<a name="isMovable--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isMovable</h4>
<pre>public&nbsp;boolean&nbsp;isMovable()</pre>
<div class="block">Returns <code>true</code> if imported piece is movable.</div>
</li>
</ul>
<a name="setMovable-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMovable</h4>
<pre>public&nbsp;void&nbsp;setMovable(boolean&nbsp;movable)</pre>
<div class="block">Sets whether imported piece is movable.</div>
</li>
</ul>
<a name="isDoorOrWindow--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDoorOrWindow</h4>
<pre>public&nbsp;boolean&nbsp;isDoorOrWindow()</pre>
<div class="block">Returns <code>true</code> if imported piece is a door or a window.</div>
</li>
</ul>
<a name="setDoorOrWindow-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDoorOrWindow</h4>
<pre>public&nbsp;void&nbsp;setDoorOrWindow(boolean&nbsp;doorOrWindow)</pre>
<div class="block">Sets whether imported piece is a door or a window.</div>
</li>
</ul>
<a name="getStaircaseCutOutShape--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaircaseCutOutShape</h4>
<pre>public&nbsp;java.lang.String&nbsp;getStaircaseCutOutShape()</pre>
<div class="block">Returns the shape used to cut out upper levels at its intersection with a staircase.</div>
</li>
</ul>
<a name="setStaircaseCutOutShape-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaircaseCutOutShape</h4>
<pre>public&nbsp;void&nbsp;setStaircaseCutOutShape(java.lang.String&nbsp;staircaseCutOutShape)</pre>
<div class="block">Sets the shape used to cut out upper levels at its intersection with a staircase.</div>
</li>
</ul>
<a name="getColor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getColor</h4>
<pre>public&nbsp;java.lang.Integer&nbsp;getColor()</pre>
<div class="block">Returns the color of the imported piece.</div>
</li>
</ul>
<a name="setColor-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setColor</h4>
<pre>public&nbsp;void&nbsp;setColor(java.lang.Integer&nbsp;color)</pre>
<div class="block">Sets the color of the imported piece.</div>
</li>
</ul>
<a name="getCategory--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCategory</h4>
<pre>public&nbsp;<a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a>&nbsp;getCategory()</pre>
<div class="block">Returns the category of the imported piece.</div>
</li>
</ul>
<a name="setCategory-com.eteks.sweethome3d.model.FurnitureCategory-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCategory</h4>
<pre>public&nbsp;void&nbsp;setCategory(<a href="../../../../com/eteks/sweethome3d/model/FurnitureCategory.html" title="class in com.eteks.sweethome3d.model">FurnitureCategory</a>&nbsp;category)</pre>
<div class="block">Sets the category of the imported piece.</div>
</li>
</ul>
<a name="getIconYaw--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIconYaw</h4>
<pre>public&nbsp;float&nbsp;getIconYaw()</pre>
<div class="block">Returns the yaw angle of the piece icon.</div>
</li>
</ul>
<a name="setIconYaw-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIconYaw</h4>
<pre>public&nbsp;void&nbsp;setIconYaw(float&nbsp;iconYaw)</pre>
<div class="block">Sets the yaw angle of the piece icon.</div>
</li>
</ul>
<a name="getIconPitch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIconPitch</h4>
<pre>public&nbsp;float&nbsp;getIconPitch()</pre>
<div class="block">Returns the pitch angle of the piece icon.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setIconPitch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIconPitch</h4>
<pre>public&nbsp;void&nbsp;setIconPitch(float&nbsp;iconPitch)</pre>
<div class="block">Sets the pitch angle of the piece icon.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="getIconScale--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getIconScale</h4>
<pre>public&nbsp;float&nbsp;getIconScale()</pre>
<div class="block">Returns the scale of the piece icon.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="setIconScale-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIconScale</h4>
<pre>public&nbsp;void&nbsp;setIconScale(float&nbsp;iconScale)</pre>
<div class="block">Sets the scale of the piece icon.</div>
<dl>
<dt><span class="simpleTagLabel">Since:</span></dt>
<dd>7.0</dd>
</dl>
</li>
</ul>
<a name="isProportional--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProportional</h4>
<pre>public&nbsp;boolean&nbsp;isProportional()</pre>
<div class="block">Returns <code>true</code> if piece proportions should be kept.</div>
</li>
</ul>
<a name="setProportional-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProportional</h4>
<pre>public&nbsp;void&nbsp;setProportional(boolean&nbsp;proportional)</pre>
<div class="block">Sets whether piece proportions should be kept or not.</div>
</li>
</ul>
<a name="isPieceOfFurnitureNameValid--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>isPieceOfFurnitureNameValid</h4>
<pre>public&nbsp;boolean&nbsp;isPieceOfFurnitureNameValid()</pre>
<div class="block">Returns <code>true</code> if piece name is valid.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ImportedFurnitureWizardController.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage"><a href='http://www.sweethome3d.com' target='_parent'><font size='+1'>Sweet Home 3D 7.5</font></a></div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/HomeView.SaveAnswer.html" title="enum in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.ImportedFurnitureWizardStepState.html" title="class in com.eteks.sweethome3d.viewcontroller"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/eteks/sweethome3d/viewcontroller/ImportedFurnitureWizardController.html" target="_top">Frames</a></li>
<li><a href="ImportedFurnitureWizardController.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small><table align='center'><tr>
                       <td><a href='http://www.sweethome3d.com' target='_parent'><img align='absmiddle' src='http://www.sweethome3d.com/images/SpaceMushroomsLogo.png' border='0'><a></td>
                       <td>&copy; Copyright 2024 Space Mushrooms
                           <br>Distributed under <a href='http://www.gnu.org/licenses/gpl-2.0.html' target='_parent'>GNU General Public License</a></td></tr></table></small></p>
</body>
</html>
